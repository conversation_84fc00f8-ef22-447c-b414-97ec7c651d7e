package com.mpolicy.manage.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.protocol.enums.SettlementProtocolEventEnum;
import com.mpolicy.manage.modules.protocol.helper.SettlementProtocolHelper;
import org.junit.Test;

import java.util.List;

public class MqTest extends AdminApplicationTests {


    @Test
    public void sendMq(){
        List<String> premCodeList= CollUtil.newArrayList("11111");
        String commissionCode = CommonUtils.createCodeLastNumber("PE");
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", "测试");
        //事件编码
        msgData.put("pushEventCode", commissionCode);
        //移除的费率key
        msgData.put("removePremCodeList", JSONObject.toJSONString(premCodeList));
        //插入的费率key
        msgData.put("insertPremCodeList", JSONObject.toJSONString(premCodeList));
        msgData.put("updatePremCodeList", JSONObject.toJSONString(premCodeList));
        SettlementProtocolHelper.pushSettlementProtocolEvent(commissionCode, SettlementProtocolEventEnum.COMMISSION_PRODUCT_PREM_CHANGE, msgData);
        SettlementProtocolHelper.pushSettlementProtocolEvent(commissionCode, SettlementProtocolEventEnum.COMMISSION_POLICY_PREM_CHANGE, msgData);
    }
}
