package com.mpolicy.manage.enums;

import com.mpolicy.manage.modules.content.enums.SearchJumpTypeEnum;
import com.mpolicy.manage.modules.content.enums.SearchTypeEnum;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/2 18:08
 */
public class SearchTypeEnumTest {
    @Test
    public void test() {
        assert !SearchTypeEnum.containType(SearchTypeEnum.HOT_SEARCH.getCode(), SearchJumpTypeEnum.PORTFOLIO.getCode());
        assert SearchTypeEnum.containType(SearchTypeEnum.HOT_SEARCH.getCode(), SearchJumpTypeEnum.QA.getCode());
        assert !SearchTypeEnum.containType(SearchTypeEnum.HOT_SEARCH.getCode(), SearchJumpTypeEnum.UNDERWRITING.getCode());

        assert !SearchTypeEnum.containType(SearchTypeEnum.HOME_SEARCH_TAG.getCode(), SearchJumpTypeEnum.PORTFOLIO.getCode());
        assert SearchTypeEnum.containType(SearchTypeEnum.HOME_SEARCH_TAG.getCode(), SearchJumpTypeEnum.QA.getCode());
        assert SearchTypeEnum.containType(SearchTypeEnum.HOME_SEARCH_TAG.getCode(), SearchJumpTypeEnum.UNDERWRITING.getCode());

        assert SearchTypeEnum.containType(SearchTypeEnum.PORTFOLIO_TAG.getCode(), SearchJumpTypeEnum.PORTFOLIO.getCode());
        assert !SearchTypeEnum.containType(SearchTypeEnum.PORTFOLIO_TAG.getCode(), SearchJumpTypeEnum.QA.getCode());
        assert !SearchTypeEnum.containType(SearchTypeEnum.PORTFOLIO_TAG.getCode(), SearchJumpTypeEnum.UNDERWRITING.getCode());

        assert !SearchTypeEnum.containType(SearchTypeEnum.UNDERWRITING_TAG.getCode(), SearchJumpTypeEnum.PORTFOLIO.getCode());
        assert !SearchTypeEnum.containType(SearchTypeEnum.UNDERWRITING_TAG.getCode(), SearchJumpTypeEnum.QA.getCode());
        assert SearchTypeEnum.containType(SearchTypeEnum.UNDERWRITING_TAG.getCode(), SearchJumpTypeEnum.UNDERWRITING.getCode());

        assert SearchTypeEnum.containType(SearchTypeEnum.PORTFOLIO_TAG.getCode(), SearchJumpTypeEnum.PORTFOLIO.getCode());
        assert !SearchTypeEnum.containType(SearchTypeEnum.PORTFOLIO_TAG.getCode(), SearchJumpTypeEnum.QA.getCode());
        assert !SearchTypeEnum.containType(SearchTypeEnum.PORTFOLIO_TAG.getCode(), SearchJumpTypeEnum.UNDERWRITING.getCode());


    }

}