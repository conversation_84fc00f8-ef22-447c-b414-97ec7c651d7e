package com.mpolicy.manage.tools;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.manage.AdminApplicationTests;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/19 10:57
 */
public class ToolsTest extends AdminApplicationTests {


    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/ag.xlsx");
        List<Map<String, Object>> readAll = reader.readAll();
        Map<String, Set<String>> hashMap = new HashMap<>();
        Set<String> idCardList = new HashSet<>();
        readAll.forEach(map -> {
            String agent_code = map.getOrDefault("agent_code", "").toString();
            if (StrUtil.isBlank(agent_code)) {
                return;
            }
            if (hashMap.containsKey(agent_code)) {
                Set<String> set = hashMap.get(agent_code);
                Object insured_id_card = map.get("insured_id_card");
                if (!StrUtil.isEmptyIfStr(insured_id_card)) {
                    set.add(insured_id_card.toString());
                    idCardList.add(insured_id_card.toString());
                }
                Object applicant_id_card = map.get("applicant_id_card");
                if (!StrUtil.isEmptyIfStr(applicant_id_card)) {
                    set.add(applicant_id_card.toString());
                    idCardList.add(applicant_id_card.toString());
                }
                hashMap.put(agent_code,set);
            } else {
                Set<String> set = new HashSet<>();
                Object insured_id_card = map.get("insured_id_card");
                if (!StrUtil.isEmptyIfStr(insured_id_card)) {
                    set.add(insured_id_card.toString());
                    idCardList.add(insured_id_card.toString());
                }
                Object applicant_id_card = map.get("applicant_id_card");
                if (!StrUtil.isEmptyIfStr(applicant_id_card)) {
                    set.add(applicant_id_card.toString());
                    idCardList.add(applicant_id_card.toString());
                }
                hashMap.put(agent_code,set);
            }
        });
        System.out.println(JSONUtil.toJsonStr(hashMap));
        System.out.println(JSONUtil.toJsonStr(idCardList));
        System.out.println(JSONUtil.toJsonStr(hashMap.keySet()));
    }

}
