package com.mpolicy.manage.tools;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.jobhandler.policy.PolicyHandler;
import com.mpolicy.manage.modules.sell.entity.SellProductSaveVo;
import com.mpolicy.manage.modules.sell.service.ISellProductService;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProductTest extends AdminApplicationTests {


    @Autowired
    private ISellProductService sellProductService;

    @Autowired
    private PolicyHandler policyHandler;
    @Autowired
    private StorageService storageService;
    @Test
    public void policyHandlerSign(){
        policyHandler.biOfflineSignPolicy();
    }
    @Test
    public void importProduct() {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        ThreadContext.bind(manager);

        Map<String, String> map = new HashMap<>();
        map.put("少儿", "FOR_THE_CROWD:CHILDREN");
        map.put("成人", "FOR_THE_CROWD:ADULT");
        map.put("老人", "FOR_THE_CROWD:OLD_MAN");
        map.put("旅游", "FOR_THE_CROWD:TRAVEL");
        map.put("国内", "FOR_THE_CROWD:TRAVEL:DOMESTIC_TRAVEL");
        map.put("国外", "FOR_THE_CROWD:TRAVEL:OVERSEAS_TRAVEL");
        map.put("自驾游", "FOR_THE_CROWD:TRAVEL:SELF_DRIVING_TOUR");
        //1.读取文件、
        ExcelReader reader = ExcelUtil.getReader("/Volumes/LSC/小鲸向海/资料/首期上线产品介绍.xlsx");
        List<Map<String, Object>> readAll = reader.readAll();
        readAll.forEach(action -> {
            if (action.get("商品名称") == null) {
                return;
            }
            StringBuffer sub = new StringBuffer();
            if (action.get("适用人群") != null) {
                String[] list = action.get("适用人群").toString().split("、");
                for (String key : list) {
                    sub.append(map.get(key)).append(";");
                }
            }
            SellProductSaveVo vo = new SellProductSaveVo();
            vo.setSupplierCode(getValue(action.get("供应商编码")));
            vo.setProductName(getValue(action.get("商品名称")));
            vo.setProductAbbreviation(getValue(action.get("商品简称（10字内）")));
            vo.setProductSubtitle(getValue(action.get("副标题（30字内）")));
            vo.setInsuranceAge(getValue(action.get("投保年龄（15字内）")));
            vo.setGuaranteePeriod(getValue(action.get("保障期间")));
            vo.setProductPrice(new BigDecimal(getValue(action.get("88.88"))));
            vo.setProductPriceUnit(getValue(action.get("元/年")));
            vo.setProductLabel(getValue(action.get("评价标签（每个5字内;分号隔开）")));
            vo.setClientType(3);
            vo.setProductThumbnail(getProductThumbnail(getValue(action.get("产品缩略图"))));
            vo.setProductCover(getProductCover(getValue(action.get("产品封面"))));
            vo.setApplicableGroupList(sub.toString());
            vo.setProductStatus(1);
            sellProductService.save(vo);
        });
    }

    private String getProductThumbnail(String imageName) {
        if (StrUtil.isBlank(imageName)) {
            return "";
        }
        String imageUrl = "/Volumes/LSC/小鲸向海/资料/产品图/产品缩略图/" + imageName;
        File file = FileUtil.newFile(imageUrl);
        OssBaseOut str = storageService.uploadFileInputSteam("/SELL/product-thumbnail/" + imageName, file);
        System.out.println(str.toString());
        return str.getFilePath();
    }

    private String getProductCover(String imageName) {
        if (StrUtil.isBlank(imageName)) {
            return "";
        }
        String imageUrl = "/Volumes/LSC/小鲸向海/资料/产品图/产品封面/" + imageName;
        File file = FileUtil.newFile(imageUrl);
        OssBaseOut str = storageService.uploadFileInputSteam("/SELL/product-cover/" + imageName, file);
        System.out.println(str.toString());
        return str.getFilePath();
    }

    private String getValue(Object value) {
        if (value == null) {
            return "";
        }
        return value.toString();
    }


    @Test
    public void testUpload() {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        ThreadContext.bind(manager);
        String productThumbnail = getProductCover("1.png");
        System.out.println(productThumbnail);
    }
}
