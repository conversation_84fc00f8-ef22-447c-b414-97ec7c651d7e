package com.mpolicy.manage.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/19 15:53
 */
@Slf4j
public class RequestUtilsTest {
    public static void main(String[] args) {
        //exportAllMinNo("/Users/<USER>/Desktop/小保单号.xlsx");
        /*Map<String, String> bigPoNo = getBigPoNo(CollUtil.newArrayList("HA01117012F6V6V37C00","HA01117012FK4TKY3800",
                "HA01117012F6P0VRN000","HA01117012FR4K477C00","HA01117012FR3PV6YV00","HA01117012FLUY0PWV00",
                "HA01117012FNNFMRRV00","HA01117012FNBHYWFG00"));

        log.info("bigPoNo={}", bigPoNo);*/
//        Map<String, String> cz = getCz(CollUtil.newArrayList("HA01117012F6V6V37C00", "HA01117012FK4TKY3800",
//                "HA01117012F6P0VRN000", "HA01117012FR4K477C00", "HA01117012FR3PV6YV00", "HA01117012FLUY0PWV00",
//                "HA01117012FNNFMRRV00", "HA01117012FNBHYWFG00"));
//        log.info("cz={}", cz);
   /*    bigPoNo.values().stream().distinct().forEach(no -> {
           List<String> minNos = getBigPoNoMinNo(no);
           log.info("maxNo=[{}],minNos={}", no, minNos);
           List<String> bigPoNoPrem = getBigPoNoPrem(no);
           log.info("对账单信息maxNo=[{}],prem={}", no, bigPoNoPrem);
        });
*/
//        List<String> bigPoNoPrem = getBigPoNoPrem("SSTI000192431167900000");
//        log.info("对账单信息maxNo=[{}],prem={}", "SSTI000192431167900000", bigPoNoPrem);


        Map<String, BigDecimal> map = readStatementAccount("/Users/<USER>/Desktop/小鲸新洲手续费明细清单");
        BigDecimal totalPrem = map.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("对账单保费金额={}",totalPrem);
        map.forEach((key,value)->{

        });
        //ExcelWriter writer = new ExcelWriter(path);
    }

    /**
     * 读取保单明细,匹配大保单号
     */
    private static void test() {

    }

    private static List<String> readDetails() {

        return null;
    }

    /**
     * 读取保司对账单
     * @param path
     * @return
     */
    private static Map<String, BigDecimal> readStatementAccount(String path) {
        List<List<Object>> readList = new ArrayList<>();
        File[] ls = FileUtil.ls(path);
        for (File file : ls) {
            File[] ls1 = FileUtil.ls(file.getAbsolutePath());
            List<List<Object>> tempList = new ArrayList<>();
            for (File file1 : ls1) {
               // log.info("开始读取解析文件=[{}]/{},sheet=[{}]的数据", file.getAbsolutePath(),file1.getName(), 0);
                ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file1);
                List<List<Object>> read = reader.read(3);
                read.forEach(ac->{
                    if (CollUtil.newArrayList("SSTI141312069224950000").contains(ac.get(2).toString())){
                        log.info("ac={}",ac);
                    }
                });

                readList.addAll(read);
                tempList.addAll(read);
            }
//            BigDecimal prem = tempList.stream().map(m -> new BigDecimal(m.get(10).toString())).reduce(BigDecimal.ZERO,
//                    BigDecimal::add);
            //log.info("文件夹={} 合计保费={}",file.getAbsolutePath(),prem);
        }
//        Map<String, BigDecimal> resultMap = readList.stream().collect(Collectors.toMap(k -> k.get(2).toString(),
//                v -> (new BigDecimal(v.get(10).toString()).multiply(new BigDecimal("0.3"))), BigDecimal::add));
//        Map<String, BigDecimal> resultMap = readList.stream().collect(Collectors.toMap(k -> k.get(2).toString(),
//                v -> (new BigDecimal(v.get(10).toString())), BigDecimal::add));
        Map<String, BigDecimal> resultMap = readList.stream()
                .filter(f->!f.get(2).toString().startsWith("SS"))
                .collect(Collectors.toMap(k -> k.get(2).toString(),
                v -> (new BigDecimal(v.get(10).toString())), BigDecimal::add));
        //2219 对账单保费金额=87729.23
        log.info("=[{}]={}",resultMap.size(),resultMap);
        return resultMap;
    }

    /**
     * 根据大保单号获取所有小保单号
     *
     * @param no
     * @return
     */
    private static List<String> getBigPoNoMinNo(String no) {
        List<String> resultList = new ArrayList<>();
        File[] ls = FileUtil.ls("/Users/<USER>/Desktop/苏宁大保单号相关数据2");
        for (File file : ls) {
            try {
                for (int ii = 0; ii < cn.hutool.poi.excel.ExcelUtil.getReader(file).getSheetCount(); ii++) {
                    log.info("开始读取解析文件=[{}]sheet=[{}]的数据", file.getAbsolutePath(), ii);
                    List<List<Object>> read = cn.hutool.poi.excel.ExcelUtil.getReader(file, ii).read(1);
                    for (int i = 0; i < read.size(); i++) {
                        List<Object> objects = read.get(i);
                        String maxNo = objects.get(0).toString();
                        String minNo = objects.get(1).toString();
                        if (no.equals(maxNo)) {
                            resultList.add(minNo);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析数据异常", e);
            }
        }
        return resultList.stream().distinct().collect(Collectors.toList());
    }

    private static void exportAllMinNo(String path) {
        //通过工具类创建writer
        ExcelWriter writer = new ExcelWriter(path);
        List<List<Object>> resultList = new ArrayList<>();
        List<Map<String, BigDecimal>> premList = new ArrayList<>();
        File[] ls = FileUtil.ls("/Users/<USER>/Desktop/苏宁大保单号相关数据2");
        for (File file : ls) {
            try {
                for (int ii = 0; ii < cn.hutool.poi.excel.ExcelUtil.getReader(file).getSheetCount(); ii++) {
                    log.info("开始读取解析文件=[{}]sheet=[{}]的数据", file.getAbsolutePath(), ii);
                    List<List<Object>> read = cn.hutool.poi.excel.ExcelUtil.getReader(file, ii).read(1);
                    resultList.addAll(read);
                }
            } catch (Exception e) {
                log.error("解析数据异常", e);
            }
        }
        List<List<String>> rows =
                resultList.stream().map(m -> m.get(1).toString()).distinct().map(CollUtil::newArrayList).collect(Collectors.toList());
        //计算一下总保费
        // 这里处理自己的逻辑
        BigDecimal reduce = resultList.stream().collect(Collectors.toMap(k -> k.get(0), v -> v, (v1, v2) -> {
            // 这里处理自己的逻辑
            if (new BigDecimal(v1.get(3).toString()).compareTo(BigDecimal.ZERO) < 0) {
                return v1;
            }
            return v2;
        })).values().stream().map(m -> new BigDecimal(m.get(4).toString())).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("合计保费={}", reduce);
        //一次性写出内容，强制输出标题
        writer.write(rows);
        //关闭writer，释放内存
        writer.close();
    }


    private static List<String> getBigPoNoPrem(String bigPoNo) {
        List<String> list = new ArrayList<>();
        File[] ls = FileUtil.ls("/Users/<USER>/Desktop/小鲸新洲手续费明细清单");
        for (File file : ls) {
            File[] ls1 = FileUtil.ls(file.getAbsolutePath());
            for (File file1 : ls1) {
                log.info("开始读取解析文件=[{}]sheet=[{}]的数据", file.getAbsolutePath(), 0);
                ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file1);
                List<List<Object>> read = reader.read(2);
                for (int i = 0; i < read.size(); i++) {
                    List<Object> objects = read.get(i);
                    if (i > 0) {
                        String poNo = objects.get(2).toString();
                        if (bigPoNo.equals(poNo)) {
                            String prem = objects.get(10).toString();
                            list.add(prem);
                        }
                    }
                }
            }
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

    private static Map<String, String> getBigPoNo(List<String> minNos) {
        Map<String, String> map = new HashMap<>();
        File[] ls = FileUtil.ls("/Users/<USER>/Desktop/苏宁大保单号相关数据2");
        for (File file : ls) {
            try {
                for (int ii = 0; ii < cn.hutool.poi.excel.ExcelUtil.getReader(file).getSheetCount(); ii++) {
                    log.info("开始读取解析文件=[{}]sheet=[{}]的数据", file.getAbsolutePath(), ii);
                    List<List<Object>> read = cn.hutool.poi.excel.ExcelUtil.getReader(file, ii).read(1);
                    for (int i = 0; i < read.size(); i++) {
                        List<Object> objects = read.get(i);
                        String maxNo = objects.get(0).toString();
                        String minNo = objects.get(1).toString();
                        if (minNos.contains(minNo)) {
                            map.put(minNo, maxNo);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析数据异常", e);
            }
        }
        return map;
    }

    private static Map<String, String> getCz(List<String> minNos) {
        Map<String, String> map = new HashMap<>();
        File[] ls = FileUtil.ls("/Users/<USER>/Desktop/苏宁大保单号-冲减-相关数据2");
        for (File file : ls) {
            try {
                for (int ii = 0; ii < cn.hutool.poi.excel.ExcelUtil.getReader(file).getSheetCount(); ii++) {
                    log.info("开始读取解析文件=[{}]sheet=[{}]的数据", file.getAbsolutePath(), ii);
                    List<List<Object>> read = cn.hutool.poi.excel.ExcelUtil.getReader(file, ii).read(1);
                    for (int i = 0; i < read.size(); i++) {
                        List<Object> objects = read.get(i);
                        String maxNo = objects.get(0).toString();
                        String minNo = objects.get(1).toString();
                        if (minNos.contains(minNo)) {
                            map.put(minNo, maxNo);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析数据异常", e);
            }
        }
        return map;
    }

}