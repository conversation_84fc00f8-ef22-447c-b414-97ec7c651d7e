package com.mpolicy.manage.modules.order;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.order.enums.RatingLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 订单风险客户测试
 *
 *
 *
 *
 * @create 2024/10/18
 * @since 1.0.0
 */
@Slf4j
public class InsureOrderCustomerRiskTest {

    @Test
    public void testRatingLevelEnum(){
        String jsonString = JSON.toJSONString(Result.success(Arrays.asList(RatingLevelEnum.values())));
        log.info("response = {}",jsonString);
    }
}

