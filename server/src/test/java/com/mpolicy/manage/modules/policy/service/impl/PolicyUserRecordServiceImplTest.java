package com.mpolicy.manage.modules.policy.service.impl;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.policy.dao.EpPolicyGroupSubInfoDao;
import com.mpolicy.manage.modules.policy.service.EpPolicyGroupSubInfoService;
import com.mpolicy.manage.modules.policy.service.PolicyGroupInfoService;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.common.enums.PolicyFamilyTypeEnum;
import com.mpolicy.policy.common.enums.PolicyGenderEnum;
import com.mpolicy.policy.common.enums.PolicyIdCardTypeEnum;
import com.mpolicy.policy.common.enums.PolicyMainFlagEnum;
import com.mpolicy.policy.common.ep.policy.base.EpInsuredBase;
import com.mpolicy.policy.common.ep.policy.group.sub.EpGroupInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.group.sub.EpGroupRelatedInsuredInfoVo;

import com.mpolicy.service.common.service.DicCacheHelper;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/7 17:01
 */
public class PolicyUserRecordServiceImplTest extends AdminApplicationTests {
    @Autowired
    SysDocumentService sysDocumentService;

    @Autowired
    StorageService storageService;

    @Autowired
    private PolicyGroupInfoService policyGroupInfoService;

    @Autowired
    private EpPolicyGroupSubInfoDao epPolicyGroupSubInfoDao;

    @Autowired
    private EpPolicyClient epPolicyClient;

    @Autowired
    private EpPolicyGroupSubInfoService epPolicyGroupSubInfoService;

    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Test
    public void asyncTest() throws ExecutionException, InterruptedException {
        CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(() -> {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new IllegalStateException(e);
            }
            System.out.println("运行在一个单独的线程当中");
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new IllegalStateException(e);
            }
            System.out.println("运行结束，返回");
            return true;
        });
        Boolean aBoolean = completableFuture.get();
        System.out.println(aBoolean);
    }




    @Test
    public void importGroupTest() {
       String fileCode="oss20211227142631sPgFJj";
        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("分单文件不存在")));


    }
}
