package com.mpolicy.manage.modules.preservation;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity;
import com.mpolicy.manage.modules.policy.entity.PreservationTeamPeopleEntity;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationBaseService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationTeamPeopleService;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationPolicyBasicChange;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.common.enums.PolicyFamilyTypeEnum;
import com.mpolicy.policy.common.enums.PolicyGenderEnum;
import com.mpolicy.policy.common.enums.PolicyIdCardTypeEnum;
import com.mpolicy.policy.common.enums.PolicyPreserveGroupPolicyInsuredChangeTypeEnum;
import com.mpolicy.policy.common.ep.policy.group.sub.EpGroupInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreserveGroupPolicyInsuredChangeInfoVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 保全测试
 *
 * <AUTHOR>
 * @date 2022-03-21 18:34
 */
@Slf4j
public class PolicyPreservationTest extends AdminApplicationTests {

    @Autowired
    private PreservationApplyService preservationApplyService;

    @Autowired
    private PreservationBaseService preservationBaseService;

    @Autowired
    private EpPolicyClient epPolicyClient;

    @Autowired
    private PreservationTeamPeopleService preservationTeamPeopleService;

    @Test
    public void policyPreservationJob(){
        // 1 获取需要处理修改保单状态的保全信息> 退保、犹豫期退保和协议解约三个保全，将根据保全生效日期将保单状态变更为退保/犹豫期退保/协议解约状态。
        List<PreservationApplyEntity> list = preservationApplyService.lambdaQuery()
                .eq(PreservationApplyEntity::getProcessingStatus, 0)
                .ge(PreservationApplyEntity::getPreservationEffectTime, DateUtil.today())
                .list();

        log.info("需要保全修改为保单状态：{}", list.size());

        // 2 遍历处理
        list.forEach(x -> {
            log.info("变更保单号={},变更状态={}", x.getContractCode(), x.getPreservationProject());
        });
        // default success
        log.info("保单保全状态变更任务完成");
    }

    @Test
    public void queryGroupInsuredInfoList(){
        Result<List<EpGroupInsuredInfoVo>> epResult = epPolicyClient.queryGroupInsuredInfoList("ct20220114142100vrMN3A");
        log.info("解析增减员信息结果={}", epResult);
    }

    @Test
    public void conversionAddOrSubtract(){
        List<PreservationPolicyBasicChange> list = preservationBaseService.conversionAddOrSubtract("ct20220302172354JbfdKj","oss202204151114132jmyK5");
        log.info("解析增减员信息结果={}", JSON.toJSONString(list));
        List<PreservationTeamPeopleEntity> teamPeopleList = builderPreservationTeamPeople("xiaoma", "sunhaijun", "policy", list);
        log.info("入db后的数据={}", JSON.toJSONString(teamPeopleList));
    }


    @Test
    public void voTest() {
        List<PreservationPolicyBasicChange> addOrSubtractList = preservationBaseService.conversionAddOrSubtract("ct20220114142100vrMN3A", "oss202204151114132jmyK5");
        log.info("提交的vo={}",JSON.toJSONString(addOrSubtractList));
        List<PreservationTeamPeopleEntity> result = new ArrayList<>();
        addOrSubtractList.forEach(x -> {
            PreservationTeamPeopleEntity bean = new PreservationTeamPeopleEntity();
            BeanUtils.copyProperties(x, bean);
            bean.setPreservationCode("xiaoma");
            bean.setContractCode("xiaoma-c");
            bean.setPolicyCode("20220606");
            bean.setPeopleNumber(x.getId());
            bean.setPeopleType(x.getType());
            result.add(bean);
        });
        preservationTeamPeopleService.saveBatch(result);

        // 增减员信息设置
        List<PreservationTeamPeopleEntity> peopleList = preservationTeamPeopleService.lambdaQuery().eq(PreservationTeamPeopleEntity::getPreservationCode, "xiaoma").list();
        log.info("入库的po={}",JSON.toJSONString(peopleList));

        List<PreservationPolicyBasicChange> resultVo = new ArrayList<>();
        peopleList.forEach(x -> {
            PreservationPolicyBasicChange bean = new PreservationPolicyBasicChange();
            BeanUtils.copyProperties(x, bean);
            bean.setId(x.getPeopleNumber());
            bean.setType(x.getPeopleType());
            resultVo.add(bean);
        });
        log.info("入库的po 转vo={}",JSON.toJSONString(resultVo));

        List<EpPreserveGroupPolicyInsuredChangeInfoVo> epPreserveGroupPolicyInsuredChangeInfoVos = builderInsuredChangeInfoVo(resultVo);

        log.info("保全提交的vo={}",JSON.toJSONString(epPreserveGroupPolicyInsuredChangeInfoVos));
    }

    private List<EpPreserveGroupPolicyInsuredChangeInfoVo> builderInsuredChangeInfoVo(List<PreservationPolicyBasicChange> addOrSubtractList) {
        List<EpPreserveGroupPolicyInsuredChangeInfoVo> result = new ArrayList<>();
        addOrSubtractList.forEach(x ->{
            EpPreserveGroupPolicyInsuredChangeInfoVo bean = new EpPreserveGroupPolicyInsuredChangeInfoVo();
            BeanUtils.copyProperties(x,bean);
            if(StringUtils.equals(x.getType(),"加人")) {
                bean.setInsuredChangeTypeEnum(PolicyPreserveGroupPolicyInsuredChangeTypeEnum.ADD_INSURED);
            }else if(StringUtils.equals(x.getType(),"减人")){
                bean.setInsuredChangeTypeEnum(PolicyPreserveGroupPolicyInsuredChangeTypeEnum.DEL_INSURED);
            }
            // 被保人性别
            bean.setInsuredGender(PolicyGenderEnum.getEnumByDesc(x.getInsuredGender()));
            // 被保人出生日期
            if(StringUtils.isNotBlank(x.getInsuredBirthday())) {
                bean.setInsuredBirthday(DateUtil.parse(x.getInsuredBirthday(), "yyyy-MM-dd"));
            }
            // 被保人证据类型
            if(StringUtils.isNotBlank(x.getInsuredIdType())) {
                bean.setInsuredIdType(PolicyIdCardTypeEnum.deName(x.getInsuredIdType()));
            }
            // 关系
            bean.setFirstInsuredRelation(PolicyFamilyTypeEnum.deFamilyDesc(x.getFirstInsuredRelation()));
            // 国籍
            List<DicCacheHelper.DicEntity> cList = DicCacheHelper.getSons("COUNTRY_LIST");
            cList.stream().filter(d -> StringUtils.equals(d.getValue(),x.getInsuredNation())).findFirst().ifPresent(p ->{
                bean.setInsuredNation(p.getKey());
            });
            result.add(bean);
        });
        return result;
    }



    private List<PreservationTeamPeopleEntity> builderPreservationTeamPeople(String preservationCode, String contractCode, String policyCode, List<PreservationPolicyBasicChange> addOrSubtractList) {
        List<PreservationTeamPeopleEntity> result = new ArrayList<>();
        addOrSubtractList.forEach(x -> {
            PreservationTeamPeopleEntity bean = new PreservationTeamPeopleEntity();
            BeanUtils.copyProperties(x, bean);
            bean.setPreservationCode(preservationCode);
            bean.setContractCode(contractCode);
            bean.setPolicyCode(policyCode);
            result.add(bean);
        });
        return result;
    }
}
