package com.mpolicy.manage.modules.protocol;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.protocol.service.EpProtocolBasicTestService;
import com.mpolicy.manage.modules.protocol.vo.ProductPremExcel;
import com.mpolicy.manage.utils.ProductPremUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

/**
 * 协议管理测试
 *
 * @author: haijun.sun
 * @date: 2021-12-08 14:21
 */
@Slf4j
public class ProtocolTest extends AdminApplicationTests {

    @Autowired
    private EpProtocolBasicTestService epProtocolBasicTestService;


    /**
     * 初始化协议
     */
    @Test
    public void initProtocol() throws FileNotFoundException {
        epProtocolBasicTestService.initProtocol("https://oss-xjxhserver.xiaowhale.com/POUNDAGE_RECONCILE/BILL/20230521/34ee0216da7a48968fbf98e2a62f1d7c/ep_protocol_basic_info.xlsx");
        log.info("数据导入完成....");
    }


    /*public static void main(String[] args) {
        //重新生成协议编码
        //1.将文件内容都读取到内存中,然后进行处理
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/协议/ep_protocol_company.xlsx");
        List<Map<String, Object>> readProtocolCompanyList = reader.readAll();
        Map<String, String> protocolCodeMap = new HashMap<>();
        readProtocolCompanyList.forEach(action -> {
            String protocolCode = getMapValueStr(action, "protocol_code");
            if (StrUtil.isBlank(protocolCode)) {
                return;
            }
            //创建新协议编码和老协议编码对应关系
            createNewProtocolCode(protocolCodeMap, protocolCode);
        });
        log.info("新code和原code={}", JSONUtil.toJsonStr(protocolCodeMap));
    }*/

    private static String createNewProtocolCode(Map<String, String> protocolCodeMap, String protocolCode) {
        //判断是否存在,如果存在那么在生成一次
        String newProtocolCode = CommonUtils.createCodeLastNumber("P");
        if (protocolCodeMap.values().contains(newProtocolCode)) {
            createNewProtocolCode(protocolCodeMap, protocolCode);
        } else {
            protocolCodeMap.put(protocolCode, newProtocolCode);
        }
        return newProtocolCode;
    }

    private static String getMapValueStr(Map<String, Object> map, String key) {
        Object obj = map.get(key);
        return obj == null ? null : obj.toString();
    }


    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/合家欢.xlsx");
        List<ProductPremExcel> readAll = reader.readAll(ProductPremExcel.class);
        for (int i = 0; i < readAll.size(); i++) {
            ProductPremExcel productPrem = readAll.get(i);
            // 校验必填项没有填写 产品名称,适用分公司,费用类型,开始时间,结束时间
            if (!ProductPremUtil.checkProtocolPremRequired(productPrem)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件第" + (i + 2) + "行数据必填项未填写,请下载模版进行核对后上传"));
            }
        }
    }

}
