package com.mpolicy.manage.modules.common.service.impl;

import cn.hutool.core.io.FileUtil;
import com.mpolicy.common.mail.IMailService;
import com.mpolicy.manage.AdminApplicationTests;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CustomMailTest extends AdminApplicationTests {

    /**
     * 注入邮件服务
     */
    @Autowired
    private IMailService mailService;


    /**
     * 测试 demo
     */
    @Test
    public void sendMailDemo(){
        String content   = "<p style=\"color: #F56C6C\">测试</p>";
        mailService.asyncSendHtml("<EMAIL>", "终止保险代理合同通知书", content, FileUtil.file("/Users/<USER>/Desktop/manage/protocol/协议手费率模版.xlsx"));
    }

}
