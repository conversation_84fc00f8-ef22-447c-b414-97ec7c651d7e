package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("tmp_reconcile_company_product")
public class TmpReconcileCompanyProductEntity implements Serializable {

    private static final long serialVersionUID = -4909549665932286682L;
    @TableId
    private Integer id;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    private String reconcileCompanyCode;
    /**
     * 保司对账单产品名称
     */
    private String companyProductName;
    /**
     * 协议险种编码
     */
    private String insuranceProductCode;
    /**
     * 协议险种名称
     */
    private String insuranceProductName;
    /**
     * 小鲸险种编码
     */
    private String productCode;

    private String endorsementNo;

    private Date insuranceTime;
    /**
     * 小鲸险种名称
     */
    private String productName;
    private String monthStr;
}
