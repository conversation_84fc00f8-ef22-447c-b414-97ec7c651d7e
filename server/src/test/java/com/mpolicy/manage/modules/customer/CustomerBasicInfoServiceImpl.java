package com.mpolicy.manage.modules.customer;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

public class CustomerBasicInfoServiceImpl extends AdminApplicationTests {

    @Autowired
    private  CustomerBasicInfoService customerBasicInfoService;
    @Test
    public void sss(){
        //获取推荐人客户数量
        int customerNum = Optional.ofNullable(customerBasicInfoService.lambdaQuery()
                .eq(CustomerBasicInfoEntity::getReferrerCode,"R20220120154044db11M0")
                .apply("COALESCE(inner_referrer_code, '') != {0}","R20220120154044db11M0")
                .count()).orElse(0);
        System.out.println(customerNum);
    }
}
