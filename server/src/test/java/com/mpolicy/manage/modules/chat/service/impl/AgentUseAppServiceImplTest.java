package com.mpolicy.manage.modules.chat.service.impl;

import cn.hutool.json.JSONUtil;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut;
import com.mpolicy.manage.modules.chat.entity.AgentUserAppListOutVo;
import com.mpolicy.manage.modules.chat.service.AgentUseAppService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class AgentUseAppServiceImplTest extends AdminApplicationTests {


    @Autowired
    private AgentUseAppService agentUseAppService;


    @Test
    public void tes() {
        AgentUserAppListOutVo vo = new AgentUserAppListOutVo();
        vo.setPage(1);
        vo.setLimit(-1);
        PageUtils<AgentUseAppListOut> page = agentUseAppService.findPageList(vo);
        System.out.println(JSONUtil.toJsonStr(page.getList()));
    }
}
