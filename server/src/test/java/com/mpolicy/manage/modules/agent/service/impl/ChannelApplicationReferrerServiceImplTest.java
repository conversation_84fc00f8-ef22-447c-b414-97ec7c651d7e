package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.customer.client.CustomerClient;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 批量插入推荐人
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 10:25
 */
@Slf4j
public class ChannelApplicationReferrerServiceImplTest extends AdminApplicationTests {
    @Value("${wx.miniapp.appid}")
    private String appid;
    @Autowired
    CustomerClient customerClient;
    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    StorageService storageService;
    @Autowired
    ChannelApplicationReferrerService channelApplicationReferrerService;

    @Before
    public void loadProperties() {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        ThreadContext.bind(manager);
    }

    /// @Test
    public void importReferrer() {
        List<Map<String, Object>> maps = jdbcTemplate.queryForList("select * from 云南区域数据汇总表");
        System.out.println(maps);

        Map<String, ChannelApplicationReferrerEntity> referrerMap = channelApplicationReferrerService.list(
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                        .in(ChannelApplicationReferrerEntity::getReferrerWno, maps.stream().map(x -> x.get("员工工号")).collect(Collectors.toList()))
        ).stream().collect(Collectors.toMap(ChannelApplicationReferrerEntity::getReferrerWno, x -> x));
        List<Object> 员工工号1 = maps.stream().map(x -> x.get("员工工号")).filter(x -> referrerMap.get(x) == null).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(员工工号1));
        System.out.println(员工工号1.size());
        List<ChannelApplicationReferrerEntity> collect = maps.stream().map(x -> {
            String 员工工号 = (String)x.get("员工工号");
            ChannelApplicationReferrerEntity channelApplicationReferrerEntity = referrerMap.get(员工工号);

            if (channelApplicationReferrerEntity == null) {
                channelApplicationReferrerEntity = ChannelApplicationReferrerEntity.builder()
                        .referrerCode(CommonUtils.createCode("R"))
                        .referrerLevel(1)
                        .applicationCode("ZHNX")
                        .performance(BigDecimal.ZERO)
                        .referrerWno(String.valueOf(x.get("员工工号")))
                        .createUser("admin")
                        .updateUser("admin")
                        .filePath("")
                        .build();
            }
            channelApplicationReferrerEntity.setReferrerRegion("REFERRER_REGION:20");
            channelApplicationReferrerEntity.setReferrerName(String.valueOf(x.get("员工姓名")));
            channelApplicationReferrerEntity.setReferrerMobile(String.valueOf(x.get("手机号")));
            return channelApplicationReferrerEntity;
        }).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(collect));
        channelApplicationReferrerService.saveOrUpdateBatch(collect);
    }

    /// @Test
    public void createQrCode() {
        List<ChannelApplicationReferrerEntity> list = channelApplicationReferrerService.list(
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                        .eq(ChannelApplicationReferrerEntity::getReferrerRegion, "REFERRER_REGION:20")
        ).stream().peek(x -> {
            Result<String> wxaCode = customerClient.createWxaCode("wxf4f3efd0ea7c9cd4", AdminPublicConstant.WX_QR_APPLICATION, x.getApplicationCode(), x.getReferrerCode());
            if (!wxaCode.isSuccess()) {
                throw new GlobalException(wxaCode);
            }
            x.setFilePath(DomainUtil.removeDomain(wxaCode.getData()));
        }).collect(Collectors.toList());
        channelApplicationReferrerService.updateBatchById(list);
    }

    @Test
    public void qrCode() {
        long start = System.currentTimeMillis();
        channelApplicationReferrerService.list().forEach(x -> {
            String filePath = x.getFilePath();
            try (InputStream inputStream = new URL(DomainUtil.addOssDomainIfNotExist(filePath)).openStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                BufferedImage waterBim = ImgUtil.read(inputStream);
                int width = waterBim.getWidth();
                int height = waterBim.getHeight();
                int scale = 60;
                //将图片放大
                Img.from(waterBim)
                        .scale(width, height + scale, Color.WHITE)
                        .pressText(x.getReferrerWno(), Color.BLACK, new Font(null, Font.PLAIN, scale / 3 - 5), 0, (height + scale) / 2, 1f)
                        .write(outputStream);
                outputStream.flush();
                // 调用oss 二维码图片进行上传
                String substring = filePath.substring(filePath.lastIndexOf('/') + 1);
                OssBaseOut ossBaseOut = storageService.uploadBytesFile("qrcode/".concat(
                        substring
                ), outputStream.toByteArray());
                x.setFilePath(ossBaseOut.getFilePath());

                channelApplicationReferrerService.updateById(x);
                log.info("工号：{}，新二维码处理完成", x.getReferrerWno());
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        long now = System.currentTimeMillis();
        log.info("用时{}毫秒", (now - start));
    }


}
