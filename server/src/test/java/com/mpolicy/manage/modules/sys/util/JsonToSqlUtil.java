package com.mpolicy.manage.modules.sys.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Json文本文件转Sql语句工具
 *
 * <AUTHOR>
 */
public class JsonToSqlUtil {

    public static void main(String[] args) throws Exception {
        long startTime = System.currentTimeMillis();
        System.out.println("----------Json文本文件转Sql开始----------");
        jsonToSql();
        System.out.println("----------Json文本文件转Sql结束，耗时：" + (System.currentTimeMillis() - startTime) + "毫秒----------");
    }

    private static void jsonToSql() throws Exception {
        // ClassPathResource resource = new ClassPathResource("/Users/<USER>/Desktop/country.json");
        File file = new File("C:\\Users\\<USER>\\Desktop\\民族.json");
        String data = FileUtils.readFileToString(file);
        JSONArray jsonArray = JSONArray.parseArray(data);
        String parentSql = "INSERT INTO `mp_dictionary` (`dic_key`, `dic_value`, `parent_key`, `remark`, `create_user`, `create_time`, `update_user`, `update_time`, `revision`) VALUES ('NATIONALITY_LIST', '民族列表', '', '所有民族列表', 'hxh', '2021-03-15 13:44:32', 'hxh', '2021-03-15 13:44:32', 1); \r\n";
        write(parentSql);
        //根据文本文件数据格式，遍历解析数据
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject province = (JSONObject)jsonArray.get(i);
            int code = i;
            String name = province.getString("name");
            String remark = "";
            String sql = "INSERT INTO `mp_dictionary` (`dic_key`, `dic_value`, `parent_key`, `sort`, `remark`, `create_user`, `create_time`, `update_user`, `update_time`, `revision`) VALUES ('NATIONALITY_LIST:" + code + "', '" + name + "', 'NATIONALITY_LIST', '" + (i + 1) + "','" + remark + "', 'hxh', '2021-03-15 13:44:32', 'hxh', '2021-03-15 13:44:32', 1); \r\n";
            System.out.println(sql);
            write(sql);
            //市
           /* JSONArray cityArray = province.getJSONArray("children");
            if (cityArray != null && cityArray.size() > 0) {
                for (int j = 0; j < cityArray.size(); j++) {
                    JSONObject city = (JSONObject) cityArray.get(j);
                    System.out.println(city);
                    String cityId = IdGen.uuid();
                    String cityCode = city.getString("code");
                    String cityName = city.getString("name");
                    String citySql = "INSERT INTO SYS_ADMINISTRATIVE_DIVISION (ID, CODE, NAME, PARENT_CODE, PARENT_NAME, DESCRIBE, IS_ENABLE)VALUES('" + cityId + "','" + cityCode + "','" + cityName + "','" + provinceCode + "','" + provinceName + "','','1'); \r\n";
                    System.out.println(citySql);
                    write(citySql);
                    //区县
                    JSONArray districtArray = city.getJSONArray("children");
                    if (districtArray != null && districtArray.size() > 0) {
                        for (int k = 0; k < districtArray.size(); k++) {
                            JSONObject district = (JSONObject) districtArray.get(k);
                            System.out.println(district);
                            String districtId = IdGen.uuid();
                            String districtCode = district.getString("code");
                            String districtName = district.getString("name");
                            String districtSql = "INSERT INTO SYS_ADMINISTRATIVE_DIVISION (ID, CODE, NAME, PARENT_CODE, PARENT_NAME, DESCRIBE, IS_ENABLE)VALUES('" + districtId + "','" + districtCode + "','" + districtName + "','" + cityCode + "','" + cityName + "','','1'); \r\n";
                            System.out.println(districtSql);
                            write(districtSql);
                        }
                    }
                }
            }*/
        }
    }

    /**
     * 写入sql文件
     *
     * @param sql
     * @throws IOException
     */
    private static void write(String sql) throws IOException {
        File file;
        file = new File("C:\\Users\\<USER>\\Desktop\\data.sql");
        if (!file.exists()) {
            file.createNewFile();
        }
        FileWriter fileWriter = new FileWriter(file, true);
        BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
        bufferedWriter.write(sql);
        bufferedWriter.close();
    }
}
