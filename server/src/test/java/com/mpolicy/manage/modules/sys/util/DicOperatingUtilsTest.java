package com.mpolicy.manage.modules.sys.util;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.redis.MPRedisService;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.sys.entity.MpDictionaryEntity;
import com.mpolicy.manage.modules.sys.service.MpDictionaryService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/3 16:51
 */
public class DicOperatingUtilsTest extends AdminApplicationTests {
    @Autowired
    MPRedisService mpRedisService;

    @Autowired
    IRedisService iRedisService;

    @Autowired
    MpDictionaryService mpDictionaryService;
    public final static String MP_PREFIX = "mp:";

    @Test
    public void add() {
        mpRedisService.hset(MP_PREFIX + "work", "degree", "学位");
        mpRedisService.hset(MP_PREFIX + "work:degree", "1", "学士");
    }

    @Test
    public void saveTest() {
        // mpDictionaryService.saveOrUpdateRedis("work", "测试");
        // mpDictionaryService.saveOrUpdateRedis("degree","学位", "work");
        // mpDictionaryService.saveOrUpdateRedis("1", "学士", "work","degree");
    }

    @Test
    public void saveServiceTest() {
        MpDictionaryEntity entity = new MpDictionaryEntity();
        entity.setDicKey("test");
        entity.setDicValue("测试");
        entity.setParentKey("4");
        mpDictionaryService.saveEntity(entity);
    }

    @Test
    public void saveRedisTest() {
        mpRedisService.hset("mp:" + "bbb", "parentKey", "aaa");
        mpRedisService.hset("mp:" + "bbb", "value", "测试");
        mpRedisService.hset("mp:" + "bbb", "sonKey", "ccc");
    }

    @Test
    public void utilsTest() {
        ArrayList<String> strings = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            strings.add(String.valueOf(i));
        }
        // DicOperatingUtils.addDic("degree", JSON.toJSONString(strings));
        // DicOperatingUtils.addDic("bachelor", "1", "degree");
        // DicOperatingUtils.addDic("freshman", "2", "bachelor");

        // System.out.println(DicOperatingUtils.getValue("degree", new TypeReference<List<String>>(){}));
        // System.out.println(DicOperatingUtils.getSons("degree"));
        // System.out.println(DicOperatingUtils.getParentKey("degree"));

        // System.out.println(mpOperating.getValueString("bachelor"));
        // System.out.println(mpOperating.getSons("bachelor"));
        // System.out.println(mpOperating.getParentKey("bachelor"));
        //
        // System.out.println(mpOperating.getValueString("freshman"));
        // System.out.println(mpOperating.getSons("freshman"));
        // System.out.println(mpOperating.getParentKey("freshman"));
    }
}