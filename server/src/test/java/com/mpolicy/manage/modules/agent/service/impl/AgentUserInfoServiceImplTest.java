package com.mpolicy.manage.modules.agent.service.impl;

import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.UpdateAreaManagerRegionVo;
import com.mpolicy.manage.modules.policy.entity.EpPolicyTransferTaskEntity;
import com.mpolicy.manage.modules.policy.service.EpPolicyTransferTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class AgentUserInfoServiceImplTest extends AdminApplicationTests {

    @Autowired
    private EpPolicyTransferTaskService epPolicyTransferTaskService;
    @Autowired
    private AgentUserInfoService agentUserInfoService;

    /**
     * 测试员工离职
     */
    @Test
    public void testAgentQuit() {
        //判断一下将要离职的代理人是否存在转移中的保单,免得他负责的保单变成孤儿单.
        String agentCode = "ag20220915140129Q2Dtzs";
        Integer count = epPolicyTransferTaskService.lambdaQuery()
                .ne(EpPolicyTransferTaskEntity::getTaskStatus, StatusEnum.NORMAL.getCode())
                .and(x -> x.eq(EpPolicyTransferTaskEntity::getSourceAgentCode, agentCode)
                        .or().eq(EpPolicyTransferTaskEntity::getTargetAgentCode, agentCode))
                .count();
        log.info("count:{}", count);
    }

    @Test
    public void transferCustomer() {
        //判断一下将要离职的代理人是否存在转移中的保单,免得他负责的保单变成孤儿单.
        UpdateAreaManagerRegionVo updateAreaManagerRegion = new UpdateAreaManagerRegionVo();
        updateAreaManagerRegion.setAreaManagerRegion("REFERRER_REGION:7");
        updateAreaManagerRegion.setAgentCode("ag20210712172633ePz9wj");
        updateAreaManagerRegion.setReplaceAgentCode("ag20210818144540M1sqro");
    }
}
