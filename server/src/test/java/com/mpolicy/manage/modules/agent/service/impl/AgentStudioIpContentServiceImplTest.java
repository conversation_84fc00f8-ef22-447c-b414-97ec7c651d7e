package com.mpolicy.manage.modules.agent.service.impl;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.agent.entity.AgentStudioIpContentEntity;
import com.mpolicy.manage.modules.agent.service.AgentStudioIpContentService;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/24 14:25
 */
public class AgentStudioIpContentServiceImplTest extends AdminApplicationTests {
    @Autowired
    AgentStudioIpContentService agentStudioIpContentService;

    @Test
    public void testSave() {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        ThreadContext.bind(manager);

        AgentStudioIpContentEntity agentStudioIpContentEntity = new AgentStudioIpContentEntity();
        agentStudioIpContentEntity.setContentText("");
        agentStudioIpContentEntity.setAgentCode("123");
        agentStudioIpContentEntity.setCreateUser("test");
        agentStudioIpContentEntity.setUpdateUser("test");
        agentStudioIpContentEntity.setCreateTime(new Date());
        agentStudioIpContentEntity.setUpdateTime(new Date());
        agentStudioIpContentService.saveOrUpdateEntity(agentStudioIpContentEntity);
    }

}