package com.mpolicy.manage.modules.sys.config;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.sys.service.MpDictionaryService;
import com.mpolicy.manage.modules.sys.service.SysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName: RedisConfigTest
 * @description: 缓存刷新
 * @author: haijun.sun
 * @date: 2021-03-18 19:32
 **/
@Slf4j
public class RedisConfigTest  extends AdminApplicationTests {

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private MpDictionaryService mpDictionaryService ;

    @Test
    public void changeEnable() {
        sysConfigService.loadAll();
        mpDictionaryService.refreshDic();

        log.info("加载完成");
    }
}
