package com.mpolicy.manage.modules.agent.service.impl;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AgentUseAppServiceImplTest extends AdminApplicationTests {

    @Autowired
    OrgInfoService orgInfoService;

    @Autowired
    AgentUserInfoService agentUserInfoService;

    @Test
    public void testAgentQuit() {
        List<String> collect = agentUserInfoService.list().stream().map(x -> x.getOrgCode()).collect(Collectors.toList());
        Map<String, OrgInfoEntity> map = orgInfoService.findFirstOrgInfo(collect);
        log.info("Map:{}", map);
    }
}
