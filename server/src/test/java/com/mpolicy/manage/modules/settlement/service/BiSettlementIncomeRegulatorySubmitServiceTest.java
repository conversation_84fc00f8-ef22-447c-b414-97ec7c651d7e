package com.mpolicy.manage.modules.settlement.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.bi.dao.BiSettlementIncomeRegulatorySubmitDao;
import com.mpolicy.manage.modules.bi.vo.IncomeRegulatorySubmitMonth;
import com.mpolicy.manage.modules.regulators.service.RegulatorsMonthReportService;
import com.mpolicy.manage.modules.regulators.vo.CreateRegulatorsMonthReport;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class BiSettlementIncomeRegulatorySubmitServiceTest extends AdminApplicationTests {

    @Autowired
    private BiSettlementIncomeRegulatorySubmitDao biSettlementIncomeRegulatorySubmitDao;

    @Autowired
    private RegulatorsMonthReportService regulatorsMonthReportService;


    @Test
    public void importFile() {
        DateTime beginTime = DateUtil.parseDate("2024-01-01 00:00:00");
        DateTime endTime = DateUtil.parseDate("2024-08-31 00:00:00");
        List<IncomeRegulatorySubmitMonth> dbList = biSettlementIncomeRegulatorySubmitDao.findIncomeRegulatorySubmitMonth(beginTime, endTime);
        Map<String, List<IncomeRegulatorySubmitMonth>> propertyInsuranceMap = dbList.stream().filter(f -> "PRODUCT:lawType:2".equals(f.getLawType())).collect(Collectors.groupingBy(IncomeRegulatorySubmitMonth::getReportOrgName));
        Map<String, List<IncomeRegulatorySubmitMonth>> lifeInsuranceMap = dbList.stream().filter(f -> "PRODUCT:lawType:1".equals(f.getLawType())).collect(Collectors.groupingBy(IncomeRegulatorySubmitMonth::getReportOrgName));
        /*
            家庭财产保险
         */
        propertyInsuranceMap.forEach((orgName, resList) -> {
            List<List<BigDecimal>> res = handleProperty(resList);
            String tempPath = StrUtil.format("/Users/<USER>/Desktop/监管报表备份/{}-产险.xlsx", orgName);
            FileUtil.copy("/Users/<USER>/Desktop/监管报表/监管报送产险模版.xlsx", tempPath, true);
            // 创建一个新的 Excel 文件用于写入
            cn.hutool.poi.excel.ExcelWriter writer = ExcelUtil.getWriter(tempPath);
            // 添加其他数据
            for (int i = 0; i < res.size(); i++) {
                List<BigDecimal> datas = res.get(i);
                for (int i1 = 0; i1 < datas.size(); i1++) {
                    writer.writeCellValue(i1 + 2, i + 3, datas.get(i1));
                }
            }
            // 刷新并关闭
            writer.flush();
            writer.close();
        });

        lifeInsuranceMap.forEach((orgName, resList) -> {
            List<List<BigDecimal>> res = handleLife(resList);
            String tempPath = StrUtil.format("/Users/<USER>/Desktop/监管报表备份/{}-寿险.xlsx", orgName);
            FileUtil.copy("/Users/<USER>/Desktop/监管报表/监管报送寿险模版.xlsx", tempPath, true);
            // 创建一个新的 Excel 文件用于写入
            cn.hutool.poi.excel.ExcelWriter writer = ExcelUtil.getWriter(tempPath);
            // 添加其他数据
            for (int i = 0; i < res.size(); i++) {
                List<BigDecimal> datas = res.get(i);
                for (int i1 = 0; i1 < datas.size(); i1++) {
                    writer.writeCellValue(i1 + 2, i + 3, datas.get(i1));
                }
            }
            // 刷新并关闭
            writer.flush();
            writer.close();
        });
    }


    @Test
    public void createRegulatorsMonthReport() {
        CreateRegulatorsMonthReport createRegulatorsMonthReport = new CreateRegulatorsMonthReport();
        createRegulatorsMonthReport.setBeginMonth(DateUtil.parseDate("2024-01-01"));
        createRegulatorsMonthReport.setEndMonth(DateUtil.parseDate("2024-08-31"));
        regulatorsMonthReportService.createRegulatorsMonthReport(createRegulatorsMonthReport);
    }

    /**
     * 处理寿险
     *
     * @param resList 寿险数据
     * @return 寿险处理结果
     */
    private List<List<BigDecimal>> handleLife(List<IncomeRegulatorySubmitMonth> resList) {
        List<List<BigDecimal>> resultList = new ArrayList<>();
        for (int i = 0; i < 23; i++) {
            List<BigDecimal> dataList = new ArrayList<>();
            for (int j = 0; j < 13; j++) {
                dataList.add(BigDecimal.ZERO);
            }
            resultList.add(dataList);
        }
        Map<String, IncomeRegulatorySubmitMonth> incomeRegulatorySubmitMonthMap = resList.stream()
                .collect(Collectors.toMap(k -> k.getLevel2Code() + "=" + (StrUtil.isNotBlank(k.getLevel3Code()) ? k.getLevel3Code() : "NULL"), Function.identity()));
        Map<String, IncomeRegulatorySubmitMonth> incomeRegulatorySubmitMonthLeve2Map = resList.stream()
                .collect(Collectors.toMap(IncomeRegulatorySubmitMonth::getLevel2Code, Function.identity(), (v1, v2) -> {
                    IncomeRegulatorySubmitMonth submitMonth = BeanUtil.copyProperties(v1, IncomeRegulatorySubmitMonth.class);
                    submitMonth.setPremium(submitMonth.getPremium().add(v2.getPremium()));// 保费
                    submitMonth.setSettlementAmount(submitMonth.getSettlementAmount().add(v2.getSettlementAmount()));// 手续费
                    submitMonth.setNewPremium(submitMonth.getNewPremium().add(v2.getNewPremium()));// 新单保费
                    submitMonth.setRenewalPremium(submitMonth.getRenewalPremium().add(v2.getRenewalPremium()));// 续期保费
                    submitMonth.setNewSettlementAmount(submitMonth.getNewSettlementAmount().add(v2.getNewSettlementAmount()));// 新单手续费
                    submitMonth.setRenewalSettlementAmount(submitMonth.getRenewalSettlementAmount().add(v2.getRenewalSettlementAmount()));// 续期手续费
                    submitMonth.setOnlinePremium(submitMonth.getOnlinePremium().add(v2.getOnlinePremium()));// 线上保费
                    submitMonth.setOnlineSettlementAmount(submitMonth.getOnlineSettlementAmount().add(v2.getOnlineSettlementAmount()));// 线上手续费
                    submitMonth.setOnlineNewPremium(submitMonth.getOnlineNewPremium().add(v2.getOnlineNewPremium()));// 线上新单保费
                    submitMonth.setOnlineRenewalPremium(submitMonth.getOnlineRenewalPremium().add(v2.getOnlineRenewalPremium()));// 线上续期保费
                    submitMonth.setOnlineNewSettlementAmount(submitMonth.getOnlineNewSettlementAmount().add(v2.getOnlineNewSettlementAmount()));// 线上新单手续费
                    submitMonth.setOnlineRenewalSettlementAmount(submitMonth.getOnlineRenewalSettlementAmount().add(v2.getOnlineRenewalSettlementAmount()));// 线上续期手续费
                    return submitMonth;
                }));

        incomeRegulatorySubmitMonthMap.forEach((key, obj) -> {
            switch (key) {
                // 普通寿险 - 定期寿险
                case "PRODUCT:LEVEL2_CAT:SX:1=PRODUCT:LEVEL2_CAT:SX:1:1": {
                    resultList.set(2, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 第三方网销平台新单保费
                            BigDecimal.ZERO,// 第三方网销平台续期保费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 普通寿险 - 两全寿险
                case "PRODUCT:LEVEL2_CAT:SX:1=PRODUCT:LEVEL2_CAT:SX:1:2": {
                    resultList.set(3, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 第三方网销平台新单保费
                            BigDecimal.ZERO,// 第三方网销平台续期保费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 普通寿险 - 终身寿险
                case "PRODUCT:LEVEL2_CAT:SX:1=PRODUCT:LEVEL2_CAT:SX:1:3": {
                    resultList.set(4, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 第三方网销平台新单保费
                            BigDecimal.ZERO,// 第三方网销平台续期保费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 普通寿险 - 年金保险
                case "PRODUCT:LEVEL2_CAT:SX:1=PRODUCT:LEVEL2_CAT:SX:1:4": {
                    resultList.set(5, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                // 分红寿险 - 定期寿险
                case "PRODUCT:LEVEL2_CAT:SX:2=PRODUCT:LEVEL2_CAT:SX:2:1": {
                    resultList.set(7, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                // 分红寿险 - 两全寿险
                case "PRODUCT:LEVEL2_CAT:SX:2=PRODUCT:LEVEL2_CAT:SX:2:2": {
                    resultList.set(8, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                // 分红寿险 - 终身寿险
                case "PRODUCT:LEVEL2_CAT:SX:2=PRODUCT:LEVEL2_CAT:SX:2:3": {
                    resultList.set(9, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                // 分红寿险 - 年金保险
                case "PRODUCT:LEVEL2_CAT:SX:2=PRODUCT:LEVEL2_CAT:SX:2:4": {
                    resultList.set(10, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                // 投资连结产品 - 年金保险
                case "PRODUCT:LEVEL2_CAT:SX:3=PRODUCT:LEVEL2_CAT:SX:3:1": {
                    resultList.set(12, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                // 万能寿险 - 年金保险
                case "PRODUCT:LEVEL2_CAT:SX:4=PRODUCT:LEVEL2_CAT:SX:4:1": {
                    resultList.set(14, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                //意外伤害险小计 -一年期以内业务
                case "PRODUCT:LEVEL2_CAT:SX:5=PRODUCT:LEVEL2_CAT:SX:5:1": {
                    resultList.set(16, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                //意外伤害险小计 -一年期业务
                case "PRODUCT:LEVEL2_CAT:SX:5=PRODUCT:LEVEL2_CAT:SX:5:2": {
                    resultList.set(17, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                //意外伤害险小计 一年期以上业务
                case "PRODUCT:LEVEL2_CAT:SX:5=PRODUCT:LEVEL2_CAT:SX:5:3": {
                    resultList.set(18, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                //健康险小计 短期健康险
                case "PRODUCT:LEVEL2_CAT:SX:6=PRODUCT:LEVEL2_CAT:SX:6:1": {
                    resultList.set(20, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
                //健康险小计 长期健康险
                case "PRODUCT:LEVEL2_CAT:SX:6=PRODUCT:LEVEL2_CAT:SX:6:2": {
                    resultList.set(21, CollUtil.newArrayList(
                            obj.getNewPremium(),// 新单保费
                            obj.getRenewalPremium(),// 续期保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getNewSettlementAmount(),// 新单手续费
                            obj.getRenewalSettlementAmount(),// 续期手续费
                            obj.getOnlineNewPremium(),// 线上新单保费
                            obj.getOnlineRenewalPremium(),// 线上续期保费
                            obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                            obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                            BigDecimal.ZERO,// 线下新单保费
                            BigDecimal.ZERO,// 线下续期保费
                            BigDecimal.ZERO,// 线下新单手续费
                            BigDecimal.ZERO));// 线下续期手续费
                    break;
                }
            }
        });
        List<BigDecimal> tempList = new ArrayList<>();
        // 这里是二级分类的合计数据
        // 普通寿险
        for (int i = 0; i < 13; i++) {
            BigDecimal total = resultList.get(2).get(i)
                    .add(resultList.get(3).get(i))
                    .add(resultList.get(4).get(i))
                    .add(resultList.get(5).get(i));
            tempList.add(total);
        }
        resultList.set(1, tempList);
        // *分红寿险
        tempList = new ArrayList<>();
        for (int i = 0; i < 13; i++) {
            BigDecimal total = resultList.get(7).get(i)
                    .add(resultList.get(8).get(i))
                    .add(resultList.get(9).get(i))
                    .add(resultList.get(10).get(i));
            tempList.add(total);
        }
        resultList.set(6, tempList);
        // *投资连结产品
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:SX:3")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:SX:3");
            resultList.set(11, CollUtil.newArrayList(
                    obj.getNewPremium(),// 新单保费
                    obj.getRenewalPremium(),// 续期保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getNewSettlementAmount(),// 新单手续费
                    obj.getRenewalSettlementAmount(),// 续期手续费
                    obj.getOnlineNewPremium(),// 线上新单保费
                    obj.getOnlineRenewalPremium(),// 线上续期保费
                    obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                    obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                    BigDecimal.ZERO,// 线下新单保费
                    BigDecimal.ZERO,// 线下续期保费
                    BigDecimal.ZERO,// 线下新单手续费
                    BigDecimal.ZERO));// 线下续期手续费
        }
        // *万能寿险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:SX:4")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:SX:4");
            resultList.set(13, CollUtil.newArrayList(
                    obj.getNewPremium(),// 新单保费
                    obj.getRenewalPremium(),// 续期保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getNewSettlementAmount(),// 新单手续费
                    obj.getRenewalSettlementAmount(),// 续期手续费
                    obj.getOnlineNewPremium(),// 线上新单保费
                    obj.getOnlineRenewalPremium(),// 线上续期保费
                    obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                    obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                    BigDecimal.ZERO,// 线下新单保费
                    BigDecimal.ZERO,// 线下续期保费
                    BigDecimal.ZERO,// 线下新单手续费
                    BigDecimal.ZERO));// 线下续期手续费
        }
        // 一、寿险小计
        tempList = new ArrayList<>();
        for (int i = 0; i < 13; i++) {
            BigDecimal total = resultList.get(1).get(i).add(resultList.get(6).get(i)).add(resultList.get(11).get(i)).add(resultList.get(13).get(i));
            tempList.add(total);
        }
        resultList.set(0, tempList);// 寿险小计
        // *意外伤害险小计
        tempList = new ArrayList<>();
        for (int i = 0; i < 13; i++) {
            BigDecimal total = resultList.get(16).get(i)
                    .add(resultList.get(17).get(i))
                    .add(resultList.get(18).get(i));
            tempList.add(total);
        }
        resultList.set(15, tempList);
        // *健康险小计
        tempList = new ArrayList<>();
        for (int i = 0; i < 13; i++) {
            BigDecimal total = resultList.get(20).get(i)
                    .add(resultList.get(21).get(i));
            tempList.add(total);
        }
        resultList.set(19, tempList);
        // *其他
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:SX:7")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:SX:7");
            resultList.set(22, CollUtil.newArrayList(
                    obj.getNewPremium(),// 新单保费
                    obj.getRenewalPremium(),// 续期保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getNewSettlementAmount(),// 新单手续费
                    obj.getRenewalSettlementAmount(),// 续期手续费
                    obj.getOnlineNewPremium(),// 线上新单保费
                    obj.getOnlineRenewalPremium(),// 线上续期保费
                    obj.getOnlineNewSettlementAmount(),// 线上新单手续费
                    obj.getOnlineRenewalSettlementAmount(),// 线上续期手续费
                    BigDecimal.ZERO,// 线下新单保费
                    BigDecimal.ZERO,// 线下续期保费
                    BigDecimal.ZERO,// 线下新单手续费
                    BigDecimal.ZERO));// 线下续期手续费
        }
        // 这里有一个总计....
        List<BigDecimal> totalList = new ArrayList<>();
        for (int i = 0; i < 13; i++) {
            BigDecimal total = resultList.get(0).get(i).add(resultList.get(15).get(i)).add(resultList.get(19).get(i)).add(resultList.get(22).get(i));
            totalList.add(total);
        }
        resultList.add(totalList);// 合计
        return resultList;
    }

    /**
     * 处理人身险
     *
     * @param resList 人身险数据
     * @return 人身险处理结果
     */
    private List<List<BigDecimal>> handleProperty(List<IncomeRegulatorySubmitMonth> resList) {
        List<List<BigDecimal>> resultList = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            List<BigDecimal> dataList = new ArrayList<>();
            for (int j = 0; j < 7; j++) {
                dataList.add(BigDecimal.ZERO);
            }
            resultList.add(dataList);
        }
        Map<String, IncomeRegulatorySubmitMonth> incomeRegulatorySubmitMonthMap = resList.stream()
                .collect(Collectors.toMap(k -> k.getLevel2Code() + "=" + (StrUtil.isNotBlank(k.getLevel3Code()) ? k.getLevel3Code() : "NULL"), Function.identity()));
        Map<String, IncomeRegulatorySubmitMonth> incomeRegulatorySubmitMonthLeve2Map = resList.stream()
                .collect(Collectors.toMap(IncomeRegulatorySubmitMonth::getLevel2Code, Function.identity(), (v1, v2) -> {
                    IncomeRegulatorySubmitMonth submitMonth = BeanUtil.copyProperties(v1, IncomeRegulatorySubmitMonth.class);
                    submitMonth.setPremium(submitMonth.getPremium().add(v2.getPremium()));// 保费
                    submitMonth.setSettlementAmount(submitMonth.getSettlementAmount().add(v2.getSettlementAmount()));// 手续费
                    submitMonth.setNewPremium(submitMonth.getNewPremium().add(v2.getNewPremium()));// 新单保费
                    submitMonth.setRenewalPremium(submitMonth.getRenewalPremium().add(v2.getRenewalPremium()));// 续期保费
                    submitMonth.setNewSettlementAmount(submitMonth.getNewSettlementAmount().add(v2.getNewSettlementAmount()));// 新单手续费
                    submitMonth.setRenewalSettlementAmount(submitMonth.getRenewalSettlementAmount().add(v2.getRenewalSettlementAmount()));// 续期手续费
                    submitMonth.setOnlinePremium(submitMonth.getOnlinePremium().add(v2.getOnlinePremium()));// 线上保费
                    submitMonth.setOnlineSettlementAmount(submitMonth.getOnlineSettlementAmount().add(v2.getOnlineSettlementAmount()));// 线上手续费
                    submitMonth.setOnlineNewPremium(submitMonth.getOnlineNewPremium().add(v2.getOnlineNewPremium()));// 线上新单保费
                    submitMonth.setOnlineRenewalPremium(submitMonth.getOnlineRenewalPremium().add(v2.getOnlineRenewalPremium()));// 线上续期保费
                    submitMonth.setOnlineNewSettlementAmount(submitMonth.getOnlineNewSettlementAmount().add(v2.getOnlineNewSettlementAmount()));// 线上新单手续费
                    submitMonth.setOnlineRenewalSettlementAmount(submitMonth.getOnlineRenewalSettlementAmount().add(v2.getOnlineRenewalSettlementAmount()));// 线上续期手续费
                    return submitMonth;
                }));

        incomeRegulatorySubmitMonthMap.forEach((key, obj) -> {
            switch (key) {
                // 家庭财产保险 - 其中：投资型家财险
                case "PRODUCT:LEVEL2_CAT:CX:13=PRODUCT:LEVEL2_CAT:CX:13:1": {
                    resultList.set(2, CollUtil.newArrayList(
                            obj.getPremium(),// 保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getSettlementAmount(),// 手续费
                            obj.getOnlinePremium(),// 线上保费
                            obj.getOnlineSettlementAmount(),// 线上手续费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 机动车辆保险 - 其中：交强险
                case "PRODUCT:LEVEL2_CAT:CX:3=PRODUCT:LEVEL2_CAT:CX:3:1": {
                    resultList.set(4, CollUtil.newArrayList(
                            obj.getPremium(),// 保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getSettlementAmount(),// 手续费
                            obj.getOnlinePremium(),// 线上保费
                            obj.getOnlineSettlementAmount(),// 线上手续费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 保证保险 - 其中：机动车辆消费贷款保证保险
                case "PRODUCT:LEVEL2_CAT:CX:7=PRODUCT:LEVEL2_CAT:CX:7:1": {
                    resultList.set(9, CollUtil.newArrayList(
                            obj.getPremium(),// 保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getSettlementAmount(),// 手续费
                            obj.getOnlinePremium(),// 线上保费
                            obj.getOnlineSettlementAmount(),// 线上手续费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 保证保险 - 其中：个人贷款抵押房屋保证保险
                case "PRODUCT:LEVEL2_CAT:CX:7=PRODUCT:LEVEL2_CAT:CX:7:2": {
                    resultList.set(10, CollUtil.newArrayList(
                            obj.getPremium(),// 保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getSettlementAmount(),// 手续费
                            obj.getOnlinePremium(),// 线上保费
                            obj.getOnlineSettlementAmount(),// 线上手续费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 健康险 - 其中：投资型健康险
                case "PRODUCT:LEVEL2_CAT:CX:1=PRODUCT:LEVEL2_CAT:CX:1:1": {
                    resultList.set(16, CollUtil.newArrayList(
                            obj.getPremium(),// 保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getSettlementAmount(),// 手续费
                            obj.getOnlinePremium(),// 线上保费
                            obj.getOnlineSettlementAmount(),// 线上手续费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
                // 意外伤害保险 - 其中：投资型意外险
                case "PRODUCT:LEVEL2_CAT:CX:2=PRODUCT:LEVEL2_CAT:CX:2:1": {
                    resultList.set(18, CollUtil.newArrayList(
                            obj.getPremium(),// 保费
                            BigDecimal.ZERO,// 应付保费
                            obj.getSettlementAmount(),// 手续费
                            obj.getOnlinePremium(),// 线上保费
                            obj.getOnlineSettlementAmount(),// 线上手续费
                            BigDecimal.ZERO,// 第三方网销平台新单手续费
                            BigDecimal.ZERO));// 第三方网销平台续期手续费
                    break;
                }
            }
        });
        // 1.企业财产保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:12")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:12");
            resultList.set(0, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        // 2.家庭财产保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:13")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:13");
            resultList.set(1, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }

        // 3.机动车辆保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:3")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:3");
            resultList.set(3, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }

        // 4.工程保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:4")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:4");
            resultList.set(5, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        // 5.责任保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:5")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:5");
            resultList.set(6, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        // 6.信用保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:6")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:6");
            resultList.set(7, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        // 7.保证保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:7")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:7");
            resultList.set(8, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }


        // 8.船舶保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:8")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:8");
            resultList.set(11, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        // 9.货物运输保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:9")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:9");
            resultList.set(12, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        //10.特殊风险保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:10")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:10");
            resultList.set(13, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        //11.农业保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:11")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:11");
            resultList.set(14, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }
        // 12.健康险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:1")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:1");
            resultList.set(15, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }

        // 13.意外伤害保险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:2")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:2");
            resultList.set(17, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }

        // 14.其他险
        if (incomeRegulatorySubmitMonthLeve2Map.containsKey("PRODUCT:LEVEL2_CAT:CX:14")) {
            IncomeRegulatorySubmitMonth obj = incomeRegulatorySubmitMonthLeve2Map.get("PRODUCT:LEVEL2_CAT:CX:14");
            resultList.set(19, CollUtil.newArrayList(
                    obj.getPremium(),// 保费
                    BigDecimal.ZERO,// 应付保费
                    obj.getSettlementAmount(),// 手续费
                    obj.getOnlinePremium(),// 线上保费
                    obj.getOnlineSettlementAmount(),// 线上手续费
                    BigDecimal.ZERO,// 第三方网销平台新单手续费
                    BigDecimal.ZERO));// 第三方网销平台续期手续费
        }

        // 这里有一个总计....
        List<BigDecimal> totalList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            BigDecimal total = resultList.get(0).get(i)
                    .add(resultList.get(1).get(i))
                    .add(resultList.get(3).get(i))
                    .add(resultList.get(5).get(i))
                    .add(resultList.get(6).get(i))
                    .add(resultList.get(7).get(i))
                    .add(resultList.get(8).get(i))
                    .add(resultList.get(11).get(i))
                    .add(resultList.get(12).get(i))
                    .add(resultList.get(13).get(i))
                    .add(resultList.get(14).get(i))
                    .add(resultList.get(15).get(i))
                    .add(resultList.get(17).get(i))
                    .add(resultList.get(19).get(i));
            totalList.add(total);
        }
        resultList.add(totalList);// 合计
        return resultList;
    }
}
