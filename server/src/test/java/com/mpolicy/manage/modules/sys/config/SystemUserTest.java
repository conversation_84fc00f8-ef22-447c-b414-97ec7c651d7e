package com.mpolicy.manage.modules.sys.config;

import org.apache.shiro.crypto.hash.Sha256Hash;
import org.junit.Test;

/***
 *
 * @version 1.0.0
 * @date 2024/12/5
 * <AUTHOR>
 ***/
public class SystemUserTest {

    @Test
    public void genPassword(){
        String password = "123456";

        String salt="ikcZ5YLbBZ3RU4J1X1Tm";
        String newPassword = new Sha256Hash(password, salt).toHex();
        System.out.println(newPassword);
    }
}
