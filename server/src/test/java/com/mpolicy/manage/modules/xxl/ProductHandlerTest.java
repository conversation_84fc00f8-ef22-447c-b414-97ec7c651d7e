package com.mpolicy.manage.modules.xxl;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.jobhandler.product.SellProductHandler;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description todo
 * @create 2024/7/15
 * @since 1.0.0
 */
public class ProductHandlerTest extends AdminApplicationTests {

    @Resource
    SellProductHandler sellProductHandler;

    @Test
    public void scheduledUpOrDownSellProductTest(){
        sellProductHandler.scheduledUpOrDownSellProduct();

    }
    @Test
    public void productRenewalReminderMessagePushTest(){
        sellProductHandler.productRenewalReminderMessagePush();

    }

}
