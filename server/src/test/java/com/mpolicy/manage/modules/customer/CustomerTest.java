package com.mpolicy.manage.modules.customer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.agent.vo.customer.ChildChannelInfoVo;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.customer.vo.UpdateCustomerChannelVo;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName: CustomerTest
 * Description:
 * date: 2024/2/23 10:49
 *
 * <AUTHOR>
 */
public class CustomerTest extends AdminApplicationTests {

    @Autowired
    private ChannelInfoService channelInfoService;
    @Autowired
    private CustomerBasicInfoService customerBasicInfoService;

    /**
     * 测试客户渠道信息
     */
    @Test
    public void testCustomerChannelList() {
        List<ChannelInfoVo> list = channelInfoService.lambdaQuery().list().stream().map(a-> {
            ChannelInfoVo bean = new ChannelInfoVo();
            BeanUtils.copyProperties(a,bean);
            return bean;
        }).collect(Collectors.toList());
        //获取配置信息
        String commonChannelConfig = ConstantCacheHelper.getValue(Constant.COMMON_CHANNEL_CONFIG, null);
        if (StringUtils.isBlank(commonChannelConfig)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("渠道配置映射缺失"));
        }
        JSONObject json = JSONObject.parseObject(commonChannelConfig);
        JSONArray channelName = json.getJSONArray("child_channel_name");
        List<ChildChannelInfoVo> collect = list.stream().map(a -> {
            ChildChannelInfoVo bean = new ChildChannelInfoVo();
            BeanUtils.copyProperties(a, bean);
            if (StringUtils.equals(a.getChannelCode(), Constant.ZHNX_CHANNEL_CODE)) {
                List<com.mpolicy.open.common.common.ChannelInfoVo> childChannelList = channelName.stream().map(b -> {
                    com.mpolicy.open.common.common.ChannelInfoVo childChannelInfo = new com.mpolicy.open.common.common.ChannelInfoVo();
                    childChannelInfo.setChannelCode(b.toString().split("-")[0]);
                    childChannelInfo.setChannelName(b.toString().split("-")[1]);
                    return childChannelInfo;
                }).collect(Collectors.toList());
                bean.setChildChannelList(childChannelList);
            }
            return bean;
        }).collect(Collectors.toList());
        System.out.println("获取到的渠道信息为:" + JSONObject.toJSONString(collect));
    }

    /**
     * 测试更新客户渠道信息
     */
    @Test
    public void testUpdateCustomerChannel(){
        UpdateCustomerChannelVo input = new UpdateCustomerChannelVo();
        input.setChannelCode("zhnx");
        input.setChannelName("中和农信");
        input.setCustomerCode("C20231115175241791208");
        input.setChildChannelCode(null);
        customerBasicInfoService.updateCustomerChannel(input);
    }
}
