package com.mpolicy.manage.modules.feign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.feign.client.DingTalkMessageClient;
import com.mpolicy.manage.feign.model.CommonResult;
import com.mpolicy.manage.feign.model.MsgPush2User;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DingTalkMessageClientTest extends AdminApplicationTests {

    @Autowired
    private DingTalkMessageClient dingTalkMessageClient;

    @Test
    public void sendMsgPush2User() {
        MsgPush2User msgPush2User = new MsgPush2User();
        msgPush2User.setMsgType("markdown");
        msgPush2User.setContent("");
        msgPush2User.setJobNumbers(CollUtil.newArrayList("CNBJ0583"));
        CommonResult<String> stringCommonResult = dingTalkMessageClient.sendMsgPush2User(msgPush2User);
        log.info("result = {}",JSONUtil.toJsonStr(stringCommonResult));
    }
}
