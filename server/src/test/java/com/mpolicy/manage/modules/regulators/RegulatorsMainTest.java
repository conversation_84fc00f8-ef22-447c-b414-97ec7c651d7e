package com.mpolicy.manage.modules.regulators;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.manage.modules.regulators.service.report.data.*;
import com.mpolicy.manage.modules.regulators.service.report.listener.*;
import com.mpolicy.manage.modules.regulators.utils.RegulatorsExcelUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Regulators
 *
 * <AUTHOR>
 * @date 2022-01-20 19:28
 */
@Slf4j
public class RegulatorsMainTest {


    public static void main(String[] args) {
        // List<CompanyAssetsLiabilitiesData> reportData = RegulatorsExcelUtil.readExcelByList("/Users/<USER>/Downloads/专业代理机构/资产负债表.xls", 1, 2, new CompanyAssetsLiabilitiesListener());
        // log.info("资产负债表信息={}", JSON.toJSONString(reportData));

        // List<CompanyBasicInfoData> companyBasicInfoData = RegulatorsExcelUtil.readExcelByList("/Users/<USER>/Downloads/专业代理机构/基本情况表.xls", 1, 2, new CompanyBasicInfoListener());
        // log.info("基本情况表报告数据={}", JSON.toJSONString(companyBasicInfoData));


        // List<CompanyIncomeData> companyIncomeData = RegulatorsExcelUtil.readExcelByList("/Users/<USER>/Downloads/专业代理机构/利润表.xls", 1, 2, new CompanyIncomeListener());
        // log.info("利润表报告数据={}", JSON.toJSONString(companyIncomeData));

        // List<LifeCompanyBusinessData> lifeCompanyBusinessData = RegulatorsExcelUtil.readExcelByList("/Users/<USER>/Downloads/专业代理机构/代理人身险公司业务表.xls", 1, 3, new LifeCompanyBusinessListener());
        // log.info("代理人身险公司业务表数据={}", JSON.toJSONString(lifeCompanyBusinessData));

        //List<PropertyCompanyBusinessData> propertyCompanyBusinessData = RegulatorsExcelUtil.readExcelByList("/Users/<USER>/Downloads/专业代理机构/代理产险公司业务表.xls", 1, 3, new PropertyCompanyBusinessListener());
        //log.info("代理产险公司业务表报告数据={}", JSON.toJSONString(propertyCompanyBusinessData));

        FileUtil.mkdir("logs/xiaoma");
    }
}
