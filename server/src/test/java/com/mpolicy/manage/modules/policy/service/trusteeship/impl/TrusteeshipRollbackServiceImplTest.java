package com.mpolicy.manage.modules.policy.service.trusteeship.impl;

import com.mpolicy.manage.AdminApplicationTests;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-07-05 14:22
 * @description: 托管单回滚
 */
@Slf4j
public class TrusteeshipRollbackServiceImplTest extends AdminApplicationTests {

    @Autowired
    private TrusteeshipRollbackServiceImpl trusteeshipRollbackService;

    @Test
    public void policyRollback() {
        trusteeshipRollbackService.policyRollback("re20230628184743q6Lwas");
    }
}
