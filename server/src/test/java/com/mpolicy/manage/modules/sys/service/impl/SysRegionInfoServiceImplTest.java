package com.mpolicy.manage.modules.sys.service.impl;

import com.alibaba.fastjson.JSON;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;
import com.mpolicy.manage.modules.sys.service.SysRegionInfoService;
import lombok.Builder;
import lombok.Data;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/18 16:48
 */
@Builder
@Data
class Cls implements Serializable {
    String code;
    String text;
    List<Cls> children;
}

public class SysRegionInfoServiceImplTest extends AdminApplicationTests {


    @Autowired
    SysRegionInfoService sysRegionInfoService;

    @Test
    public void test() {
        List<SysRegionInfo> list = sysRegionInfoService.list();
        Map<String, List<SysRegionInfo>> collect = list.stream().collect(Collectors.groupingBy(SysRegionInfo::getProvinceName));
        List<Cls> collect3 = collect.keySet().stream().map(x -> {
            List<SysRegionInfo> sysRegionInfos = collect.get(x);
            Map<String, List<SysRegionInfo>> collect1 = sysRegionInfos.stream().collect(Collectors.groupingBy(SysRegionInfo::getCityName));
            List<Cls> collect2 = collect1.keySet().stream().map(y -> {
                List<SysRegionInfo> sysRegionInfoList = collect1.get(y);
                return Cls.builder().text(y).code(sysRegionInfoList.get(0).getCityCode())
                        .children(
                                sysRegionInfoList.stream().map(regin ->
                                        Cls.builder().text(regin.getName()).code(regin.getCode()).children(new ArrayList<>())
                                                .build()).collect(Collectors.toList())
                        ).build();
            }).collect(Collectors.toList());
            return Cls.builder().text(x).code(sysRegionInfos.get(0).getProvinceCode()).children(collect2).build();
        }).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(collect3));
    }
}