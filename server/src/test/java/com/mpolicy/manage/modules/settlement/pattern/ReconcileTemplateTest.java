package com.mpolicy.manage.modules.settlement.pattern;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyProductDao;
import com.mpolicy.manage.modules.settlement.dao.TempReconcileCompanyProductDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyProductEntity;
import com.mpolicy.manage.modules.settlement.entity.TmpReconcileCompanyProductEntity;
import com.mpolicy.manage.modules.settlement.vo.HzEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ReconcileTemplateTest extends AdminApplicationTests {

    @Autowired
    private SettlementReconcileCompanyProductDao settlementReconcileCompanyProductDao;


    @Autowired
    private TempReconcileCompanyProductDao tempReconcileCompanyProductDao;

    @Test
    public void readFile() {
        /*new LambdaUpdateChainWrapper<>(tempReconcileCompanyProductDao)
                .remove();*/
        //File[] files = FileUtil.ls("/Users/<USER>/Desktop/对账相关/短期险模板-6月对账");
        File[] files = FileUtil.ls("/Users/<USER>/Desktop/对账相关/对账单-7月对账");
        List<TmpReconcileCompanyProductEntity> tempReconcileCompanyProductList = new ArrayList<>();
        for (File file : files) {
            if (file.getName().endsWith(".xlsx")) {
                log.info("读取文件:{}", file.getName());
                List<Map<String, Object>> maps = ExcelUtil.getReader(file).readAll();
                maps.forEach(action -> {
                    String policyNo = action.get("保单号").toString();
                    Object endorsementNo = action.get("批单号");
                    Object insuranceTime = action.get("投保时间");
                    if (StrUtil.isBlank(policyNo)) {
                        throw new RuntimeException();
                    }
                    TmpReconcileCompanyProductEntity tempReconcileCompanyProduct = new TmpReconcileCompanyProductEntity();
                    if (!StrUtil.isEmptyIfStr(endorsementNo)) {
                        tempReconcileCompanyProduct.setEndorsementNo(endorsementNo.toString());
                    }
                    if (insuranceTime != null) {
                        tempReconcileCompanyProduct.setInsuranceTime(DateUtil.parseDate(insuranceTime.toString()));
                    }
                    tempReconcileCompanyProduct.setReconcileCompanyCode(file.getName().split(" ")[0]);
                    tempReconcileCompanyProduct.setPolicyNo(policyNo);
                    tempReconcileCompanyProduct.setCompanyProductName(action.get("保司对账单产品名称").toString());
                    tempReconcileCompanyProduct.setMonthStr("2023-07");
                    tempReconcileCompanyProductList.add(tempReconcileCompanyProduct);
                });
            }
        }
        tempReconcileCompanyProductDao.insertBatchSomeColumn(tempReconcileCompanyProductList);
    }

    /**
     * 读取泰康费率表
     */
    @Test
    public void getTkPrem() {
        Map<String, Map<String, Object>> map = ExcelUtil.getReader("/Users/<USER>/Desktop/剩余6月所需对账的保司/product.xlsx")
                .readAll().stream().collect(Collectors.toMap(k -> k.get("policy_no").toString(), v -> v));
        Map<String, Map<String, Object>> reconcileMap = ExcelUtil.getReader("/Users/<USER>/Desktop/剩余6月所需对账的保司/company.xlsx")
                .readAll().stream().collect(Collectors.toMap(k -> k.get("reconcile_company_code").toString(), v -> v));

        //读取数据
        List<HzEntity> list = export6Month(false, "/Users/<USER>/Desktop/剩余6月所需对账的保司/");
        //处理数据
        List<SettlementReconcileCompanyProductEntity> dataList = list.stream()
                .filter(f -> map.containsKey(f.getPolicyCode()))
                .collect(Collectors.toMap(k -> k.getCompanyName() + k.getProductName(), v -> v,
                        (v1, v2) -> {
                            // 这里处理自己的逻辑
                            return v2;
                        })).values().stream().map(m -> {
                    SettlementReconcileCompanyProductEntity tempReconcileCompanyProduct = new SettlementReconcileCompanyProductEntity();
                    tempReconcileCompanyProduct.setCompanyProductName(m.getProductName());
                    Map<String, Object> companyMap = reconcileMap.get(m.getReconcileCompanyCode());
                    Map<String, Object> policyMap = map.get(m.getPolicyCode());
                    tempReconcileCompanyProduct.setCompanyCode(companyMap.get("company_code").toString());
                    tempReconcileCompanyProduct.setCompanyName(companyMap.get("company_name").toString());
                    tempReconcileCompanyProduct.setProductName(policyMap.get("main_product_name").toString());
                    tempReconcileCompanyProduct.setProductCode(policyMap.get("main_product_code").toString());
                    tempReconcileCompanyProduct.setInsuranceProductCode(policyMap.get("insurance_product_code").toString());
                    tempReconcileCompanyProduct.setInsuranceProductName(policyMap.get("insurance_product_name").toString());
                    return tempReconcileCompanyProduct;
                }).collect(Collectors.toList());
        //删除数据
        new LambdaUpdateChainWrapper<>(settlementReconcileCompanyProductDao)
                .remove();
        //插入数据
        settlementReconcileCompanyProductDao.insertBatchSomeColumn(dataList);


    }


    /**
     * map.put("fileName", ".xlsx");//文件名称
     * map.put("sheetIndex", "");//读取的sheet索引
     * map.put("productName", "");//保司险种名称
     * map.put("policyNo", "");//保单号
     * map.put("companyName", "");//保司名称
     * map.put("companyCode", "");//保司编码
     * map.put("endorsementNo", "");//批单号
     * map.put("policyCash", "");//实收保费
     * map.put("policyCommission", "");//手续费
     */
    private static List<HzEntity> export7Month(boolean isWriter, String basePath) {

        List<Map<String, String>> fileList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("fileName", "太保深圳分-小鲸北京202305-06.xlsx");
        map.put("sheetIndex", "1");
        map.put("productName", "投保产品名称");
        map.put("policyNo", "保单号");
        map.put("companyName", "中国太平洋财产保险股份有限公司");
        map.put("companyCode", "TPYC000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "实收保费");
        map.put("policyCommission", "应付手续费");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "泰康人寿甘肃分-小鲸甘肃分202306.xlsx");//文件名称
        map.put("sheetIndex", "2");//读取的sheet索引
        map.put("productName", "险种名称");//保司险种名称
        map.put("policyNo", "保单号");//保单号
        map.put("companyName", "泰康人寿保险有限责任公司");//保司名称
        map.put("companyCode", "TKRS000000");//保司编码
        map.put("endorsementNo", "");//批单号
        map.put("policyCash", "保费");//实收保费
        map.put("policyCommission", "首年销售手续费");//手续费
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸甘肃-阳光人寿202306.xlsx");//文件名称
        map.put("sheetIndex", "2");//读取的sheet索引
        map.put("productName", "险种简称");//保司险种名称
        map.put("policyNo", "保单号码");//保单号
        map.put("companyName", "阳光人寿保险股份有限公司");//保司名称
        map.put("companyCode", "YGRS000000");//保司编码
        map.put("endorsementNo", "");//批单号
        map.put("policyCash", "保费");//实收保费
        map.put("policyCommission", "");//手续费
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-国寿财张家口团险202306（线下）.xlsx");//文件名称
        map.put("sheetIndex", "1");//读取的sheet索引
        map.put("productName", "险种名称");//保司险种名称
        map.put("policyNo", "业务号");//保单号
        map.put("companyName", "中国人寿财产保险股份有限公司");//保司名称
        map.put("companyCode", "ZGSC000000");//保司编码
        map.put("endorsementNo", "");//批单号
        map.put("policyCash", "实收保费(CNY)");//实收保费
        map.put("policyCommission", "手续费(CNY)");//手续费
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸湖南-国寿财株洲202306.xlsx");//文件名称
        map.put("sheetIndex", "2");//读取的sheet索引
        map.put("productName", "险种名称");//保司险种名称
        map.put("policyNo", "业务号");//保单号
        map.put("companyName", "中国人寿财产保险股份有限公司");//保司名称
        map.put("companyCode", "ZGSC000000");//保司编码
        map.put("endorsementNo", "");//批单号
        map.put("policyCash", "实收保费(CNY)");//实收保费
        map.put("policyCommission", "手续费(CNY)");//手续费
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸湖南-中华湖南分202306.xlsx");//文件名称
        map.put("sheetIndex", "1");//读取的sheet索引
        map.put("productName", "险种");//保司险种名称
        map.put("policyNo", "保单号");//保单号
        map.put("companyName", "中华联合财产保险股份有限公司");//保司名称
        map.put("companyCode", "ZHLC000000");//保司编码
        map.put("endorsementNo", "");//批单号
        map.put("policyCash", "实收保费");//实收保费
        map.put("policyCommission", "应付手续费金额");//手续费
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-泰康202306.xlsx");
        map.put("sheetIndex", "2");
        map.put("productName", "销售方案名称");
        map.put("policyNo", "保单号");
        map.put("companyName", "泰康在线财产保险股份有限公司");
        map.put("companyCode", "TKZX000000");
        map.put("endorsementNo", "批单号");
        map.put("policyCash", "实收保费(元)");
        map.put("policyCommission", "结算金额(元)");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-泰康车险202306.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "险种险别");
        map.put("policyNo", "分单保单号");
        map.put("companyName", "泰康在线财产保险股份有限公司");
        map.put("companyCode", "TKZX000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "实收保费(元)");
        map.put("policyCommission", "结算金额(元)");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-中华联合202306.xlsx");
        map.put("sheetIndex", "3");
        map.put("productName", "投保产品");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "保费");
        map.put("policyCommission", "");
        //fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-中华联合202306.xlsx");
        map.put("sheetIndex", "4");
        map.put("productName", "投保产品");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "保费");
        map.put("policyCommission", "");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-众安202306.xlsx");
        map.put("sheetIndex", "2");
        map.put("productName", "产品组合名称");
        map.put("policyNo", "保单号");
        map.put("companyName", "众安在线财产保险股份有限公司");
        map.put("companyCode", "ZAZX000000");
        map.put("endorsementNo", "批单号");
        map.put("policyCash", "保费");
        map.put("policyCommission", "佣金");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "信美-小鲸202306.xlsx");
        map.put("sheetIndex", "1");
        map.put("productName", "险种简称");
        map.put("policyNo", "合同号");
        map.put("companyName", "信美人寿相互保险社");
        map.put("companyCode", "XMXH000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "");
        map.put("policyCommission", "");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "阳光财兰州中-小鲸甘肃202306.xlsx");
        map.put("sheetIndex", "3");
        map.put("productName", "类型");
        map.put("policyNo", "业务号");
        map.put("companyName", "阳光财产保险股份有限公司");
        map.put("companyCode", "YGCX000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "税前保费");
        map.put("policyCommission", "手续费金额");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "中华赤峰车险-小鲸北京202306.xlsx");
        map.put("sheetIndex", "1");
        map.put("productName", "险种");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("endorsementNo", "");
        map.put("policyCash", "实收保费");
        map.put("policyCommission", "手续费");
        fileList.add(map);
        List<HzEntity> list = new ArrayList<>();
        fileList.forEach(m -> {
            List<Map<String, Object>> maps = ExcelUtil.getReader(basePath + m.get("fileName"), Integer.parseInt(m.get("sheetIndex")))
                    .readAll();
            log.info("读取{}数据长度:{}", m.get("fileName"), maps.size());
            maps.forEach(action -> {
                if (StrUtil.isEmptyIfStr(action.get(m.get("policyNo")))) {
                    log.info("文件[{}]存在保单号为空的数据={}", m.get("fileName"), JSONUtil.toJsonStr(action));
                    throw new RuntimeException();
                }
                HzEntity hz = new HzEntity();
                hz.setCompanyName(m.get("companyName"));
                hz.setPolicyCode(action.get(m.get("policyNo")) + "");
                hz.setEndorsementNo(StrUtil.isNotBlank(m.get("endorsementNo")) ? (!StrUtil.isEmptyIfStr(action.get(m.get("endorsementNo"))) ? action.get(m.get("endorsementNo")).toString() : "") : "");
                hz.setPolicyCash(StrUtil.isNotBlank(m.get("policyCash")) ? (!StrUtil.isEmptyIfStr(action.get(m.get("policyCash"))) ? action.get(m.get("policyCash")).toString() : "") : "");
                hz.setPolicyCommission(StrUtil.isNotBlank(m.get("policyCommission")) ? (!StrUtil.isEmptyIfStr(action.get(m.get("policyCommission"))) ? action.get(m.get("policyCommission")).toString() : "") : "");
                if (action.containsKey(m.get("productName"))) {
                    String productName = action.get(m.get("productName")).toString();
                    if ("0810".equals(productName)) {
                        hz.setProductName("泰康车险驾乘意外险基础版");
                    } else if ("0808".equals(productName)) {
                        hz.setProductName("泰康车险单商");
                    } else if ("0807".equals(productName)) {
                        hz.setProductName("泰康车险单交");
                    } else if ("0809".equals(productName)) {
                        hz.setProductName("泰康车险新能源客车");
                    } else {
                        hz.setProductName(productName);
                    }
                }
                list.add(hz);

            });

        });
        log.info("合计读取数据={}条,开始保存数据", list.size());
        if (isWriter) {
            ExcelWriter writer = ExcelUtil.getBigWriter(basePath + "2023年7月待对账保单集合-申佳.xlsx");
            //ExcelWriter writer = ExcelUtil.getBigWriter("/Users/<USER>/Desktop/7月所需对账的保司/" + System.currentTimeMillis() + ".xlsx");
            // 一次性写出内容，使用默认样式
            writer.write(list);
            // 关闭writer，释放内存
            writer.close();
            log.info("数据写入完毕.....");
        }
        return list;
    }


    private static List<HzEntity> export6Month(boolean isWriter, String basePath) {
        List<Map<String, String>> fileList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("fileName", "小鲸-泰康202305.xlsx");
        map.put("sheetIndex", "1");
        map.put("productName", "销售方案名称");
        map.put("policyNo", "保单号");
        map.put("companyName", "泰康在线财产保险股份有限公司");
        map.put("companyCode", "TKZX000000");
        map.put("reconcileCompanyCode", "SR20230619155636312822");
        map.put("endorsementNo", "批单号");
        map.put("policyCash", "实收保费(元)");
        map.put("policyCommission", "结算金额(元)");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-泰康车险202305.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "险种险别");
        map.put("policyNo", "分单保单号");
        map.put("companyName", "泰康在线财产保险股份有限公司");
        map.put("companyCode", "TKZX000000");
        map.put("reconcileCompanyCode", "SR20230619155636312822");
        map.put("endorsementNo", "");
        map.put("policyCash", "实收保费(元)");
        map.put("policyCommission", "结算金额(元)");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-中华联合202305.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "投保产品");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("reconcileCompanyCode", "SR20230704175425834975");
        map.put("endorsementNo", "");
        map.put("policyCash", "保费");
        map.put("policyCommission", "");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-中华联合202305.xlsx");
        map.put("sheetIndex", "1");
        map.put("productName", "险种");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("reconcileCompanyCode", "SR20230704175425834975");
        map.put("endorsementNo", "");
        map.put("policyCash", "签单保费_含税");
        map.put("policyCommission", "");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸-众安202305.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "产品组合名称");
        map.put("policyNo", "保单号");
        map.put("companyName", "众安在线财产保险股份有限公司");
        map.put("companyCode", "ZAZX000000");
        map.put("reconcileCompanyCode", "SR20230619154956099609");
        map.put("endorsementNo", "批单号");
        map.put("policyCash", "保费");
        map.put("policyCommission", "佣金");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸湖南-国寿财株洲202305.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "险种名称");
        map.put("policyNo", "业务号");
        map.put("companyName", "中国人寿财产保险股份有限公司");
        map.put("companyCode", "ZGSC000000");
        map.put("reconcileCompanyCode", "SR20230620154925716574");
        map.put("endorsementNo", "");
        map.put("policyCash", "实收保费(CNY)");
        map.put("policyCommission", "手续费(CNY)");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸湖南-国寿财株洲202305.xlsx");
        map.put("sheetIndex", "1");
        map.put("productName", "投保产品名称");
        map.put("policyNo", "保单号");
        map.put("companyName", "中国人寿财产保险股份有限公司");
        map.put("companyCode", "ZGSC000000");
        map.put("reconcileCompanyCode", "SR20230620154925716574");
        map.put("endorsementNo", "批单号");
        map.put("policyCash", "净保费");
        map.put("policyCommission", "基础结算佣金");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "阳光财兰州中支-小鲸甘肃202305.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "类型1");
        map.put("policyNo", "业务号");
        map.put("companyName", "阳光财产保险股份有限公司");
        map.put("companyCode", "YGCX000000");
        map.put("reconcileCompanyCode", "SR20230619215250360944");
        map.put("endorsementNo", "");
        map.put("policyCash", "税后保费");
        map.put("policyCommission", "手续费金额");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "中华赤峰车险-小鲸北京202304-05.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "险种");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("reconcileCompanyCode", "SR20230704175425834975");
        map.put("endorsementNo", "批单号");
        map.put("policyCash", "实收保费");
        map.put("policyCommission", "手续费");
        fileList.add(map);
        map = new HashMap<>();
        map.put("fileName", "小鲸湖南-中华长沙支202304-05.xlsx");
        map.put("sheetIndex", "0");
        map.put("productName", "险种");
        map.put("policyNo", "保单号");
        map.put("companyName", "中华联合财产保险股份有限公司");
        map.put("companyCode", "ZHLC000000");
        map.put("reconcileCompanyCode", "SR20230620154852545674");
        map.put("endorsementNo", "");
        map.put("policyCash", "实收保费");
        map.put("policyCommission", "应付手续费金额");
        fileList.add(map);
        List<HzEntity> list = new ArrayList<>();
        fileList.forEach(m -> {
            List<Map<String, Object>> maps = ExcelUtil.getReader(basePath + m.get("fileName"), Integer.parseInt(m.get("sheetIndex")))
                    .readAll();
            log.info("读取{}数据长度:{}", m.get("fileName"), maps.size());
            maps.forEach(action -> {
                HzEntity hz = new HzEntity();
                hz.setCompanyName(m.get("companyName"));
                hz.setPolicyCode(action.get(m.get("policyNo")) + "");
                hz.setEndorsementNo(StrUtil.isNotBlank(m.get("endorsementNo")) ? (!StrUtil.isEmptyIfStr(action.get(m.get("endorsementNo"))) ? action.get(m.get("endorsementNo")).toString() : "") : "");
                hz.setPolicyCash(StrUtil.isNotBlank(m.get("policyCash")) ? (!StrUtil.isEmptyIfStr(action.get(m.get("policyCash"))) ? action.get(m.get("policyCash")).toString() : "") : "");
                hz.setPolicyCommission(StrUtil.isNotBlank(m.get("policyCommission")) ? (!StrUtil.isEmptyIfStr(action.get(m.get("policyCommission"))) ? action.get(m.get("policyCommission")).toString() : "") : "");
                hz.setReconcileCompanyCode(m.get("reconcileCompanyCode"));
                if (action.containsKey(m.get("productName"))) {
                    String productName = action.get(m.get("productName")).toString();
                    if ("0810".equals(productName)) {
                        hz.setProductName("泰康车险驾乘意外险基础版");
                    } else if ("0808".equals(productName)) {
                        hz.setProductName("泰康车险单商");
                    } else if ("0807".equals(productName)) {
                        hz.setProductName("泰康车险单交");
                    } else if ("0809".equals(productName)) {
                        hz.setProductName("泰康车险新能源客车");
                    } else {
                        hz.setProductName(productName);
                    }
                }
                list.add(hz);
            });
        });
        log.info("合计读取数据={}条", list.size());
        if (isWriter) {
            log.info("开始写入数据....");
            ExcelWriter writer = ExcelUtil.getBigWriter("/Users/<USER>/Desktop/剩余6月所需对账的保司/2023年6月待对账保单集合-申佳.xlsx");
            //ExcelWriter writer = ExcelUtil.getBigWriter("/Users/<USER>/Desktop/剩余6月所需对账的保司/"+System.currentTimeMillis()+".xlsx");
            // 一次性写出内容，使用默认样式
            writer.write(list);
            // 关闭writer，释放内存
            writer.close();
            log.info("数据写入完毕.....");
        }
        return list;

    }


}
