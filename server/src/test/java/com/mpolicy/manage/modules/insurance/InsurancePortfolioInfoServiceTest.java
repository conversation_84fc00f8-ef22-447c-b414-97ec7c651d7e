package com.mpolicy.manage.modules.insurance;

import cn.hutool.core.date.StopWatch;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.insurance.service.InsurancePortfolioInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * @className: InsurancePortfolioInfoServiceTest
 * @description: 组合测试
 * @author: haijun.sun
 * @date: 2021-11-29 11:12
 */
@Slf4j
public class InsurancePortfolioInfoServiceTest extends AdminApplicationTests {

    @Autowired
    private InsurancePortfolioInfoService insurancePortfolioInfoService;

    @Test
    public void importPortfolioScore() {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        insurancePortfolioInfoService.importPortfolioScore("oss20211129104925gm3wNk");
        // 设置结束
        stopWatch.stop();
        long millis = stopWatch.getTotalTimeMillis();
        log.info("导入组合得分Excel完成，耗时={}",millis);
    }
}
