package com.mpolicy.manage.modules.settlement.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

@Data
public class HzEntity implements Serializable {
    private static final long serialVersionUID = -187452963797195630L;
    @Alias("company_name")
    private String companyName;

    @Alias("product_name")
    private String productName;

    @Alias("policy_code")
    private String policyCode;

    private String reconcileCompanyCode;

    @Alias("endorsement_no")
    private String endorsementNo;

    @Alias("policy_cash")
    private String policyCash;

    @<PERSON>as("policy_commission")
    private String policyCommission;
}
