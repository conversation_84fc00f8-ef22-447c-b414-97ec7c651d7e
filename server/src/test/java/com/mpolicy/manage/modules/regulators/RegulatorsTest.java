package com.mpolicy.manage.modules.regulators;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.thread.ThreadUtils;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.regulators.common.RegulatorsConstant;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportInfoService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportOrgService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportService;
import com.mpolicy.manage.modules.regulators.strategy.ReportStrategyFactory;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportInput;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportList;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportData;
import com.mpolicy.manage.utils.AdminCommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Regulators
 *
 * <AUTHOR>
 * @date 2022-01-20 19:28
 */
@Slf4j
public class RegulatorsTest extends AdminApplicationTests {

    @Autowired
    private ReportStrategyFactory reportStrategyFactory;

    @Autowired
    private RegulatorsReportInfoService regulatorsReportInfoService;


    @Autowired
    private RegulatorsReportOrgService regulatorsReportOrgService;

    /***
     * oss文件操作
     */
    @Autowired
    private StorageService storageService;

    @Value("${mp.download.folder:logs/}")
    String destPath;

    /**
     * 机构月度报备初始化
     */
    @Test
    public void initReportInfo(){
        RegulatorsReportInfoEntity bean = new RegulatorsReportInfoEntity();

        DateTime date = DateUtil.date();
        // 偏移1月，获取上个月的月份 + 年份
        date = DateUtil.offsetMonth(date,-1);
        int year = DateUtil.year(date);
        int month = DateUtil.month(date);
        // 犹豫12月为0,所以需要特殊处理， 好比 2022年1月 算出来的  year = 2022  month = 0 ，需要进行转换为 2021/12
        if(month == 0){
            year = year - 1;
            month = 12;
        }else{
            month = month + 1;
        }

        bean.setRegulatorsNo(CommonUtils.createCodeLastNumber("REG"));
        bean.setRegulatorsName(StrUtil.format(RegulatorsConstant.REGULATORS_MONTH_DESC, year, month));
        bean.setReportStatus(0);
        bean.setUploadCompleteStatus(0);
        bean.setRegulatorsOrgCount(0);
        bean.setRegulatorsYear(year);
        bean.setRegulatorsMonth(month);
        regulatorsReportInfoService.save(bean);
    }

    @Test
    public void orgReport(){
        OrgRegulatorsReportInput orgRegulatorsReportInput = new OrgRegulatorsReportInput();

        orgRegulatorsReportInput.setRegulatorsNo("REG20220121160054756309");
        orgRegulatorsReportInput.setOrgCode("001");
        orgRegulatorsReportInput.setOrgName("小马哥");
        orgRegulatorsReportInput.setFileCode("oss20220121101512kjzI17");
        orgRegulatorsReportInput.setReportType(RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getCode());

        // 1 构建报告模式需要用的请求数据
        RegulatorsReportData regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);
        // 2 获取报备报告服务
        RegulatorsReportService<RegulatorsReportData> regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(RegulatorsReportTypeEnum.deCode(RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getCode()), RegulatorsReportData.class);
        // 3 执行机构报备操作
        regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);

        log.info("资产负债表配置完成.....");
        ThreadUtils.sleep(2000);

        orgRegulatorsReportInput.setFileCode("oss20220121101446jLFphi");
        orgRegulatorsReportInput.setReportType(RegulatorsReportTypeEnum.COMPANY_INCOME.getCode());
        // 1 构建报告模式需要用的请求数据
        regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);
        // 2 获取报备报告服务
        regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(RegulatorsReportTypeEnum.deCode(RegulatorsReportTypeEnum.COMPANY_INCOME.getCode()), RegulatorsReportData.class);
        // 3 执行机构报备操作
        regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);
        log.info("利润表配置完成.....");
        ThreadUtils.sleep(2000);

        orgRegulatorsReportInput.setFileCode("oss202201211014296r43Mz");
        orgRegulatorsReportInput.setReportType(RegulatorsReportTypeEnum.COMPANY_BASIC_INFO.getCode());
        // 1 构建报告模式需要用的请求数据
        regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);
        // 2 获取报备报告服务
        regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(RegulatorsReportTypeEnum.deCode(RegulatorsReportTypeEnum.COMPANY_BASIC_INFO.getCode()), RegulatorsReportData.class);
        // 3 执行机构报备操作
        regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);

        log.info("基本情况表配置完成.....");
        ThreadUtils.sleep(2000);

        orgRegulatorsReportInput.setFileCode("oss202201211014160yJgfG");
        orgRegulatorsReportInput.setReportType(RegulatorsReportTypeEnum.LIFE_COMPANY_BUSINESS.getCode());
        // 1 构建报告模式需要用的请求数据
        regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);
        // 2 获取报备报告服务
        regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(RegulatorsReportTypeEnum.deCode(RegulatorsReportTypeEnum.LIFE_COMPANY_BUSINESS.getCode()), RegulatorsReportData.class);
        // 3 执行机构报备操作
        regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);

        log.info("代理人身险公司业务表配置完成.....");
        ThreadUtils.sleep(2000);

        orgRegulatorsReportInput.setFileCode("oss20220121101400vumJG8");
        orgRegulatorsReportInput.setReportType(RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getCode());
        // 1 构建报告模式需要用的请求数据
        regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);
        // 2 获取报备报告服务
        regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(RegulatorsReportTypeEnum.deCode(RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getCode()), RegulatorsReportData.class);
        // 3 执行机构报备操作
        regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);

        log.info("代理产险公司业务表配置完成.....");
        ThreadUtils.sleep(2000);
    }

    @Test
    public void export(){

        String regulatorsNo = "REG20220121160054756309";
        Map<String, Object> params = new HashMap<>();

        params.put("limit", "-1");
        PageUtils<OrgRegulatorsReportList> page = regulatorsReportOrgService.queryOrgRegulatorsReportList(regulatorsNo, params);
        // 获取结果列表
        List<OrgRegulatorsReportList> list = page.getList();
        log.info("获取续导出的纪录条数={}",list.size());
        if(list.isEmpty()){
            log.info("查无数据");
            return;
        }

        // 多线程下载机构报备文件
        String targetPath = destPath.concat(regulatorsNo);
        // 多线程下载机构报备文件
        list.parallelStream().forEach(x ->{
            String orgTargetPath = destPath.concat(regulatorsNo).concat("/").concat(x.getOrgName());
            File dirFile = new File(orgTargetPath);
            if (!dirFile.exists()) {
                dirFile.mkdirs();
            }
            AdminCommonUtils.saveUrlFile(x.getDomainPath(),orgTargetPath.concat("/").concat(x.getReportFileName()));
        });


        File zipFileDir = new File(destPath.concat("zip"));
        if (!zipFileDir.exists()) {
            zipFileDir.mkdirs();
        }
        // 生成压缩文件
        File zipFile = ZipUtil.zip(System.getProperty("user.dir").concat("/").concat(targetPath), System.getProperty("user.dir").concat("/").concat(destPath).concat("zip/").concat(regulatorsNo).concat(".zip"),true);
        log.info("压缩zip文件完成");
        // 上传oss
        OssBaseOut ossBaseOut = storageService.uploadFileInputSteam(FileModelEnum.OTHER.ossObjectFileName(regulatorsNo, zipFile.getName()), zipFile);
        log.info("zip文件上传完成，上传结果={}", JSON.toJSONString(ossBaseOut));
        // 删除临时目录
        FileUtil.del(System.getProperty("user.dir").concat("/").concat(targetPath));

        log.info("代理产险公司业务表配置完成.....");
        ThreadUtils.sleep(2000);
    }


    /**
     * <p>
     * 构建报告模式需要用的请求数据
     * </p>
     *
     * @param orgRegulatorsReportInput 页面机构报备所属对象数据
     * @return com.mpolicy.manage.modules.regulators.vo.RegulatorsReportData
     * <AUTHOR>
     * @since 2022/1/20
     */
    private RegulatorsReportData builderRegulatorsReportData(OrgRegulatorsReportInput orgRegulatorsReportInput) {
        RegulatorsReportData result = new RegulatorsReportData();
        BeanUtils.copyProperties(orgRegulatorsReportInput, result);
        result.setReportFileCode(orgRegulatorsReportInput.getFileCode());
        return result;
    }
}
