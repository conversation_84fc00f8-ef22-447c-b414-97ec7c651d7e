package com.mpolicy.manage.modules.settlement.service;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.settlement.vo.invoice.InvoiceEnclosureItem;
import com.mpolicy.manage.modules.settlement.vo.invoice.SendToCompanyVo;
import jodd.util.ThreadUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class SettlementReconcileInvoiceServiceTest  extends AdminApplicationTests {

    @Autowired
    private   SettlementReconcileInvoiceService settlementReconcileInvoiceService;

    /**
     * 发送保司
     */
    @Test
    public void sendToCompany() {

        SendToCompanyVo sendToCompany = new SendToCompanyVo();

        List<InvoiceEnclosureItem> mailEnclosureList = new ArrayList<>();
        InvoiceEnclosureItem item = new InvoiceEnclosureItem();
        item.setFileCode("oss202101291556103EvAf2");
        mailEnclosureList.add(item);
        item = new InvoiceEnclosureItem();
        item.setFileCode("oss20210202153854mNPgik");
        mailEnclosureList.add(item);
        // 发送邮件
        sendToCompany.setInvoiceCode("BXFPSQ1731405964651");
        sendToCompany.setMailContent("邮件内容手动阀是的撒");
        sendToCompany.setMailSubject("邮件标题玩儿二位");
        sendToCompany.setMailEnclosureList(mailEnclosureList);
        try {
            settlementReconcileInvoiceService.sendToCompany(sendToCompany);
        }catch (Exception e){
            e.printStackTrace();
        }
        ThreadUtil.sleep(10000);

    }
}
