package com.mpolicy.manage.modules.sys.service.impl;

import com.alibaba.excel.annotation.ExcelColumnNum;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.content.entity.QaInfoSearchEntity;
import com.mpolicy.manage.modules.content.service.GreenChannelServiceService;
import com.mpolicy.manage.modules.content.service.QaInfoSearchService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.util.ThreadContext;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/28 10:24
 */
public class GreenChannelServiceServiceImplTest extends AdminApplicationTests {
    @Autowired
    GreenChannelServiceService greenChannelServiceService;

    @Autowired
    QaInfoSearchService qaInfoSearchService;

    @Test
    public void testSearch() {
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", "众安");
        PageUtils pageUtils = greenChannelServiceService.queryPage(map);
        System.out.println(pageUtils.getCurrPage());
        System.out.println(pageUtils.getList());
        System.out.println(pageUtils.getTotalPage());
    }

    @Test
    public void testDelete() {
        greenChannelServiceService.deleteEntity("LT20210128144516xDE02");
    }

    @Resource
    org.apache.shiro.mgt.SecurityManager securityManager;

    @Before
    public void setUp() {
        ThreadContext.bind(securityManager);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class A extends BaseRowModel {
        @ExcelColumnNum(0)
        String code;
        @ExcelColumnNum(1)
        String value;
    }


    @Test
    public void testString() throws IOException {

        File file = new File("C:\\Users\\<USER>\\Desktop\\a.xlsx");

        FileInputStream fileInput = new FileInputStream(file);
        MultipartFile toMultipartFile = new MockMultipartFile("file", "a.xlsx", "text/plain",
                IOUtils.toByteArray(fileInput));
        // 读取excel
        List<Object> objects = ExcelUtil.readExcel(toMultipartFile, new A());
        ArrayList<QaInfoSearchEntity> objects1 = new ArrayList<>();
        objects.forEach(x -> {
            A a2 = new A();
            BeanUtils.copyProperties(x, a2);
            String code = a2.getCode();
            System.out.println(code);
            List<QaInfoSearchEntity> a1 = Arrays.stream(a2.getValue().split("a"))
                    .map(a -> {
                        QaInfoSearchEntity qaInfoSearchEntity = new QaInfoSearchEntity();

                        qaInfoSearchEntity.setQaCode(code);
                        qaInfoSearchEntity.setQaSearchKeywordCode(CommonUtils.createCode("ss"));
                        qaInfoSearchEntity.setKeyword(a);
                        return qaInfoSearchEntity;
                    }).collect(Collectors.toList());

            objects1.addAll(a1);
        });

        qaInfoSearchService.saveBatch(objects1);

    }
}