package com.mpolicy.manage.modules.insurance.service.impl;

import com.alibaba.fastjson.JSON;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.insurance.vo.ProductChannel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class InsuranceProductInfoServiceImplTest extends AdminApplicationTests {

    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;

    @Test
    public void testGetProductChannelByProtocolFlag() {
        Integer protocolFlag = 1;
        String companyCode = "ASTP000000";
        // 1. 模拟是协议险种，保司编码为ASTP000000，得到的险种渠道
        List<ProductChannel> productChannel = insuranceProductInfoService.getProductChannelByProtocolFlag(protocolFlag, companyCode);
        System.out.println("险种渠道 = " + JSON.toJSONString(productChannel));

        // 2. 模拟不是协议险种，得到的是所有分销渠道（不含XJXH001）
        protocolFlag = 0;
        productChannel = insuranceProductInfoService.getProductChannelByProtocolFlag(protocolFlag,"");
        System.out.println("险种渠道 = " +JSON.toJSONString(productChannel));
    }
}