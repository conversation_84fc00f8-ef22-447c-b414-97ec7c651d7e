package com.mpolicy.manage.modules.sys.service.impl;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.content.dao.UnderwritingInfoDao;
import com.mpolicy.manage.modules.content.entity.UnderwritingInfoEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/26 19:31
 */
public class UnderwritingInfoServiceImplTest extends AdminApplicationTests {
    @Autowired
    UnderwritingInfoDao underwritingInfoDao;

    @Test
    public void changeEnable() {

        UnderwritingInfoEntity underwritingInfoEntity = new UnderwritingInfoEntity();
        underwritingInfoEntity.setEnabled(1);
        underwritingInfoEntity.setRevision(11);
        UnderwritingInfoEntity underwritingInfoEntity1 = underwritingInfoDao.selectById(1);
        System.out.println(underwritingInfoEntity1.toString());
        //System.out.println(underwritingInfoDao.update(underwritingInfoEntity, new QueryWrapper<UnderwritingInfoEntity>().lambda().eq(UnderwritingInfoEntity::getUnderwritingCode, "HB202101251632139y9Ncv")));
    }
}