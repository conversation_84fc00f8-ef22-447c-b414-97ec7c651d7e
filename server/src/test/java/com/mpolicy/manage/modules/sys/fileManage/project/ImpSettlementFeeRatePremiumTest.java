package com.mpolicy.manage.modules.sys.fileManage.project;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductService;
import com.mpolicy.manage.modules.settlement.dao.SettlementPolicyProductFeePremMapper;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductFeePremService;
import com.mpolicy.manage.modules.settlement.service.SettlementPremChangeLogService;
import com.mpolicy.manage.modules.settlement.vo.ImportPolicyProductFeePremExcelResult;
import com.mpolicy.manage.modules.settlement.vo.PolicyProductFeePremExcel;
import com.mpolicy.manage.modules.sys.entity.SystemBusinessFileManageEntity;
import com.mpolicy.manage.modules.sys.fileManage.dto.BusinessFileManageHandlerResult;
import com.mpolicy.manage.modules.sys.fileManage.enums.FileManageProjectEnum;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.policy.common.ep.policy.OptUserInfoVo;
import com.mpolicy.policy.common.ep.policy.common.PolicyFileImportApplyVo;
import generator.domain.SettlementPolicyProductFeePrem;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ImpSettlementFeeRatePremiumTest {

    @Mock
    private SettlementPremChangeLogService mockSettlementPremChangeLogService;
    @Mock
    private SettlementPolicyProductFeePremMapper mockPolicyProductFeePremMapper;
    @Mock
    private StorageService mockStorageService;
    @Mock
    private TransactionTemplate mockTransactionTemplate;
    @Mock
    private SettlementPolicyProductFeePremService mockProductFeePremService;
    @Mock
    private SettlementPolicyProductFeePremService mockPolicyProductFeePremService;
    @Mock
    private IEpProtocolInsuranceProductService mockProtocolInsuranceProductService;
    @Mock
    private SysDocumentService mockSysDocumentService;

    @InjectMocks private ImpSettlementFeeRatePremium impSettlementFeeRatePremiumUnderTest;

    @Test
    public void testBusinessRun() {
        // Setup
        final SystemBusinessFileManageEntity bean = SystemBusinessFileManageEntity.builder()
                .fileManageCode("fileManageCode")
                .fileManageStatus(0)
                .sourceFileCode("sourceFileCode")
                .businessExceptionCode("businessExceptionCode")
                .businessExceptionType("businessExceptionType")
                .businessResultMsg("businessResultMsg")
                .finishMillis(0L)
                .finishTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .businessOpeUserName("businessOpeUserName")
                .updateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .build();
        final BusinessFileManageHandlerResult expectedResult = new BusinessFileManageHandlerResult();
        expectedResult.setBusinessRunResult(0);
        expectedResult.setImportSuccessNumber(0);
        expectedResult.setImportFailNumber(0);
        expectedResult.setResultFileCode("resultFileCode");
        expectedResult.setResultFilePath("domainPath");
        expectedResult.setBusinessResultMsg("businessResultMsg");

        when(mockSysDocumentService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockProtocolInsuranceProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockProductFeePremService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final BusinessFileManageHandlerResult result = impSettlementFeeRatePremiumUnderTest.businessRun(bean);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockStorageService).downloadFileToLocal("filePath", "localFileName");

        // Confirm SettlementPolicyProductFeePremMapper.insertBatchSomeColumn(...).
        final SettlementPolicyProductFeePrem policyProductFeePrem = new SettlementPolicyProductFeePrem();
        policyProductFeePrem.setId(0);
        policyProductFeePrem.setPremCode("premCode");
        policyProductFeePrem.setPolicyNo("policyNo");
        policyProductFeePrem.setBatchCode("batchCode");
        policyProductFeePrem.setReconcileType(0);
        policyProductFeePrem.setProductCode("productCode");
        policyProductFeePrem.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePrem.setInsuranceProductName("insuranceProductName");
        policyProductFeePrem.setPremium(new BigDecimal("0.00"));
        policyProductFeePrem.setTaxAfterPremium(new BigDecimal("0.00"));
        policyProductFeePrem.setTaxRate(new BigDecimal("0.00"));
        final List<SettlementPolicyProductFeePrem> entityList = Arrays.asList(policyProductFeePrem);
        verify(mockPolicyProductFeePremMapper).insertBatchSomeColumn(entityList);

        // Confirm SettlementPolicyProductFeePremService.updateBatchById(...).
        final SettlementPolicyProductFeePrem policyProductFeePrem1 = new SettlementPolicyProductFeePrem();
        policyProductFeePrem1.setId(0);
        policyProductFeePrem1.setPremCode("premCode");
        policyProductFeePrem1.setPolicyNo("policyNo");
        policyProductFeePrem1.setBatchCode("batchCode");
        policyProductFeePrem1.setReconcileType(0);
        policyProductFeePrem1.setProductCode("productCode");
        policyProductFeePrem1.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePrem1.setInsuranceProductName("insuranceProductName");
        policyProductFeePrem1.setPremium(new BigDecimal("0.00"));
        policyProductFeePrem1.setTaxAfterPremium(new BigDecimal("0.00"));
        policyProductFeePrem1.setTaxRate(new BigDecimal("0.00"));
        final List<SettlementPolicyProductFeePrem> entityList1 = Arrays.asList(policyProductFeePrem1);
        verify(mockPolicyProductFeePremService).updateBatchById(entityList1);
        verify(mockSettlementPremChangeLogService).saveSettlementPremChangeLog("pushEventCode", 0, "businessCode", "data");
    }

    @Test
    public void testGetFileManageProjectEnum() {
        assertThat(impSettlementFeeRatePremiumUnderTest.getFileManageProjectEnum()).isEqualTo(FileManageProjectEnum.IMP_SETTLEMENT_PROTOCOL_TAX_RATE_PREMIUM);
    }

    @Test
    public void testUploadPremiumFile() {
        // Setup
        final PolicyProductFeePremExcel policyProductFeePremExcel = new PolicyProductFeePremExcel();
        policyProductFeePremExcel.setPolicyNo("policyNo");
        policyProductFeePremExcel.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePremExcel.setProductCode("productCode");
        policyProductFeePremExcel.setPremium("premium");
        policyProductFeePremExcel.setTaxAfterPremium("premium");
        policyProductFeePremExcel.setTaxRate("premium");
        policyProductFeePremExcel.setPremCode("premCode");
        final List<PolicyProductFeePremExcel> productFeePremExcelList = Arrays.asList(policyProductFeePremExcel);
        final OptUserInfoVo operator = new OptUserInfoVo();
        operator.setUsername("username");
        operator.setIsAllOrg(0);
        operator.setOrgPermissionList(Arrays.asList("value"));
        operator.setIsAllChannel(0);
        operator.setOpId("fileManageCode");

        final ImportPolicyProductFeePremExcelResult productFeePremExcelResult = new ImportPolicyProductFeePremExcelResult();
        productFeePremExcelResult.setErrorCount(0);
        productFeePremExcelResult.setSuccessCount(0);
        productFeePremExcelResult.setTotal(0);
        productFeePremExcelResult.setErrorUrl("domainPath");
        productFeePremExcelResult.setMsg("businessResultMsg");

        when(mockProtocolInsuranceProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockProductFeePremService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        impSettlementFeeRatePremiumUnderTest.uploadPremiumFile(productFeePremExcelList, 0, operator, productFeePremExcelResult);

        // Verify the results
        // Confirm SettlementPolicyProductFeePremMapper.insertBatchSomeColumn(...).
        final SettlementPolicyProductFeePrem policyProductFeePrem = new SettlementPolicyProductFeePrem();
        policyProductFeePrem.setId(0);
        policyProductFeePrem.setPremCode("premCode");
        policyProductFeePrem.setPolicyNo("policyNo");
        policyProductFeePrem.setBatchCode("batchCode");
        policyProductFeePrem.setReconcileType(0);
        policyProductFeePrem.setProductCode("productCode");
        policyProductFeePrem.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePrem.setInsuranceProductName("insuranceProductName");
        policyProductFeePrem.setPremium(new BigDecimal("0.00"));
        policyProductFeePrem.setTaxAfterPremium(new BigDecimal("0.00"));
        policyProductFeePrem.setTaxRate(new BigDecimal("0.00"));
        final List<SettlementPolicyProductFeePrem> entityList = Arrays.asList(policyProductFeePrem);
        verify(mockPolicyProductFeePremMapper).insertBatchSomeColumn(entityList);

        // Confirm SettlementPolicyProductFeePremService.updateBatchById(...).
        final SettlementPolicyProductFeePrem policyProductFeePrem1 = new SettlementPolicyProductFeePrem();
        policyProductFeePrem1.setId(0);
        policyProductFeePrem1.setPremCode("premCode");
        policyProductFeePrem1.setPolicyNo("policyNo");
        policyProductFeePrem1.setBatchCode("batchCode");
        policyProductFeePrem1.setReconcileType(0);
        policyProductFeePrem1.setProductCode("productCode");
        policyProductFeePrem1.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePrem1.setInsuranceProductName("insuranceProductName");
        policyProductFeePrem1.setPremium(new BigDecimal("0.00"));
        policyProductFeePrem1.setTaxAfterPremium(new BigDecimal("0.00"));
        policyProductFeePrem1.setTaxRate(new BigDecimal("0.00"));
        final List<SettlementPolicyProductFeePrem> entityList1 = Arrays.asList(policyProductFeePrem1);
        verify(mockPolicyProductFeePremService).updateBatchById(entityList1);
        verify(mockSettlementPremChangeLogService).saveSettlementPremChangeLog("pushEventCode", 0, "businessCode", "data");
    }

    @Test
    public void testEntryImport() {
        // Setup
        final PolicyFileImportApplyVo importApplyVo = new PolicyFileImportApplyVo();
        importApplyVo.setSerialNo("fileManageCode");
        importApplyVo.setFileCode("sourceFileCode");
        importApplyVo.setModuleType("code");
        final OptUserInfoVo operator = new OptUserInfoVo();
        operator.setUsername("username");
        operator.setOpId("fileManageCode");
        importApplyVo.setOperator(operator);

        final ImportPolicyProductFeePremExcelResult expectedResult = new ImportPolicyProductFeePremExcelResult();
        expectedResult.setErrorCount(0);
        expectedResult.setSuccessCount(0);
        expectedResult.setTotal(0);
        expectedResult.setErrorUrl("domainPath");
        expectedResult.setMsg("businessResultMsg");

        when(mockSysDocumentService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockProtocolInsuranceProductService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockProductFeePremService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Run the test
        final ImportPolicyProductFeePremExcelResult result = impSettlementFeeRatePremiumUnderTest.entryImport(importApplyVo, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockStorageService).downloadFileToLocal("filePath", "localFileName");

        // Confirm SettlementPolicyProductFeePremMapper.insertBatchSomeColumn(...).
        final SettlementPolicyProductFeePrem policyProductFeePrem = new SettlementPolicyProductFeePrem();
        policyProductFeePrem.setId(0);
        policyProductFeePrem.setPremCode("premCode");
        policyProductFeePrem.setPolicyNo("policyNo");
        policyProductFeePrem.setBatchCode("batchCode");
        policyProductFeePrem.setReconcileType(0);
        policyProductFeePrem.setProductCode("productCode");
        policyProductFeePrem.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePrem.setInsuranceProductName("insuranceProductName");
        policyProductFeePrem.setPremium(new BigDecimal("0.00"));
        policyProductFeePrem.setTaxAfterPremium(new BigDecimal("0.00"));
        policyProductFeePrem.setTaxRate(new BigDecimal("0.00"));
        final List<SettlementPolicyProductFeePrem> entityList = Arrays.asList(policyProductFeePrem);
        verify(mockPolicyProductFeePremMapper).insertBatchSomeColumn(entityList);

        // Confirm SettlementPolicyProductFeePremService.updateBatchById(...).
        final SettlementPolicyProductFeePrem policyProductFeePrem1 = new SettlementPolicyProductFeePrem();
        policyProductFeePrem1.setId(0);
        policyProductFeePrem1.setPremCode("premCode");
        policyProductFeePrem1.setPolicyNo("policyNo");
        policyProductFeePrem1.setBatchCode("batchCode");
        policyProductFeePrem1.setReconcileType(0);
        policyProductFeePrem1.setProductCode("productCode");
        policyProductFeePrem1.setInsuranceProductCode("insuranceProductCode");
        policyProductFeePrem1.setInsuranceProductName("insuranceProductName");
        policyProductFeePrem1.setPremium(new BigDecimal("0.00"));
        policyProductFeePrem1.setTaxAfterPremium(new BigDecimal("0.00"));
        policyProductFeePrem1.setTaxRate(new BigDecimal("0.00"));
        final List<SettlementPolicyProductFeePrem> entityList1 = Arrays.asList(policyProductFeePrem1);
        verify(mockPolicyProductFeePremService).updateBatchById(entityList1);
        verify(mockSettlementPremChangeLogService).saveSettlementPremChangeLog("pushEventCode", 0, "businessCode", "data");
    }
}
