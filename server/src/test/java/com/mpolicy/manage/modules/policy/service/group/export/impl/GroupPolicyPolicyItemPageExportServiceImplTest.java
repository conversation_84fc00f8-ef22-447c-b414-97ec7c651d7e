package com.mpolicy.manage.modules.policy.service.group.export.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelBranchInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.ChannelBranchInfoService;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.policy.dao.PreservationTeamPeopleDao;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyInsuredInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyProductInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyProductInsuredMapEntity;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyInsuredInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyProductInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyProductInsuredMapService;
import com.mpolicy.manage.modules.policy.service.impl.PolicyGroupInfoServiceImpl;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationTeamPeopleService;
import com.mpolicy.manage.modules.policy.vo.group.export.GroupPolicyItemExportAllItemVo;
import org.apache.commons.io.output.BrokenOutputStream;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.WebApplicationContext;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupPolicyPolicyItemPageExportServiceImplTest {

    @Mock
    private PolicyGroupInfoServiceImpl mockPolicyGroupInfoService;
    @Mock
    private EpPolicyContractInfoService mockEpPolicyContractInfoService;
    @Mock
    private AgentUserInfoService mockAgentUserInfoService;
    @Mock
    private ChannelApplicationReferrerService mockChannelApplicationReferrerService;
    @Mock
    private ChannelInfoService mockChannelInfoService;
    @Mock
    private PreservationApplyService mockPreservationApplyService;
    @Mock
    private PreservationTeamPeopleService mockPreservationTeamPeopleService;
    @Mock
    private EpPolicyInsuredInfoService mockEpPolicyInsuredInfoService;
    @Mock
    private EpPolicyProductInsuredMapService mockEpPolicyProductInsuredMapService;
    @Mock
    private ChannelBranchInfoService mockChannelBranchInfoService;
    @Mock
    private PreservationTeamPeopleDao mockPreservationTeamPeopleDao;
    @Mock
    private EpPolicyProductInfoService mockEpPolicyProductInfoService;

    @InjectMocks
    private GroupPolicyPolicyItemPageExportServiceImpl groupPolicyPolicyItemPageExportServiceImplUnderTest;
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void initAllTable() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), AgentUserInfoEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), ChannelApplicationReferrerEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), ChannelBranchInfoEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), ChannelInfoEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), EpPolicyContractInfoEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), EpPolicyInsuredInfoEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), EpPolicyProductInfoEntity.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), EpPolicyProductInsuredMapEntity.class);
    }

    @Test
    public void testQueryExportPage() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test(expected = IOException.class)
    public void testQueryExportPage_BrokenOut() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new BrokenOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);
    }

    @Test
    public void testQueryExportPage_AgentUserInfoServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_AgentUserInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_ChannelApplicationReferrerServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_ChannelApplicationReferrerServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_ChannelBranchInfoServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_ChannelBranchInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_ChannelInfoServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_ChannelInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyContractInfoServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyContractInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyProductInfoServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyProductInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyInsuredInfoServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyInsuredInfoServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_PreservationTeamPeopleDaoReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value"))).thenReturn(null);

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_PreservationTeamPeopleDaoReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure EpPolicyProductInsuredMapService.list(...).
        final List<EpPolicyProductInsuredMapEntity> epPolicyProductInsuredMapEntities = Arrays.asList(
                EpPolicyProductInsuredMapEntity.builder()
                        .contractCode("contractCode")
                        .insuredCode("insuredCode")
                        .planCode("planCode")
                        .planName("planName")
                        .premium(new BigDecimal("0.00"))
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInsuredMapEntities);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyProductInsuredMapServiceReturnsNull() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testQueryExportPage_EpPolicyProductInsuredMapServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<String, Object> paramMap = new HashMap<>();
        final OutputStream out = new ByteArrayOutputStream();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Configure PolicyGroupInfoServiceImpl.getQueryWrapper(...).
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(
                EpPolicyContractInfoEntity.builder()
                        .id(0)
                        .contractCode("contractCode")
                        .policyNo("policyNo")
                        .agentCode("agentCode")
                        .companyName("companyName")
                        .applicantName("applicantName")
                        .salesType(0)
                        .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .referrerCode("referrerCode")
                        .channelDistributionCode("channelDistributionCode")
                        .deleted(0)
                        .build());
        when(mockPolicyGroupInfoService.getQueryWrapper(new HashMap<>(), Arrays.asList("value"), Arrays.asList("value"),
                Arrays.asList("value"))).thenReturn(lambdaQueryWrapper);

        // Configure EpPolicyContractInfoService.list(...).
        final List<EpPolicyContractInfoEntity> entities = Arrays.asList(EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build());
        when(mockEpPolicyContractInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // Configure EpPolicyProductInfoService.list(...).
        final List<EpPolicyProductInfoEntity> epPolicyProductInfoEntities = Arrays.asList(
                EpPolicyProductInfoEntity.builder()
                        .contractCode("contractCode")
                        .planCode("planCode")
                        .planName("planName")
                        .deleted(0)
                        .build());
        when(mockEpPolicyProductInfoService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(epPolicyProductInfoEntities);

        // Configure EpPolicyInsuredInfoService.list(...).
        final List<EpPolicyInsuredInfoEntity> infoEntities = Arrays.asList(EpPolicyInsuredInfoEntity.builder()
                .contractCode("contractCode")
                .insuredCode("insuredCode")
                .mainFlag(0)
                .mainInsuredCode("mainInsuredCode")
                .insuredName("insuredName")
                .insuredGender(0)
                .insuredBirthday(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .insuredIdType("insuredIdType")
                .insuredIdCard("insuredIdCard")
                .insuredCareer("insuredCareer")
                .firstInsuredRelation("firstInsuredRelation")
                .insuredOccupationalCategory("insuredOccupationalCategory")
                .surrendered(0)
                .insuredOptType("insuredOptType")
                .insuredEffectiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelReferrerCode("channelReferrerCode")
                .deleted(0)
                .build());
        when(mockEpPolicyInsuredInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(infoEntities);

        // Configure PreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(...).
        final List<GroupPolicyItemExportAllItemVo> groupPolicyItemExportAllItemVos = Arrays.asList(
                GroupPolicyItemExportAllItemVo.builder()
                        .contractCode("contractCode")
                        .singlePremium(new BigDecimal("0.00"))
                        .firstInsuredRelation("firstInsuredRelation")
                        .insuredName("insuredName")
                        .mainInsuredName("mainInsuredName")
                        .insuredGender("insuredGender")
                        .insuredBirthday("insuredBirthday")
                        .insuredIdType("insuredIdType")
                        .insuredIdCard("insuredIdCard")
                        .planCode("planCode")
                        .insuredCareer("insuredCareer")
                        .insuredOccupationalCategory("insuredOccupationalCategory")
                        .referrerCode("referrerCode")
                        .channelReferrerCode("channelReferrerCode")
                        .peopleType("insuredOptType")
                        .endorsementNo("endorsementNo")
                        .preservationEffectTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                        .build());
        when(mockPreservationTeamPeopleDao.qryGroupPolicyItemExportAllItemVo(Arrays.asList("value")))
                .thenReturn(groupPolicyItemExportAllItemVos);

        when(mockEpPolicyProductInsuredMapService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        groupPolicyPolicyItemPageExportServiceImplUnderTest.queryExportPage(paramMap, out);

        // Verify the results
    }

    @Test
    public void testGetAgentUserInfoEntityMap() {
        // Setup
        final Map<String, AgentUserInfoEntity> expectedResult = new HashMap<>();

        // Configure AgentUserInfoService.list(...).
        final List<AgentUserInfoEntity> agentUserInfoEntities = Arrays.asList(AgentUserInfoEntity.builder()
                .agentCode("agentCode")
                .agentName("agentName")
                .businessCode("businessCode")
                .build());
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(agentUserInfoEntities);

        // Run the test
        final Map<String, AgentUserInfoEntity> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getAgentUserInfoEntityMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetAgentUserInfoEntityMap_AgentUserInfoServiceReturnsNull() {
        // Setup
        final Map<String, AgentUserInfoEntity> expectedResult = new HashMap<>();
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final Map<String, AgentUserInfoEntity> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getAgentUserInfoEntityMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetAgentUserInfoEntityMap_AgentUserInfoServiceReturnsNoItems() {
        // Setup
        final Map<String, AgentUserInfoEntity> expectedResult = new HashMap<>();
        when(mockAgentUserInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, AgentUserInfoEntity> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getAgentUserInfoEntityMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetreferrerMap() {
        // Setup
        final Map<String, ChannelApplicationReferrerEntity> expectedResult = new HashMap<>();

        // Configure ChannelApplicationReferrerService.list(...).
        final List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntities = Arrays.asList(
                ChannelApplicationReferrerEntity.builder()
                        .referrerCode("referrerCode")
                        .branchCode("branchCode")
                        .channelCode("channelCode")
                        .referrerWno("channelReferrerWno")
                        .referrerName("channelReferrerName")
                        .build());
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(channelApplicationReferrerEntities);

        // Run the test
        final Map<String, ChannelApplicationReferrerEntity> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getreferrerMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetreferrerMap_ChannelApplicationReferrerServiceReturnsNull() {
        // Setup
        final Map<String, ChannelApplicationReferrerEntity> expectedResult = new HashMap<>();
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final Map<String, ChannelApplicationReferrerEntity> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getreferrerMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetreferrerMap_ChannelApplicationReferrerServiceReturnsNoItems() {
        // Setup
        final Map<String, ChannelApplicationReferrerEntity> expectedResult = new HashMap<>();
        when(mockChannelApplicationReferrerService.list(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, ChannelApplicationReferrerEntity> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getreferrerMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetChannelInfoMap() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Configure ChannelInfoService.list(...).
        final List<ChannelInfoEntity> channelInfoEntities = Arrays.asList(ChannelInfoEntity.builder()
                .channelCode("channelCode")
                .channelName("channelName")
                .build());
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelInfoEntities);

        // Run the test
        final Map<String, String> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getChannelInfoMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetChannelInfoMap_ChannelInfoServiceReturnsNull() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final Map<String, String> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getChannelInfoMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetChannelInfoMap_ChannelInfoServiceReturnsNoItems() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();
        when(mockChannelInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, String> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getChannelInfoMap();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBranchInfo() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();

        // Configure ChannelBranchInfoService.list(...).
        final List<ChannelBranchInfoEntity> channelBranchInfoEntities = Arrays.asList(ChannelBranchInfoEntity.builder()
                .branchCode("branchCode")
                .branchName("branchName")
                .build());
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(channelBranchInfoEntities);

        // Run the test
        final Map<String, String> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getBranchInfo();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBranchInfo_ChannelBranchInfoServiceReturnsNull() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final Map<String, String> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getBranchInfo();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBranchInfo_ChannelBranchInfoServiceReturnsNoItems() {
        // Setup
        final Map<String, String> expectedResult = new HashMap<>();
        when(mockChannelBranchInfoService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, String> result = groupPolicyPolicyItemPageExportServiceImplUnderTest.getBranchInfo();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testIsOnlineSales() {
        // Setup
        final EpPolicyContractInfoEntity entity = EpPolicyContractInfoEntity.builder()
                .id(0)
                .contractCode("contractCode")
                .policyNo("policyNo")
                .agentCode("agentCode")
                .companyName("companyName")
                .applicantName("applicantName")
                .salesType(0)
                .enforceTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())
                .referrerCode("referrerCode")
                .channelDistributionCode("channelDistributionCode")
                .deleted(0)
                .build();

        // Run the test
        final boolean result = GroupPolicyPolicyItemPageExportServiceImpl.isOnlineSales(entity);

        // Verify the results
        assertFalse(result);
    }
}
