package com.mpolicy.manage.modules.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.agent.enums.ReferrerQueryTypeEnum;
import com.mpolicy.manage.modules.agent.service.ChannelCustomerService;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 15:06
 */
public class ChannelCustomerServiceImplTest extends AdminApplicationTests {
    @Autowired
    ChannelCustomerService channelCustomerService;

    @Test
    public void queryReferrer() {
        List<ChannelApplicationReferrerVo> channelApplicationReferrerVos = channelCustomerService.queryReferrer("ZHNX", ReferrerQueryTypeEnum.NAME, "徐");
        System.out.println(JSON.toJSONString(channelApplicationReferrerVos));
        channelApplicationReferrerVos = channelCustomerService.queryReferrer("ZHNX", ReferrerQueryTypeEnum.WORK_NO, "HBXX");
        System.out.println(JSON.toJSONString(channelApplicationReferrerVos));
    }

}