package com.mpolicy.manage.modules.sys.fileManage.project;

import com.alibaba.fastjson.JSON;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.sys.entity.SystemBusinessFileManageEntity;
import com.mpolicy.manage.modules.sys.fileManage.dto.BusinessFileManageHandlerResult;
import com.mpolicy.manage.modules.sys.service.SystemBusinessFileManageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class ImpPolicyStandardPremiumTest extends AdminApplicationTests {

    @Resource
    ImpPolicyStandardPremium impPolicyStandardPremium;

    @Resource
    SystemBusinessFileManageService systemBusinessFileManageService;

    public void testGetFileManageProjectEnum() {
    }

    @Test
    public void testBusinessRun() {

        SystemBusinessFileManageEntity entity = systemBusinessFileManageService.lambdaQuery()
                .eq(SystemBusinessFileManageEntity::getFileManageCode, "FM20240723140614815283").one();

        BusinessFileManageHandlerResult result = impPolicyStandardPremium.businessRun(entity);
        log.info("查询结果：{}", JSON.toJSONString(result));
    }
}