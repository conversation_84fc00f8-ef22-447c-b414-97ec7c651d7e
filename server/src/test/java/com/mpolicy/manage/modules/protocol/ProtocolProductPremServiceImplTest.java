package com.mpolicy.manage.modules.protocol;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductProductEntity;
import com.mpolicy.manage.modules.protocol.entity.ProtocolProductPremEntity;
import com.mpolicy.manage.modules.protocol.enums.SettlementProtocolEventEnum;
import com.mpolicy.manage.modules.protocol.helper.SettlementProtocolHelper;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductProductService;
import com.mpolicy.manage.modules.protocol.service.ProtocolProductPremService;
import com.mpolicy.manage.modules.protocol.vo.ProtocolProductPremChange;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ProtocolProductPremServiceImplTest extends AdminApplicationTests {


    @Autowired
    private ProtocolProductPremService protocolProductPremService;

    @Autowired
    private IEpProtocolInsuranceProductProductService protocolInsuranceProductProductService;


    @Test
    public void productPremChange() {
        String protocolCode = "P20230516173442141532";
        List<String> insuranceProductCodeList = protocolProductPremService.lambdaQuery().eq(ProtocolProductPremEntity::getProtocolCode, protocolCode)
                .list().stream().map(ProtocolProductPremEntity::getInsuranceProductCode).distinct().collect(Collectors.toList());
        List<ProtocolProductPremChange> productCodeList = protocolInsuranceProductProductService.lambdaQuery()
                .in(EpProtocolInsuranceProductProductEntity::getInsuranceProductCode, insuranceProductCodeList)
                .list()
                .stream()
                .map(m -> BeanUtil.copyProperties(m, ProtocolProductPremChange.class))
                .collect(Collectors.toList());
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName","刘曙尘");
        //当前解绑的代理人姓名
        msgData.put("pushEventCode", CommonUtils.createCodeLastNumber("PE"));
        //解绑的客户列表 可以你
        msgData.put("productCodeList",JSONObject.toJSONString(productCodeList));
        // 保存变更记录
        //settlementPremChangeLogService.saveSettlementPremChangeLog(pushEventCode, PremChangeTypeEnum.CONTRACT_PRODUCT.getCode(), input.getContractCode(), msgData.toJSONString());

        SettlementProtocolHelper.pushSettlementProtocolEvent(protocolCode, SettlementProtocolEventEnum.PROTOCOL_PRODUCT_PREM_CHANGE, msgData);
    }

}
