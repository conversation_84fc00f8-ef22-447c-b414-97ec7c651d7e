package com.mpolicy.manage.modules.standard.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.helper.DocumentHelper;
import com.mpolicy.manage.modules.standard.constants.EnumStandardPremiumModuleType;
import com.mpolicy.manage.modules.standard.entity.PolicyStandardPremiumProductEntity;
import com.mpolicy.manage.modules.standard.utils.StandardPremiumUtils;
import com.mpolicy.manage.modules.standard.vo.StandardPremiumProductExpXls;
import com.mpolicy.manage.modules.standard.vo.StandardPremiumProductXls;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.fileManage.dto.BusinessFileManageHandlerResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ImpPolicyStandardPremiumServiceTest extends AdminApplicationTests {

    @Autowired
    private PolicyStandardPremiumProductService policyStandardPremiumProductService;

    @Autowired
    private PolicyStandardPremiumService policyStandardPremiumService;

    @Autowired
    private PolicyStandardPremiumProductCommissionService policyStandardPremiumProductCommissionService;

    @Test
    public void queryPage() {
    }

    @Test
    public void queryDetail() {
    }

    @Test
    public void removeStandardPremiumCode() {
    }

    @Test
    public void importStandardPremiumProduct() {
        String fileManageCode = "FM20240326161922225393";
        SysUserEntity user = new SysUserEntity();
        user.setUsername("sunhaijun");
        SysDocumentEntity document = DocumentHelper.getDocumentInfo("oss20240326161735yFlPFE");
        List<StandardPremiumProductXls> standardPremiumProductXls = JSONArray.parseArray("[{\"applicantAge\":\"18~35\",\"applicantGender\":\"男\",\"cellStyleMap\":{},\"commissionRate\":\"0.25\",\"commissionRate2year\":\"0.15\",\"commissionRate3year\":\"0.1\",\"commissionRate4year\":\"0.08\",\"commissionRate5year\":\"0.05\",\"commissionRate6year\":\"0.02\",\"commissionRateOtherYear\":\"7_1%|8_0%\",\"companyCode\":\"HQRS000000\",\"companyName\":\"横琴人寿保险有限公司\",\"endDate\":\"45657\",\"insuredAge\":\"18~35\",\"insuredGender\":\"男\",\"insuredPeriod\":\"按年保_1\",\"paymentPeriod\":\"按年缴_1\",\"periodType\":\"年缴\",\"premiumProductNumber\":\"1\",\"productChannelCode\":\"zhnx\",\"productCode\":\"HQRSXFXZ22110901,HQRSXFXZ22110902\",\"productName\":\"横琴人寿小飞象终身重大疾病保险（互联网）\",\"productPlan\":\"基础版\",\"startDate\":\"45292\"},{\"applicantAge\":\"50~80\",\"applicantGender\":\"女\",\"cellStyleMap\":{},\"commissionRate\":\"0.15\",\"commissionRate2year\":\"0.25\",\"commissionRate3year\":\"0.3\",\"commissionRate4year\":\"0.8\",\"commissionRateOtherYear\":\"7_1%|8_0%\",\"companyCode\":\"ZHLC000000\",\"companyName\":\"中华联合财产保险有限公司\",\"endDate\":\"45657\",\"insuredAge\":\"15~30\",\"insuredGender\":\"男\",\"insuredPeriod\":\"按年保_1\",\"paymentPeriod\":\"按年缴_1\",\"periodType\":\"年缴\",\"premiumProductNumber\":\"2\",\"productChannelCode\":\"zhnx\",\"productCode\":\"ZHCXGRYW24011513,ZHCXGRYW24011512\",\"productName\":\"中华个人意外险\",\"productPlan\":\"豪华版\",\"startDate\":\"45292\"}]", StandardPremiumProductXls.class);
        BusinessFileManageHandlerResult result = policyStandardPremiumService.importStandardPremiumProduct(fileManageCode, document, standardPremiumProductXls,user);
        log.info("执行结果={}", JSON.toJSONString(result));
    }

    @Test
    public void expStandardPremiumProduct() {
        Map<String, Object> params = new HashMap<>();
        SysUserEntity user = new SysUserEntity();
        user.setUsername("sunhaijun");
        List<StandardPremiumProductExpXls> result = policyStandardPremiumProductCommissionService.queryStandardPremiumProductExpXls(params, 0, 500, user);
        log.info("执行结果={}", JSON.toJSONString(result));
    }

    @Test
    public void importStandardPremiumProductCheck() {
        String startDate = "45292";
        String endDate = "45657";
        PolicyStandardPremiumProductEntity bean = policyStandardPremiumProductService.lambdaQuery().eq(PolicyStandardPremiumProductEntity::getStandardPremiumCode, "PS20240329160022355143").one();
        log.info("{}校验时间=【{}-{}】", StandardPremiumUtils.formatExcelDate(startDate), bean.getEffectiveStartDate(), bean.getEffectiveEndDate());
        log.info("{}校验时间=【{}-{}】", StandardPremiumUtils.formatExcelDate(startDate), bean.getEffectiveStartDate(), bean.getEffectiveEndDate());
        log.info("开始时间-执行结果={}", DateUtil.isIn(StandardPremiumUtils.formatExcelDate(startDate), bean.getEffectiveStartDate(), bean.getEffectiveEndDate()));
        log.info("结束时间-执行结果={}", DateUtil.isIn(StandardPremiumUtils.formatExcelDate(endDate), bean.getEffectiveStartDate(), bean.getEffectiveEndDate()));
    }
}