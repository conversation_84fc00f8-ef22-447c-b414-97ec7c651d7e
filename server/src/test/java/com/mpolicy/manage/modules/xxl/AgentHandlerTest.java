package com.mpolicy.manage.modules.xxl;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.jobhandler.agent.AgentHandler;
import com.mpolicy.manage.jobhandler.bi.BiHandler;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class AgentHandlerTest  extends AdminApplicationTests {


    @Autowired
    private AgentHandler agentHandler;

    @Autowired
    private BiHandler biHandler;

    @Test
    public void syncAgentToBiAgentInfo(){
        agentHandler.syncAgentToBiAgentInfo();
    }


    @Test
    public void syncAgentGroupStat(){
        biHandler.syncAgentGroupStat();
    }
}
