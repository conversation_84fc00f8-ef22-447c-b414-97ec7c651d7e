package com.mpolicy.manage.modules.commission;

import com.mpolicy.manage.AdminApplicationTests;
import com.mpolicy.manage.modules.commission.service.CommissionBasicPremService;
import com.mpolicy.manage.modules.contract.service.ContractInsuranceProductPremService;
import com.mpolicy.manage.modules.protocol.service.ProtocolProductPremService;
import com.mpolicy.manage.modules.protocol.vo.ProductPremExcel;
import com.mpolicy.manage.utils.ProductPremUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CommissionBasicPremServiceImplTest extends AdminApplicationTests {

    @Autowired
    private CommissionBasicPremService commissionBasicPremService;


    @Autowired
    private ProtocolProductPremService protocolProductPremService;


    @Autowired
    private ContractInsuranceProductPremService contractInsuranceProductPremService;


    /**
     * 初始化协议
     */
    @Test
    public void correctRateCode() {
        /*commissionBasicPremService.list().forEach(entity -> {
            CommissionBasicPremTemplate template = new CommissionBasicPremTemplate();
            template.setFirstYearBrokage(entity.getFirstYearBrokage());
            template.setRenewalBrokage2year(entity.getRenewalBrokage2year());
            template.setRenewalBrokage3year(entity.getRenewalBrokage3year());
            template.setRenewalBrokage4year(entity.getRenewalBrokage4year());
            template.setRenewalBrokage5year(entity.getRenewalBrokage5year());
            template.setRenewalBrokage6year(entity.getRenewalBrokage6year());
            template.setRenewalAutoExpand(entity.getRenewalAutoExpand());
            entity.setRateCode(ProductPremUtil.getCommissionRateCode(template));
            commissionBasicPremService.updateById(entity);
        });*/

        protocolProductPremService.list().forEach(entity -> {
            ProductPremExcel template = new ProductPremExcel();
            template.setFirstYearBrokage(entity.getFirstYearBrokage());
            template.setRenewalBrokage2year(entity.getRenewalBrokage2year());
            template.setRenewalBrokage3year(entity.getRenewalBrokage3year());
            template.setRenewalBrokage4year(entity.getRenewalBrokage4year());
            template.setRenewalBrokage5year(entity.getRenewalBrokage5year());
            template.setRenewalBrokage6year(entity.getRenewalBrokage6year());
            template.setRenewalAutoExpand(entity.getRenewalAutoExpand());
            entity.setRateCode(ProductPremUtil.getProtocolRateCode(template));
            protocolProductPremService.updateById(entity);
        });

        contractInsuranceProductPremService.list().forEach(entity -> {
            ProductPremExcel template = new ProductPremExcel();
            template.setFirstYearBrokage(entity.getFirstYearBrokage());
            template.setRenewalBrokage2year(entity.getRenewalBrokage2year());
            template.setRenewalBrokage3year(entity.getRenewalBrokage3year());
            template.setRenewalBrokage4year(entity.getRenewalBrokage4year());
            template.setRenewalBrokage5year(entity.getRenewalBrokage5year());
            template.setRenewalBrokage6year(entity.getRenewalBrokage6year());
            template.setRenewalAutoExpand(entity.getRenewalAutoExpand());
            entity.setRateCode(ProductPremUtil.getProtocolRateCode(template));
            contractInsuranceProductPremService.updateById(entity);
        });

    }
}
