package com.mpolicy.manage.annotation;

import com.mpolicy.manage.enums.InvalidCacheEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 设置失效缓存
 * 实用此注解需要严谨 InvalidCacheAspect的缓存失效逻辑
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-22 16:30
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface InvalidCache {

    /**
     * 模块枚举
     */
    InvalidCacheEnum modelCode() ;
}
