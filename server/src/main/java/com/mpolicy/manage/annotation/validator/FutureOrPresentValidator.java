package com.mpolicy.manage.annotation.validator;

import com.mpolicy.manage.annotation.FutureOrPresent;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description todo
 * @create 2024/2/23
 * @since 1.0.0
 */
public class FutureOrPresentValidator implements ConstraintValidator<FutureOrPresent, Date> {

    private Integer secondsToAdd;

    @Override
    public void initialize(FutureOrPresent constraintAnnotation) {
        this.secondsToAdd = constraintAnnotation.secondsToAdd();
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(Date date, ConstraintValidatorContext constraintValidatorContext) {
        if (date == null) {
            return true;
        }
        return date.getTime() > (System.currentTimeMillis() + secondsToAdd * 1000);
    }
}
