package com.mpolicy.manage.annotation;

import com.mpolicy.manage.annotation.validator.FutureOrPresentValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * description: 该注解是JSR303中定义的，用于校验时间是否大于当前时间加指定秒数。
 * author: huanghao
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FutureOrPresentValidator.class)
public @interface FutureOrPresent {
    String message() default "时间不能超过当前时间加指定秒数";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    int secondsToAdd() default 0;
}
