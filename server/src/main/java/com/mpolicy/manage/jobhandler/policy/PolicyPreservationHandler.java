package com.mpolicy.manage.jobhandler.policy;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.mq.RabbitMQService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.thread.ThreadUtils;
import com.mpolicy.manage.modules.helper.entity.PolicyFixLogEntity;
import com.mpolicy.manage.modules.helper.service.PolicyFixLogService;
import com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.PreservationSurrenderEntity;
import com.mpolicy.manage.modules.policy.enums.PreservationTopicEnum;
import com.mpolicy.manage.modules.policy.enums.PreservationWhyEnum;
import com.mpolicy.manage.modules.policy.service.EpPolicyProductInsuredMapService;
import com.mpolicy.manage.modules.policy.service.common.EpPolicyBaseService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationSurrenderService;
import com.mpolicy.manage.modules.policy.service.renewal.PolicyRenewalTermService;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationApplyInput;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationSurrenderDetailVo;
import com.mpolicy.manage.modules.sys.util.LambdaUtils;
import com.mpolicy.manage.mq.policy.service.PreservationEventService;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.client.PolicyRenewalTermClient;
import com.mpolicy.policy.common.enums.PolicyProductTypeEnum;
import com.mpolicy.policy.common.enums.PreservationProjectEnum;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInfoVo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreserveSurrenderResultVo;
import com.mpolicy.policy.common.ep.policy.product.EpInsuredProductVo;
import com.mpolicy.policy.common.ep.policy.renewal.EpPolicyRenewalTermVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 保全job
 *
 * <AUTHOR>
 * @date 2022-03-21 18:23
 */
@Component
@Slf4j
public class PolicyPreservationHandler {

    @Autowired
    private PreservationApplyService preservationApplyService;

    @Autowired
    private PreservationEventService preservationEventService;

    @Autowired
    private PolicyRenewalTermClient renewalTermClient;

    @Autowired
    private PolicyRenewalTermService renewalTermService;

    @Autowired
    protected EpPolicyBaseService epPolicyBaseService;

    @Autowired
    private EpPolicyProductInsuredMapService policyProductInsuredMapService;

    @Autowired
    private PreservationSurrenderService preservationSurrenderService;

    @Autowired
    private PolicyFixLogService policyFixLogService;

    @Autowired
    private RabbitMQService rabbitMQService;

    /**
     * 保单保全状态
     */
    @XxlJob("policyPreservationJob")
    public void policyPreservationJob() throws Exception {
        // 1 获取需要处理修改保单状态的保全信息> 退保、犹豫期退保和协议解约三个保全，将根据保全生效日期将保单状态变更为退保/犹豫期退保/协议解约状态。
        List<PreservationApplyEntity> list = preservationApplyService.lambdaQuery()
                .eq(PreservationApplyEntity::getProcessingStatus, 0)
                .list();

        XxlJobHelper.log("保全调用保单中心请求变更状态的纪录数=：{}", list.size());
        // 2 遍历处理
        list.forEach(x -> {
            ThreadUtils.sleep(1000);
            // 发送mq进行保全操作
            MQMessage msg = new MQMessage();
            msg.setCode(x.getPreservationCode());
            msg.setOpeType(PreservationTopicEnum.PRESERVATION_APPLY.getRouteKey());
            rabbitMQService.sendTopicMessage(PreservationTopicEnum.PRESERVATION_APPLY.getExchange(), PreservationTopicEnum.PRESERVATION_APPLY.getRouteKey(), msg);
            log.info("变更保单号={},变更状态={}", x.getContractCode(), x.getPreservationProject());
        });
        XxlJobHelper.handleSuccess("保单保全状态变更任务完成");
    }


    /**
     * 保单保全状态
     */
    @XxlJob("execute-preservation")
    public void run() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始执行保全操作:{}", param);
        if (StringUtils.isBlank(param)) {
            return;
        }

        String[] codeList = param.split(",|，");
        for (String code : codeList) {
            preservationEventService.preservationChange(code);
        }
        XxlJobHelper.log("保全操作执行完成");
    }

    @XxlJob("preservation-surrender-detail-fix")
    public void preservationSurrenderDetailFix() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始补齐退保明细:{}", param);

        int size = 500;
        int maxTry = 500;
        AtomicInteger i = new AtomicInteger(0);
        for(;;){
            List<PreservationApplyEntity> data = preservationApplyService.querySurrender4EmptyFix(param,size);
            if(CollectionUtils.isEmpty(data)){
                break;
            }

            preservationSurrenderDetailFix(data);
            if(i.incrementAndGet() >maxTry){
                log.info("超过最大尝试次数");
                break;
            }
        }
        XxlJobHelper.log("开始补齐退保明细");
    }


    public void preservationSurrenderDetailFix(List<PreservationApplyEntity> data){
        List<PolicyFixLogEntity> logEntityList = data.stream().map(this::preservationSurrenderDetailFix).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(logEntityList)){
            policyFixLogService.saveBatch(logEntityList,1000);
        }
    }

    private List<PreservationSurrenderEntity> convertSurrenderDetail(PreservationApplyEntity apply) {
        //在保或者终止的险种数据都会列出来
        Result<EpPolicyRenewalTermVo> r = renewalTermClient.currentRenewalTerm(apply.getPolicyCode());
        int period = 1;
        if(r.isSuccess()){
            EpPolicyRenewalTermVo renewalTermVo = r.getData();
            if(renewalTermVo!=null){
                period = renewalTermVo.getPeriod();
            }
        }

        List<EpInsuredProductVo> productList = null;

        if(period==1) {
            productList = policyProductInsuredMapService.listInsuredProduct(apply.getContractCode());
        }else {
            productList = renewalTermService.listInsuredProduct(apply.getPolicyCode(),period);
        }

        return productList.stream()
                .map(product -> {
                    PreservationSurrenderEntity entity = new PreservationSurrenderEntity();
                    BeanUtils.copyProperties(product, entity);
                    entity.setPreservationCode(apply.getPreservationCode());
                    return entity;
                })
                .collect(Collectors.toList());
    }

    public PolicyFixLogEntity preservationSurrenderDetailFix(PreservationApplyEntity data){
        //1.记录处理日志
        String preservationCode = data.getPreservationCode();
        PolicyFixLogEntity fixLog = new PolicyFixLogEntity();
        fixLog.setPolicyNo(data.getPolicyCode());
        fixLog.setType(2);
        //2.判断补全的前置条件
        List<String> mutexPreservationList = Arrays.asList(PreservationProjectEnum.TERMINATION_PRODUCT.getCode(),
                PreservationProjectEnum.ADD_OR_SUBTRACT.getCode()
                );
        String contractCode = data.getContractCode();
        int a = preservationApplyService.lambdaQuery()
                .eq(PreservationApplyEntity::getContractCode,contractCode)
                .in(PreservationApplyEntity::getPreservationProject,mutexPreservationList)
                .eq(PreservationApplyEntity::getDeleted,0)
                .count();
        if(a>0){
            fixLog.setRemark("该保单存在其他保全，无法补全退保明细");
            return fixLog;
        }

        EpContractBriefInfoVo policy = epPolicyBaseService.queryPolicyContractBriefInfo(contractCode, true);
        if(Objects.equals(policy.getPolicyProductType(), PolicyProductTypeEnum.GROUP.getCode())){
            fixLog.setRemark("团险无法补全退保明细");
            return fixLog;
        }

        //3.开始补全退保明细
        List<PreservationSurrenderEntity> detailList = convertSurrenderDetail(data);
        if(CollectionUtils.isEmpty(detailList)){
            fixLog.setRemark("保全明细为空，需要人工核对");
            return fixLog;
        }
        boolean r =preservationSurrenderService.saveBatch(detailList,1000);
        log.info("保全明细保存完成:{},{}",preservationCode,r);
        return fixLog;
    }

    /**
     * 保单保全状态
     */
    @XxlJob("surrender-renewal-term-period-fix")
    public void surrenderRenewalTermPeriodFix() {
        String param = XxlJobHelper.getJobParam();

        log.info("开始修复退保保全期数:{}", param);

        List<PreservationApplyEntity> applyList = preservationApplyService.lambdaQuery()
                .eq(StringUtils.isNotBlank(param),PreservationApplyEntity::getPolicyCode,param)
                .in(PreservationApplyEntity::getPreservationProject,
                        Arrays.asList(PreservationProjectEnum.SURRENDER.getCode(),
                                PreservationProjectEnum.PROTOCOL_TERMINATION.getCode(),
                                PreservationProjectEnum.HESITATION_SURRENDER.getCode()
                        ))
                .eq(PreservationApplyEntity::getDeleted, 0)
                .list();
        if(!CollectionUtils.isEmpty(applyList)){
            applyList.forEach(this::surrenderRenewalTermPeriodFix);
        }

        log.info("修复退保保全期数执行完成");
    }

    public void surrenderRenewalTermPeriodFix(PreservationApplyEntity entity) {
        Integer renewalTermPeriod = entity.getRenewalTermPeriod();
        String policyNo = entity.getPolicyCode();

        Integer renewalTermPeriod2 = 1;
        Result<EpPolicyRenewalTermVo> r2 = renewalTermClient.currentRenewalTerm(policyNo);
        if (!r2.isSuccess()) {
            log.warn("查询续期数据失败:{}",policyNo);
            return;
        }
        EpPolicyRenewalTermVo renewalTerm = r2.getData();
        if (renewalTerm != null) {
            renewalTermPeriod2 = renewalTerm.getPeriod();

        }


        log.info("检查期数是否相同:{},{},{}", policyNo,renewalTermPeriod,renewalTermPeriod2);

        if(!Objects.equals(renewalTermPeriod,renewalTermPeriod2)){
            boolean r = preservationApplyService.lambdaUpdate()
                    .set(PreservationApplyEntity::getRenewalTermPeriod,renewalTermPeriod2)
                    .eq(PreservationApplyEntity::getId,entity.getId())
                    .update();
            log.info("退保保全更新完成:{},{},{},{}",policyNo,renewalTermPeriod,renewalTermPeriod2,r);
        }
    }

}
