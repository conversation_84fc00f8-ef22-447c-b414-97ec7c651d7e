package com.mpolicy.manage.jobhandler.commission;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.mpolicy.manage.enums.ProgrammeStatusEnum;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicInfoEntity;
import com.mpolicy.manage.modules.commission.entity.CommissionFloatRewardEntity;
import com.mpolicy.manage.modules.commission.service.CommissionBasicInfoService;
import com.mpolicy.manage.modules.commission.service.CommissionFloatRewardService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 佣金定时任务
 * commissionStatus 佣金配置状态0:未生效 1:已生效 2:已到期
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommissionJob {

    @Autowired
    private CommissionBasicInfoService commissionBasicInfoService;

    @Autowired
    private CommissionFloatRewardService commissionFloatRewardService;

    /**
     * 处理基础佣金配置
     */
    @XxlJob("handleCommissionBasicStatus")
    public void handleCommissionBasicStatus() {
        commissionBasicToBeEffective();
        commissionBasicEffective();
        commissionBasicInvalidation();
    }


    /**
     * 处理浮动奖励状态
     * 0:未发布 1:未开始 2:活动中 3:活动结束 4:活动暂停 5:活动作废
     */
    @XxlJob("handleCommissionFloatRewardStatus")
    public void handleCommissionFloatRewardStatus() {
        //1.满足活动中的设置为活动中  未开始 满足开始条件
        commissionFloatRewardService.lambdaUpdate()
                .eq(CommissionFloatRewardEntity::getProgrammeStatus, ProgrammeStatusEnum.NOT_STARTED.getCode())
                .le(CommissionFloatRewardEntity::getBeginTime, new Date())
                .set(CommissionFloatRewardEntity::getProgrammeStatus, ProgrammeStatusEnum.IN_PROGRESS.getCode())
                .update();

        //2.将满足结束条件的数据设置为结束  活动中 当前时间大于结束时间
        commissionFloatRewardService.lambdaUpdate()
                .eq(CommissionFloatRewardEntity::getProgrammeStatus, ProgrammeStatusEnum.IN_PROGRESS.getCode())
                .lt(CommissionFloatRewardEntity::getEndTime, new Date())
                .set(CommissionFloatRewardEntity::getProgrammeStatus, ProgrammeStatusEnum.ENDED.getCode())
                .update();

        //3.重置未开始数据 活动中 当前时间小于开时间
        commissionFloatRewardService.lambdaUpdate()
                .eq(CommissionFloatRewardEntity::getProgrammeStatus, ProgrammeStatusEnum.IN_PROGRESS.getCode())
                .gt(CommissionFloatRewardEntity::getBeginTime, new Date())
                .set(CommissionFloatRewardEntity::getProgrammeStatus, ProgrammeStatusEnum.NOT_STARTED.getCode())
                .update();

    }

    /**
     * 处理基础佣金没有满足生效条件的设置待生效
     * 佣金配置状态0:未生效 1:已生效 2:已到期
     */
    private void commissionBasicToBeEffective() {
        XxlJobHelper.log("开始处理基础佣金没有满足生效条件的设置待生效");
        try {
            DateTime beginOfDay = DateUtil.beginOfDay(new Date());
            // 将不满足生效时间的协议设置为待生效 开始时间大于当前时间
            List<String> commissionCodeList = commissionBasicInfoService.lambdaQuery()
                    .gt(CommissionBasicInfoEntity::getBeginTime, beginOfDay)
                    .ne(CommissionBasicInfoEntity::getCommissionStatus, 0)
                    .list()
                    .stream()
                    .map(CommissionBasicInfoEntity::getCommissionCode)
                    .collect(Collectors.toList());
            if (!commissionCodeList.isEmpty()) {
                commissionBasicInfoService.lambdaUpdate()
                        .in(CommissionBasicInfoEntity::getCommissionCode, commissionCodeList)
                        .set(CommissionBasicInfoEntity::getCommissionStatus, 0)
                        .update();
            }
            XxlJobHelper.handleSuccess("处理基础佣金待生效成功");
        } catch (Exception e) {
            XxlJobHelper.handleFail("处理基础佣金待生效失败啦!");
        }

    }

    /**
     * 处理基础佣金生效
     * 佣金配置状态0:未生效 1:已生效 2:已到期
     */
    private void commissionBasicEffective() {
        XxlJobHelper.log("开始处理基础佣金让满足生效条件的生效");
        try {
            // 获取没有生效,且满足生效时间的数据 开始时间 <= 当前时间 状态为待生效
            DateTime beginOfDay = DateUtil.beginOfDay(new Date());
            List<String> commissionCodeList = commissionBasicInfoService.lambdaQuery()
                    .le(CommissionBasicInfoEntity::getBeginTime, beginOfDay)
                    .eq(CommissionBasicInfoEntity::getCommissionStatus, 0)
                    .list()
                    .stream()
                    .map(CommissionBasicInfoEntity::getCommissionCode)
                    .collect(Collectors.toList());
            if (!commissionCodeList.isEmpty()) {
                commissionBasicInfoService.lambdaUpdate()
                        .in(CommissionBasicInfoEntity::getCommissionCode, commissionCodeList)
                        .set(CommissionBasicInfoEntity::getCommissionStatus, 1)
                        .update();
            }
            XxlJobHelper.handleSuccess("处理基础佣金生效成功");
        } catch (Exception e) {
            XxlJobHelper.handleFail("处理基础佣金生效失败啦!");
        }

    }

    /**
     * 处理基础佣金失效
     * 佣金配置状态0:未生效 1:已生效 2:已到期
     */
    private void commissionBasicInvalidation() {
        XxlJobHelper.log("开始处理基础佣金让满足失效条件的失效");
        try {
            // 获取没有生效,且满足生效时间的数据 状态不是已结束 && 结束时间 < 当前时间 或者没有配置结束时间
            DateTime beginOfDay = DateUtil.beginOfDay(new Date());
            List<String> commissionCodeList = commissionBasicInfoService.lambdaQuery()
                    .ne(CommissionBasicInfoEntity::getCommissionStatus, 2)
                    .isNotNull(CommissionBasicInfoEntity::getEndTime)
                    .lt(CommissionBasicInfoEntity::getEndTime, beginOfDay)
                    .list()
                    .stream()
                    .map(CommissionBasicInfoEntity::getCommissionCode)
                    .collect(Collectors.toList());
            if (!commissionCodeList.isEmpty()) {
                commissionBasicInfoService.lambdaUpdate()
                        .in(CommissionBasicInfoEntity::getCommissionCode, commissionCodeList)
                        .set(CommissionBasicInfoEntity::getCommissionStatus, 2)
                        .update();
            }
            XxlJobHelper.handleSuccess("处理基础佣金失效成功");
        } catch (Exception e) {
            XxlJobHelper.handleFail("处理基础佣金失效失败啦!");
        }
    }

}
