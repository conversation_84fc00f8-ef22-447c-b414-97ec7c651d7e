package com.mpolicy.manage.jobhandler.material;

import cn.hutool.core.date.DateUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.modules.article.entity.MaterialArticleInfoEntity;
import com.mpolicy.manage.modules.article.service.MaterialArticleInfoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * ClassName: ArticleJob
 * Description: 素材文章增加浏览量任务
 * date: 2023/7/20 16:08
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ArticleJob {

    @Autowired
    private MaterialArticleInfoService materialArticleInfoService;

    @XxlJob("addArticlePageView")
    public void addArticlePageView() throws Exception {
        log.info("素材文章增加浏览量 start date= {}", DateUtil.now());
        List<MaterialArticleInfoEntity> list = materialArticleInfoService.lambdaQuery().eq(MaterialArticleInfoEntity::getStatus, StatusEnum.NORMAL.getCode()).list();
        List<MaterialArticleInfoEntity> collect = list.stream().map(a -> {
            MaterialArticleInfoEntity updateBean = new MaterialArticleInfoEntity();
            updateBean.setId(a.getId());
            updateBean.setAddPageView(new Random().nextInt(10));
            return updateBean;
        }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(collect)){
            this.materialArticleInfoService.updateBatchById(collect);
        }
        XxlJobHelper.handleSuccess("素材文章增加浏览量处理任务完成");
    }
}
