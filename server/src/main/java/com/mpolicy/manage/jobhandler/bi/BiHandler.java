package com.mpolicy.manage.jobhandler.bi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.manage.modules.agent.entity.BiAgentGroupStatEntity;
import com.mpolicy.manage.modules.agent.entity.BiGroupAgentEntrustPolicyEntity;
import com.mpolicy.manage.modules.agent.service.BiAgentGroupStatService;
import com.mpolicy.manage.modules.agent.service.BiGroupAgentEntrustPolicyService;
import com.mpolicy.manage.modules.chat.dao.AgentUseAppDao;
import com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut;
import com.mpolicy.manage.modules.chat.entity.AgentUserAppListOutVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BiHandler {

    @Autowired
    private BiGroupAgentEntrustPolicyService biGroupAgentEntrustPolicyService;

    @Autowired
    private BiAgentGroupStatService biAgentGroupStatService;

    @Autowired
    private AgentUseAppDao agentUseAppDao;


    /**
     * 同步代理人信息到BiAgent数据库
     */
    @XxlJob("syncAgentGroupStat")
    public void syncAgentGroupStat() {
        try {
            XxlJobHelper.log("开始统计代理人数据");
            //删除数据
            biAgentGroupStatService.lambdaUpdate().remove();
            Date date = new Date();
            DateTime beginOfDay = DateUtil.beginOfDay(new Date());
            //获取分组
            Map<String, List<BiGroupAgentEntrustPolicyEntity>> map = biGroupAgentEntrustPolicyService.list().stream()
                    .collect(Collectors.groupingBy(BiGroupAgentEntrustPolicyEntity::getGroupName));
            //插入的数据列表
            List<BiAgentGroupStatEntity> batchList = new ArrayList<>();
            //请求参数
            AgentUserAppListOutVo agentUserAppList = new AgentUserAppListOutVo();
            agentUserAppList.setTotalBeginTime("2020-01-01 00:00:00");
            agentUserAppList.setTotalEndTime(beginOfDay.toString());
            agentUserAppList.setBeginTime(DateUtil.offsetDay(beginOfDay, -1).toString());
            agentUserAppList.setEndTime(beginOfDay.toString());
            //处理数据
            map.forEach((groupName, list) -> {
                List<String> agentCodeList = list.stream().map(BiGroupAgentEntrustPolicyEntity::getAgentCode).distinct().collect(Collectors.toList());
                agentUserAppList.setAgentCodeList(agentCodeList);
                BiAgentGroupStatEntity biAgentGroupStat = new BiAgentGroupStatEntity();
                biAgentGroupStat.setGroupName(groupName);
                biAgentGroupStat.setCreateTime(date);
                //服务客户数
                List<AgentUseAppListOut> agentCustomerList = agentUseAppDao.findAgentCustomerList(agentUserAppList);
                if (CollUtil.isEmpty(agentCustomerList)) {
                    biAgentGroupStat.setCustomerNum(0);
                    biAgentGroupStat.setNewlyAddedCustomerNum(0);
                } else {
                    biAgentGroupStat.setCustomerNum(agentCustomerList.stream().mapToInt(m -> m.getCustomerBindTotal()).sum());
                    biAgentGroupStat.setNewlyAddedCustomerNum(agentCustomerList.stream().mapToInt(m -> m.getCustomerBindAdded()).sum());
                }
                // 托管保单数
                List<AgentUseAppListOut> policyCustodyList = agentUseAppDao.findPolicyCustodyList(agentUserAppList);
                if (CollUtil.isEmpty(policyCustodyList)) {
                    biAgentGroupStat.setPolicyNum(0);
                    biAgentGroupStat.setNewlyAddedPolicyNum(0);
                } else {
                    biAgentGroupStat.setPolicyNum(policyCustodyList.stream().mapToInt(m -> m.getPolicyCustodyTotal()).sum());
                    biAgentGroupStat.setNewlyAddedPolicyNum(policyCustodyList.stream().mapToInt(m -> m.getPolicyCustodyAdded()).sum());
                }

                batchList.add(biAgentGroupStat);

            });
            if (CollUtil.isNotEmpty(batchList)) {
                biAgentGroupStatService.saveBatch(batchList);
            }
            XxlJobHelper.log("统计代理人数据完成");
        } catch (Exception e) {
            XxlJobHelper.log("统计代理人数据失败");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }

    }

    public static void main(String[] args) {
        //String json = "[{\"agentCode\":\"ag20210331150245GPQNf1\",\"businessCode\":\"XJ1000012100002\"},{\"agentCode\":\"ag20210413183603tnkn34\",\"businessCode\":\"XJ200147896541\"},{\"agentCode\":\"ag202105201728057cmA2o\",\"businessCode\":\"XJ1000010100003\"},{\"agentCode\":\"ag20210525172356uoBqQ6\",\"businessCode\":\"XJ1000010100002\"},{\"agentCode\":\"ag20210602165115Q88kqw\",\"businessCode\":\"XJ1000010100004\"},{\"agentCode\":\"ag20210604103346K8uP8c\",\"businessCode\":\"XJ1000010100005\"},{\"agentCode\":\"ag20210604173718DmjgKd\",\"businessCode\":\"XJ1000012100003\"},{\"agentCode\":\"ag20210608140342nwbCOG\",\"businessCode\":\"XJ1000010100006\"},{\"agentCode\":\"ag20210610134003Kwq6F3\",\"businessCode\":\"XJ1000012100001\"},{\"agentCode\":\"ag20210618103731OqJPq1\",\"businessCode\":\"XJ1000010100007\"},{\"agentCode\":\"ag20210624101319DgAq4s\",\"businessCode\":\"XJ1000010100008\"},{\"agentCode\":\"ag20210624104510awzxQI\",\"businessCode\":\"XJ1000010100009\"},{\"agentCode\":\"ag20210720114115K7pzlo\",\"businessCode\":\"XJ1000010100011\"},{\"agentCode\":\"ag20210720114230PkM2J8\",\"businessCode\":\"XJ1000010100010\"},{\"agentCode\":\"ag20210721155307h8ohut\",\"businessCode\":\"XJ1000010400001\"},{\"agentCode\":\"ag20210727110314Pbznlg\",\"businessCode\":\"XJ1000010100012\"},{\"agentCode\":\"ag20210802141136OQdcxm\",\"businessCode\":\"XJ1000010100013\"},{\"agentCode\":\"ag20210802151025gz5HE0\",\"businessCode\":\"XJ1000010100014\"},{\"agentCode\":\"ag20210804091902oma6DE\",\"businessCode\":\"XJ1000010100015\"},{\"agentCode\":\"ag20210804163152JDnfyE\",\"businessCode\":\"XJ1000010100016\"},{\"agentCode\":\"ag20210804164409FEnPLf\",\"businessCode\":\"XJ1000010100017\"},{\"agentCode\":\"ag20210804165400MzIMc3\",\"businessCode\":\"XJ1000010100018\"},{\"agentCode\":\"ag202108041707019aC8N7\",\"businessCode\":\"XJ1000010100019\"},{\"agentCode\":\"ag202108041722583PHKCg\",\"businessCode\":\"XJ1000010100020\"},{\"agentCode\":\"ag20210809171854A5JGN8\",\"businessCode\":\"XJ1000010100021\"},{\"agentCode\":\"ag20210811163919F3o8Au\",\"businessCode\":\"XJ1000010100022\"},{\"agentCode\":\"ag20210816152207qOagO9\",\"businessCode\":\"XJ1679638629254\"},{\"agentCode\":\"ag2021082515471646evAN\",\"businessCode\":\"XJ1000010100024\"},{\"agentCode\":\"ag20210827161904cAnlCt\",\"businessCode\":\"XJ1000010100025\"},{\"agentCode\":\"ag20210827161919hNheap\",\"businessCode\":\"XJ1681986101563\"},{\"agentCode\":\"ag20210906130136bol0KO\",\"businessCode\":\"XJ1679638617690\"},{\"agentCode\":\"ag20210909092037kcFLrB\",\"businessCode\":\"XJ1679638604897\"},{\"agentCode\":\"ag20210909161540s7o0L5\",\"businessCode\":\"XJ1681986264551\"},{\"agentCode\":\"ag20210913113651knkknf\",\"businessCode\":\"XJ1000010100030\"},{\"agentCode\":\"ag202109141056504fihQ9\",\"businessCode\":\"XJ1000010100031\"},{\"agentCode\":\"ag20210914161717OOomnh\",\"businessCode\":\"XJ1679638591857\"},{\"agentCode\":\"ag20210914162621Q35ik5\",\"businessCode\":\"XJ1000010100033\"},{\"agentCode\":\"ag20210914163501AIB8NQ\",\"businessCode\":\"XJ1000010100034\"},{\"agentCode\":\"ag202109181745050DoD4M\",\"businessCode\":\"XJ1000010100035\"},{\"agentCode\":\"ag20210929155819bHvrPt\",\"businessCode\":\"XJ1000010100036\"},{\"agentCode\":\"ag202109301545490Cvu7c\",\"businessCode\":\"XJ1681986361881\"},{\"agentCode\":\"ag20211009122455kkbo30\",\"businessCode\":\"XJ1681986436232\"},{\"agentCode\":\"ag20211018171751amyyI1\",\"businessCode\":\"XJ1681986535200\"},{\"agentCode\":\"ag20211019175710wdkMMp\",\"businessCode\":\"XJ1681986626214\"},{\"agentCode\":\"ag20211021144604Q9lKPa\",\"businessCode\":\"XJ1000010100041\"},{\"agentCode\":\"ag20211021145358yOxjDC\",\"businessCode\":\"XJ1000010100042\"},{\"agentCode\":\"ag20211021163644oD7Pge\",\"businessCode\":\"XJ1000010100044\"},{\"agentCode\":\"ag20211021164116n52BcM\",\"businessCode\":\"XJ1681278351870\"},{\"agentCode\":\"ag20211021165741dQ4bNM\",\"businessCode\":\"XJ1681278375004\"},{\"agentCode\":\"ag20211029094107we7cQP\",\"businessCode\":\"XJ1681986672228\"},{\"agentCode\":\"ag20211029095643E3ILys\",\"businessCode\":\"XJ1000010100047\"},{\"agentCode\":\"ag202110291055078aHpkq\",\"businessCode\":\"XJ1000010200003\"},{\"agentCode\":\"ag20211029120634uGv5cj\",\"businessCode\":\"XJ1000040100001\"},{\"agentCode\":\"ag20211029123518tFAes4\",\"businessCode\":\"XJ1000010200004\"},{\"agentCode\":\"ag20211029135113zNhIlG\",\"businessCode\":\"XJ1000010200005\"},{\"agentCode\":\"ag20211029143414bI4ya1\",\"businessCode\":\"XJ1000010100048\"},{\"agentCode\":\"ag2021102915413320li9j\",\"businessCode\":\"XJ1679638578157\"},{\"agentCode\":\"ag20211029160136pFBMbH\",\"businessCode\":\"XJ1000020100001\"},{\"agentCode\":\"ag20211029163127CHKhtn\",\"businessCode\":\"XJ1681986738867\"},{\"agentCode\":\"ag202111010955204azic9\",\"businessCode\":\"XJ1000010200006\"},{\"agentCode\":\"ag202111011012081PiCBm\",\"businessCode\":\"XJ1000010200007\"},{\"agentCode\":\"ag20211101103651gBhm2H\",\"businessCode\":\"XJ1000010200008\"},{\"agentCode\":\"ag20211101105003pi3Jx1\",\"businessCode\":\"XJ1000030100001\"},{\"agentCode\":\"ag20211101170440nh5Cpj\",\"businessCode\":\"XJ1000010100001\"},{\"agentCode\":\"ag20211101171909weqDKp\",\"businessCode\":\"XJ1000010200009\"},{\"agentCode\":\"ag20211101174722NqwFvu\",\"businessCode\":\"XJ1000010200010\"},{\"agentCode\":\"ag20211109135632AOw4yc\",\"businessCode\":\"XJ1000010100051\"},{\"agentCode\":\"ag20211130180521xaQAxg\",\"businessCode\":\"XJ1000010100052\"},{\"agentCode\":\"ag20211202105854uHJ3vl\",\"businessCode\":\"XJ1000010100053\"},{\"agentCode\":\"ag20211203162348yc9L1C\",\"businessCode\":\"XJ1681986776663\"},{\"agentCode\":\"ag20211206092938Kg408f\",\"businessCode\":\"XJ1000010200011\"},{\"agentCode\":\"ag20211208102924am0OJI\",\"businessCode\":\"XJ1681986830390\"},{\"agentCode\":\"ag202112081043163D6zNb\",\"businessCode\":\"XJ1681986863169\"},{\"agentCode\":\"ag20211208171213leFm54\",\"businessCode\":\"XJ1681278363800\"},{\"agentCode\":\"ag20211208172906julxjM\",\"businessCode\":\"XJ1681986922121\"},{\"agentCode\":\"ag20211208173549ePKLdO\",\"businessCode\":\"XJ1681987116745\"},{\"agentCode\":\"ag20211208174558xuQyJL\",\"businessCode\":\"XJ1681987197057\"},{\"agentCode\":\"ag20211208175647HqD0IQ\",\"businessCode\":\"XJ1681278327321\"},{\"agentCode\":\"ag20211208180528P297eh\",\"businessCode\":\"XJ1681278340201\"},{\"agentCode\":\"ag20211208181828EmswPf\",\"businessCode\":\"XJ1000010100063\"},{\"agentCode\":\"ag20211214150222LkkIe6\",\"businessCode\":\"XJ1681987315144\"},{\"agentCode\":\"ag20220105104026q2y513\",\"businessCode\":\"XJ1679638566855\"},{\"agentCode\":\"ag2022013012090170OuBc\",\"businessCode\":\"XJ201990440104800001\"},{\"agentCode\":\"ag20220228110426upgMAc\",\"businessCode\":\"XJ1681987357542\"},{\"agentCode\":\"ag20220228183252rHmcK2\",\"businessCode\":\"XJ1000010200012\"},{\"agentCode\":\"ag202203151010221t4esO\",\"businessCode\":\"XJ1681987424857\"},{\"agentCode\":\"ag20220321134211sGfGKv\",\"businessCode\":\"XJ1647840956932\"},{\"agentCode\":\"ag20220324152916GnJFvb\",\"businessCode\":\"XJ1648106615554\"},{\"agentCode\":\"ag20220329134122C43FJP\",\"businessCode\":\"XJ1648532313795\"},{\"agentCode\":\"ag20220329135757FKdmpL\",\"businessCode\":\"XJ1648533265935\"},{\"agentCode\":\"ag20220329140721Ow1P7w\",\"businessCode\":\"XJ1648533856721\"},{\"agentCode\":\"ag20220406165723w1A61L\",\"businessCode\":\"XJ1649235177985\"},{\"agentCode\":\"ag20220411143316xMlre5\",\"businessCode\":\"XJ1649659469625\"},{\"agentCode\":\"ag20220418152240rbCxCH\",\"businessCode\":\"XJ1650266399757\"},{\"agentCode\":\"ag202204181628545OGnHM\",\"businessCode\":\"XJ1681987469463\"},{\"agentCode\":\"ag20220420135936iglCxE\",\"businessCode\":\"XJ1681987525246\"},{\"agentCode\":\"ag20220420174811gzoMrh\",\"businessCode\":\"XJ1679638552230\"},{\"agentCode\":\"ag20220424130907A7vxv4\",\"businessCode\":\"XJ1679638527852\"},{\"agentCode\":\"ag20220424161342EHN51x\",\"businessCode\":\"XJ1679638514067\"},{\"agentCode\":\"ag20220427141155ekDoAO\",\"businessCode\":\"XJ1651039467262\"},{\"agentCode\":\"ag202204271435276PvQcw\",\"businessCode\":\"XJ1679638499163\"},{\"agentCode\":\"ag20220427145442y14o9M\",\"businessCode\":\"XJ1679638481581\"},{\"agentCode\":\"ag20220428095540sCCsgN\",\"businessCode\":\"XJ1651110783129\"},{\"agentCode\":\"ag20220429153352HbOL7j\",\"businessCode\":\"XJ1679638467664\"},{\"agentCode\":\"ag20220506135448yy4Lit\",\"businessCode\":\"XJ1651816273319\"},{\"agentCode\":\"ag202205071824107nLj0C\",\"businessCode\":\"XJ1651918820302\"},{\"agentCode\":\"ag202205091523477Brmjd\",\"businessCode\":\"XJ1679638436148\"},{\"agentCode\":\"ag20220511175227lMcO5l\",\"businessCode\":\"XJ1652262422606\"},{\"agentCode\":\"ag20220607094906Ge66a7\",\"businessCode\":\"XJ1654566243885\"},{\"agentCode\":\"ag20220608100025Ft1Mcb\",\"businessCode\":\"XJ1654653377445\"},{\"agentCode\":\"ag20220620095021rldPh8\",\"businessCode\":\"XJ1655689585490\"},{\"agentCode\":\"ag20220621093526liohxl\",\"businessCode\":\"XJ1655775173410\"},{\"agentCode\":\"ag20220630150145Nwyy9r\",\"businessCode\":\"XJ1656572208316\"},{\"agentCode\":\"ag20220704092908brJgK6\",\"businessCode\":\"XJ1656898038456\"},{\"agentCode\":\"ag202207040935376sxi8L\",\"businessCode\":\"XJ1656898308711\"},{\"agentCode\":\"ag202207221142474nw9GC\",\"businessCode\":\"XJ1681987574422\"},{\"agentCode\":\"ag20220811165700t5obF8\",\"businessCode\":\"XJ1660207967212\"},{\"agentCode\":\"ag20220826181227OdQIoB\",\"businessCode\":\"XJ1661508758756\"},{\"agentCode\":\"ag20220901161421F3b2v6\",\"businessCode\":\"XJ1681987610125\"},{\"agentCode\":\"ag20220901164810JkQDPg\",\"businessCode\":\"XJ1681987653748\"},{\"agentCode\":\"ag2022090611361145bFrP\",\"businessCode\":\"XJ1681987706752\"},{\"agentCode\":\"ag20220916110825EsuwOJ\",\"businessCode\":\"XJ1663296452406\"},{\"agentCode\":\"ag20220919164838xCEGvO\",\"businessCode\":\"XJ1681987783882\"},{\"agentCode\":\"ag20220921103031rCGicn\",\"businessCode\":\"XJ1663727128647\"},{\"agentCode\":\"ag20220921165638PsLMKi\",\"businessCode\":\"XJ1681987860323\"},{\"agentCode\":\"ag20220922161913EGrw6l\",\"businessCode\":\"XJ1681989459426\"},{\"agentCode\":\"ag2022092216302190kfBb\",\"businessCode\":\"XJ1681989500739\"},{\"agentCode\":\"ag20220927105300M4buMb\",\"businessCode\":\"XJ1664246819268\"},{\"agentCode\":\"ag20220929161708fPG9BH\",\"businessCode\":\"XJ1681989539846\"},{\"agentCode\":\"ag20221012112607Jui5tp\",\"businessCode\":\"XJ1665544949580\"},{\"agentCode\":\"ag20221012160939q1Bcup\",\"businessCode\":\"XJ1665561995910\"},{\"agentCode\":\"ag20221020150811e3nbkk\",\"businessCode\":\"XJ1666249516256\"},{\"agentCode\":\"ag20221025094201moGF1p\",\"businessCode\":\"XJ1666661949424\"},{\"agentCode\":\"ag202210261741143E3q8x\",\"businessCode\":\"XJ1666776625638\"},{\"agentCode\":\"ag20221026175830aONNGA\",\"businessCode\":\"XJ1666778215645\"},{\"agentCode\":\"ag20221026181834v8F3Ah\",\"businessCode\":\"XJ1666779043461\"},{\"agentCode\":\"ag20221026183145uMnnrc\",\"businessCode\":\"XJ1666779900751\"},{\"agentCode\":\"ag20221027155631fml6Ka\",\"businessCode\":\"XJ1666857316856\"},{\"agentCode\":\"ag20221125154000cp4D9l\",\"businessCode\":\"XJ1681989606603\"},{\"agentCode\":\"ag20221212090317Peo4u0\",\"businessCode\":\"XJ1670806908150\"},{\"agentCode\":\"ag202212120909405lNJPr\",\"businessCode\":\"XJ1670807665990\"},{\"agentCode\":\"ag20221215161442oED01z\",\"businessCode\":\"XJ1671093173464\"},{\"agentCode\":\"ag20221215161520hEiO9P\",\"businessCode\":\"XJ1671094088137\"},{\"agentCode\":\"ag20221215161537os9geq\",\"businessCode\":\"XJ1671093531058\"},{\"agentCode\":\"ag20230118134711jv6G5B\",\"businessCode\":\"XJ1674020757160\"},{\"agentCode\":\"ag202301190930380jQj7i\",\"businessCode\":\"XJ1674091753458\"},{\"agentCode\":\"ag20230214161624MzIiQk\",\"businessCode\":\"XJ1676364200876\"},{\"agentCode\":\"ag20230217112337dCsLx8\",\"businessCode\":\"XJ1676604090731\"},{\"agentCode\":\"ag20230222104733l5cwkD\",\"businessCode\":\"XJ1677047603036\"},{\"agentCode\":\"ag20230301185611qezCtx\",\"businessCode\":\"XJ1677739108966\"},{\"agentCode\":\"ag20230302144607w34HNk\",\"businessCode\":\"XJ1677740800615\"},{\"agentCode\":\"ag202303011156444cIbwp\",\"businessCode\":\"XJ1677735805190\"},{\"agentCode\":\"ag202303031340359mNL3B\",\"businessCode\":\"XJ1677832322531\"},{\"agentCode\":\"ag20230314171104hP3GDE\",\"businessCode\":\"XJ1678842367117\"},{\"agentCode\":\"ag20230411155401PjLnvP\",\"businessCode\":\"XJ1681206368540\"},{\"agentCode\":\"ag20230427150719MhMKEz\",\"businessCode\":\"XJ1682586320925\"},{\"agentCode\":\"ag202305111453040MaAKo\",\"businessCode\":\"XJ1684112905618\"},{\"agentCode\":\"ag20230516175830gfbO2e\",\"businessCode\":\"XJ1684296029181\"},{\"agentCode\":\"ag202305171516433r7Gwj\",\"businessCode\":\"XJ1684910776094\"},{\"agentCode\":\"ag20230531095614CsCtnv\",\"businessCode\":\"XJ1685502655337\"},{\"agentCode\":\"ag20230602140155ajr8PO\",\"businessCode\":\"XJ1685687563849\"},{\"agentCode\":\"ag20230608233608QCnvzD\",\"businessCode\":\"XJ1686290395456\"},{\"agentCode\":\"ag20230609174952EsvKJ0\",\"businessCode\":\"XJ1686535699841\"},{\"agentCode\":\"ag20230611120619dLosbL\",\"businessCode\":\"XJ1686619200277\"},{\"agentCode\":\"ag20230611064603BuyOqh\",\"businessCode\":\"XJ1686721622788\"},{\"agentCode\":\"ag20230612124502AJgIfQ\",\"businessCode\":\"XJ1686879557600\"},{\"agentCode\":\"ag20230621092145Nqs6pL\",\"businessCode\":\"XJ1687328493093\"},{\"agentCode\":\"ag20230620104215GyEzvD\",\"businessCode\":\"XJ1687239062516\"},{\"agentCode\":\"ag20230628172827JpAzQg\",\"businessCode\":\"XJ1687947734737\"},{\"agentCode\":\"ag20230628164108oJMGAy\",\"businessCode\":\"XJ1688091957382\"},{\"agentCode\":\"ag20230703164230jPniKn\",\"businessCode\":\"XJ1688433892687\"}]";
        String json = "[{\"agentCode\":\"ag20210924184250CGyu33\",\"businessCode\":\"XJ1000010100004\"},{\"agentCode\":\"ag20220224104504ys3qcA\",\"businessCode\":\"XJ1000010100012\"},{\"agentCode\":\"ag20221103172107d2Nt2M\",\"businessCode\":\"XJ1000010100036\"},{\"agentCode\":\"ag202304121047126eLKAC\",\"businessCode\":\"XJ1000012100002\"}]";
        Map<String, String> userMap = JSONUtil.parseArray(json)
                .stream().map(JSONUtil::parseObj)
                .collect(Collectors.toMap(k -> k.get("businessCode").toString(), v -> v.get("agentCode").toString(),
                        (k1, k2) -> k1));

        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/团队.xlsx");
        List<List<Object>> readAll = reader.read();
        StringBuffer buffer = new StringBuffer("INSERT INTO `bi_group_agent_entrust_policy` (`agent_code`, `agent_name`, `business_code`, `group_name`, `create_user`, `create_time`, `update_user`, `update_time`, `revision`) VALUES ");
        readAll.forEach(list -> {
            String businessCode = list.get(0).toString();
            String agentName = list.get(1).toString();
            String groupName = list.get(2).toString();
            String agentCode = userMap.get(businessCode);
            if (StrUtil.isBlank(agentCode)) {
                log.info("businessCode={}", businessCode);
                return;
            }
            buffer.append("('").append(agentCode).append("','")
                    .append(agentName).append("','")
                    .append(businessCode).append("','")
                    .append(groupName).append("','SYSTEM','2023-07-18 03:00:00','SYSTEM','2023-07-18 03:00:00',1),");
        });
        System.out.println(buffer);

    }
}
