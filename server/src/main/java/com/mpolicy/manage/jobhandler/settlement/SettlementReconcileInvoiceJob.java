package com.mpolicy.manage.jobhandler.settlement;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.enums.ReconcileInvoiceApplyStatusEnum;
import com.mpolicy.manage.enums.ReconcileInvoicingFinanceStatusEnum;
import com.mpolicy.manage.enums.ReconcileInvoicingSystemStatusEnum;
import com.mpolicy.manage.feign.client.DingTalkMessageClient;
import com.mpolicy.manage.feign.model.CommonResult;
import com.mpolicy.manage.feign.model.MsgPush2User;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInfoDingTalkEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInfoDingTalkService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceService;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SettlementReconcileInvoiceJob {

    @Autowired
    private SettlementReconcileInvoiceService settlementReconcileInvoiceService;


    @Autowired
    private SettlementReconcileInfoDingTalkService settlementReconcileInfoDingTalkService;

    @Autowired
    private DingTalkMessageClient dingTalkMessageClient;

    /**
     * 处理延期开票
     * 定时任务一天一次
     */
    @XxlJob("handleDelayedInvoicing")
    public void handleDelayedInvoicing() {
        XxlJobHelper.log("处理延期开票");
        // 财务审核通过 并且未开票的
        List<SettlementReconcileInvoiceEntity> dbList = settlementReconcileInvoiceService.lambdaQuery()
                .eq(SettlementReconcileInvoiceEntity::getInvoicingFinanceStatus, ReconcileInvoicingFinanceStatusEnum.AUDIT_PASS.getCode())
                .eq(SettlementReconcileInvoiceEntity::getInvoicingSystemStatus, ReconcileInvoicingSystemStatusEnum.WAIT_INVOICE.getCode())
                .list();
        dbList.forEach(action -> {
            DateTime endTime = DateUtil.endOfDay(new Date());
            // 判断开票时间和当前时间是否一致 同一天 如果是同一天
            if (StatusEnum.NORMAL.getCode().equals(action.getInvoicingTimeAsk())
                    && DateUtil.compare(action.getInvoicingTime(), endTime) < 1) {
                settlementReconcileInvoiceService.invoiceToFttm(action.getInvoiceCode());
            }
        });
        XxlJobHelper.handleSuccess("处理延期开票完成");
    }

    /**
     * 申请财务审核提醒(2小时未审核)
     * 定时任务3分钟一次
     */
    @XxlJob("handleApplyFinanceExamineTips")
    public void handleApplyFinanceExamineTips() {
        DateTime towHour = DateUtil.date().offset(DateField.HOUR, -2);
        DateTime fiveHour = DateUtil.offsetHour(towHour, -3);
        // 获取2个小时前的时间
        XxlJobHelper.log("申请财务审核提醒(2小时未审核)");
        // 判断当前状态是否为财务审核状态  并且 财务未审核
        List<SettlementReconcileInvoiceEntity> settlementReconcileInvoiceList = settlementReconcileInvoiceService.lambdaQuery()
                .eq(SettlementReconcileInvoiceEntity::getInvoicingFinanceStatus, ReconcileInvoicingFinanceStatusEnum.WAIT_AUDIT.getCode())
                .eq(SettlementReconcileInvoiceEntity::getApplyStatus, ReconcileInvoiceApplyStatusEnum.WAIT_FINANCE_AUDIT.getCode())
                .ge(SettlementReconcileInvoiceEntity::getApplyTime, fiveHour)
                .le(SettlementReconcileInvoiceEntity::getApplyTime, towHour)
                .list();
        if (CollUtil.isNotEmpty(settlementReconcileInvoiceList)) {
            List<String> invoiceCodeList = settlementReconcileInvoiceList.stream().map(SettlementReconcileInvoiceEntity::getInvoiceCode).collect(Collectors.toList());
            // 判断2小时上没有进行财务审核 钉钉推送次数小于2次的数据
            Map<String, Long> settlementReconcileInfoDingTalkMap = settlementReconcileInfoDingTalkService.lambdaQuery()
                    .eq(SettlementReconcileInfoDingTalkEntity::getEventCode, 1)
                    .in(SettlementReconcileInfoDingTalkEntity::getInvoiceCode, invoiceCodeList)
                    .list().stream().collect(Collectors.groupingBy(SettlementReconcileInfoDingTalkEntity::getInvoiceCode, Collectors.counting()));
            String title = "【开票审核通知】";
            List<SettlementReconcileInfoDingTalkEntity> settlementReconcileInfoDingTalkList = new ArrayList<>();
            settlementReconcileInvoiceList.forEach(action -> {
                if (!settlementReconcileInfoDingTalkMap.containsKey(action.getInvoiceCode()) || settlementReconcileInfoDingTalkMap.get(action.getInvoiceCode()).intValue() > 1) {
                    log.warn("推送次数大于2次，跳过推送，推送记录：{}", action);
                    return;
                }
                String content = StrUtil.format("您有新的发票申请需要审核，请及时处理。发票抬头{}，申请开票金额{}元。", action.getInvoiceTitle(), action.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP));
                // 财务人员
                String jobNumberConfig = ConstantCacheHelper.getValue(Constant.FINANCIAL_STAFF_JOB_NUMBERS, null);
                if (StrUtil.isNotBlank(jobNumberConfig)) {
                    List<String> jobNumbers = StrUtil.split(jobNumberConfig, '#');
                    MsgPush2User msgPush2User = new MsgPush2User();
                    msgPush2User.setContent(title + "\n" + content);
                    msgPush2User.setMsgType("markdown");
                    msgPush2User.setJobNumbers(jobNumbers);
                    msgPush2User.setTitle(title);
                    CommonResult<String> stringCommonResult = dingTalkMessageClient.sendMsgPush2User(msgPush2User);
                    //保存发送记录
                    SettlementReconcileInfoDingTalkEntity settlementReconcileInfoDingTalk = new SettlementReconcileInfoDingTalkEntity();
                    settlementReconcileInfoDingTalk.setSendMsg(JSONUtil.toJsonStr(msgPush2User));
                    settlementReconcileInfoDingTalk.setSendResult(JSONUtil.toJsonStr(stringCommonResult));
                    settlementReconcileInfoDingTalk.setEventCode(1);
                    settlementReconcileInfoDingTalk.setInvoiceCode(action.getInvoiceCode());
                    settlementReconcileInfoDingTalk.setSendMsgType("markdown");
                    settlementReconcileInfoDingTalk.setJobNumbers(JSONUtil.toJsonStr(jobNumbers));
                    settlementReconcileInfoDingTalkList.add(settlementReconcileInfoDingTalk);

                }
            });
            // 批量插入记录
            if (CollUtil.isNotEmpty(settlementReconcileInfoDingTalkList)) {
                settlementReconcileInfoDingTalkService.saveBatch(settlementReconcileInfoDingTalkList);
            }
        }
        XxlJobHelper.handleSuccess("申请财务审核提醒(2小时未审核)完成");
    }
}
