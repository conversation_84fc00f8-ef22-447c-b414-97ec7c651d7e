package com.mpolicy.manage.jobhandler.policy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.modules.policy.entity.*;
import com.mpolicy.manage.modules.policy.service.*;
import com.mpolicy.policy.common.enums.TrustStatusEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 保单中心定时任务
 */
@Component
@Slf4j
public class PolicyHandler {
    private static final Logger logger = LoggerFactory.getLogger(PolicyHandler.class);

    @Autowired
    private EpPolicyContractInfoService epPolicyContractInfoService;
    @Autowired
    private PolicyUserRecordService policyUserRecordService;
    @Autowired
    private PolicyManualEntryService policyManualEntryService;
    @Autowired
    private PolicyBaseInsuredService policyBaseInsuredService;
    @Autowired
    private BiOfflineSignInfoService biOfflineSignInfoService;

    /**
     * 托管保单状态修改
     */
    @XxlJob("policyUserRecordStatusHandler")
    public void policyUserRecordStatusHandler() {
        try {
            IPage<PolicyUserRecordEntity> page;
            int i = 0;
            do {
                // 分页获取托管信息
                page = policyUserRecordService.page(new Page<>(++i, 500, false), Wrappers.lambdaQuery());
                List<PolicyUserRecordEntity> list = page.getRecords();
                Set<String> contractCodeSet = list.stream().map(PolicyUserRecordEntity::getContractCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                if (contractCodeSet.isEmpty()) {
                    continue;
                }

                Map<String, EpPolicyContractInfoEntity> contractMap = epPolicyContractInfoService.list(
                        Wrappers.<EpPolicyContractInfoEntity>lambdaQuery()
                                .in(EpPolicyContractInfoEntity::getContractCode, contractCodeSet)
                ).stream().collect(Collectors.toMap(EpPolicyContractInfoEntity::getContractCode, x -> x, (x, y) -> x));

                list.stream().filter(x -> contractMap.get(x.getContractCode()) != null)
                        .filter(x -> {
                            String contractCode = x.getContractCode();
                            String policyStatus = x.getPolicyStatus();
                            EpPolicyContractInfoEntity policyContractInfoEntity = contractMap.get(contractCode);
                            return !policyContractInfoEntity.getPolicyStatus().equals(policyStatus);
                        }).forEach(x -> {
                            EpPolicyContractInfoEntity policyContractInfoEntity = contractMap.get(x.getContractCode());
                            policyUserRecordService.update(
                                    Wrappers.<PolicyUserRecordEntity>lambdaUpdate()
                                            .set(PolicyUserRecordEntity::getPolicyStatus, policyContractInfoEntity.getPolicyStatus())
                                            .set(PolicyUserRecordEntity::getUpdateTime, new Date())
                                            .eq(PolicyUserRecordEntity::getId, x.getId())
                            );
                        });
            } while (page.getRecords().size() > 0);
            XxlJobHelper.handleSuccess("托管保单状态修改");
        } catch (Exception e) {
            XxlJobHelper.log("托管保单状态修改发生异常");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 手工托管保单状态修改
     */
    @XxlJob("policyManualStatusHandler")
    public void policyManualStatusHandler() {
        try {
            IPage<PolicyManualEntryEntity> page;
            int i = 0;
            do {
                // 分页获取托管信息
                page = policyManualEntryService.page(new Page<>(++i, 500, false), Wrappers.lambdaQuery());
                List<PolicyManualEntryEntity> list = page.getRecords();
                Set<String> contractCodeSet = list.stream().map(PolicyManualEntryEntity::getContractCode)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                if (contractCodeSet.isEmpty()) {
                    continue;
                }

                Map<String, EpPolicyContractInfoEntity> contractMap = epPolicyContractInfoService.list(
                        Wrappers.<EpPolicyContractInfoEntity>lambdaQuery()
                                .in(EpPolicyContractInfoEntity::getContractCode, contractCodeSet)
                ).stream().collect(Collectors.toMap(EpPolicyContractInfoEntity::getContractCode, x -> x, (x, y) -> x));

                list.stream().filter(x -> contractMap.get(x.getContractCode()) != null)
                        .filter(x -> {
                            EpPolicyContractInfoEntity policyContractInfoEntity = contractMap.get(x.getContractCode());
                            return !policyContractInfoEntity.getPolicyStatus().equals(x.getPolicyStatus());
                        }).forEach(x -> {
                            EpPolicyContractInfoEntity policyContractInfoEntity = contractMap.get(x.getContractCode());
                            policyManualEntryService.update(
                                    Wrappers.<PolicyManualEntryEntity>lambdaUpdate()
                                            .set(PolicyManualEntryEntity::getPolicyStatus, policyContractInfoEntity.getPolicyStatus())
                                            .set(PolicyManualEntryEntity::getUpdateTime, new Date())
                                            .eq(PolicyManualEntryEntity::getId, x.getId())
                            );
                        });
            } while (page.getRecords().size() > 0);
            XxlJobHelper.handleSuccess("手工托管保单状态修改");
        } catch (Exception e) {
            XxlJobHelper.log("手工托管保单状态修改发生异常");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 修改所有保单的险种定义状态
     */
    @XxlJob("refreshProductDutyDefineStatus")
    public void refreshProductDutyDefineStatus() {
        try {
            IPage<PolicyUserRecordEntity> page;
            int i = 0;
            do {
                // 获取险种定义状态为未定义的托管记录
                page = policyUserRecordService.page(new Page<>(++i, 500, false),
                        Wrappers.<PolicyUserRecordEntity>lambdaQuery()
                                .eq(PolicyUserRecordEntity::getRecordStatus, TrustStatusEnum.PRODUCT_UNDEFINED.getTrustType())
                                .or()
                                .eq(PolicyUserRecordEntity::getRecordStatus, TrustStatusEnum.POLICY_SAVING.getTrustType())
                );
                List<PolicyUserRecordEntity> list = page.getRecords();
                if (list.isEmpty()) {
                    break;
                }

                // 责任进行过定义的拆单列表
                List<PolicyBaseInsuredEntity> updateList = policyBaseInsuredService.list(
                        Wrappers.<PolicyBaseInsuredEntity>lambdaQuery()
                                .eq(PolicyBaseInsuredEntity::getIsDefinition, StatusEnum.NORMAL.getCode())
                                .in(PolicyBaseInsuredEntity::getContractCode, list.stream().map(PolicyUserRecordEntity::getContractCode).collect(Collectors.toSet()))
                );

                if (updateList.isEmpty()) {
                    break;
                }

                // 更新进行过责任定义的托管记录
                policyUserRecordService.update(
                        Wrappers.<PolicyUserRecordEntity>lambdaUpdate()
                                .set(PolicyUserRecordEntity::getRecordStatus, TrustStatusEnum.PRODUCT_DEFINED.getTrustType())
                                .in(PolicyUserRecordEntity::getContractCode, updateList.stream().map(PolicyBaseInsuredEntity::getContractCode).collect(Collectors.toSet()))
                );
            } while (page.getRecords().size() > 0);

            XxlJobHelper.handleSuccess("修改托管记录的险种定义状态");
        } catch (Exception e) {
            XxlJobHelper.log("险种定义状态修改发生异常");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 线下单签署同步
     */
    @XxlJob("biOfflineSignPolicy")
    public void biOfflineSignPolicy() {
        try {
            //获取最新坐标
            int id = 0;
            BiOfflineSignInfoEntity one = biOfflineSignInfoService.lambdaQuery().orderByDesc(BiOfflineSignInfoEntity::getPolicyId).last("limit 1").one();
            if (one != null) {
                id = one.getPolicyId();
            }
            IPage<EpPolicyContractInfoEntity> page;
            ArrayList<BiOfflineSignInfoEntity> list = Lists.newArrayList();
            int i = 0;
            do {
                list.clear();
                // 分页获取线下单信息
                page = epPolicyContractInfoService.page(
                        new Page<>(++i, 500, false), new LambdaQueryWrapper<EpPolicyContractInfoEntity>()
                                .eq(EpPolicyContractInfoEntity::getSalesType, 1)
                                .gt(EpPolicyContractInfoEntity::getId, id)
                                .orderByAsc(EpPolicyContractInfoEntity::getId)
                );
                page.getRecords().forEach(x -> {
                    BiOfflineSignInfoEntity entity = new BiOfflineSignInfoEntity();
                    BeanUtils.copyProperties(x, entity);
                    entity.setId(null);
                    entity.setPolicyId(x.getId());
                    list.add(entity);
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    biOfflineSignInfoService.saveBatch(list);
                }
            } while (page.getRecords().size() > 0);
            XxlJobHelper.handleSuccess("线下单签署同步完成");
        } catch (Exception e) {
            XxlJobHelper.log("线下单签署同步发生异常");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }
    }
}
