package com.mpolicy.manage.jobhandler.regulators;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.modules.regulators.common.RegulatorsConstant;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.service.RegulatorsMonthReportService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportInfoService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportOrgService;
import com.mpolicy.manage.modules.regulators.vo.CreateRegulatorsMonthReport;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * 监管报备-定时任务
 *
 * <AUTHOR>
 * @date 2022-01-20 13:14:35
 */
@Component
@Slf4j
public class RegulatorsJob {

    @Autowired
    private RegulatorsReportInfoService regulatorsReportInfoService;

    @Autowired
    private RegulatorsReportOrgService regulatorsReportOrgService;

    @Autowired
    private RegulatorsMonthReportService regulatorsMonthReportService;

    /**
     * 监管月度报告-初始化
     * 每个月1日：0点10分执行
     */
    @XxlJob("monthInitReportJob")
    public void monthInitReportJob(String param) throws Exception {
        log.info("监管月度报告-初始化 start date= {}", DateUtil.now());
        RegulatorsReportInfoEntity bean = new RegulatorsReportInfoEntity();
        DateTime date = DateUtil.date();
        date = DateUtil.offsetMonth(date, -1);
        int year = DateUtil.year(date);
        int month = DateUtil.month(date);
        // 月份从0开始
        if (month == 0) {
            month = 1;
        } else {
            month = month + 1;
        }
        // 判断是否已经初始化过月度数据
        RegulatorsReportInfoEntity reportInfo = Optional.ofNullable(
                regulatorsReportInfoService.lambdaQuery()
                        .eq(RegulatorsReportInfoEntity::getRegulatorsYear, year)
                        .eq(RegulatorsReportInfoEntity::getRegulatorsMonth, month)
                        .one()
        ).orElse(null);
        if (reportInfo == null) {
            bean.setRegulatorsNo(CommonUtils.createCodeLastNumber("REG"));
            bean.setRegulatorsName(StrUtil.format(RegulatorsConstant.REGULATORS_MONTH_DESC, year, month));
            bean.setReportStatus(0);
            bean.setUploadCompleteStatus(0);
            bean.setRegulatorsOrgCount(0);
            bean.setRegulatorsYear(year);
            bean.setRegulatorsMonth(month);
            regulatorsReportInfoService.save(bean);
        } else {
            log.info("监管月度报告-初始化,数据研究存在，year={}, month={}", year, month);
        }
        XxlJobHelper.handleSuccess("监管月度报告-初始化处理任务完成");
    }

    /**
     * 监管月度报告-变更为报备完成
     * 每个月15日：0点10分执行
     */
    @XxlJob("monthReportFinishJob")
    public void monthReportFinishJob(String param) throws Exception {
        log.info("监管月度报告-变更为报备完成 start date= {}", DateUtil.now());
        DateTime date = DateUtil.date();
        // 偏移1月，获取上个月的月份 + 年份
        date = DateUtil.offsetMonth(date, -1);
        int year = DateUtil.year(date);
        int month = DateUtil.month(date);
        // 月份从0开始
        if (month == 0) {
            month = 1;
        } else {
            month = month + 1;
        }

        RegulatorsReportInfoEntity reportInfo = Optional.ofNullable(
                regulatorsReportInfoService.lambdaQuery()
                        .eq(RegulatorsReportInfoEntity::getRegulatorsYear, year)
                        .eq(RegulatorsReportInfoEntity::getRegulatorsMonth, month)
                        .one()
        ).orElse(null);

        if (reportInfo != null) {
            // 执行更新为已报备
            regulatorsReportInfoService.lambdaUpdate().set(RegulatorsReportInfoEntity::getReportStatus, 1)
                    .eq(RegulatorsReportInfoEntity::getRegulatorsNo, reportInfo.getRegulatorsNo()).update();

            // 更新报告月度为已报备
            regulatorsReportOrgService.lambdaUpdate().set(RegulatorsReportOrgEntity::getReportStatus, 1)
                    .eq(RegulatorsReportOrgEntity::getRegulatorsNo, reportInfo.getRegulatorsNo()).update();
            XxlJobHelper.handleSuccess("监管月度报告-变更为报备完成处理任务完成");
        } else {
            log.warn("未读取到月度报备数据，year={}, month={}", year, month);
        }
    }


    @XxlJob("createRegulatorsMonthReport")
    public void createRegulatorsMonthReport() {
        //  获取上个月最后一天时间
        DateTime endOfMonth = DateUtil.endOfMonth(DateUtil.offsetMonth(new Date(), -1));
        CreateRegulatorsMonthReport input = new CreateRegulatorsMonthReport();
        input.setBeginMonth(DateUtil.parse("2024-01-01 00:00:00"));
        input.setEndMonth(endOfMonth);
        regulatorsMonthReportService.createRegulatorsMonthReport(input);
    }
}
