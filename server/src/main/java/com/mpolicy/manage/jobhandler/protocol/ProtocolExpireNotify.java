package com.mpolicy.manage.jobhandler.protocol;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.manage.modules.protocol.service.ProtocolBasicInfoService;
import com.mpolicy.manage.modules.protocol.vo.ProtocolBasicInfoListInput;
import com.mpolicy.manage.modules.protocol.vo.ProtocolBasicInfoListOut;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/7
 * @Version 1.0
 */
@Component
@Slf4j
public class ProtocolExpireNotify {

    @Autowired
    private ProtocolBasicInfoService protocolBasicInfoService;

    @Value("${dingTalkRobot.protocolExpireNotify.token}")
    private String token;

    @Value("${dingTalkRobot.protocolExpireNotify.secret}")
    private String secret;

    @Value("${dingTalkRobot.protocolExpireNotify.at}")
    private Set<String> at;

    @Value("${dingTalkRobot.protocolExpireNotify.domain}")
    private String dingTalkDomain;

    @Value("${dingTalkRobot.protocolExpireNotify.redirectUrl}")
    private String redirectUrl;


    @XxlJob("protocolExpireNotifyJob")
    public void protocolExpireDelayJob(String param) throws Exception {


        try{
            List<ProtocolBasicInfoListOut> expireList = protocolBasicInfoService.findBeExpireList(new ProtocolBasicInfoListInput());
            if (CollectionUtil.isEmpty(expireList)) {
                log.info("近30天无到期协议提醒");
                return;
            }

            JSONObject requestBody = constructNotify(expireList.size());

            sendNotify(requestBody);

        } catch(Exception e) {
            log.warn("protocolExpireNotify exception", e);
            XxlJobHelper.handleFail("protocolExpireNotify exception");
        } finally {
            XxlJobHelper.handleSuccess("协议到期通知完成");
        }

    }

    public JSONObject constructNotify(int num) {


        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("msgtype", "link");
//        jsonObject.put(
//                "link"
//                , new JSONObject().fluentPut("text", "近30天内5份协议即将到期，请尽快处理！")
//                        .fluentPut("title", "协议到期告警")
//                        .fluentPut("messageUrl", "https://manage-g.xiaowhale.com/#/product-protocolManage")
//
//        );
        String text = " {} \n**近30天内{}份协议即将到期，请尽快处理**\n" +
                "\n" +
                "------\n" +
                "\n" +
                "[查看详情]({})";
//        "@15700719141"
        if (CollectionUtil.isNotEmpty(at)) {
            text = StrUtil.format(text, at.stream().map(x -> "@" + x).collect(Collectors.joining(" ")), num, redirectUrl);
            jsonObject.put(
                    "at"
                    , new JSONObject().fluentPut("atMobiles", new JSONArray(new ArrayList<>(at))).fluentPut("isAtAll", false)
            );
        } else {
            text = StrUtil.format(text, "", num);
        }

        jsonObject.put("msgtype", "markdown");
        jsonObject.put(
                "markdown", new JSONObject().fluentPut("title", "协议到期告警")
                        .fluentPut("text", text)
        );

        return jsonObject;
    }

    public void sendNotify(JSONObject jsonObject) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {

        Long timestamp = System.currentTimeMillis();

        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance(HmacAlgorithm.HmacSHA256.getValue());
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HmacAlgorithm.HmacSHA256.getValue()));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");


        System.out.println(sign);

        String url = StrUtil.format("{}/robot/send?access_token={}&timestamp={}&sign={}", dingTalkDomain, token, timestamp, sign);
        String post = HttpUtil.post(url, jsonObject.toJSONString());
        log.info("发送结果：{}", post);
    }


}
