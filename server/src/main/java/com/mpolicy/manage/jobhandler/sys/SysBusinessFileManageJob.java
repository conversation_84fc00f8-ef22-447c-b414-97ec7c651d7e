package com.mpolicy.manage.jobhandler.sys;

import com.mpolicy.manage.modules.sys.entity.SystemBusinessFileManageEntity;
import com.mpolicy.manage.modules.sys.fileManage.enums.FileManageProjectEnum;
import com.mpolicy.manage.modules.sys.fileManage.factory.SysBusinessFileManageFactory;
import com.mpolicy.manage.modules.sys.fileManage.handler.SystemBusinessFileManageHandler;
import com.mpolicy.manage.modules.sys.service.SystemBusinessFileManageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * XxlJob开发示例（Bean模式）
 *
 * 开发步骤：
 *      1、任务开发：在Spring Bean实例中，开发Job方法；
 *      2、注解配置：为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
 *      3、执行日志：需要通过 "XxlJobHelper.log" 打印执行日志；
 *      4、任务结果：默认任务结果为 "成功" 状态，不需要主动设置；如有诉求，比如设置任务结果为失败，可以通过 "XxlJobHelper.handleFail/handleSuccess" 自主设置任务结果；
 *
 * <AUTHOR> 2024-02-21 17:52:51
 */
@Component
@Slf4j
public class SysBusinessFileManageJob {

    @Autowired
    private SystemBusinessFileManageService systemBusinessFileManageService;

    /**
     * 通用导入导出JOB
     *
     * <AUTHOR>
     * @since 2024/2/21
     */
    @XxlJob("businessFileManageJob")
    public void businessFileManageJob() throws Exception {
        // 1 获取需要处理的通用文件业务纪录
        List<SystemBusinessFileManageEntity> fileManageList = systemBusinessFileManageService.lambdaQuery()
                .eq(SystemBusinessFileManageEntity::getFileManageStatus, 0).list();
        log.info("共处理{}个通用文件业务记录", fileManageList.size());

        // 2 循环执行处理
        for(SystemBusinessFileManageEntity bean : fileManageList){
            // 处理枚举
            FileManageProjectEnum projectEnum = FileManageProjectEnum.matchSearchCode(bean.getFileManageProjectCode());
            SystemBusinessFileManageHandler fileManageHandler = SysBusinessFileManageFactory.getFileManageHandler(projectEnum);
            fileManageHandler.handler(bean);
        }
        // default success
        XxlJobHelper.handleSuccess("customer任务执行完成了");
    }
}
