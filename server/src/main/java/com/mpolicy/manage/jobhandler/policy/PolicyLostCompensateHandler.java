package com.mpolicy.manage.jobhandler.policy;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mpolicy.common.mail.IMailService;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.constant.SlsQueryConstant;
import com.mpolicy.manage.enums.PolicyActivityTypeEnum;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.SlsQueryLogService;
import com.mpolicy.manage.modules.agent.vo.SlsQueryLogInputVo;
import com.mpolicy.manage.modules.agent.vo.SlsQueryLogOutputVo;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.customer.vo.CustomerBasicInfoVo;
import com.mpolicy.manage.modules.policy.entity.lost.EpPolicyLostEmailEntity;
import com.mpolicy.manage.modules.policy.entity.lost.EpPolicyLostRegisterEntity;
import com.mpolicy.manage.modules.policy.enums.lost.HandleResultEnum;
import com.mpolicy.manage.modules.policy.enums.lost.HandleStatusEnum;
import com.mpolicy.manage.modules.policy.service.EpPolicyLostEmailService;
import com.mpolicy.manage.modules.policy.service.EpPolicyLostRegisterService;
import com.mpolicy.manage.modules.policy.service.PolicyContractInfoService;
import com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterExportVo;
import com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterList;
import com.mpolicy.manage.utils.LocalDateUtil;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInfoVo;
import com.mpolicy.policy.common.ep.policy.referrer.ApplyPolicyReferrerReqVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 丢单补偿定时任务
 *
 * <AUTHOR>
 * @date 2022-03-21 18:23
 */
@Component
@Slf4j
public class PolicyLostCompensateHandler {


    @Autowired
    private EpPolicyLostRegisterService epPolicyLostRegisterService;

    @Autowired
    private EpPolicyClient epPolicyClient;

    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;

    @Autowired
    private CustomerBasicInfoService customerBasicInfoService;

    @Autowired
    private EpPolicyClient policyClient;

    @Autowired
    private EpPolicyLostEmailService epPolicyLostEmailService;

    /**
     * 注入邮件服务
     */
    @Autowired
    private IMailService mailService;

    @Autowired
    private SlsQueryLogService slsQueryLogService;

    /**
     * 保单保全状态
     */
    @XxlJob("policyLostCompensateJob")
    public void policyLostCompensateJob() {

        //1、获取前一个月的日期
        LocalDate currentDate = LocalDate.now();
        Date date = LocalDateUtil.localDateTimeToUdate(currentDate.minusMonths(1).atStartOfDay());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = sdf.format(date);
        //2、获取需要补偿的丢单登记记录（状态为待处理或处理失败的，近一个月记录）
        List<EpPolicyLostRegisterEntity> list = epPolicyLostRegisterService.lambdaQuery()
                .ge(EpPolicyLostRegisterEntity::getRegisterTime,date)
                .and(a -> a.eq(EpPolicyLostRegisterEntity::getHandleStatus, 0)
                        .or()
                        .eq(EpPolicyLostRegisterEntity::getHandleStatus, 2)
                        .in(EpPolicyLostRegisterEntity::getHandleResult,5,8,14))
                .list();

        XxlJobHelper.log("丢单补偿记录=：{}", list.size());

        if (!CollectionUtils.isEmpty(list)){
            //保单推荐人实名认证客户列表
            List<CustomerBasicInfoEntity> customerBasicInfoEntityList = customerBasicInfoService.lambdaQuery()
                    .inSql(CustomerBasicInfoEntity::getInnerReferrerCode,"select t2.referrer_code from ep_policy_lost_register t1\n" +
                            "left join channel_application_referrer t2 on t1.recommend_id = t2.referrer_wno " +
                            "where register_time >= '"+dateString+"' and (handle_status = 0 or (handle_status = 2 and handle_result in (5,8,14))) and t2.referrer_code is not null")
                    .eq(CustomerBasicInfoEntity::getInnerReferrerFlag,1)
                    .list();
            log.info("保单推荐人实名认证客户列表:{},日期{}",JSONObject.toJSONString(customerBasicInfoEntityList),date.toLocaleString());
            Map<String,CustomerBasicInfoEntity> customerBasicInfoEntityMap = customerBasicInfoEntityList.stream()
                    .collect(Collectors.toMap(CustomerBasicInfoEntity::getInnerReferrerCode, x -> x, (x, y) -> x));

            // 2 遍历处理
            list.forEach(x -> {
                //2.1 补全保单推荐人信息
                applyPolicyReferrer(x,customerBasicInfoEntityMap);
                try {
                    //丢单分析添加失败原因标签 该处理不影响业务, 所以不抛异常
                    analysisLostPolicy(x);
                }catch(Exception e){
                    log.warn("analysisLostPolicy error",e);
                }
            });

            //3、修改丢单登记记录的处理状态
            epPolicyLostRegisterService.updateBatchById(list);
        }

        XxlJobHelper.handleSuccess("丢单补偿任务完成");
    }

    /**
     * 失败订单发送邮件
     */
    @XxlJob("policyLostSendEmail")
    public void policyLostSendEmail() {

        //1、获取前7天的日期
        LocalDate currentDate = LocalDate.now();
        Date startDate = LocalDateUtil.localDateTimeToUdate(currentDate.minusDays(7).atStartOfDay());
        Date endDate = LocalDateUtil.localDateTimeToUdate(currentDate.minusDays(1).atTime(23,59,59));

        //2、获取本周丢单处理失败的记录
        List<PolicyLostRegisterList> list = epPolicyLostRegisterService.queryFailedData();
        XxlJobHelper.log("本周丢单失败记录=：{}", list.size());
        if (!CollectionUtils.isEmpty(list)) {
            String time = new DateTime(endDate.getTime()).toString("MM月dd日");
            String sevenDaysBefore = new DateTime(startDate.getTime()).toString("MM月dd日");
            File file = exportPolicy(sevenDaysBefore,time,list);
            sendEmail(sevenDaysBefore, file,time);
        }
        XxlJobHelper.handleSuccess("丢单补偿任务完成");
    }

    /**
     * 删除文件
     */
    @XxlJob("policyLostDeleteFile")
    public void policyLostDeleteFile() {
        //1、获取前7天的日期
        LocalDate currentDate = LocalDate.now();
        Date startDate = LocalDateUtil.localDateTimeToUdate(currentDate.minusDays(7).atStartOfDay());
        Date endDate = LocalDateUtil.localDateTimeToUdate(currentDate.minusDays(1).atTime(23,59,59));
        String time = new DateTime(endDate.getTime()).toString("MM月dd日");
        String sevenDaysBefore = new DateTime(startDate.getTime()).toString("MM月dd日");
        String fileName = "本周丢单失败数据("+sevenDaysBefore+"-"+time+").xlsx";
        File file = new File(StrUtil.format("logs/{}", fileName));
        //删除丢单文件
        file.delete();
    }

    /**
     * 发送邮件
     * @param sevenDaysBefore
     * @param file
     * @param time
     */
    public void sendEmail(String sevenDaysBefore,File file,String time){
        List<EpPolicyLostEmailEntity> emailEntityList = epPolicyLostEmailService.lambdaQuery()
                .ge(EpPolicyLostEmailEntity::getDeleted,0)
                .list();
        if (CollectionUtils.isEmpty(emailEntityList)){
            XxlJobHelper.log("未维护丢单失败数据发送邮箱");
            return ;
        }

        String content   = "<span style=\"mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';\n" +
                "font-size:10.5000pt;mso-font-kerning:1.0000pt;\">你好：</span><span style=\"mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';\n" +
                "font-size:10.5000pt;mso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';\n" +
                "font-size:10.5000pt;mso-font-kerning:1.0000pt;\"></span></p><p><span style=\"mso-spacerun:'yes';font-family:等线;mso-bidi-font-family:'Times New Roman';\n" +
                "font-size:10.5000pt;mso-font-kerning:1.0000pt;\">&nbsp;&nbsp;&nbsp;&nbsp;附件为丢单登记-系统处理失败记录，请查收。</span>";
        for (EpPolicyLostEmailEntity entity:emailEntityList) {
            mailService.asyncSendHtml(entity.getEmail(),
                    "丢单登记-系统处理失败记录",
                    StrUtil.format(content), file);
        }
    }

    /**
     * 生成附件
     * @param sevenDaysBefore
     * @param time
     * @param list
     * @return
     */
    public File exportPolicy(String sevenDaysBefore,String time,List<PolicyLostRegisterList> list) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        OutputStream out = null;
        String fileName = "丢单登记-系统处理失败记录.xlsx";
        try {
            List<PolicyLostRegisterExportVo> exportVos = new ArrayList<>(list.size());
            for (int i = 0;i<list.size();i++){
                PolicyLostRegisterList x= list.get(i);
                PolicyLostRegisterExportVo exportVo = new PolicyLostRegisterExportVo();
                BeanUtils.copyProperties(x,exportVo);
                exportVo.setIndex(i+1);
                exportVo.setHandleResultContent(Objects.isNull(x.getHandleResult())?"":HandleResultEnum.decode(x.getHandleResult()).getDesc());
                exportVo.setHandleStatusName(Objects.isNull(x.getHandleStatus())?"":HandleStatusEnum.decode(x.getHandleStatus()).getName());
                exportVos.add(exportVo);
            }
            // 生成丢单数据
            out = Files.newOutputStream(Paths.get(StrUtil.format("logs/{}", fileName)));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 构建需要导出的保单
            Sheet sheet = new Sheet(1, 0, PolicyLostRegisterExportVo.class);
            sheet.setSheetName("丢单列表");
            writer.write(exportVos, sheet);
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            writer.finish();
            out.flush();
            // 上传到oss
        } catch (IOException e) {
            XxlJobHelper.log("生成丢单错误数据excel发生异常");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    XxlJobHelper.log("生成丢单错误数据excel发生异常");
                    XxlJobHelper.log(e);
                    XxlJobHelper.handleFail();
                }
            }
        }
        return new File(StrUtil.format("logs/{}", fileName));
    }

    /**
     * 校验丢单登记保单相关信息
     * 1.推荐人是否已在小程序实名
     * 2.推荐人是是否已离职
     * 3.调用保单中心补全保单推荐人信息
     * @param registerEntity
     * @param customerBasicInfoEntityMap
     */
    private void applyPolicyReferrer(EpPolicyLostRegisterEntity registerEntity,Map<String,CustomerBasicInfoEntity> customerBasicInfoEntityMap) {
        List<ChannelApplicationReferrerEntity> referrerEntityList = channelApplicationReferrerService.list(
                new LambdaQueryWrapper<ChannelApplicationReferrerEntity>()
                        .eq(ChannelApplicationReferrerEntity::getReferrerWno,registerEntity.getRecommendId())
                        .orderByDesc(ChannelApplicationReferrerEntity::getId)
        );

        Map<String,ChannelApplicationReferrerEntity> referrerMap = referrerEntityList.stream()
                .collect(Collectors.toMap(ChannelApplicationReferrerEntity::getReferrerWno, x -> x, (x, y) -> x));

        ChannelApplicationReferrerEntity recommendReferrerEntity = referrerMap.get(registerEntity.getRecommendId());
        if (Objects.isNull(recommendReferrerEntity)) {
            registerEntity.setHandleStatus(HandleStatusEnum.FAILED.getCode());
            registerEntity.setHandleResult(HandleResultEnum.NO_EXIST_RECOMMEND.getCode());
            log.warn("丢单补偿失败：渠道推荐人信息不存在，{}", JSONObject.toJSONString(registerEntity));
            return;
        }

        //1.提交人是否已在小程序实名
        CustomerBasicInfoEntity customerBasicInfoEntity = customerBasicInfoEntityMap.get(recommendReferrerEntity.getReferrerCode());
        log.info("丢单日志,登记人{}，实名列表{}", recommendReferrerEntity.getReferrerCode(),JSONObject.toJSONString(customerBasicInfoEntityMap));
        if (Objects.isNull(customerBasicInfoEntity)) {
            registerEntity.setHandleStatus(HandleStatusEnum.FAILED.getCode());
            registerEntity.setHandleResult(HandleResultEnum.UNNAMED.getCode());
            log.warn("丢单补偿失败：提交人未在小程序实名认证，{}", JSONObject.toJSONString(registerEntity));
            return;
        }

        //2.推荐人是是否已离职
       if (recommendReferrerEntity.getReferrerServiceStatus()==1){
            registerEntity.setHandleStatus(HandleStatusEnum.FAILED.getCode());
            registerEntity.setHandleResult(HandleResultEnum.DIMISSION.getCode());
            log.warn("丢单补偿失败：推荐人已离职，登记单信息={} 推荐人信息={}", JSONObject.toJSONString(registerEntity),JSONObject.toJSONString(recommendReferrerEntity));
            return;
        }

        if (checkWhalePolicyInfo(registerEntity)) {
            //3、调用保单中心补全保单推荐人信息
            ApplyPolicyReferrerReqVo applyPolicyReferrer = ApplyPolicyReferrerReqVo.builder().policyNo(registerEntity.getPolicyNo())
                    .referrerCode(recommendReferrerEntity.getReferrerCode()).build();
            Result<String> result = policyClient.applyPolicyReferrer(applyPolicyReferrer);
            if (!result.isSuccess()) {
                log.warn("调用保单中心申请补全保单推荐人，出现异常 policyNo={} referrerCode={} recommendId={} msg={}", registerEntity.getPolicyNo()
                        , recommendReferrerEntity.getReferrerCode(), registerEntity.getRecommendId(), JSON.toJSONString(result));
                if (!StringUtils.isBlank(result.getMsg()) && result.getMsg().contains("存在推荐人")) {
                    registerEntity.setHandleResult(HandleResultEnum.EXIST_RECOMMEND.getCode());
                    registerEntity.setRemark(result.getMsg());
                }else{
                    registerEntity.setHandleResult(Objects.isNull(HandleResultEnum.deName(result.getMsg()))?null:HandleResultEnum.deName(result.getMsg()).getCode());
                    registerEntity.setRemark(!Objects.isNull(HandleResultEnum.deName(result.getMsg()))?"":result.getMsg());
                }
                registerEntity.setHandleStatus(HandleStatusEnum.FAILED.getCode());
            } else {
                registerEntity.setHandleResult(HandleResultEnum.SUCCESS.getCode());
                registerEntity.setHandleStatus(HandleStatusEnum.SUCCESS.getCode());
            }
        }
    }

    /**
     * 校验是否存在符合条件的保单
     * 1.保单号是否存在
     * 2.保单是否包含登记被保人信息
     * 3.保单的投保时间是否晚于该客户经理的登记时间
     * 4.保单的渠道推荐人，代理人，推荐人是否未空且保单的销售渠道为ZHNX
     * 5.保单的渠道推荐人与推荐人是否一致
     * @param x
     */
    private boolean checkWhalePolicyInfo(EpPolicyLostRegisterEntity x) {
        //1.获取保单信息
        Result<EpContractInfoVo> entryContractInfoVoResult = epPolicyClient.queryPolicyContractDetailInfoByPolicyNo(x.getPolicyNo());

        //1.保单号是否存在
        if (!entryContractInfoVoResult.isSuccess() || Objects.isNull(entryContractInfoVoResult.getData())) {
            x.setHandleStatus(HandleStatusEnum.FAILED.getCode());
            x.setHandleResult(HandleResultEnum.NO_MATCHING_POLICY_NO.getCode());
            log.warn("丢单补偿失败：未匹配到保单信，{}", JSONObject.toJSONString(entryContractInfoVoResult));
            return false;
        }


        EpContractInfoVo epContractInfoVo = entryContractInfoVoResult.getData();
        log.info("丢单补偿保单信息：{}", JSONObject.toJSONString(epContractInfoVo));

        List<EpInsuredInfoVo> insuredInfoVoList = epContractInfoVo.getInsuredInfoList();

        //3.检验保单是否包含登记的被保人(个险)
        if (insuredInfoVoList.stream().noneMatch(
                insuredInfoVo->insuredInfoVo.getInsuredIdCard().equals(x.getInsuredIdNumber())
                        || insuredInfoVo.getInsuredName().equals(x.getInsuredName()))) {
            x.setHandleStatus(HandleStatusEnum.FAILED.getCode());
            x.setHandleResult(HandleResultEnum.NO_MATCHING_ID_NUMBER.getCode());
            log.info("丢单补偿失败：未匹配到被保人信息,{}", JSONObject.toJSONString(x));
            return false;
        }
        return true;
    }

    /**
     * 丢单分析添加失败原因标签
     * @param epPolicyLostRegisterEntity
     */
    private void analysisLostPolicy(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity){
        if(!Objects.equals(HandleStatusEnum.FAILED.getCode(),epPolicyLostRegisterEntity.getHandleStatus())){
            //非失败的数据不处理
            return;
        }
        //因为失败的记录会一直处理,所以打记录过失败标签的数据无需再打
        if(StringUtils.isNotBlank(epPolicyLostRegisterEntity.getFailResult())){
            //已经打过标签的不处理
            return;
        }
        //1.获取保单信息
        Result<EpContractInfoVo> entryContractInfoVoResult = epPolicyClient.queryPolicyContractDetailInfoByPolicyNo(epPolicyLostRegisterEntity.getPolicyNo());
        EpContractInfoVo epContractInfoVo = entryContractInfoVoResult.getData();
        if(Objects.isNull(epContractInfoVo)){
            //查无保单则不做处理
            return;
        }
        EpContractBaseInfoVo contractBaseInfo = epContractInfoVo.getContractBaseInfo();
        if(Objects.isNull(contractBaseInfo)){
            //查无保单则不做处理
            return;
        }
        //1.分析是否为四级分销
        checkDistribution(epPolicyLostRegisterEntity,epContractInfoVo);
        //2.分析保单跨月
        checkBeyondMonth(epPolicyLostRegisterEntity,contractBaseInfo);
        //根据推荐人ID获取渠道推荐人信息实体
        ChannelApplicationReferrerEntity channelApplicationReferrer = getChannelApplicationReferrerEntity(epPolicyLostRegisterEntity);
        if(Objects.isNull(channelApplicationReferrer)){
            //查无推荐人信息则不做处理
            return;
        }
        //3.分析保单归属为绑定推荐人还是分享推荐人
        checkGetCustomerBind(epPolicyLostRegisterEntity,epContractInfoVo,channelApplicationReferrer);
        //4.检查当前保单24小时内是否有扫描过分享海报且绑定客户经理与分享海报不一致则添加失败原因
        checkShareProductBind(epPolicyLostRegisterEntity,epContractInfoVo,channelApplicationReferrer);
    }

    private CustomerBasicInfoVo getCustomerBingInfo(EpContractInfoVo epContractInfoVo){
        // 获取合同基本信息、渠道信息和扩展信息
        EpPolicyChannelInfoVo channelInfoVo = epContractInfoVo.getChannelInfo();

        // 准备查询参数，以客户代码为中心，查询客户基本信息。
        Map<String,Object> params = new HashMap<>();
        params.put("limit","1");
        params.put("page","0");
        params.put("customerCode",channelInfoVo.getMiniAppCustomerCode());

        // 根据查询参数，查询客户基本信息。
        PageUtils<CustomerBasicInfoVo> page = customerBasicInfoService.queryPage(params);

        // 如果查询结果为空，则直接返回，不进行后续处理。
        if(CollectionUtils.isEmpty(page.getList())){
            return null;
        }

        // 获取查询结果中的第一条记录，即客户基本信息。
        CustomerBasicInfoVo customerBasicInfoVo = page.getList().get(0);
        return customerBasicInfoVo;
    }


    /**
     * 检查保单是否属于四级分销类型
     * 该方法用于分析传入的保单损失登记实体是否关联了四级分销类型的保单活动。
     * 如果存在四级分销类型的保单活动，则将该保单损失登记实体标记为分销单。
     *
     * @param epPolicyLostRegisterEntity 保单损失登记实体，可能被标记为分销单
     * @param epContractInfoVo 合同信息视图对象，包含与保单相关的活动信息
     */
    private void checkDistribution(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity, EpContractInfoVo epContractInfoVo) {
        // 获取合同信息中的保单活动列表
        List<EpPolicyActivityVo> epPolicyActivitys = epContractInfoVo.getEpPolicyActivitys();

        // 如果保单活动列表为空，则无需进一步检查，直接返回
        if (CollectionUtils.isEmpty(epPolicyActivitys)) {
            return;
        }

        // 判断保单活动列表中是否存在类型为四级分销的保单活动
        // type=200为四级分销
        boolean isDistribution = epPolicyActivitys.stream().anyMatch(x -> PolicyActivityTypeEnum.FOURTH_LEVEL_DISTRIBUTION.getCode().equals(x.getType()));

        // 如果存在四级分销类型的保单活动，则将保单损失登记实体标记为分销单
        if (isDistribution) {
            appendFailResult(epPolicyLostRegisterEntity,"分销单");
        }
    }

    /**
     * 检查当前保单24小时内是否有扫描过获客二维码且绑定客户经理与获客二维码不一致则添加失败原因
     * @param epPolicyLostRegisterEntity
     * @param epContractInfoVo
     * @param channelApplicationReferrer
     */
    private void checkGetCustomerBind(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity, EpContractInfoVo epContractInfoVo, ChannelApplicationReferrerEntity channelApplicationReferrer) {
        // 获取渠道信息
        EpPolicyChannelInfoVo channelInfo = epContractInfoVo.getChannelInfo();
        // 获取订单时间
        Date orderTime = epContractInfoVo.getContractBaseInfo().getOrderTime();
        // 获取保单对应的客户编码
        String customerCode = channelInfo.getMiniAppCustomerCode();
        // 丢单客户经理对应的推荐人编码
        String referrerCode = channelApplicationReferrer.getReferrerCode();

        CustomerBasicInfoVo customerBasicInfoVo = customerBasicInfoService.getBasicInfo(customerCode);
        //获取客户的openId
        String openId = customerBasicInfoVo.getOpenId();

        // 检查客户编码是否为空，如果为空则记录日志并返回
        if(StringUtils.isBlank(customerCode)){
            log.warn("checkPolicyBind 分析分享产品绑定 客户编码不存在 epPolicyLostRegisterEntity= {}",JSON.toJSONString(epPolicyLostRegisterEntity));
            return;
        }

        // 构建查询SQL，用于查询分享行为日志  100848_002 前端埋点区分环境编码
        String querySql = "whale-mini and 100848_002 and eventAttr.paramFrom:\"inner\" and eventAttr.referrerCode:\"#referrerCode#\" and #openId#";
        // 替换查询SQL中的占位符，填充实际的用户编码和微信场景值列表
        querySql = querySql.replace("#referrerCode#",referrerCode);
        querySql = querySql.replace("#openId#",openId);

        SlsQueryLogOutputVo slsQueryLogOutputVo = queryLog(orderTime,querySql);
        if(Objects.isNull(slsQueryLogOutputVo) || CollectionUtils.isEmpty(slsQueryLogOutputVo.getData())){
            return;
        }
        String message = "最近扫{0}{1}获客二维码";
        // 打印分享记录的信息。
        appendFailResult(epPolicyLostRegisterEntity,MessageFormat.format(message,channelApplicationReferrer.getReferrerName(),channelApplicationReferrer.getReferrerWno()));
    }


    /**
     * 检查当前保单24小时内是否有扫描过分享海报且绑定客户经理与分享海报不一致则添加失败原因
     * @param epPolicyLostRegisterEntity
     * @param epContractInfoVo
     * @param channelApplicationReferrer
     */
    private void checkShareProductBind(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity, EpContractInfoVo epContractInfoVo, ChannelApplicationReferrerEntity channelApplicationReferrer){
        // 获取渠道信息
        EpPolicyChannelInfoVo channelInfo = epContractInfoVo.getChannelInfo();
        // 获取订单时间
        Date orderTime = epContractInfoVo.getContractBaseInfo().getOrderTime();
        // 获取小程序客户编码
        String customerCode = channelInfo.getMiniAppCustomerCode();

        // 检查客户编码是否为空，如果为空则记录日志并返回
        if(StringUtils.isBlank(customerCode)){
            log.warn("checkPolicyBind 分析分享产品绑定 客户编码不存在 epPolicyLostRegisterEntity= {}",JSON.toJSONString(epPolicyLostRegisterEntity));
            return;
        }
        // 丢单客户经理对应的推荐人编码
        String referrerCode = channelApplicationReferrer.getReferrerCode();
        // 构建查询SQL，用于查询分享行为日志 100848_002 前端埋点区分环境编码
        String querySql = "whale-mini and 100848_002 and (eventAttr:paramsJson and primaryKey:#userId# and #shareCode# and inner) or (eventAttr.scene:STC* and eventAttr.manageWno:\"#manageWno#\")";
        // 替换查询SQL中的占位符，填充实际的用户编码和微信场景值列表
        querySql = querySql.replace("#userId#",customerCode);
        querySql = querySql.replace("#shareCode#",referrerCode);
        querySql = querySql.replace("#manageWno#",epPolicyLostRegisterEntity.getRecommendId());

        SlsQueryLogOutputVo slsQueryLogOutputVo = queryLog(orderTime,querySql);

        // 检查日志数据是否为空，如果为空则直接返回。
        if(Objects.isNull(slsQueryLogOutputVo) || Objects.isNull(slsQueryLogOutputVo.getData()) || slsQueryLogOutputVo.getData().size() == 0){
            return;
        }
        // 打印分享记录的信息。
        appendFailResult(epPolicyLostRegisterEntity,"支付成功前24小时内进入登记推荐人的产品链接/海报");
    }

    /**
     * 本次业务查询日志封装方法
     * @param orderTime
     * @param querySql
     * @return
     */
    private SlsQueryLogOutputVo queryLog(Date orderTime,String querySql){
        // 创建日志查询输入参数Vo
        SlsQueryLogInputVo inputVo = new SlsQueryLogInputVo(querySql,SlsQueryConstant.SLS_PROJECT_USER_EVENT_PROJECT,SlsQueryConstant.SLS_USER_EVENT_PROJECT_USER_EVENT_LOG_STORE);
        // 设置查询SQL和相关项目、日志库信息
        inputVo.setQuery(querySql);
        // 设置查询的时间范围，为订单时间的前一天到订单时间
        Date startTime = DateUtils.addDateDays(orderTime,-1);
        inputVo.setStartTime(startTime);
        inputVo.setEndTime(orderTime);
        // 调用日志查询服务，查询分享行为日志
        return slsQueryLogService.queryLog(inputVo);
    }


    /**
     * 跨月丢单分析:该保单交单时间是否在同一月中
     * @param epPolicyLostRegisterEntity
     * @return
     */
    private void checkBeyondMonth(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity,EpContractBaseInfoVo contractBaseInfo){
        // 获取保单登记时间
        Date registerTime = epPolicyLostRegisterEntity.getRegisterTime();
        // 获取订单时间
        Date orderTime = contractBaseInfo.getOrderTime();
        String registerTimeStr = null;
        String orderTimeStr = null;
        // 定义用于存储时间的字符串格式
        String yyymm = "yyyy-MM";
        // 检查保单登记时间是否为空，如果为空则记录警告日志并返回
        if(Objects.isNull(registerTime)){
            log.warn("checkBeyondMonth 跨月丢单分析参数异常,缺少丢单等级时间epPolicyLostRegisterEntity= {}",JSON.toJSONString(epPolicyLostRegisterEntity));
            return;
        }else{
            // 将保单登记时间格式化为字符串
            registerTimeStr = DateUtils.format(registerTime, yyymm);
        }
        // 如果订单时间不为空，则将其格式化为字符串
        if(Objects.nonNull(orderTime)){
            orderTimeStr = DateUtils.format(orderTime, yyymm);
        }
        // 比较保单登记时间和订单时间的月份是否相同，如果不同，则设置保单丢失原因为空
        if(Objects.equals(registerTimeStr,orderTimeStr)){
            return;
        }
        appendFailResult(epPolicyLostRegisterEntity,"跨月保单");
    }

    /**
     * 根据推荐人ID获取渠道推荐人信息实体。
     * 该方法通过查询所有推荐人ID与给定ID匹配的渠道推荐人信息，并从中选择最新的记录返回。
     * 这样做的目的是为了确保总是获取到最新的推荐人信息，以反映当前状态。
     *
     * @param epPolicyLostRegisterEntity 报失登记实体，包含需要查询的推荐人ID。
     * @return ChannelApplicationReferrerEntity 最新的渠道推荐人信息实体。
     */
    //获取渠道推荐人信息
    private ChannelApplicationReferrerEntity getChannelApplicationReferrerEntity(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity){
        // 使用LambdaQueryWrapper查询推荐人信息，条件为推荐人ID与报失登记实体中的推荐人ID相等，
        // 并按ID降序排序，确保返回的是最新的记录。
        List<ChannelApplicationReferrerEntity> referrerEntityList = channelApplicationReferrerService.list(
                new LambdaQueryWrapper<ChannelApplicationReferrerEntity>()
                        .eq(ChannelApplicationReferrerEntity::getReferrerWno,epPolicyLostRegisterEntity.getRecommendId())
                        .orderByDesc(ChannelApplicationReferrerEntity::getId)
        );

        // 将查询结果转换为Map，以推荐人ID为键，对应的推荐人实体为值。
        // 如果存在相同的推荐人ID，则通过Lambda表达式中的(x, y) -> x确保只保留最新的记录。
        Map<String,ChannelApplicationReferrerEntity> referrerMap = referrerEntityList.stream()
                .collect(Collectors.toMap(ChannelApplicationReferrerEntity::getReferrerWno, x -> x, (x, y) -> x));

        // 通过推荐人ID从Map中获取对应的推荐人实体，返回最新的渠道推荐人信息。
        return referrerMap.get(epPolicyLostRegisterEntity.getRecommendId());
    }


    /**
     * 分析用户分享行为，确定分享或绑定推荐人的影响结果。
     *
     * @param businessOwner 业绩归属人
     * @param epPolicyLostRegisterEntity 保险单丢失登记实体，用于设置分享或绑定结果。
     * @param slsQueryLogOutputVo 日志查询输出对象，包含用户行为日志数据。
     * @param channelApplicationReferrer 渠道应用推荐人实体，用于比对渠道绑定的推荐人。
     *
     * 此方法主要通过分析日志数据，找出用户的最后一次分享记录，然后根据该记录的推荐人代码，
     * 确定分享或绑定推荐人的影响结果，并在保险单丢失登记实体中设置相应的结果。
     */
    private void analysisCustomerShareBehavior(String businessOwner,EpPolicyLostRegisterEntity epPolicyLostRegisterEntity,SlsQueryLogOutputVo slsQueryLogOutputVo,ChannelApplicationReferrerEntity channelApplicationReferrer){
        // 检查日志数据是否为空，如果为空则直接返回。
        if(Objects.isNull(slsQueryLogOutputVo) || Objects.isNull(slsQueryLogOutputVo.getData()) || slsQueryLogOutputVo.getData().size() == 0){
            return;
        }
        // 打印分享记录的信息。
        log.info("analysisCustomerShareBehavior slsQueryLogOutputVo= {}",JSON.toJSONString(slsQueryLogOutputVo));
        appendFailResult(epPolicyLostRegisterEntity,"支付成功前24小时内进入登记推荐人的产品链接/海报");
    }


    /**
     * 判断传入的JSON对象是否为最新的分享记录。
     * <p>
     * 本函数通过检查事件属性中的引荐码(referrerCode)是否存在来判断。
     * 如果引荐码存在且不为空，则认为该记录是最新的分享记录。
     * <p>
     * @param jsonObject 待检查的JSON对象，包含事件属性。
     * @return 如果传入的JSON对象是最新的分享记录，则返回原对象；
     * 否则返回null，表示该记录不是最新的分享记录。
     */
    private JSONObject isLastShareRecord(JSONObject jsonObject){
        // 获取事件属性部分的JSON对象
        JSONObject eventAttr = jsonObject.getJSONObject("eventAttr");

        // 如果事件属性不存在，则直接返回null
        if(Objects.isNull(eventAttr)){
            return null;
        }

        // 获取引荐码，并检查其是否为空
        String referrerCode = eventAttr.getString("referrerCode");
        if(StringUtils.isNotBlank(referrerCode)){
            // 如果引荐码存在且不为空，则返回原JSON对象
            return jsonObject;
        }

        // 如果引荐码为空，则返回null，表示该记录不是最新的分享记录
        return null;
    }

    /**
     * 添加失败结果信息到EpPolicyLostRegisterEntity实体中。
     * 如果当前实体中的失败结果为空，则直接设置新的失败结果；
     * 如果已有失败结果，则在原有结果基础上追加新的失败结果。
     *
     * @param epPolicyLostRegisterEntity 需要更新失败结果的实体对象。
     * @param failResult 新的失败结果信息。
     */
    private void appendFailResult(EpPolicyLostRegisterEntity epPolicyLostRegisterEntity, String failResult){
        // 检查新失败结果是否为空，为空则不进行任何操作
        if(StringUtils.isBlank(failResult)){
            return;
        }
        // 如果实体中的失败结果为空，则直接设置新的失败结果
        if(StringUtils.isBlank(epPolicyLostRegisterEntity.getFailResult())){
            epPolicyLostRegisterEntity.setFailResult(failResult);
        }else{
            // 如果实体中已有失败结果，则在原有结果后追加新的失败结果
            epPolicyLostRegisterEntity.setFailResult(epPolicyLostRegisterEntity.getFailResult()+"，"+failResult);
        }
    }
}
