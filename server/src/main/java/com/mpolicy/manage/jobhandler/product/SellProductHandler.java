package com.mpolicy.manage.jobhandler.product;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.manage.common.SellProductKeys;
import com.mpolicy.manage.feign.client.DingTalkRobotMessageClient;
import com.mpolicy.manage.feign.model.DingTalkRobotMessageTextRequest;
import com.mpolicy.manage.modules.sell.entity.SellProductGiftInfoEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductInfoEntity;
import com.mpolicy.manage.modules.sell.service.ISellProductService;
import com.mpolicy.manage.modules.sell.service.SellProductGiftInfoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *         销售产品的定时任务
 *          包含定时下架等处理器
 *
 * @create 2024/7/15
 * @since 1.0.0
 */
@Slf4j
@Component
public class SellProductHandler {

    private static final String DING_TALK_ROBOT_MESSAGE_FORMAT = "【%S】在【%S】中的礼品数量剩余【%S】，请及时跟进补充。";
    private static final String DING_TALK_ROBOT_MESSAGE_TITLE = "客户管理系统告警:";


    private static final String SELL_RENEWAL_NOTICE_DING_TALK_ROBOT_MESSAGE_FORMAT = "销售商品：%s该商品创建快1年，请商品的续保，请及时跟进补充。";
    private static final String SELL_RENEWAL_NOTICE_DING_TALK_ROBOT_MESSAGE_TITLE = "销售商品续保提醒:";

    /**
     *
     * 客户礼品告警的剩余数量
     *
     *
     */
    private static final Integer CUSTOMER_GIFT_QUANTITY_EMERGENCY_NUM  = 5;


    @Value("${dingTalkRobotMessage.customerGiftNoticeToken}")
    private String customerGiftDingTallToken;

    @Value("${dingTalkRobotMessage.sellRenewalNoticeToken}")
    private String sellRenewalNoticeToken;

    @Resource
    private ISellProductService sellProductService;

    @Resource
    private DingTalkRobotMessageClient dingTalkRobotMessageClient;

    @Resource
    private SellProductGiftInfoService sellProductGiftInfoService;


    @Resource
    private IRedisService iRedisService;


    @XxlJob("customerGiftQuantityEmergency")
    public void customerGiftQuantityEmergency() {
        List<SellProductGiftInfoEntity> sellProductGiftInfoEntityList = sellProductGiftInfoService.getActiveLessQuantity(CUSTOMER_GIFT_QUANTITY_EMERGENCY_NUM);
        if (CollectionUtils.isNotEmpty(sellProductGiftInfoEntityList)){
            for (SellProductGiftInfoEntity sellProductGiftInfoEntity : sellProductGiftInfoEntityList) {
                Long id = sellProductGiftInfoEntity.getId();
                if (!iRedisService.exists(SellProductKeys.SELL_CUSTOMER_GIFT_EMERGENCY,String.valueOf(id))){
                    String referrerOgrName = sellProductGiftInfoEntity.getReferrerOgrName();
                    String productName = sellProductGiftInfoEntity.getProductName();
                    String productCode = sellProductGiftInfoEntity.getProductCode();
                    Integer remainingGiftQuantity = sellProductGiftInfoEntity.getRemainingGiftQuantity();
                    String message = String.format(DING_TALK_ROBOT_MESSAGE_FORMAT,referrerOgrName,productName,remainingGiftQuantity);
                    log.info("客户管理系统告警，customerGiftDingTallToken={},message={}",customerGiftDingTallToken,message);
                    DingTalkRobotMessageTextRequest request = DingTalkRobotMessageTextRequest.getByText(new DingTalkRobotMessageTextRequest.Text(DING_TALK_ROBOT_MESSAGE_TITLE, message));
                    String result = dingTalkRobotMessageClient.robotSend(customerGiftDingTallToken, request);
                    log.info("客户管理系统告警，钉钉发送消息result={}",result);
                    iRedisService.set(SellProductKeys.SELL_CUSTOMER_GIFT_EMERGENCY,String.valueOf(id),productCode);
                }
            }
        }
    }



    /**
     *
     *  销售商品定时下架处理器
     *  原理是根据商品内部定时下架的时间字段
     *  默认每15分钟扫描一次
     *  小于当前时间的所有定时下架商品进行下架处理
     *  通过xxl-job去更改处理的时间
     *
     *
     *
     */
    @XxlJob("scheduledUpOrDownSellProductJob")
    public void scheduledUpOrDownSellProduct(){
        DateTime endDate = DateUtil.date();
        DateTime startDate = DateUtil.offsetDay(endDate, -1);
        List<String> productCodes = sellProductService.timeUpdateStatusV2(startDate, endDate);
        XxlJobHelper.handleSuccess(String.format("定时上下架任务完成 处理的产品编码有 productCodes={%s}", JSON.toJSON(productCodes)));
    }

    /**
     *
     *  销售商品的创建时间
     *  发送消息到制定的钉钉群
     *  提醒管理员进行安排处理
     *  该商品的续保的事项
     *
     *
     */
    @XxlJob("productRenewalReminderMessagePushJob")
    public void productRenewalReminderMessagePush(){
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取去年前60天的日期
        LocalDate lastYearBefore60Days = currentDate.minusYears(1).minusDays(60);
        // 获取当天的开始时间
        LocalDateTime startOfDay = lastYearBefore60Days.atStartOfDay();
        // 获取当天的结束时间
        LocalDateTime endOfDay = lastYearBefore60Days.atTime(LocalTime.MAX);

        log.info("产品续保提醒查询条件，startOfDay={},endOfDay={}",startOfDay,endOfDay);
        List<SellProductInfoEntity> sellProductInfoEntities = sellProductService.getListByCreateTime(startOfDay, endOfDay);
        log.info("产品续保提醒查询结果，sellProductInfoEntities={}", JSONObject.toJSON(sellProductInfoEntities));

        if (CollectionUtils.isNotEmpty(sellProductInfoEntities)){

            StringBuilder productNamesBuilder = new StringBuilder();
            for (SellProductInfoEntity sellProductInfoEntity : sellProductInfoEntities) {
                String productName = sellProductInfoEntity.getProductName();
                productNamesBuilder.append(productName).append(",");
            }
            String productNames = productNamesBuilder.toString();
            String message = String.format(SELL_RENEWAL_NOTICE_DING_TALK_ROBOT_MESSAGE_FORMAT,productNames);

            log.info("销售商品续保通知，customerGiftDingTallToken={},message={}",sellRenewalNoticeToken,message);
            DingTalkRobotMessageTextRequest request = DingTalkRobotMessageTextRequest.getByText(new DingTalkRobotMessageTextRequest.Text(SELL_RENEWAL_NOTICE_DING_TALK_ROBOT_MESSAGE_TITLE, message));
            DingTalkRobotMessageTextRequest.At at = new DingTalkRobotMessageTextRequest.At();
            at.setIsAtAll(true);
            request.setAt(at);
            String result = dingTalkRobotMessageClient.robotSend(sellRenewalNoticeToken, request);
            log.info("销售商品续保通知，钉钉发送消息result={}",result);
        }

        XxlJobHelper.handleSuccess("处理成功消息通知");
    }

}
