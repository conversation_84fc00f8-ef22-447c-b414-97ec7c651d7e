package com.mpolicy.manage.jobhandler.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.policy.entity.EpPolicyApplicantInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyInsuredInfoEntity;
import com.mpolicy.manage.modules.policy.entity.PolicyUserRecordEntity;
import com.mpolicy.manage.modules.policy.service.EpPolicyApplicantInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyInsuredInfoService;
import com.mpolicy.manage.modules.policy.service.PolicyUserRecordService;
import com.mpolicy.policy.common.enums.TrustStatusEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomerJob {

    @Autowired
    private CustomerBasicInfoService customerBasicInfoService;

    @Autowired
    private PolicyUserRecordService policyUserRecordService;

    @Autowired
    private EpPolicyInsuredInfoService policyInsuredInfoService;

    @Autowired
    private EpPolicyApplicantInfoService policyApplicantInfoService;

    @Autowired
    private EpPolicyContractInfoService policyContractInfoService;


    /**
     * 刷新客户的保单数量
     */
    @XxlJob("refreshAllCustomerPolicyNum")
    public void refreshAllCustomerPolicyNum() {
        XxlJobHelper.handleSuccess("开始刷新客户的保单数量");
        int maxId = customerBasicInfoService.findMaxCustomerId();
        //一次插入1000条
        int limit = 1000;
        int startId = 1;
        int endId = limit;
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        //如果开始id小于最大值 那么就查数据去
        while (startId < maxId) {
            try {
                // 获取实名客户数,查询他的保单信息,更新数据
                List<CustomerBasicInfoEntity> customerList = customerBasicInfoService.lambdaQuery()
                        .eq(CustomerBasicInfoEntity::getCertificationStatus, StatusEnum.NORMAL.getCode())
                        .eq(CustomerBasicInfoEntity::getCancelStatus, StatusEnum.INVALID.getCode())
                        .between(CustomerBasicInfoEntity::getId, startId, endId)
                        .list();
                if (!customerList.isEmpty()) {
                    log.info("更新客户完成，操作startId={},endId={} 记录数={}  ", startId, endId, customerList.size());
                    updateCustomerBasicInfo(customerList);
                }

            } catch (Exception e) {
                log.warn("客户ID startId={},endId={} 数据异常", startId, endId, e);
                XxlJobHelper.log("客户ID startId=[" + startId + "],endId=[" + endId + "]数据异常");
            } finally {
                startId += limit;
                endId += limit;
            }
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("刷新客户的保单数量成功,耗时=[{}]毫秒", millis);
        XxlJobHelper.handleSuccess("刷新客户的保单数量成功,耗时=[" + millis + "]毫秒");
    }


    /**
     * 刷新客户的保单数量
     */
    @XxlJob("refreshAllCustomerPolicyNumTest")
    public void refreshAllCustomerPolicyNumTest() {
        XxlJobHelper.handleSuccess("开始刷新客户的保单数量");
        int maxId = customerBasicInfoService.findMaxCustomerId();
        //一次插入1000条
        int limit = 1000;
        int startId = 1;
        int endId = limit;
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        //如果开始id小于最大值 那么就查数据去
        while (startId < maxId) {
            try {
                // 获取实名客户数,查询他的保单信息,更新数据
                List<CustomerBasicInfoEntity> customerList = customerBasicInfoService.lambdaQuery()
                        .eq(CustomerBasicInfoEntity::getCertificationStatus, StatusEnum.NORMAL.getCode())
                        .eq(CustomerBasicInfoEntity::getCancelStatus, StatusEnum.INVALID.getCode())
                        .between(CustomerBasicInfoEntity::getId, startId, endId)
                        .list();
                if (!customerList.isEmpty()) {
                    updateCustomerBasicInfo(customerList);
                }

            } catch (Exception e) {
                log.warn("客户ID startId={},endId={}数据异常", startId, endId, e);
                XxlJobHelper.log("客户ID startId=[" + startId + "],endId=[" + endId + "]数据异常");
            } finally {
                startId += limit;
                endId += limit;
            }
            if (startId > 5000) {
                break;
            }
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("刷新客户的保单数量成功,耗时=[{}]毫秒", millis);
        XxlJobHelper.handleSuccess("刷新客户的保单数量成功,耗时=[" + millis + "]毫秒");
    }

    /**
     * 更新客户信息
     *
     * @param customerList
     */
    private void updateCustomerBasicInfo(List<CustomerBasicInfoEntity> customerList) {
        if (CollUtil.isEmpty(customerList)) {
            return;
        }
        // 托管单信息
        List<String> customerCodeList = customerList.stream().map(CustomerBasicInfoEntity::getCustomerCode).collect(Collectors.toList());
        Map<String, Long> policyUserRecordMap = policyUserRecordService.lambdaQuery()
                .in(PolicyUserRecordEntity::getCustomerCode, customerCodeList)
                .in(PolicyUserRecordEntity::getRecordStatus, CollUtil.newArrayList(TrustStatusEnum.PRODUCT_UNDEFINED.getTrustType(), TrustStatusEnum.PRODUCT_DEFINED.getTrustType(), TrustStatusEnum.POLICY_SAVING.getTrustType()))
                .list()
                .stream().collect(Collectors.groupingBy(PolicyUserRecordEntity::getCustomerCode, Collectors.counting()));

        List<String> idCardList = customerList.stream().map(CustomerBasicInfoEntity::getCertiNo).collect(Collectors.toList());
        // 被保人信息
        Map<String, List<EpPolicyInsuredInfoEntity>> insuredInfoMap = policyInsuredInfoService.lambdaQuery()
                .in(EpPolicyInsuredInfoEntity::getInsuredIdCard, idCardList)
                .list().stream().collect(Collectors.groupingBy(EpPolicyInsuredInfoEntity::getContractCode));
        // 投保人信息
        Map<String, List<EpPolicyApplicantInfoEntity>> applicantInfoMap = policyApplicantInfoService.lambdaQuery()
                .in(EpPolicyApplicantInfoEntity::getApplicantIdCard, idCardList)
                .list().stream().collect(Collectors.groupingBy(EpPolicyApplicantInfoEntity::getContractCode));
        //合同信息是否合法,获取满足条件的合同
        List<String> contractCodeList = Stream.concat(insuredInfoMap.keySet().stream(), applicantInfoMap.keySet().stream())
                .distinct()
                .collect(Collectors.toList());
        List<String> contractCodeArr = policyContractInfoService.lambdaQuery()
                .eq(EpPolicyContractInfoEntity::getIntact, StatusEnum.NORMAL.getCode())
                .eq(EpPolicyContractInfoEntity::getShowModel, StatusEnum.NORMAL.getCode())
                .in(EpPolicyContractInfoEntity::getContractCode, contractCodeList)
                .list().stream().map(EpPolicyContractInfoEntity::getContractCode).distinct().collect(Collectors.toList());
        Map<String, Integer> customerMap = new HashMap<>(1);
        insuredInfoMap.forEach((contractCode, insuredInfoList) -> {
            if (contractCodeArr.contains(contractCode)) {
                insuredInfoList.stream()
                        .collect(Collectors.groupingBy(EpPolicyInsuredInfoEntity::getInsuredIdCard, Collectors.counting()))
                        .forEach((key, value) -> customerMap.merge(key, value.intValue(), Integer::sum));
            }
        });
        applicantInfoMap.forEach((contractCode, applicantInfoList) -> {
            if (contractCodeArr.contains(contractCode)) {
                applicantInfoList.stream()
                        .collect(Collectors.groupingBy(EpPolicyApplicantInfoEntity::getApplicantIdCard, Collectors.counting()))
                        .forEach((key, value) -> customerMap.merge(key, value.intValue(), Integer::sum));
            }
        });
        customerList.forEach(action -> {
            //托管保单数
            Long entrustPolicyNumber = policyUserRecordMap.getOrDefault(action.getCustomerCode(), 0L);
            action.setEntrustPolicyNumber(entrustPolicyNumber.intValue());
            //获取被保人信息
            Integer tradePolicyNumber = customerMap.getOrDefault(action.getCertiNo(), 0);
            action.setTradePolicyNumber(tradePolicyNumber);
            action.setWhalePolicyNumber(tradePolicyNumber);
            action.setPolicyNumber(action.getEntrustPolicyNumber() + action.getTradePolicyNumber());
        });
        //更新客户信息
        customerBasicInfoService.updateBatchById(customerList);
    }

}
