package com.mpolicy.manage.jobhandler.protocol;

import com.mpolicy.manage.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.manage.modules.contract.entity.ContractInsuranceProductPremEntity;
import com.mpolicy.manage.modules.contract.service.ContractInsuranceProductPremService;
import com.mpolicy.manage.modules.protocol.entity.ProtocolProductPremEntity;
import com.mpolicy.manage.modules.protocol.service.ProtocolProductPremService;
import com.mpolicy.manage.modules.protocol.vo.ProductPremExcel;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductPremService;
import com.mpolicy.manage.utils.ProductPremUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * 协议相关定时任务
 *
 * <AUTHOR>
 * @date 2021-12-04 11:14:35
 */
@Component
@Slf4j
public class PremJob {
    @Autowired
    private ProtocolProductPremService protocolProductPremService;
    @Autowired
    private ContractInsuranceProductPremService contractInsuranceProductPremService;

    @Autowired
    private SettlementPolicyProductPremService settlementPolicyProductPremService;
    @Autowired
    private CommissionBasicPolicyPremService commissionBasicPolicyPremService;

    /**
     * 协议过期和生效定时任务处理
     * todo....
     */
    @XxlJob("updateProtocolPremCode")
    public void updatePremCode() throws Exception {
        /**
         * 更新协议费率表
         */
        protocolProductPremService.list().forEach(entity -> {
            ProductPremExcel template = new ProductPremExcel();
            template.setFirstYearBrokage(entity.getFirstYearBrokage());
            template.setRenewalBrokage2year(entity.getRenewalBrokage2year());
            template.setRenewalBrokage3year(entity.getRenewalBrokage3year());
            template.setRenewalBrokage4year(entity.getRenewalBrokage4year());
            template.setRenewalBrokage5year(entity.getRenewalBrokage5year());
            template.setRenewalBrokage6year(entity.getRenewalBrokage6year());
            template.setRenewalAutoExpand(entity.getRenewalAutoExpand());
            template.setInsuranceProductCode(entity.getInsuranceProductCode());
            template.setMainInsuranceProductCode(entity.getMainInsuranceProductCode());
            template.setPersistencyRate(entity.getPersistencyRate());
            template.setProductPlan(entity.getProductPlan());
            template.setCoveragePeriod(entity.getCoveragePeriod());
            template.setExpireAge(entity.getExpireAge());
            template.setApplicantAge(entity.getApplicantAge());
            template.setApplicantGender(entity.getApplicantGender());
            template.setInsuredAge(entity.getInsuredAge());
            template.setInsuredGender(entity.getInsuredGender());
            template.setPaymentType(entity.getPaymentType());
            template.setPaymentPeriod(entity.getPaymentPeriod());
            template.setUnderwritingRate(entity.getUnderwritingRate());
            template.setCostType(entity.getCostType());
            template.setSettlementMethodDesc(entity.getSettlementStandard());
            template.setAutoSettlementFlag(entity.getAutoSettlementFlag());
            template.setRenewalSettlementFlag(entity.getRenewalSettlementFlag());
            template.setOrgCode(entity.getOrgCode());
            template.setSelfPreservation(entity.getSelfPreservation());
            template.setEffectiveStartDate(entity.getEffectiveStartDate());
            template.setEffectiveEndDate(entity.getEffectiveEndDate());
            protocolProductPremService.lambdaUpdate()
                    .set(ProtocolProductPremEntity::getPremCode, ProductPremUtil.getProtocolPremCode(template, true))
                    .set(ProtocolProductPremEntity::getRateCode, ProductPremUtil.getProtocolRateCode(template))
                    .eq(ProtocolProductPremEntity::getId, entity.getId())
                    .update();
        });
    }

    @XxlJob("updateContractPremCode")
    public void updateContractPremCode() throws Exception {
        /**
         * 更新合约费率表
         */
        contractInsuranceProductPremService.list().forEach(entity -> {
            ProductPremExcel template = new ProductPremExcel();
            template.setFirstYearBrokage(entity.getFirstYearBrokage());
            template.setRenewalBrokage2year(entity.getRenewalBrokage2year());
            template.setRenewalBrokage3year(entity.getRenewalBrokage3year());
            template.setRenewalBrokage4year(entity.getRenewalBrokage4year());
            template.setRenewalBrokage5year(entity.getRenewalBrokage5year());
            template.setRenewalBrokage6year(entity.getRenewalBrokage6year());
            template.setRenewalAutoExpand(entity.getRenewalAutoExpand());
            template.setInsuranceProductCode(entity.getInsuranceProductCode());
            template.setMainInsuranceProductCode(entity.getMainInsuranceProductCode());
            template.setPersistencyRate(entity.getPersistencyRate());
            template.setProductPlan(entity.getProductPlan());
            template.setCoveragePeriod(entity.getCoveragePeriod());
            template.setExpireAge(entity.getExpireAge());
            template.setApplicantAge(entity.getApplicantAge());
            template.setApplicantGender(entity.getApplicantGender());
            template.setInsuredAge(entity.getInsuredAge());
            template.setInsuredGender(entity.getInsuredGender());
            template.setPaymentType(entity.getPaymentType());
            template.setPaymentPeriod(entity.getPaymentPeriod());
            template.setUnderwritingRate(entity.getUnderwritingRate());
            template.setCostType(entity.getCostType());
            template.setSettlementMethodDesc(entity.getSettlementStandard());
            template.setAutoSettlementFlag(entity.getAutoSettlementFlag());
            template.setRenewalSettlementFlag(entity.getRenewalSettlementFlag());
            template.setOrgCode(entity.getOrgCode());
            template.setSelfPreservation(entity.getSelfPreservation());
            template.setEffectiveStartDate(entity.getEffectiveStartDate());
            template.setEffectiveEndDate(entity.getEffectiveEndDate());
            contractInsuranceProductPremService.lambdaUpdate()
                    .set(ContractInsuranceProductPremEntity::getPremCode, ProductPremUtil.getProtocolPremCode(template, true))
                    .set(ContractInsuranceProductPremEntity::getRateCode, ProductPremUtil.getProtocolRateCode(template))
                    .eq(ContractInsuranceProductPremEntity::getId, entity.getId())
                    .update();
        });

    }

    @XxlJob("checkFactor")
    public void checkFactor() throws Exception {
        // 更新合约和协议的一单一议费率code
        settlementPolicyProductPremService.list().stream()
            .collect(Collectors.toMap(ProductPremUtil::getProtocolPolicyFactor, v -> v, (v1, v2) -> {
                XxlJobHelper.log("保单号=[],ID:[{}-{}]匹配因子一样了,请检查....",v1.getPolicyNo(),v1.getId(),v2.getId());
                return v1;
            }));

        commissionBasicPolicyPremService.list().stream()
            .collect(Collectors.toMap(ProductPremUtil::getCommissionPolicyFactor, v -> v, (v1, v2) -> {
                XxlJobHelper.log("基础佣金保单号=[],ID:[{}-{}]匹配因子一样了,请检查....",v1.getPolicyNo(),v1.getId(),v2.getId());
                return v1;
            }));

    }

}
