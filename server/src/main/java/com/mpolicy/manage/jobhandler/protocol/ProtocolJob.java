package com.mpolicy.manage.jobhandler.protocol;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.protocol.entity.ProtocolBasicInfoEntity;
import com.mpolicy.manage.modules.protocol.enums.ProtocolStatusEnum;
import com.mpolicy.manage.modules.protocol.service.ProtocolBasicInfoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 协议相关定时任务
 *
 * <AUTHOR>
 * @date 2021-12-04 11:14:35
 */
@Component
@Slf4j
public class ProtocolJob {

    @Autowired
    private ProtocolBasicInfoService protocolBasicInfoService;

    /**
     * 协议过期和生效定时任务处理
     * todo....
     */
    @XxlJob("protocolExpireDelayJob")
    public void protocolExpireDelayJob(String param) throws Exception {
        DateTime now = DateUtil.beginOfDay(new Date());
        /***********************协议失效处理************************/
        try {
            protocolExpireDelayHandle(now);
        } catch (Exception e) {
            XxlJobHelper.handleFail("protocolExpireDelayHandle exception");
        }finally {
            XxlJobHelper.handleSuccess("协议失效处理任务完成");
        }
        /***********************协议生效处理************************/
        try {
            protocolEffectiveHandle(now);
        } catch (Exception e) {
            XxlJobHelper.handleFail("protocolEffectiveHandle exception");
        } finally {
            XxlJobHelper.handleSuccess("协议生效处理任务完成");
        }
    }

    /**
     * 协议生效处理
     *
     * @param date
     */
    private void protocolEffectiveHandle(Date date) {
        //获取开始时间小于等于当前时间且没有生效过得协议设置为生效中
        List<ProtocolBasicInfoEntity> protocolEffectiveList = protocolBasicInfoService.lambdaQuery()
                .eq(ProtocolBasicInfoEntity::getProtocolStatus, ProtocolStatusEnum.Ready_Status.getCode())
                .le(ProtocolBasicInfoEntity::getStartDate, date)
                .list();
        if (CollectionUtil.isEmpty(protocolEffectiveList)) {
            log.warn("未查到即将生效的协议 date= {}", DateUtils.format(date));
            return;
        }
        protocolEffectiveList.forEach(x -> {
            try {
                //设置协议生效
                protocolBasicInfoService.lambdaUpdate()
                        .set(ProtocolBasicInfoEntity::getProtocolStatus, ProtocolStatusEnum.Effect_Status.getCode())
                        .eq(ProtocolBasicInfoEntity::getProtocolCode, x.getProtocolCode())
                        .update();
            } catch (Exception e) {
                log.error("协议生效处理异常 data= {}", JSON.toJSONString(x));
            }
        });
    }

    /**
     * 协议失效处理
     *
     * @param date
     */
    private void protocolExpireDelayHandle(Date date) {
        List<ProtocolBasicInfoEntity> protocolBasicInfoEntityList = protocolBasicInfoService.lambdaQuery()
                .le(ProtocolBasicInfoEntity::getEndDate, date)
                .in(ProtocolBasicInfoEntity::getProtocolStatus,
                        CollUtil.newArrayList(
                                ProtocolStatusEnum.Effect_Status.getCode(),
                                ProtocolStatusEnum.Delay_Status.getCode())
                )
                .list();
        if (CollectionUtil.isEmpty(protocolBasicInfoEntityList)) {
            log.warn("未查到即将过期的协议 date= {}", DateUtils.format(date));
            return;
        }
        protocolBasicInfoEntityList.forEach(x -> {
            try {
                protocolBasicInfoService.lambdaUpdate()
                        .set(ProtocolBasicInfoEntity::getProtocolStatus, ProtocolStatusEnum.Expire_Status.getCode())
                        .notIn(ProtocolBasicInfoEntity::getProtocolStatus, CollUtil.newArrayList(
                                ProtocolStatusEnum.Expire_Status.getCode(),
                                ProtocolStatusEnum.Stop_Status.getCode()))
                        .and(a -> a.eq(ProtocolBasicInfoEntity::getProtocolCode, x.getProtocolCode())
                                .or()
                                .eq(ProtocolBasicInfoEntity::getParentProtocolNo, x.getProtocolCode()))
                        .update();
            } catch (Exception e) {
                log.error("协议过期异常 data= {}", JSON.toJSONString(x));
            }
        });
    }
}
