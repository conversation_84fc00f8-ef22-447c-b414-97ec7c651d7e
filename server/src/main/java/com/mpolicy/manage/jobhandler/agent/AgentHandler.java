package com.mpolicy.manage.jobhandler.agent;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.BiAgentInfoEntity;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.enums.AgentStatusEnum;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.BiAgentInfoService;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;
import com.mpolicy.manage.modules.sys.service.SysRegionInfoService;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgentHandler {

    @Autowired
    private AgentUserInfoService agentUserInfoService;

    @Autowired
    private BiAgentInfoService biAgentInfoService;

    @Autowired
    private OrgInfoService orgInfoService;

    @Autowired
    private SysRegionInfoService sysRegionInfoService;

    /**
     * 同步代理人信息到BiAgent数据库
     */
    @XxlJob("syncAgentToBiAgentInfo")
    public void syncAgentToBiAgentInfo() {

        try {
            XxlJobHelper.log("开始同步代理人信息到BI数据库");
            //获取代理人信息中最大的ID
            Integer maxId = agentUserInfoService.lambdaQuery()
                    .orderByDesc(AgentUserInfoEntity::getId)
                    .last("limit 1").one().getId();
            //删除数据 准备一会重新写入
            biAgentInfoService.lambdaUpdate().remove();
            //一次插入1000条
            int limit = 1000;
            int startId = 1;
            int endId = limit;
            while (startId < maxId) {
                try {
                    List<BiAgentInfoEntity> batchList = new ArrayList<>();
                    XxlJobHelper.log("开始处理代理人ID startId={},endId={}数据", startId, endId);
                    List<AgentUserInfoEntity> agentList = agentUserInfoService.lambdaQuery()
                            .between(AgentUserInfoEntity::getId, startId, endId)
                            .eq(AgentUserInfoEntity::getAgentStatus, AgentStatusEnum.VALID.getCode())
                            .list();
                    List<String> cityCodeList = agentList.stream().map(AgentUserInfoEntity::getCityCode).distinct().collect(Collectors.toList());
                    Map<String, SysRegionInfo> sysRegionInfoMap = sysRegionInfoService.lambdaQuery()
                            .in(SysRegionInfo::getCityCode, cityCodeList)
                            .list().stream()
                            .collect(Collectors.toMap(SysRegionInfo::getCityCode, v -> v,
                                    (v1, v2) -> v2));

                    Map<String, OrgInfoEntity> firstOrgInfoMap = orgInfoService.findFirstOrgInfo(agentList.stream().map(AgentUserInfoEntity::getOrgCode).distinct().collect(Collectors.toList()));
                    agentList.forEach(action -> {
                        BiAgentInfoEntity biAgentInfo = BeanUtil.copyProperties(action, BiAgentInfoEntity.class);
                        if (firstOrgInfoMap.containsKey(action.getOrgCode())) {
                            OrgInfoEntity orgInfo = firstOrgInfoMap.get(action.getOrgCode());
                            biAgentInfo.setFirstOrgName(orgInfo.getOrgName());
                            biAgentInfo.setFirstOrgCode(orgInfo.getOrgCode());
                        } else {
                            XxlJobHelper.log("代理人{}没有顶级机构,请检查", action.getAgentCode());
                            log.info("代理人{}没有顶级机构,请检查", action.getAgentCode());
                        }
                        if (sysRegionInfoMap.containsKey(action.getCityCode())) {
                            SysRegionInfo sysRegionInfo = sysRegionInfoMap.get(action.getCityCode());
                            biAgentInfo.setProvinceName(sysRegionInfo.getProvinceName());
                            biAgentInfo.setProvinceCode(sysRegionInfo.getProvinceCode());
                            biAgentInfo.setCityCode(sysRegionInfo.getCityCode());
                            biAgentInfo.setCityName(sysRegionInfo.getCityName());
                        }
                        biAgentInfo.setAcquisitionAreaDesc(DicCacheHelper.getValue(action.getAcquisitionArea()));
                        biAgentInfo.setAgentNatureDesc(DicCacheHelper.getValue(action.getAgentNature()));
                        biAgentInfo.setAgentTypeDesc(DicCacheHelper.getValue(action.getAgentType()));
                        batchList.add(biAgentInfo);
                    });
                    log.info("插入数据库数据:{}", JSONUtil.toJsonStr(batchList));
                    biAgentInfoService.saveBatch(batchList);
                } catch (Exception e) {
                    XxlJobHelper.log("代理人ID startId={},endId={}数据异常", startId, endId, e);
                } finally {
                    startId += limit;
                    endId += limit;
                }
            }

            XxlJobHelper.log("同步代理人信息到BI数据库已完成");
        } catch (Exception e) {
            XxlJobHelper.log("处理代理人数据到BI数据失败");
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail();
        }

    }
}
