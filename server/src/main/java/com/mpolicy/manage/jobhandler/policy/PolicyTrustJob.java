package com.mpolicy.manage.jobhandler.policy;

import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.common.service.impl.PublicBaseService;
import com.mpolicy.manage.modules.policy.service.PolicyUserRecordService;
import com.mpolicy.manage.modules.policy.vo.PolicyUserRecordMsgListVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClassName: PolicyTrustJob
 * Description: 保单托管定时任务
 * date: 2023/11/2 14:29
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PolicyTrustJob {

    @Autowired
    private PolicyUserRecordService policyUserRecordService;
    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;
    @Autowired
    private PublicBaseService publicBaseService;

    /**
     * 保单托管给客户经理发送信息
     * @throws Exception
     */
    @XxlJob("policyTrustSendMsg")
    public void policyTrustSendMsg() throws Exception {
        // 1. 统计保单托管信息列表
        List<PolicyUserRecordMsgListVo> list = policyUserRecordService.querySendMsgList();
        if(CollectionUtils.isEmpty(list)){
            log.info("无需要发送的托管信息数据");
            return;
        }
        // 2. 数据汇总
        Map<String, List<PolicyUserRecordMsgListVo>> collect = list.stream().collect(Collectors.groupingBy(PolicyUserRecordMsgListVo::getReferrerCode));
        // 3.发送数据
        collect.forEach((code,lists) ->{
            log.info("保单托管定时任务需发送客户经理referrerCode为:{}",code);
            if(StringUtils.isBlank(code)){
                return ;
            }
            ChannelApplicationReferrerEntity referrer = this.channelApplicationReferrerService.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerCode, code)
                    .eq(ChannelApplicationReferrerEntity::getReferrerServiceStatus, StatusEnum.INVALID.getCode()).one();
            if(Objects.isNull(referrer)){
                log.info("客户经理编码:{},已离职，无需发送保单托管信息");
                return ;
            }
            //发送数据
            Map<String, List<PolicyUserRecordMsgListVo>> idMap = lists.stream().collect(Collectors.groupingBy(PolicyUserRecordMsgListVo::getIdentificationNum));
            List<String> nameList = Lists.newArrayList();
            idMap.forEach((idCard,cardList)->{
                if(StringUtils.isBlank(idCard)){
                    return ;
                }
                nameList.add(cardList.get(0).getRealName());
            });
            String nameJoin = String.join("、", nameList);
            publicBaseService.whaleNotice(referrer.getReferrerWno(),null,nameJoin,"完成了保单托管信息上传","保单分析预计48小时内完成并公众号通知您",null);
        });
    }
}
