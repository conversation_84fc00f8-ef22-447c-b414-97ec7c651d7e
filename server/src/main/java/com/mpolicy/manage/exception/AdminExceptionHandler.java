package com.mpolicy.manage.exception;

import com.mpolicy.common.log.LogUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * admin异常统一处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/15 17:21
 */
@RestControllerAdvice
@Slf4j
public class AdminExceptionHandler {
    /**
     * 权限不足异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(UnauthorizedException.class)
    public Result unauthorizedExceptionHandler(UnauthorizedException e) {
        // 写入错误日志
        LogUtil.printLog(e, UnauthorizedException.class);
        return Result.error(BasicCodeMsg.AUTHORIZATION_ERROR.setMsg("权限不足"));
    }
}

