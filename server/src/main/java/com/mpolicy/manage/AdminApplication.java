package com.mpolicy.manage;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;

@Slf4j
@EnableAsync
@EnableDiscoveryClient
@EnableCircuitBreaker
@MapperScan(basePackages = {"com.mpolicy.**.dao"})
@SpringBootApplication
@ComponentScan(basePackages = {"com"}, excludeFilters = {@ComponentScan.Filter(type = FilterType.REGEX, pattern =
        {"com.aliyun.openservices.shade.com.alibaba.fastjson..*",
                "com.taobao.csp.third.com.alibaba.fastjson..*",
                "com.alibaba.csp.ahas.shaded.com.alibaba.fastjson..*",
                "com.aliyun.mse.shaded.com.alibaba.fastjson..*",
        })})
//调用aliyun的rocketmq  com.aliyun.openservices 又用shade在其他包下打了一个完整个fastjson包，包括@ControllerAdvice注解 ，全量扫 beanName 冲突了只能在启动类取消一下
@EnableFeignClients(basePackages = "com.mpolicy.**.client")
public class AdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
        log.info("管理后台启动成功");
    }
}
