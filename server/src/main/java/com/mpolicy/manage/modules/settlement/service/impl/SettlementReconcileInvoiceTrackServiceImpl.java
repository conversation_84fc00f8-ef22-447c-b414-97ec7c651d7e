package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInvoiceTrackDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceTrackEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceTrackService;
import org.springframework.stereotype.Service;

@Service("settlementReconcileInvoiceTrackService")
public class SettlementReconcileInvoiceTrackServiceImpl extends ServiceImpl<SettlementReconcileInvoiceTrackDao, SettlementReconcileInvoiceTrackEntity> implements SettlementReconcileInvoiceTrackService {


}
