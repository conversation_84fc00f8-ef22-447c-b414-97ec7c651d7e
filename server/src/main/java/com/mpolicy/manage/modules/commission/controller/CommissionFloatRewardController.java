package com.mpolicy.manage.modules.commission.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.commission.service.CommissionFloatRewardService;
import com.mpolicy.manage.modules.commission.vo.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 浮动奖励佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-22 14:10:56
 */
@RestController
@RequestMapping("commission/float/reward")
@Api(tags = "浮动奖励佣金配置")
public class CommissionFloatRewardController {

    @Autowired
    private CommissionFloatRewardService commissionFloatRewardService;


    @GetMapping("list")
    @RequiresPermissions("commission:float-reward:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<CommissionFloatRewardListOut>> list(CommissionFloatRewardListInput input) {
        PageUtils<CommissionFloatRewardListOut> page = commissionFloatRewardService.findCommissionFloatRewardList(input);
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @RequiresPermissions("commission:float-reward:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<CommissionFloatRewardInfoOut> info(@PathVariable(value = "id", required = false)
                                                     @NotNull(message = "操作的数据id不能为空")
                                                     @ApiParam(value = "详情ID") Integer id) {
        CommissionFloatRewardInfoOut commissionFloatReward = commissionFloatRewardService.findCommissionFloatRewardById(id);
        return Result.success(commissionFloatReward);
    }

    @SysLog("保存浮动奖励佣金配置数据")
    @PostMapping("save")
    @RequiresPermissions("commission:float-reward:save")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid CommissionFloatRewardSaveInput input) {
        commissionFloatRewardService.saveCommissionFloatReward(input);
        return Result.success();
    }

    @SysLog("修改浮动奖励佣金配置数据")
    @PostMapping("update")
    @RequiresPermissions("commission:float-reward:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid CommissionFloatRewardUpdateInput input) {
        commissionFloatRewardService.updateCommissionFloatRewardById(input);
        return Result.success();
    }

    @SysLog("修改浮动奖励佣金配置状态")
    @PostMapping("updateStatus")
    @RequiresPermissions("commission:float-reward:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result updateStatus(@RequestBody(required = false) @Valid CommissionFloatRewardStatusInput input) {
        commissionFloatRewardService.updateStatus(input);
        return Result.success();
    }

    @SysLog("删除浮动奖励佣金配置信息")
    @PostMapping("delete")
    @RequiresPermissions("commission:float-reward:delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false)
                         @NotEmpty(message = "删除的数据ids不能为空")
                         @ApiParam(value = "批量删除的ID") Integer[] ids) {
        commissionFloatRewardService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }
}
