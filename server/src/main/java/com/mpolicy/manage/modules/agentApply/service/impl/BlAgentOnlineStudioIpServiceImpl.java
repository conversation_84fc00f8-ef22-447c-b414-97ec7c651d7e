package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineStudioIpDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineStudioIpEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineStudioIpService;
import org.springframework.stereotype.Service;

/**
 * @Description 代理人线上入职工作室IP实现
 * @Date 15:37 2024/4/8
 * @Param 
 * <AUTHOR>
 * @return 
 **/
@Service("blAgentOnlineStudioIpService")
public class BlAgentOnlineStudioIpServiceImpl extends ServiceImpl<BlAgentOnlineStudioIpDao, BlAgentOnlineStudioIpEntity> implements BlAgentOnlineStudioIpService {

}
