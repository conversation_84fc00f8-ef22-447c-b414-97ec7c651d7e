package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/12/18 9:24 下午
 * @Version 1.0
 */

@Data
public class SettlementCostAutoInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 结佣月份
     */
    @ApiModelProperty(value = "结佣月份")
    private String settlementMonth;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String regionCode;

    /**
     * 分支
     */
    @ApiModelProperty(value = "分支")
    private String objectOrgCode;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String sendObjectCode;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    private Integer settlementStatus;

    /**
     * 结算机构
     */
    @ApiModelProperty(value = "结算机构")
    private String settlementInstitution;

    private String dynamicFlag;
}
