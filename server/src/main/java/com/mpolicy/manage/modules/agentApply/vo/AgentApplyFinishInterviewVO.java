package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ClassName: AgentApplyFinishInterview
 * Description: 代理人申请完成面试
 * date: 2022/11/30 12:30
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyFinishInterviewVO implements Serializable {

    @ApiModelProperty(value = "代理人编码",required = true)
    @NotBlank(message = "代理人编码不能为空")
    private String agentCode;
    /**
     * 面试申请表扫描件文件编码
     */
    @ApiModelProperty(value = "面试申请表扫描件文件编码",required = true)
    private String interviewUrlFileCode;
    /**
     * 面试申请表扫描件
     */
    @ApiModelProperty(value = "面试申请表扫描件",required = true)
    private String interviewUrl;
}
