package com.mpolicy.manage.modules.settlement.vo.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 对账单保单科目汇总信息
 *
 * <AUTHOR>
 * @since 2023/6/1 00:44
 */
@Data
@ApiModel(value = "对账单保单科目汇总信息", description = "对账单保单科目汇总信息")
public class SettlementReconcileConfirmSubject implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称", example = "首期年佣金")
    private String reconcileSubjectName;

    /**
     * 总保费
     */
    @ApiModelProperty(value = "总保费", example = "529.00")
    private BigDecimal totalPremium = BigDecimal.ZERO;

    /**
     * 总保费
     */
    @ApiModelProperty(value = "结算金额", example = "529.00")
    private BigDecimal settlementAmount = BigDecimal.ZERO;
}
