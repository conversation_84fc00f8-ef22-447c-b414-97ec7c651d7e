package com.mpolicy.manage.modules.settlement.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 支出端科目类型
 *
 * <AUTHOR>
 * @version 2023/08/24
 */
@Getter
public enum CostSubjectEnum {

    SHORT_PROMOTION("短险推广费","SHORT_PROMOTION","THIRD_PARTY"),
    LONG_PROMOTION("长险推广费暂发","LONG_PROMOTION","THIRD_PARTY"),
    LONG_REISSUE_PROMOTION("长险推广费补发","LONG_REISSUE_PROMOTION","THIRD_PARTY"),
    SUPERVISOR_PERFORMANCE("督导绩效暂发", "SUPERVISOR_PERFORMANCE","THIRD_PARTY"),
    SUPERVISOR_REISSUE_PERFORMANCE("督导绩效补发", "SUPERVISOR_REISSUE_PERFORMANCE","THIRD_PARTY"),
    PCO_ALLOWANCE("pco津贴", "PCO_ALLOWANCE","THIRD_PARTY"),
    PCO_PERFORMANCE("pco绩效暂发", "PCO_PERFORMANCE","THIRD_PARTY"),
    PCO_REISSUE_PERFORMANCE("pco绩效补发", "PCO_REISSUE_PERFORMANCE","THIRD_PARTY"),
    DIRECTOR_INCENTIVE("主任激励奖", "DIRECTOR_INCENTIVE","THIRD_PARTY"),
    AGRICULTURAL_MACHINERY_PERFORMANCE("农机险绩效", "AGRICULTURAL_MACHINERY_PERFORMANCE","THIRD_PARTY"),
    VEHICLE_PERFORMANCE("车险后台绩效", "VEHICLE_PERFORMANCE","THIRD_PARTY"),
    VEHICLE_VESSEL_TAX("车险车船税推广费", "VEHICLE_VESSEL_TAX","THIRD_PARTY"),
    LONG_NOT_RENEWAL_REBATE_COMM("长险未续回扣", "LONG_NOT_RENEWAL_REBATE_COMM","THIRD_PARTY"),
    LONG_RESTATEMENT_REISSUE_COMM("长险复效补发", "LONG_RESTATEMENT_REISSUE_COMM","THIRD_PARTY"),
    RURAL_PROXY("整村推进","RURAL_PROXY","THIRD_PARTY"),
    ISSUING_REGIONAL_MARKETING_FEE("代发区域营销费","ISSUING_REGIONAL_MARKETING_FEE","THIRD_PARTY"),
    ADD_COMM("加佣奖励", "ADD_COMM","THIRD_PARTY"),
    FIRST_BASIC_COMM("首续年佣金", "FIRST_BASIC_COMM","THIRD_PARTY"),
    DYNAMIC_SUBJECT("动态科目", "DYNAMIC_SUBJECT","THIRD_PARTY"),

    ;
    /**
     * 支出科目名称
     */
    private final String name;
    /**
     * 支出科目编码
     */
    private final String code;

    private final String grantCode;


    CostSubjectEnum(String name, String code,String grantCode) {
        this.name = name;
        this.code = code;
        this.grantCode = grantCode;
    }

    public static boolean contains(String code) {
        return Arrays.stream(CostSubjectEnum.values()).anyMatch(x -> Objects.equals(x.getCode(), code));
    }

}
