package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
@TableName("settlement_reconcile_invoice_mailbox")
public class SettlementReconcileInvoiceMailboxEntity  implements Serializable {
    private static final long serialVersionUID = -4208890466964494552L;

    private Integer id;

    /**
     * 发票申请编码
     */
    private String invoiceCode;
    /**
     * 邮箱
     */
    private String mailbox;

    /**
     * 是否默认邮箱
     */
    private Integer isDefault;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
