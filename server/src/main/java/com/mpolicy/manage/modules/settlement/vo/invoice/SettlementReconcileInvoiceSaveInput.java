package com.mpolicy.manage.modules.settlement.vo.invoice;

import com.mpolicy.manage.modules.settlement.entity.ApplyForInvoicingReconcileItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInvoiceSaveInput implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "请选择开票对账单")
    private List<ApplyForInvoicingReconcileItem> invoicingReconcileList;

    /**
     * 发票附件
     */
    private List<InvoiceEnclosureItem> invoiceEnclosureList;
    /**
     * 对账单类型
     */
    @NotNull(message = "对账单类型不能为空")
    private Integer reconcileType;
    /**
     * 开票申请信息/提前开票原因
     */
    private String invoiceMessage;

    /**
     * 开票组织编码
     */
    @ApiModelProperty(value = "开票组织编码")
    @NotBlank(message = "开票组织不能为空")
    private String invoicingOrgCode;

    @ApiModelProperty(value = "开票组织名称")
    @NotBlank(message = "开票组织不能为空")
    private String invoicingOrgName;

    @NotNull(message = "开票时点不能为空")
    private Integer invoiceMode;
    /**
     * 开票类型，1:普票，2:专票
     */
    @NotBlank(message = "开票类型不能为空")
    private String invoiceType;
    /**
     * 抬头类型；1:企业，2:机关事业单位，3:个人，4:其他'
     */
    @NotBlank(message = "开票抬头类型不能为空")
    private String invoiceTitleType;
    /**
     * 发票抬头
     */
    @NotBlank(message = "开票抬头不能为空")
    private String invoiceTitle;
    /**
     * 纳税人识别号
     */
    @NotBlank(message = "纳税人识别号不能为空")
    private String taxpayerNum;
    /**
     * 开户银行 增加规则 如果invoiceTitleType =1 则检验这个值不能为空
     */
    private String depositBank;
    /**
     * 银行账户 增加规则 如果invoiceTitleType =1 则检验这个值不能为空
     */
    private String bankAccount;
    /**
     * 公司电话 增加规则 如果invoiceTitleType =1 则检验这个值不能为空
     */
    private String companyPhone;
    /**
     * 公司地址 增加规则 如果invoiceTitleType =1 则检验这个值不能为空
     */
    private String companyAddress;
    /**
     * 邮箱地址
     */
    @NotBlank(message = "邮箱地址不能为空")
    private String mailAddress;
    /**
     * 是否需要邮寄；0:否，1:是
     */
    @NotBlank(message = "请选择是否需要邮寄")
    private String needMail;
    /**
     * 收件人姓名 needMail= 1 必填
     */
    private String receiverName;
    /**
     * 收件人电话 needMail= 1 必填
     */
    private String receiverPhone;
    /**
     * 收件人地址 needMail= 1 必填
     */
    private String receiverAddress;
    /**
     * PDF;OFD;XML
     */
    @NotBlank(message = "发票格式不能为空")
    private String invoiceFileType;
    /**
     * 开票时效要求
     */
    private String invoiceTimeRequire;
    /**
     * 备注
     */
    private String remark;

    @NotEmpty(message = "开票邮箱不能为空")
    private List<InvoiceMailboxVo> invoiceMailboxList;

    /**
     * 是否发送邮件
     */
    @NotNull(message = "是否需要发送邮件不能为空")
    private Integer isSendMailbox;

    private String invoicingRemark;

}
