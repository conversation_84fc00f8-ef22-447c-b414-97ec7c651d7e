package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgainReconcileVo implements Serializable {
    private static final long serialVersionUID = -4842313796131153063L;

    @NotNull(message = "上传的模版类型")
    @ApiModelProperty(value = "上传的模版类型", example = "0")
    private Integer reconcileCode;

    @NotNull(message = "保司模版类型")
    @ApiModelProperty(value = "保司模版类型", example = "0")
    private String templateCode;

    @NotBlank(message = "对账编码不能为空")
    @ApiModelProperty(value = "对账编码", example = "PR20221114180324452560")
    private String reconcileBillCode;

    @NotEmpty(message = "上传的文件不能为空")
    @ApiModelProperty(value = "上传的文件列表")
    private List<PoundageReconcileFileVo> fileList;
}
