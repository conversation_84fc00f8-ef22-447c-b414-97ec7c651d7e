package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanyInfoService;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 结算保司配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:32:26
 */
@Api(tags = "结算保司配置表")
@RestController
@RequestMapping("settlement/reconcile/company/info")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SettlementReconcileCompanyInfoController {

    private final SettlementReconcileCompanyInfoService settlementReconcileCompanyInfoService;


    @PostMapping("list")
    @RequiresPermissions(value = {"settlement:reconcile-contract-info:list"
            ,"settlement:reconcile-contract-info:info"
            ,"settlement:reconcile-contract-info:save"
            ,"settlement:reconcile-contract-info:update"
            ,"settlement:reconcile-contract-info:delete"
            ,"settlement:reconcile:company:info:save"},logical = Logical.OR)
    public Result<PageUtils<ReconcileCompanyInfoListOut>> list(@RequestBody @Valid ReconcileCompanyInfoListInput input) {
        PageUtils<ReconcileCompanyInfoListOut> page = settlementReconcileCompanyInfoService.queryPage(input);
        return Result.success(page);
    }

    /**
     * 新增结算保司信息
     *
     * @param params
     * @return
     */
    @PostMapping("save")
    @RequiresPermissions(value = {"settlement:reconcile-contract-info:list"
            ,"settlement:reconcile-contract-info:info"
            ,"settlement:reconcile-contract-info:save"
            ,"settlement:reconcile-contract-info:update"
            ,"settlement:reconcile-contract-info:delete"
            ,"settlement:reconcile:company:info:save"},logical = Logical.OR)
    @SysLog(value = "新增结算保司信息")
    public Result save(@RequestBody @Valid SaveReconcileCompanyInfo params) {
        settlementReconcileCompanyInfoService.saveReconcileCompanyInfo(params);
        return Result.success();
    }

    /**
     * 修改协议保司配置
     *
     * @param params
     * @return
     */
    @PostMapping("update")
    @SysLog(value = "修改协议保司配置")
    @RequiresPermissions(value = {"settlement:reconcile-contract-info:list"
            ,"settlement:reconcile-contract-info:info"
            ,"settlement:reconcile-contract-info:save"
            ,"settlement:reconcile-contract-info:update"
            ,"settlement:reconcile-contract-info:delete"
            ,"settlement:reconcile:company:info:save"},logical = Logical.OR)
    public Result update(@RequestBody @Valid UpdateReconcileCompanyInfo params) {
        settlementReconcileCompanyInfoService.updateReconcileCompanyInfo(params);
        return Result.success();
    }

    /**
     * 信息
     */
    @GetMapping("info/{id}")
    public Result<SettlementReconcileCompanyInfo> info(@PathVariable("id") Integer id) {
        SettlementReconcileCompanyInfo info = settlementReconcileCompanyInfoService.info(id);
        return Result.success(info);
    }

    /**
     * 删除
     */
    @PostMapping("delete/{id}")
    @SysLog(value = "删除协议保司配置")
    public Result<SettlementReconcileCompanyInfo> delete(@PathVariable("id") Integer id) {
        settlementReconcileCompanyInfoService.deleteReconcileCompanyInfo(id);
        return Result.success();
    }
}
