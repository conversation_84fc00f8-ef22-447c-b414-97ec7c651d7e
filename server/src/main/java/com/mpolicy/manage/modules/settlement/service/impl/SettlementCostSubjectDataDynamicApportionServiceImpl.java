package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostSubjectDataDynamicApportionDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApportionEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicApportionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 科目范围(动态科目)分摊明细
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
@Slf4j
@Service
public class SettlementCostSubjectDataDynamicApportionServiceImpl extends ServiceImpl<SettlementCostSubjectDataDynamicApportionDao, SettlementCostSubjectDataDynamicApportionEntity> implements SettlementCostSubjectDataDynamicApportionService {


}

