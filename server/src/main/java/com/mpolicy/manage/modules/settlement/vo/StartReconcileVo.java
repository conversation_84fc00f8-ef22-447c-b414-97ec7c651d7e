package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileFileEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StartReconcileVo implements Serializable {

    /**
     * 对账单文件
     */
    private List<SettlementReconcileFileEntity> settlementReconcileFileList;

    @NotBlank(message = "对账编码不能为空")
    private String reconcileCode;
}
