package com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/2/2 02:30
 * @Version 1.0
 */

@Data
public class DetailSummarySupervisorPerformance {
    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("所管辖团队推广费")
    private BigDecimal promotion;

    @ApiModelProperty("督导绩效提成比例")
    private BigDecimal commissionRate;

    @ApiModelProperty("督导长期险续期旅")
    private BigDecimal renewalRate;

    @ApiModelProperty("发放比例")
    private BigDecimal grantRate;

    @ApiModelProperty("发放金额")
    private BigDecimal grantAmount;
}
