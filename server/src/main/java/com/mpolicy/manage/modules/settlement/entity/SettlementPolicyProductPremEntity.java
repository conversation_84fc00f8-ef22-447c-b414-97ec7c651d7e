package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算保单险种费率
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-22 15:08:37
 */
@TableName("settlement_policy_product_prem")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementPolicyProductPremEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 费率编码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String premCode;

	/**
	 * 保单号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String policyNo;
	/**
	 * 批单号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String batchCode;

	/**
	 * 保单年期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer year;
	/**
	 * 缴费期次
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer period;
	/**
	 * 结算类型0:小鲸 1:非小鲸
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer reconcileType;
	/**
	 * 小鲸险种编码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String productCode;
	/**
	 * 小鲸险种名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String productName;
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String insuranceProductCode;
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String insuranceProductName;
	/**
	 * 费率
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal yearRate;

	/**
	 * 结算方式 按全保费(含税)结算 按净保费(税后)结算
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String settlementMethod;
	/**
	 * 税率
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal taxRate;
	/**
	 * 税前保费
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal premium;
	/**
	 * 税后保费
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal taxAfterPremium;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
