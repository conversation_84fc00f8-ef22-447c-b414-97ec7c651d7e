package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司结算对账单汇总表
 * 
 * <AUTHOR>
 * @date 2023-05-23 14:32:21
 */
@TableName("settlement_reconcile_confirm")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileConfirmEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 对账保单业务汇总明细编号
	 */
	private String billCode;

	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 对账单名称
	 */
	private String reconcileName;
	/**
	 * 对账月度
	 */
	private String reconcileMonth;
	/**
	 * 对账模式;线上对账、线下对账
	 */
	private String reconcileModel;
	/**
	 * 是否有效
	 */
	private Integer billEnable;
	/**
	 * 保险公司编码
	 */
	private String companyCode;
	/**
	 * 保险公司名称
	 */
	private String companyName;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 保单产品类型;个团车财
	 */
	private String policyProductType;
	/**
	 * 科目编码
	 */
	private String reconcileSubjectCode;
	/**
	 * 科目名称
	 */
	private String reconcileSubjectName;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 协议产品编码
	 */
	private String protocolProductCode;
	/**
	 * 协议产品名称
	 */
	private String protocolProductName;
	/**
	 * 计划编码
	 */
	private String planCode;
	/**
	 * 计划名称
	 */
	private String planName;
	/**
	 * 险种大类
	 */
	private String productGroup;

	/**
	 * 二级分类编码
	 */
	private String level2Code;
	/**
	 * 三级分类编码
	 */
	private String level3Code;

	/**
	 * 批单号
	 */
	private String endorsementNo;
	/**
	 * 获取批单号集合
	 */
	private String endorsementNoList;

	/**
	 * 保全生效时间
	 */
	private Date preservationEffectTime;

	/**
	 * 续期年期
	 */
	private Integer renewalYear;
	/**
	 * 续期期次
	 */
	private Integer renewalPeriod;
	/**
	 * 保额
	 */
	private BigDecimal coverage;
	/**
	 * 保额单位;保额单位，0：元，1：份，2：元/天
	 */
	private Integer coverageUnit;
	/**
	 * 保额单位名称
	 */
	private String coverageUnitName;
	/**
	 * 保障期间类型
	 */
	private String insuredPeriodType;
	/**
	 * 保障时长
	 */
	private Integer insuredPeriod;
	/**
	 * 缴费方式
	 */
	private String periodType;
	/**
	 * 缴费期间类型
	 */
	private String paymentPeriodType;
	/**
	 * 缴费时长
	 */
	private Integer paymentPeriod;
	/**
	 * 保费
	 */
	private BigDecimal premium;

	/**
	 * 保费差额
	 */
	private BigDecimal diffPremium;

	/**
	 * 险种总保费
	 */
	private BigDecimal productPremiumTotal;
	/**
	 * 小鲸结算费率
	 */
	private String xiaowhaleSettlementRate;
	/**
	 * 小鲸手续费金额
	 */
	private BigDecimal xiaowhaleAmount = BigDecimal.ZERO;
	/**
	 * 保司结算费率
	 */
	private String companySettlementRate;
	/**
	 * 保司手续费金额
	 */
	private BigDecimal companyAmount = BigDecimal.ZERO;
	/**
	 * 保司保费
	 */
	private BigDecimal companyPremium;
	/**
	 * 差异冲正金额
	 */
	private BigDecimal reversalAmount;
	/**
	 * 差额
	 */
	private BigDecimal diffAmount;

	/**
	 * 结算费率
	 */
	private String settlementRate;
	/**
	 * 结算金额
	 */
	private BigDecimal settlementAmount;
	/**
	 * 结算保费
	 */
	private BigDecimal settlementPremium;
	/**
	 * 是否为差异纪录 0否1是
	 */
	private Integer diffFlag;
	/**
	 * 系统差异类型
	 */
	private String diffType;

	/**
	 * 差异处理状态0待处理1已处理
	 */
	private Integer diffStatus = 0;
	/**
	 * 差异处理说明
	 */
	private String diffDesc;
	private Integer opeType;
	/**
	 * 平账原因
	 */
	private String diffWhy;
	/**
	 * 差异处理人员
	 */
	private String diffOpeUserName;
	/**
	 * 差异受理编号
	 */
	private String backlogCode;

	/**
	 * 对账数据来源;0保单明细-1小鲸导入
	 */
	private Integer billSource;
	/**
	 * 对账状态
	 */
	private Integer reconcileStatus;
	/**
	 * 标记状态0:未标记 1:已标记
	 */
	private Integer markStatus;

	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
