package com.mpolicy.manage.modules.settlement.pattern;

import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import org.springframework.beans.factory.InitializingBean;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class ReconcileTemplateHandler implements InitializingBean {

    /**
     * 读取模版内容返回固定格式的内容
     *
     * @param inputStream   文件
     * @param reconcileCode 对账单编码
     * @return 返回结果
     */
    public abstract List<ReconcileRuleFileTemplate> readFile(InputStream inputStream, String reconcileCode);

    /**
     * 获取当前使用的模版
     *
     * @return
     */
    public abstract ReconcileTemplateEnum getReconcileTemplate();

    /**
     * 储存模版信息
     *
     * @param reconcileTemplate 模版
     */
    public abstract void setReconcileTemplate(ReconcileTemplateEnum reconcileTemplate);
}