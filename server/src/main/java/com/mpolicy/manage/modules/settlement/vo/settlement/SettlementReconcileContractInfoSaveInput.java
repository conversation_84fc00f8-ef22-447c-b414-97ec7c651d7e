package com.mpolicy.manage.modules.settlement.vo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 结算合约配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-28 14:21:38
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileContractInfoSaveInput implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 结算保司名称
     */
    private String reconcileCompanyName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 内部签署方类型
     */
    private String innerSignatoryType;
    /**
     * 结算企业
     */
    private String settlementCompanyName;
    /**
     * 备注
     */
    private String remark;

}
