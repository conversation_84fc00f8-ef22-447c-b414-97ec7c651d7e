package com.mpolicy.manage.modules.agentApply.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * ClassName: AgentOnlineTypeEnum
 * Description: 代理人线上入职-增员人类型枚举
 * date: 2022/11/25 16:10
 *
 * <AUTHOR>
 */
@Getter
public enum AgentOnlineTypeEnum {
    // 专属保险顾问
    AGENT_TYPE1("AGENT:AGENT_TYPE:AGENT", "专属保险顾问"),
    // 渠道保险专家
    AGENT_TYPE2("AGENT:AGENT_TYPE:INSIDE", "渠道保险专家");

    private final String code;
    private final String name;

    AgentOnlineTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AgentOnlineTypeEnum getNameByCode(String code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
