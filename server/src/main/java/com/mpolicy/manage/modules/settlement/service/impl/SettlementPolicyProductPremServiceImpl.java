package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.PremChangeTypeEnum;
import com.mpolicy.manage.enums.SettlementMethodEnum;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductEntity;
import com.mpolicy.manage.modules.protocol.enums.SettlementProtocolEventEnum;
import com.mpolicy.manage.modules.protocol.helper.SettlementProtocolHelper;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductService;
import com.mpolicy.manage.modules.settlement.dao.SettlementPolicyProductPremDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductFeePremService;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductPremService;
import com.mpolicy.manage.modules.settlement.service.SettlementPremChangeLogService;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.utils.ProductPremUtil;
import generator.domain.SettlementPolicyProductFeePrem;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("settlementPolicyProductPremService")
public class SettlementPolicyProductPremServiceImpl
        extends ServiceImpl<SettlementPolicyProductPremDao, SettlementPolicyProductPremEntity>
        implements SettlementPolicyProductPremService {

    @Autowired
    private SettlementPolicyProductPremDao settlementPolicyProductPremDao;

    @Autowired
    private SettlementPremChangeLogService settlementPremChangeLogService;


    @Autowired
    private IEpProtocolInsuranceProductService protocolInsuranceProductService;

    /**
     * 上传一单一议保单费率
     *
     * @param input
     */
    @Override
    public void uploadPremiumFile(UploadPolicyProductPremInput input) {
        SysUserEntity userEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (userEntity == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("非法操作哦~"));
        }
        List<PolicyProductPremExcel> readAll;
        try{
            ExcelReader reader = ExcelUtil.getReader(input.getFile().getInputStream());
            readAll = reader.readAll(PolicyProductPremExcel.class);
        } catch(Exception e) {
            log.warn("读取费率表异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件出现异常"));
        }
        log.info("读取文件内容:{}", JSONUtil.toJsonStr(readAll));
        if (CollUtil.isEmpty(readAll)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件内容为空"));
        }
        //1.校验文件
        List<SettlementPolicyProductPremEntity> excePremList = new ArrayList<>();
        Map<String, Integer> map = new HashMap<>(1);
        // 获取协议产品编码集合
        Map<String, String> insuranceProductMap = new HashMap<>();
        List<String> insuranceProductCodeList = readAll.stream().map(PolicyProductPremExcel::getInsuranceProductCode).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(insuranceProductCodeList)) {
            Map<String, String> dbInsuranceProductMap = protocolInsuranceProductService.lambdaQuery()
                    .in(EpProtocolInsuranceProductEntity::getInsuranceProductCode, insuranceProductCodeList)
                    .eq(EpProtocolInsuranceProductEntity::getReconcileType, input.getReconcileType())
                    .list().stream().collect(Collectors.toMap(
                            EpProtocolInsuranceProductEntity::getInsuranceProductCode,
                            EpProtocolInsuranceProductEntity::getInsuranceProductName
                    ));
            insuranceProductCodeList.removeAll(new ArrayList<>(dbInsuranceProductMap.keySet()));
            if (CollUtil.isNotEmpty(insuranceProductCodeList)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保司协议险种编码匹配失败=" + JSONUtil.toJsonStr(insuranceProductCodeList)));
            }
            insuranceProductMap.putAll(dbInsuranceProductMap);
        }
        for (int i = 0; i < readAll.size(); i++) {
            PolicyProductPremExcel action = readAll.get(i);
            int rowInt = i + 2;
            if (StrUtil.isBlank(action.getPolicyNo())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + rowInt + "行保单号为空啦"));
            }
            if (StrUtil.isBlank(action.getSettlementMethod())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + rowInt + "行结算方式为空啦"));
            }
            if (action.getYearRate() == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + rowInt + "行费率为空啦"));
            }
            //  判断这个值在0-1之间
            if (action.getYearRate().compareTo(BigDecimal.ZERO) < 0
                    || action.getYearRate().compareTo(BigDecimal.ONE) > 0) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + rowInt + "行费率错误取值应[0~1]"));
            }

            // 判断如果是税后 需要计算税率 如果税率计算失败,提示给前端
            SettlementMethodEnum settlementMethodEnum =
                    SettlementMethodEnum.matchSearchDesc(action.getSettlementMethod());
            if (settlementMethodEnum == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + rowInt + "结算方式不合法"));
            }
            action.setSettlementMethod(settlementMethodEnum.getCode());
            if (SettlementMethodEnum.NET_PREMIUM == settlementMethodEnum) {
                // 1.判断税率或者 税前税后保费都都是为空
                if (StrUtil.isEmpty(action.getTaxRate()) && (action.getPremium() == null || action.getTaxAfterPremium() == null)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            "文件第" + rowInt + "税费或(税前保费（元）/税前后保费（元）两项其中一个条件满足)"));
                }
                if (StrUtil.isEmpty(action.getTaxRate())) {
                    try{
                        // 四舍五入保留4位小数
                        action.setTaxRate(
                                action.getPremium().divide(action.getTaxAfterPremium(), 4, RoundingMode.HALF_UP).toPlainString());
                    } catch(Exception e) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                "文件第" + rowInt + "税前保费（元）➗ 税前后保费（元）计算出错,请检查"));
                    }
                }
            }
            SettlementPolicyProductPremEntity settlementPolicyProductPrem = new SettlementPolicyProductPremEntity();
            settlementPolicyProductPrem.setPolicyNo(action.getPolicyNo());
            settlementPolicyProductPrem.setBatchCode(
                    StrUtil.isBlank(action.getBatchCode()) ? null : action.getBatchCode());
            settlementPolicyProductPrem.setYearRate(action.getYearRate());
            settlementPolicyProductPrem.setPremium(action.getPremium());
            settlementPolicyProductPrem.setTaxAfterPremium(action.getTaxAfterPremium());
            settlementPolicyProductPrem.setPremCode(action.getPremCode());
            settlementPolicyProductPrem.setProductCode(action.getProductCode());
            settlementPolicyProductPrem.setProductName(action.getProductName());
            settlementPolicyProductPrem.setReconcileType(input.getReconcileType());
            settlementPolicyProductPrem.setSettlementMethod(action.getSettlementMethod());
            if (StrUtil.isNotEmpty(action.getTaxRate())) {
                settlementPolicyProductPrem.setTaxRate(new BigDecimal(action.getTaxRate()));
            }
            settlementPolicyProductPrem.setPeriod(action.getPeriod());
            settlementPolicyProductPrem.setYear(action.getYear());
            if (StrUtil.isNotBlank(action.getInsuranceProductCode())) {
                settlementPolicyProductPrem.setInsuranceProductCode(action.getInsuranceProductCode());
                settlementPolicyProductPrem.setInsuranceProductName(insuranceProductMap.get(action.getInsuranceProductCode()));
            }
            // 税率为空的时候默认为1.06
            if (settlementPolicyProductPrem.getTaxRate() == null) {
                settlementPolicyProductPrem.setTaxRate(new BigDecimal("1.06"));
            }
            //费率匹配因子是唯一的
            String premCode = ProductPremUtil.getProtocolPolicyFactor(settlementPolicyProductPrem);
            if (map.containsKey(premCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("文件第{}行和文件第{}行数据重复,请检查", map.get(premCode), rowInt)));
            } else {
                map.put(premCode, rowInt);
            }
            settlementPolicyProductPrem.setPremCode(premCode);
            excePremList.add(settlementPolicyProductPrem);
        }
        List<String> policyNos = excePremList.stream().map(SettlementPolicyProductPremEntity::getPolicyNo).distinct()
                .collect(Collectors.toList());

        Map<String, SettlementPolicyProductPremEntity> policyPremMap =
                lambdaQuery().in(SettlementPolicyProductPremEntity::getPolicyNo, policyNos).list().stream()
                        .collect(Collectors.toMap(ProductPremUtil::getProtocolPolicyFactor, v -> v, (v1, v2) -> {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                    StrUtil.format("数据库中存在相同premCode他们的ID是[{}-{}]的数据,请联系开发人员检查", v1.getId(),
                                                   v2.getId()
                                    )));
                        }));
        // 新增费率信息
        List<SettlementPolicyProductPremEntity> insertPolicyPremList = new ArrayList<>();
        List<SettlementPolicyProductPremEntity> updatePolicyPremList = new ArrayList<>();
        List<String> removePolicyPremList = new ArrayList<>();
        excePremList.forEach(action -> {
            // 费率的匹配因子一样
            if (policyPremMap.containsKey(action.getPremCode())) {
                SettlementPolicyProductPremEntity settlementPolicyProductPrem = policyPremMap.get(action.getPremCode());
                //判断他们的费率信息是否一样,如果不一样才处理
                if (!ProductPremUtil.getProtocolPolicyRateCode(action)
                        .equals(ProductPremUtil.getProtocolPolicyRateCode(settlementPolicyProductPrem))) {
                    action.setId(settlementPolicyProductPrem.getId());
                    String premCode = ProductPremUtil.getProtocolPolicyPremCode(action);
                    action.setPremCode(premCode);
                    updatePolicyPremList.add(action);
                    removePolicyPremList.add(settlementPolicyProductPrem.getPremCode());
                }
            } else {
                // 匹配因子不一样直接进行追加
                String premCode = ProductPremUtil.getProtocolPolicyPremCode(action);
                action.setPremCode(premCode);
                insertPolicyPremList.add(action);
            }
        });

        //批量插入
        if (!insertPolicyPremList.isEmpty()) {
            settlementPolicyProductPremDao.insertBatchSomeColumn(insertPolicyPremList);
        }
        if (!updatePolicyPremList.isEmpty()) {
            // 批量更新数据
            updateBatchById(updatePolicyPremList);
        }
        if (removePolicyPremList.isEmpty() && updatePolicyPremList.isEmpty() && insertPolicyPremList.isEmpty()) {
            log.info("数据没有变更不处理.....");
            return;
        }

        //更新到税率信息表中
        updatePolicyTaxPremList(
                Stream.concat(updatePolicyPremList.stream(), insertPolicyPremList.stream()).collect(Collectors.toList())
                , userEntity
        );

        String businessCode = CommonUtils.createCodeLastNumber("PE");
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", userEntity.getUsername());
        //事件编码
        msgData.put("pushEventCode", businessCode);
        // 移除的保单数据
        msgData.put("removePolicyPremList", removePolicyPremList);
        // 修改的保单数据
        msgData.put("updatePolicyPremList", JSONObject.toJSONString(
                updatePolicyPremList.stream().map(SettlementPolicyProductPremEntity::getPremCode)
                        .collect(Collectors.toList())));
        // 新增的保单号数据
        msgData.put("insertPolicyPremList", JSONObject.toJSONString(
                insertPolicyPremList.stream().map(SettlementPolicyProductPremEntity::getPremCode)
                        .collect(Collectors.toList())));
        // 结算类型 0:协议 1:合约
        if (StatusEnum.INVALID.getCode().equals(input.getReconcileType())) {
            settlementPremChangeLogService.saveSettlementPremChangeLog(businessCode,
                                                                       PremChangeTypeEnum.PROTOCOL_POLICY.getCode(), businessCode, msgData.toJSONString()
            );
        } else {
            settlementPremChangeLogService.saveSettlementPremChangeLog(businessCode,
                                                                       PremChangeTypeEnum.CONTRACT_POLICY.getCode(), businessCode, msgData.toJSONString()
            );
        }
        SettlementProtocolHelper.pushSettlementProtocolEvent(businessCode,
                                                             input.getReconcileType().equals(StatusEnum.NORMAL.getCode())
                                                                     ? SettlementProtocolEventEnum.CONTRACT_POLICY_PREM_CHANGE
                                                                     : SettlementProtocolEventEnum.PROTOCOL_POLICY_PREM_CHANGE, msgData
        );
    }

    @Override
    public PageUtils<SettlementPolicyProductPremListOut> findSettlementPolicyProductPremList(
            SettlementPolicyProductPremListInput input) {
        IPage<SettlementPolicyProductPremListOut> page = baseMapper.findSettlementPolicyProductPremList(
                new Page<SettlementPolicyProductPremListOut>(input.getPage(), input.getLimit()), input);
        page.getRecords().forEach(action -> {
            action.setSettlementMethod(SettlementMethodEnum.matchSearchCode(action.getSettlementMethod()).getDesc());
        });
        return new PageUtils(page);
    }

    @Override
    public void savePolicyProductPrem(SavePolicyProductPrem params) {

    }

    @Override
    public void updatePolicyProductPrem(UpdatePolicyProductPrem params) {

    }

    @Override
    public SettlementPolicyProductPrem info(Integer id) {
        return null;
    }

    @Override
    public void deletePolicyProductPrem(Integer id) {
        SysUserEntity userEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (userEntity == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("非法操作哦~"));
        }
        //获取数据
        SettlementPolicyProductPremEntity settlementPolicyProductPrem = getById(id);
        // 删除数据
        removeById(id);
        String businessCode = CommonUtils.createCodeLastNumber("PE");
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", userEntity.getUsername());
        //事件编码
        msgData.put("pushEventCode", businessCode);
        // 移除的保单数据
        msgData.put(
                "removePolicyPremList",
                JSONObject.toJSONString(CollUtil.newArrayList(settlementPolicyProductPrem.getPremCode()))
        );
        // 修改的保单数据
        msgData.put("updatePolicyPremList", "[]");
        // 新增的保单号数据
        msgData.put("insertPolicyPremList", "[]");

        // 保存变更记录
        // 结算类型 0:协议 1:合约
        if (StatusEnum.INVALID.getCode().equals(settlementPolicyProductPrem.getReconcileType())) {
            settlementPremChangeLogService.saveSettlementPremChangeLog(businessCode,
                                                                       PremChangeTypeEnum.PROTOCOL_POLICY.getCode(), businessCode, msgData.toJSONString()
            );
        } else {
            settlementPremChangeLogService.saveSettlementPremChangeLog(businessCode,
                                                                       PremChangeTypeEnum.CONTRACT_POLICY.getCode(), businessCode, msgData.toJSONString()
            );
        }

        SettlementProtocolHelper.pushSettlementProtocolEvent(businessCode,
                                                             settlementPolicyProductPrem.getReconcileType().equals(StatusEnum.NORMAL.getCode())
                                                                     ? SettlementProtocolEventEnum.CONTRACT_POLICY_PREM_CHANGE
                                                                     : SettlementProtocolEventEnum.PROTOCOL_POLICY_PREM_CHANGE, msgData
        );

    }

    @Autowired
    private SettlementPolicyProductFeePremService policyProductFeePremService;

    public void updatePolicyTaxPremList(List<SettlementPolicyProductPremEntity> list, SysUserEntity userEntity) {

        policyProductFeePremService.updatePolicyTaxPremList(list, userEntity);


    }
}
