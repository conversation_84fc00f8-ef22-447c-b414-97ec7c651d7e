package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementCompanyOut implements Serializable {
    private static final long serialVersionUID = 2050738917402992766L;

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "编码")
    private String settlementCompanyCode;

    @ApiModelProperty(value = "账单生成日")
    private Integer billDay;

    @ApiModelProperty(value = "生效状态0:未生效 1:已生效", example = "0")
    private Integer effectiveStatus;

    @ApiModelProperty(value = "是否为月底最后一天 0:不是 1:是", example = "0")
    private Integer isMonthEnd;

    @ApiModelProperty(value = "保司编码")
    private String companyCode;

    @ApiModelProperty(value = "保司名称")
    private String companyName;

    @ApiModelProperty(value = "外部签署方名称")
    private String externalSignatoryName;

    @ApiModelProperty(value = "外部签署方类型")
    private String externalSignatoryType;

    @ApiModelProperty(value = "外部签署方类型描述")
    private String externalSignatoryTypeDesc;

    @ApiModelProperty(value = "内部签署方分支")
    private String innerSignatoryBranch;

    @ApiModelProperty(value = "内部签署方分支名称", example = "")
    private String innerSignatoryBranchName;
}
