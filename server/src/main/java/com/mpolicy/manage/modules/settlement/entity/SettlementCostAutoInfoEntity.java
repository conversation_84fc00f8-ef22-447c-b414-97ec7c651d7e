package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算支出端佣金信息表(拉横)
 * 
 * <AUTHOR>
 * @since 2023-12-18 20:03:47
 */
@TableName("settlement_cost_auto_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostAutoInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 结佣月份
	 */
	@ApiModelProperty(value = "结佣月份")
	private String settlementMonth;

	/**
	 * 结佣机构编码  当全部汇总是为空
	 */
	@ApiModelProperty(value = "结佣机构编码")
	private String settlementInstitution;

	/**
	 * 结佣机构名称
	 */
	@ApiModelProperty(value = "结佣机构名称")
	private String settlementInstitutionName;

	/**
	 * 区域编码
	 */
	@ApiModelProperty(value = "区域编码")
	private String regionCode;
	/**
	 * 区域
	 */
	@ApiModelProperty(value = "区域")
	private String regionName;

	/**
	 * 分支编码
	 */
	@ApiModelProperty(value = "分支编码")
	private String objectOrgCode;

	/**
	 * 分支
	 */
	@ApiModelProperty(value = "分支")
	private String objectOrgName;

	/**
	 * 工号
	 */
	@ApiModelProperty(value = "工号")
	private String sendObjectCode;
	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String sendObjectName;

	/**
	 * 短险推广费(实际月份：上月)
	 */
	@ApiModelProperty(value = "短险推广费(实际月份：上月)")
	private BigDecimal shortPromotion;

	/**
	 * 长险推广费暂发
	 */
	@ApiModelProperty(value = "长险推广费暂发(实际月份：上上月)")
	private BigDecimal longPromotion;

	/**
	 * 长险推广费补发 LONG_REISSUE_PROMOTION
	 */
	@ApiModelProperty(value = "长险推广费补发(实际月份：上上上月)")
	private BigDecimal longReissuePromotion;

	/**
	 * 加佣
	 */
	@ApiModelProperty(value = "加佣")
	private BigDecimal addComm;

	/**
	 * 督导绩效暂发
	 */
	@ApiModelProperty(value = "督导绩效暂发(上月)")
	private BigDecimal supervisorPerformance;

	/**
	 * 督导绩效补发
	 */
	@ApiModelProperty(value = "督导绩效补发(实际月份：上上上月)")
	private BigDecimal supervisorReissuePerformance;

	/**
	 * 津贴
	 */
	@ApiModelProperty(value = "津贴")
	private BigDecimal pcoAllowance;

	/**
	 * pco绩效暂发
	 */
	@ApiModelProperty(value = "pco绩效暂发(上月)")
	private BigDecimal pcoPerformance;

	/**
	 * pco绩效补发
	 */
	@ApiModelProperty(value = "pco绩效补发")
	private BigDecimal pcoReissuePerformance;

	/**
	 * 主任激励奖
	 */
	@ApiModelProperty(value = "主任激励奖")
	private BigDecimal directorIncentive;

	/**
	 * 农机险绩效
	 */
	@ApiModelProperty(value = "农机险绩效")
	private BigDecimal agriculturalMachineryPerformance;
	/**
	 * 车险后台绩效
	 */
	@ApiModelProperty(value = "车险后台绩效")
	private BigDecimal vehiclePerformance;

	/**
	 * 车险车船税推广费
	 */
	@ApiModelProperty(value = "车险车船税推广费")
	private BigDecimal vehicleVesselTax;
	/**
	 * 长险未续回扣
	 */
	@ApiModelProperty(value = "长险未续回扣")
	private BigDecimal longNotRenewalRebateComm;

	/**
	 * 长险复效补发
	 */
	@ApiModelProperty(value = "长险复效补发")
	private BigDecimal longRestatementReissueComm;

	/**
	 * 整村推进
	 */
	@ApiModelProperty(value = "整村推进")
	private BigDecimal ruralProxy;

	/**
	 * 代发区域营销费
	 */
	@ApiModelProperty(value = "代发区域营销费")
	private BigDecimal issuingRegionalMarketingFee;

	/**
	 * 自定义项目
	 */
	@ApiModelProperty(value = "自定义项目")
	private BigDecimal customSubject;





	/**
	 * 结算确认状态
	 */
	@ApiModelProperty(value = "结算确认状态 0未确认(未结算)，1已确认")
	private Integer confirmStatus;

	/**
	 * 确认结算时间
	 */
	@ApiModelProperty(value = "确认结算时间")
	private Date confirmTime;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
