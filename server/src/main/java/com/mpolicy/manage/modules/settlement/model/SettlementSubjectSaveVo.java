package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementSubjectSaveVo implements Serializable {
    private static final long serialVersionUID = 8846341998734482877L;

    @ApiModelProperty(value = "科目模版编码")
    private String subjectCode;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;


    @ApiModelProperty(value = "承保结束时间")
    private Integer approvedEndDay;

    @ApiModelProperty(value = "回执结束时间")
    private Integer receiptEndDay;

    @ApiModelProperty(value = "回访结束时间")
    private Integer revisitEndDay;

    @ApiModelProperty(value = "承保结束时间")
    private String approvedEndDayType;

    @ApiModelProperty(value = "回执结束时间")
    private String receiptEndDayType;

    @ApiModelProperty(value = "回访结束时间")
    private String revisitEndDayType;
}
