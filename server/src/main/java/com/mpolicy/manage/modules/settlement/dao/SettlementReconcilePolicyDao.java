package com.mpolicy.manage.modules.settlement.dao;

import com.mpolicy.manage.modules.settlement.entity.SettlementReconcilePolicyEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileDiffExcel;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 保司结算对账单关联数据
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcilePolicyDao extends ImsBaseMapper<SettlementReconcilePolicyEntity> {

    /**
     * 获取结算差异列表
     *
     * @param billCodeList
     * @return
     */
    List<SettlementReconcileDiffExcel> findSettlementReconcileDiffList(@Param("billCodeList") List<String> billCodeList);
}
