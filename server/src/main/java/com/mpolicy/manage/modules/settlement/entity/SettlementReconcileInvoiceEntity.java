package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */
@TableName("settlement_reconcile_invoice")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInvoiceEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 发票编码
     */
    private String invoiceCode;
    private Integer applyStatus;
    /**
     * 申请人
     */
    private String applyUserName;
    /**
     * 申请人用户ID
     */
    private Long applyUserId;
    /**
     * 红冲状态
     */
    private Integer redFlushStatus;

    /**
     * 开票组织编码
     */
    private String invoicingOrgCode;

    /**
     * 开票组织名称
     */
    private String invoicingOrgName;
    /**
     * 申请时间
     */
    private Date applyTime;
    private Integer pushCompanyStatus;
    /**
     * 开票方式 1:提前开票 0:正常开票
     */
    private Integer invoiceMode;
    /**
     * 对账单类型
     */
    private Integer reconcileType;
    /**
     * 开票进展0:开票申请
     */
    private Integer invoiceStep;
    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 开票申请信息/提前开票原因
     */
    private String invoiceMessage;
    /**
     * 开票类型，1:普票，2:专票
     */
    private String invoiceType;
    /**
     * 抬头类型；1:企业，2:机关事业单位，3:个人，4:其他'
     */
    private String invoiceTitleType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 纳税人识别号
     */
    private String taxpayerNum;
    /**
     * 开户银行
     */
    private String depositBank;
    /**
     * 银行账户
     */
    private String bankAccount;
    /**
     * 公司电话
     */
    private String companyPhone;
    /**
     * 公司地址
     */
    private String companyAddress;
    /**
     * 邮箱地址
     */
    private String mailAddress;
    /**
     * 是否需要邮寄；0:否，1:是
     */
    private String needMail;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人电话
     */
    private String receiverPhone;
    /**
     * 收件人地址
     */
    private String receiverAddress;
    /**
     * PDF;OFD;XML
     */
    private String invoiceFileType;
    /**
     * 开票时效要求
     */
    private String invoiceTimeRequire;
    /**
     * 备注
     */
    private String remark;

    // ------------------------- 财务审核信息 -------------------------
    /**
     * 财务审核状态
     */
    private Integer invoicingFinanceStatus;

    /**
     * 开票税率'
     */
    private BigDecimal invoicingTaxRate;

    /**
     * 开票项目编码
     */
    private String invoicingItemCode;

    /**
     * 开票项目名称
     */
    private String invoicingItemName;

    /**
     * 有无时间要求
     */
    private Integer invoicingTimeAsk;

    /**
     * '开票时间'
     */
    private Date invoicingTime;

    /**
     * 备注
     */
    private String invoicingRemark;

    /**
     * 开票意见
     */
    private String invoicingOpinion;

    /**
     * 操作人
     */
    private String invoicingOperateUser;

    /**
     * 操作时间
     */
    private Date invoicingOperateTime;


    // ------------------------- 系统开票 -------------------------

    /**
     * 系统开票回退原因
     */
    private String invoicingFallbackReason;
    /**
     * 系统开票状态状态 0:待开票 1:开票中 2:开票成功  3:开票失败 4:系统开票回退
     */
    private Integer invoicingSystemStatus;

    /**
     * 系统开票结果(开票成功 / 系统开票失败原因)
     */
    private String invoicingSystemResult;


    // ------------------------- 邮件配置 -------------------------
    /**
     * 邮箱主题
     */
    private String mailSubject;

    /**
     * 邮件内容
     */
    private String mailContent;

    // ------------------------- 快递信息 -------------------------
    /**
     * 快递状态 0:无需快递 1:未发送 2:已发送 ....
     */
    private Integer expressStatus;
    /**
     * 快递名称
     */
    private String expressName;

    /**
     * 运单号
     */
    private String expressWaybillNumber;

    /**
     * 是否发送邮件
     */
    private Integer isSendMailbox;
    /**
     * 发送邮件状态0:不需要发送 1:需要发送没有发送 2:已发送
     */
    private Integer sendMailboxStatus;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;

    /**
     * 数电号
     */
    @ApiModelProperty(value = "数电号")
    private String blueElectricCode;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    private String invoiceDate;
}
