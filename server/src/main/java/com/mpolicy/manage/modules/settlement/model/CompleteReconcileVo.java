package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CompleteReconcileVo implements Serializable {
    private static final long serialVersionUID = -7794990194442835144L;

    @NotBlank(message = "对账编码不能为空")
    @ApiModelProperty(value = "对账编码", example = "PR20221114180324452560")
    private String reconcileBillCode;
}
