package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.settlement.service.SettlementProgrammeService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeSubjectVo;
import com.netflix.discovery.converters.Auto;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 结算方案相关控制器
 * @date 2023/11/28 2:38 下午
 * @Version 1.0
 */@RestController
@RequestMapping("/settlement/programme")
@Api(tags = "佣金结算")
@Slf4j
public class SettlementProgrammeController {
     @Autowired
     private SettlementProgrammeService settlementProgrammeService;

    @PostMapping("/listProgrammeSubjects")
    public Result<List<SettlementProgrammeSubjectVo>> listProgrammeSubjects(@RequestParam("programmeCode") String programmeCode) {
        return Result.success(settlementProgrammeService.listProgrammeSubject(programmeCode));
    }
}
