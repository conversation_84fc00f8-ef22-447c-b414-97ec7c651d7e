package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileConfirmEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.manage.modules.settlement.vo.manage.*;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 保司结算对账单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
public interface SettlementReconcileInfoService extends IService<SettlementReconcileInfoEntity> {

    /**
     * 获取对账单基本信息
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileInfo
     * <AUTHOR>
     * @since 2023/7/3
     */
    SettlementReconcileInfo querySettlementReconcileInfo(String reconcileCode);

    /**
     * 分页查询保单对账单管理列表
     *
     * @param paramMap 查询条件
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileInfo>
     * <AUTHOR>
     * @since 2023/5/25 01:05
     */
    PageUtils<SettlementReconcileInfo> querySettlementReconcileInfoList(Map<String, Object> paramMap);


    /**
     * 获取对账单差异列表信息
     *
     * @param reconcileCode 保司对账单唯一编号
     * @param paramMap      paramMap
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff>
     * <AUTHOR>
     * @since 2023/5/25 01:13
     */
    PageUtils<SettlementReconcileDiff> querySettlementReconcileDiffList(String reconcileCode, Map<String, Object> paramMap);

    /**
     * 开始对账
     *
     * <AUTHOR>
     * @since 2023/5/25 01:17
     */
    String startReconcile(StartReconcileVo input);

    /**
     * 对账单文件列表
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return java.util.List<com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileFile>
     * <AUTHOR>
     * @since 2023/5/30 15:08
     */
    List<SettlementReconcileFile> reconcileFileList(String reconcileCode);

    /**
     * 上传对账单文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileTemplate 对账单模版类型
     * @param fileCode          文件编码
     * @param userName          操作人
     * <AUTHOR>
     * @since 2023/5/22 20:43
     */
    void uploadCompanyReconcileFile(String reconcileCode, ReconcileTemplateEnum reconcileTemplate, String fileCode, String userName);

    /**
     * 重新上传对账单文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileTemplate 对账单模版类型
     * @param sourceFileCode    原文件编码
     * @param fileCode          文件编码
     * @param userName          操作人
     * <AUTHOR>
     * @since 2023/5/22 21:11
     */
    void retryUploadCompanyReconcileFile(String reconcileCode, ReconcileTemplateEnum reconcileTemplate, String sourceFileCode, String fileCode, String userName);

    /**
     * 删除对账单规则文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileFileCode 文件编码
     * @param userName          操作人
     * <AUTHOR>
     * @since 2023/5/22 21:13
     */
    void removeReconcileFile(String reconcileCode, String reconcileFileCode, String userName);

    /**
     * 对账单批量调整精度
     *
     * @param input    批量处理精度信息
     * @param userName 操作人
     * <AUTHOR>
     * @since 2023/5/25 17:51
     */
    String reconcileAmountAccuracy(AmountAccuracyInput input, String userName);

    /**
     * 重新对账
     *
     * @param reconcileCode 保司对账单唯一编号
     * @param userName      操作人
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 01:37
     */
    String retryStartReconcile(String reconcileCode, String userName);

    /**
     * 完成对账
     *
     * @param reconcileCode 保司对账单唯一编号
     * @param userName      操作人
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 18:47
     */
    String finishReconcile(String reconcileCode, String userName);

    /**
     * 差异申请
     *
     * @param diffBacklogInput 差异处理请求对象
     * @param userName         操作人
     * <AUTHOR>
     * @since 2023/5/24 22:04
     */
    void diffBacklog(ReconcileDiffBacklogInput diffBacklogInput, String userName);

    /**
     * 对账单保单科目列表信息集合
     *
     * @param reconcileCode 保司对账单唯一编号
     * @param paramMap      paramMap
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileConfirm>
     * <AUTHOR>
     * @since 2023/6/1 01:00
     */
    PageUtils<SettlementReconcileConfirm> confirmReconcileList(String reconcileCode, Map<String, Object> paramMap);

    /**
     * 对账单保单科目汇总信息
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileConfirmSummary
     * <AUTHOR>
     * @since 2023/6/1 01:02
     */
    SettlementReconcileConfirmSummary reconcileOverview(String reconcileCode);

    /**
     * 刷新对账单保单数据
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作人
     * <AUTHOR>
     * @since 2023/6/27
     */
    String reconcileRefreshPolicy(String reconcileCode, String userName);

    /**
     * 关闭对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作人
     * <AUTHOR>
     * @since 2023/6/27
     */
    void closeReconcile(String reconcileCode, String userName);

    /**
     * 打包导出保司对账单
     *
     * @param reconcileCode 对账编码
     * @param response
     */
    void exportReconcileFile(String reconcileCode, HttpServletResponse response);

    /**
     * 导出缺失保单
     *
     * @param input
     * @return
     */
    List<ExportDeletionPolicyOut> exportDeletionPolicy(ExportDeletionPolicyVo input);

    /**
     * 获取差异详情
     *
     * @param billCode
     * @return
     */
    SettlementReconcileDiff querySettlementReconcileDiffInfo(String billCode);

    /**
     * 获取溯源信息
     *
     * @param billCode
     * @return
     */
    TraceSourceOut findTraceSource(String billCode);

    /**
     * 刷新定时任务
     *
     * @param input
     */
    void refreshSettlementEventJob(RefreshJobVo input);

    /**
     * 刷新明细
     *
     * @param input
     */
    void handleSettlementPolicyInfo(RefreshPolicyVo input);

    /**
     * 批量刷新保费费率
     *
     * @param reconcileCode
     */
    String batchRefreshRate(String reconcileCode);

    /**
     * 设置标记状态
     *
     * @param billCode 编码
     */
    void updateMarkStatus(String billCode);

    /**
     * 创建对账单
     *
     * @param vo
     */
    void createReconcile(CreateReconcileVo vo);

    /**
     * 设置延期下个月
     *
     * @param input
     */
    void nextPostponedMonth(NextPostponedMonthVo input);

    /**
     * 获取导出的差异明细数据
     *
     * @param settlementReconcileInfo 结算信息
     * @param records                 确认单记录
     * @return
     */
    List<SettlementReconcileDiffExcel> exportSettlementReconcileDiffList(SettlementReconcileInfoEntity settlementReconcileInfo, List<SettlementReconcileConfirmEntity> records);

    void batchDelayReconcile(ReconcileBatchDelayInput delayInput);

    void batchNeedlessReconcile(BatchNeedlessReconcileInput needlessReconcileInput);

    void batchRefreshFailEvent(List<String> billCodeList);

    /**
     * 设置挂起数据
     * @param input 请求参数
     */
    void hangUp(ReconcileHangUpVo input);

    /**
     * 取消数据挂起
     * @param input 请求参数
     */
    void unHangUp(ReconcileHangUpVo input);

    /**
     * 处理仅报送保费数据
     * @param input 请求参数
     */
    void handleOnlySubmitPremium(ReconcileHangUpVo input);

    /**
     * 设置对账单为待对账
     * @param input 请求参数
     */
    void setToBeReconciled(ReconcileHangUpVo input);
}

