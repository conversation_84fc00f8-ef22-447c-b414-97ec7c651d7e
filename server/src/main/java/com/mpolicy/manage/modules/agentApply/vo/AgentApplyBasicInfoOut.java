package com.mpolicy.manage.modules.agentApply.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * ClassName: AgentApplyBasicInfoOut
 * Description:
 * date: 2022/11/30 11:08
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyBasicInfoOut implements Serializable {
    private static final long serialVersionUID = 3279024360461805807L;

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty("头像信息")
    private String avatar;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    @Length(max = 10,message = "姓名不能超过10个字符")
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]{1,10})$", message = "姓名只能为汉字")
    private String agentName;
    /**
     * 姓名拼音
     */
    @ApiModelProperty(value = "姓名拼音")
    private String pinyin;

    @ApiModelProperty("代理人昵称")
    private String agentNickName;
    /**
     * 性别:0:女 1:男 -1:未知
     */
    @ApiModelProperty(value = "性别:0:女 1:男 -1:未知", required = true)
    private Integer gender;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", required = true)
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "联系电话格式不正确")
    private String mobile;

    /**
     * 证件类型 0:身份证  1:其他
     */
    @ApiModelProperty(value = "证件类型", required = true)
    @NotBlank(message = "证件类型不能为空")
    private String idType;
    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号", required = true)
    @NotBlank(message = "身份证号码不能为空")
    @Pattern(regexp = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X|x)$", message = "身份证号格式不正确")
    private String idCard;
    /**
     * 证件开始时间
     */
    @ApiModelProperty(value = "证件开始时间", required = true)
    @JSONField(format = "yyyy-MM-dd")
    @NotNull(message = "身份证有效开始时间不能为空")
    private String idStartDate;
    /**
     * 证件结束时间
     */
    @ApiModelProperty(value = "证件结束时间")
    @JSONField(format = "yyyy-MM-dd")
    private String idEndDate;
    /**
     * 身份证是否长期有效0:否 1:是
     */
    @ApiModelProperty(value = "身份证是否长期有效0:否 1:是")
    private boolean idLongTerm;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    private String birthday;
    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型", required = true)
    @NotBlank(message = "人员类型不能为空")
    private String agentType;
    /**
     * 入职方式
     */
    @ApiModelProperty(value = "入职方式")
    private String entryType;
    /**
     * 入职日期
     */
    @ApiModelProperty(value = "入职日期")
    @JSONField(format = "yyyy-MM-dd")
    private String entryDate;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位", required = true)
    @NotBlank(message = "职位不能为空")
    private String position;
    /**
     * 职位等级
     */
    @ApiModelProperty(value = "职位等级")
    private String positionDegree;

    @ApiModelProperty(value = "是否选为专属顾问")
    private Integer isOptional;
    /**
     * 学历
     */
    @ApiModelProperty(value = "学历", required = true)
    @NotBlank(message = "学历不能为空")
    private String degree;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 婚姻状态
     */
    @ApiModelProperty("婚姻状态")
    @NotBlank(message = "婚姻状态不能为空")
    private String marital;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌", required = true)
    @NotBlank(message = "政治面貌不能为空")
    private String politics;
    /**
     * 民族
     */
    @ApiModelProperty(value = "民族", required = true)
    @NotBlank(message = "民族不能为空")
    private String nation;
    /**
     * 毕业学校
     */
    @ApiModelProperty(value = "毕业学校", required = true)
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]{0,50})$", message = "毕业学校只能为汉字")
    private String school;
    /**
     * 微信账号(非微信昵称)
     */
    @ApiModelProperty(value = "微信账号(非微信昵称)", required = true)
    @NotBlank(message = "微信账号不能为空")
    private String weChat;
    /**
     * 从业年限
     */
    @ApiModelProperty(value = "从业年限", required = true)
    @Pattern(regexp = "^[0-9]*$", message = "保险行业从业年数只能为数字")
    private String workTime;
    /**
     * 初始化服务人数
     */
    @ApiModelProperty(value = "初始化服务人数", required = true)
    private Integer initServiceNum;

    @ApiModelProperty("服务属性")
    private String serviceAttribute;
    /**
     * 擅长领域
     */
    @ApiModelProperty(value = "擅长领域")
    private String expertise;
    /**
     * 是否完成公司面试(0:已完成 -1:未完成)
     */
    @ApiModelProperty(value = "是否完成公司面试(0:已完成 -1:未完成)")
    private Integer isFinish;
    /**
     * 面试日期
     */
    @ApiModelProperty(value = "面试日期")
    @JSONField(format = "yyyy-MM-dd")
    private String interview;
    /**
     * 面试申请表扫描件文件编码
     */
    @ApiModelProperty(value = "面试申请表扫描件文件编码")
    private String interviewUrlFileCode;
    /**
     * 面试申请表扫描件
     */
    @ApiModelProperty(value = "面试申请表扫描件")
    private String interviewUrl;
    /**
     * 身份证正面图片文件编码
     */
    @ApiModelProperty(value = "身份证正面图片文件编码")
    private String idCardFrontFileCode;
    /**
     * 身份证正面图片
     */
    @ApiModelProperty(value = "身份证正面图片")
    private String idCardFront;
    /**
     * 身份证反面图片文件编码
     */
    @ApiModelProperty(value = "身份证反面图片文件编码")
    private String idCardBackFileCode;
    /**
     * 身份证反面图片
     */
    @ApiModelProperty(value = "身份证反面图片")
    private String idCardBack;
    /**
     * 标准证件照片文件编码
     */
    @ApiModelProperty(value = "标准证件照片文件编码")
    private String certificatesPhoneFileCode;
    /**
     * 标准证件照片
     */
    @ApiModelProperty(value = "标准证件照片")
    private String certificatesPhone;
    /**
     * 银行卡正面照片文件编码
     */
    @ApiModelProperty(value = "银行卡正面照片文件编码")
    private String bankCardPhoneFileCode;
    /**
     * 银行卡正面照片
     */
    @ApiModelProperty(value = "银行卡正面照片")
    private String bankCardPhone;
    /**
     * 最高学历毕业证照片文件编码
     */
    @ApiModelProperty(value = "最高学历毕业证照片文件编码")
    private String degreePhoneFileCode;
    /**
     * 最高学历毕业证照片
     */
    @ApiModelProperty(value = "最高学历毕业证照片")
    private String degreePhone;
    /**
     * 增员人编码
     */
    @ApiModelProperty(value = "增员人编码")
    private String recruitCode;
    /**
     * 申请状态(1:已提交 2:完成面试 3:待完成考试 4:待线上签约 5:待审核 6:待复核 7:待修改信息 8:已完成)
     */
    @ApiModelProperty(value = "申请状态(1:已提交 2:完成面试 3:待完成考试 4:待线上签约 5:待审核 6:待复核 7:待修改信息 8:已完成)")
    private Integer status;

    /**
     * 更新日期
     */
    @ApiModelProperty(value = "申请日期")
    @JSONField(format = "yyyy-MM-dd")
    private String updateTime;

    @ApiModelProperty(value = "版本号",required = true)
    @NotNull(message = "版本号不能为空")
    private long revision;

    @ApiModelProperty(value = "业务编码", required = true)
    @NotBlank(message = "业务编码不能为空")
    private String businessCode;

    @ApiModelProperty(value = "是否推荐", required = true)
    @NotNull(message = "是否推荐不能为空")
    private Integer recommendStatus;

    @ApiModelProperty(value = "国籍", required = true)
    @NotBlank(message = "国籍不能为空")
    private String country;

    @ApiModelProperty(value = "人员类别", required = true)
    @NotBlank(message = "人员类别不能为空")
    private String agentCategory;

    @ApiModelProperty(value = "人员性质", required = true)
    @NotBlank(message = "人员性质不能为空")
    private String agentNature;

    @ApiModelProperty(value = "居住省份", required = true)
    private String liveProvinceCode;

    @ApiModelProperty(value = "居住城市", required = true)
    private String liveCityCode;

    @ApiModelProperty(value = "居住地区", required = true)
    @NotBlank(message = "居住地区不能为空")
    private String areaCode;

    @ApiModelProperty(value = "服务省份", required = true)
    private String serverProvince;

    @ApiModelProperty(value = "服务城市", required = true)
    private String cityCode;

    @ApiModelProperty("展业地区")
    private String acquisitionArea;

    @ApiModelProperty("个人标签")
    private String agentLabel;
}