package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostProgrammeRecordDao;
import com.mpolicy.manage.modules.settlement.dao.SettlementProgrammeSubjectDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeRecordEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeSubjectEntity;
import com.mpolicy.manage.modules.settlement.enums.ConfirmStatusEnum;
import com.mpolicy.manage.modules.settlement.service.SettlementCostProgrammeRecordService;
import com.mpolicy.manage.modules.settlement.service.SettlementCostProgrammeSubjectService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeRecordVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeSubjectVo;
import com.mpolicy.manage.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 2:45 下午
 * @Version 1.0
 */
@Slf4j
@Service
public class SettlementCostProgrammeRecordServiceImpl extends ServiceImpl<SettlementCostProgrammeRecordDao, SettlementCostProgrammeRecordEntity> implements SettlementCostProgrammeRecordService {

    @Override
    public SettlementProgrammeRecordVo getProgrammeRecord(String programmeCode,String costSettlementCycle){

        SettlementCostProgrammeRecordEntity entity =  this.lambdaQuery().eq(SettlementCostProgrammeRecordEntity::getProgrammeCode,programmeCode)
                .eq(SettlementCostProgrammeRecordEntity::getCostSettlementCycle,costSettlementCycle)
                .eq(SettlementCostProgrammeRecordEntity::getDeleted,0)
                .one();
        if(Objects.isNull(entity)){
            return null;
        }
        SettlementProgrammeRecordVo vo = new SettlementProgrammeRecordVo();
        BeanUtils.copyProperties(entity,vo);
        return vo;
    }
}
