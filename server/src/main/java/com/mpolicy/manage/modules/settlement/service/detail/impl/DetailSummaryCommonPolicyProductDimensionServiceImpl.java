package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsPolicyProductDimensionService;
import com.mpolicy.manage.modules.settlement.service.detail.BaseDynamicShowType;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryCommon;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/26 10:14
 * @Version 1.0
 */
@Service
public class DetailSummaryCommonPolicyProductDimensionServiceImpl extends AbsPolicyProductDimensionService<List<DetailSummaryCommon>> implements BaseDynamicShowType {
    @Override
    public String dynamicShowType() {
        return "policy_product";
    }

    @Override
    public List<DetailSummaryCommon> querySummary(PageSettlementDetailParams p) {
        BigDecimal summaryCommon = costAutoInfoDao.shortSummary(p);
        DetailSummaryCommon r = new DetailSummaryCommon();
        r.setCostAmount(summaryCommon);
        r.setMonth(calLastMonth(p));
        return Lists.newArrayList(r);
    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Collections.singletonList(CostSubjectEnum.DYNAMIC_SUBJECT);
    }
}
