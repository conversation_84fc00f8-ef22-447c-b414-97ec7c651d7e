package com.mpolicy.manage.modules.endorsement.controller;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.endorsement.service.EndorsementOrderInfoService;
import com.mpolicy.manage.modules.endorsement.vo.EndorsementOrderInfo;
import com.mpolicy.manage.modules.endorsement.vo.EndorsementRecallOut;
import com.mpolicy.order.common.endorsement.PolicyEndorsementOrderData;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;


/**
 * 保全订单信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:10:55
 */
@RestController
@RequestMapping("endorsement/order")
@Api(tags = "保全订单信息表")
@Slf4j
public class EndorsementOrderInfoController {

    @Autowired
    private EndorsementOrderInfoService endorsementOrderInfoService;


    /**
     * 列表
     */
    @ApiOperation(value = "保全订单列表", notes = "保全订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "referrerName", dataType = "String", value = "推荐人", example = "张三"),
            @ApiImplicitParam(paramType = "query", name = "holderName", dataType = "String", value = "投保人姓名", example = "张三"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保司编码", example = "AHFGT522"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间", example = "2024-01-08"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间", example = "2024-01-08"),
            @ApiImplicitParam(paramType = "query", name = "endorsementStatus", dataType = "int", value = "订单状态", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "endorsementCode", dataType = "String", value = "保全编号", example = "ENS20240103142932045533"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "商品编码", example = "P123424323424"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号", example = "HL1100001093018290"),
            @ApiImplicitParam(paramType = "query", name = "policyCode", dataType = "String", value = "批单号", example = "BS20240108160300719731"),
            @ApiImplicitParam(paramType = "query", name = "endorsementType", dataType = "String", value = "保全类型", example = "ENDORSEMENT_TYPE:1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    public Result<PageUtils<EndorsementOrderInfo>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        PageUtils<EndorsementOrderInfo> page = endorsementOrderInfoService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 保全订单详情
     *
     * @param endorsementCode:保全编号
     * @return : com.mpolicy.common.result.Result<PolicyEndorsementOrderData>
     * <AUTHOR>
     * @date 2024/1/11 15:38
     */
    @ApiOperation(value = "保全订单详情", notes = "保全订单详情")
    @GetMapping("/detail/{endorsementCode}")
    public Result<PolicyEndorsementOrderData> detail(@PathVariable("endorsementCode") @ApiParam(name = "endorsementCode", value = "保全编号") String endorsementCode) {
        return Result.success(endorsementOrderInfoService.queryEndorsementDetail(endorsementCode));
    }

    /**
     * 保全订单回溯信息
     *
     * @param endorsementCode: 保全编号
     * @return : com.mpolicy.common.result.Result<com.mpolicy.manage.modules.endorsement.vo.EndorsementRecallOut>
     * <AUTHOR>
     * @date 2024/1/11 15:48
     */
    @ApiOperation(value = "保全订单回溯信息", notes = "保全订单回溯信息")
    @GetMapping("/recall_data/{endorsementCode}")
    public Result<EndorsementRecallOut> recallData(@PathVariable("endorsementCode") @ApiParam(name = "endorsementCode", value = "保全编号") String endorsementCode) {
        return Result.success(endorsementOrderInfoService.queryEndorsementRecallData(endorsementCode));
    }

    /**
     * 导出
     */
    @ApiOperation(value = "保全订单列表导出", notes = "保全订单列表导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "referrerName", dataType = "String", value = "推荐人", example = "张三"),
            @ApiImplicitParam(paramType = "query", name = "holderName", dataType = "String", value = "开始时间", example = "张三"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保司编码", example = "AHFGT522"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间", example = "2024-01-08"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间", example = "2024-01-08"),
            @ApiImplicitParam(paramType = "query", name = "endorsementStatus", dataType = "int", value = "订单状态", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "endorsementCode", dataType = "String", value = "保全编号", example = "ENS20240103142932045533"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "商品编码", example = "P123424323424"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号", example = "HL1100001093018290"),
            @ApiImplicitParam(paramType = "query", name = "policyCode", dataType = "String", value = "批单号", example = "BS20240108160300719731"),
            @ApiImplicitParam(paramType = "query", name = "endorsementType", dataType = "String", value = "保全类型", example = "ENDORSEMENT_TYPE:1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        log.info("订单导出生成 .....开始");
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode("保全订单.xlsx"));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, EndorsementOrderInfo.class);
            sheet.setSheetName("sheet1");

            int page = 1;
            params.put("limit", "2000");
            while (true) {
                // 分批查询
                params.put("page", String.valueOf(page));
                List<EndorsementOrderInfo> list = endorsementOrderInfoService.queryPage(params).getList();
                if (!list.isEmpty()) {
                    writer.write(list, sheet);
                    log.info("保全订单导出生成，page={}, dataSize={}", page, list.size());
                    // 赋值maxId ： 需要+1
                    page++;
                } else {
                    log.info("保全订单导出生成，退出构建，执行导出");
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("保全订单导出生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.success();
    }
}
