package com.mpolicy.manage.modules.regulators.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("regulators_month_report")
public class RegulatorsMonthReportEntity implements Serializable {
    private static final long serialVersionUID = -9148067730268954508L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报表月份
     */
    private String reportMonth;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    // 设置sql入库时间格式
    private Date endTime;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
