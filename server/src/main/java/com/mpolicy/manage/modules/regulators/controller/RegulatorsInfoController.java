package com.mpolicy.manage.modules.regulators.controller;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.export.ExportApiService;
import com.mpolicy.manage.modules.policy.service.PolicyContractInfoService;
import com.mpolicy.manage.modules.policy.vo.EpPolicyExportVo;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportInfoService;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportList;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 监管报表管理
 *
 * <AUTHOR>
 * @date 2022-01-20 15:14:35
 */
@RestController
@RequestMapping("regulators/info")
@Api(tags = "监管报表管理")
@Slf4j
public class RegulatorsInfoController {

    /**
     * 监管报月度表
     */
    @Autowired
    private RegulatorsReportInfoService regulatorsReportInfoService;

    @Autowired
    PolicyContractInfoService policyContractInfoService;

    @Autowired
    private ExportApiService exportApiService;

    /**
     * <p>
     * 协议管理列表分页查询
     * </p>
     */
    @ApiOperation(value = "企业月度报告分页列表", notes = "企业月度报告分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "reportMonth", dataType = "String", value = "报告月度", example = "2022-01"),
            @ApiImplicitParam(paramType = "query", name = "reportStatus", dataType = "String", value = "报送状态0未报送 1报送", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "uploadDate", dataType = "String", value = "上传时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions("regulators:list")
    public Result<PageUtils<RegulatorsReportList>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("获取月度报告分页列表，查询条件={}", params);
        PageUtils<RegulatorsReportList> page = regulatorsReportInfoService.queryRegulatorsReportList(params);
        return Result.success(page);
    }

    @GetMapping("/exportPolicy/apply")
    @ApiOperation(value = "导出保单申请", notes = "导出保单申请")
    @RequiresPermissions("regulators:list")
    public Result<String> exportApply(@RequestParam Map<String, Object> params, HttpServletResponse response) throws IOException {
        exportApiService.exportReport(params);
        return Result.success("success");
    }

    @GetMapping("/exportPolicyNew")
    @ApiOperation(value = "导出保单", notes = "导出保单")
    @RequiresPermissions("regulators:list")
    public void exportNew(@RequestParam Map<String, Object> params, HttpServletResponse response) throws IOException {
        // 获取权限信息
        boolean sensitiveDataAccess = ShiroUtils.getSubject().isPermitted("policy:query");
        // 权限 机构 如果是部分才处理
        List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        if (orgCodeList != null && orgCodeList.size() == 0) {
            return;
        }
        params.put("orgCodeList", orgCodeList);
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        if (channelBranchCodeList != null && channelBranchCodeList.size() == 0) {
            return;
        }
        params.put("channelBranchCodeList", channelBranchCodeList);
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        OutputStream out = null;
        try {
            out = new FileOutputStream("logs/temp.xlsx");
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, EpPolicyExportVo.class);
            sheet.setSheetName("保单列表");
            int page = 0;
            int limitSize = 10000;
            while (true) {
                // 分批查询
                int startSize = page > 0 ? page * limitSize + 1 : 0;
                params.put("startSize", String.valueOf(startSize));
                params.put("limitSize", String.valueOf(limitSize));
                List<EpPolicyExportVo> expData = policyContractInfoService.exportPolicy(params, sensitiveDataAccess);
                if (!expData.isEmpty()) {
                    writer.write(expData, sheet);
                    log.info("保单导出中，startSize={},limitSize={}", startSize, limitSize);
                    // 赋值maxId ： 需要+1
                    page++;
                } else {
                    log.info("保单构建完成，退出构建，执行导出");
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("构建完成.....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
