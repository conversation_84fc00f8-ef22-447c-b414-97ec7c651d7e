package com.mpolicy.manage.modules.agentApply.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * ClassName: AgentApplyStatusEnum
 * Description: 代理人线上申请状态枚举
 * date: 2022/11/25 15:56
 *
 * <AUTHOR>
 */
@Getter
public enum AgentApplyStatusEnum {
    //申请状态(1:已提交 2:完成面试 3:待完成考试 4:待线上签约 5:待审核 6:待复核 7:待修改信息 8:已完成 9:已关闭)
    STATUS1(1, "已提交"),
    STATUS2(2, "完成面试"),
    STATUS3(3, "待完成考试"),
    STATUS4(4, "待线上签约"),
    STATUS5(5, "待审核"),
    STATUS6(6, "待复审"),
    STATUS7(7, "待修改信息"),
    STATUS8(8, "已完成"),
    STATUS9(9, "已关闭"),
    ;

    private final Integer code;
    private final String name;

    AgentApplyStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AgentApplyStatusEnum getNameByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
