package com.mpolicy.manage.modules.settlement.vo.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 批量处理精度信息
 *
 * <AUTHOR>
 * @since 2023-05-25 16:43
 */
@Data
@ApiModel(value = "批量处理精度信息", description = "批量处理精度信息")
public class AmountAccuracyInput implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    @NotBlank(message = "对账唯一单号不能为空")
    private String reconcileCode;

    /**
     * 处理精度类型
     */
    @ApiModelProperty(value = "处理精度类型", example = "RC2023052302020200")
    @NotBlank(message = "处理精度类型不能为空")
    private String accuracyType;

    /**
     * 处理说明
     */
    @ApiModelProperty(value = "差异处理说明", example = "差异处理说明")
    private String diffDesc;
}