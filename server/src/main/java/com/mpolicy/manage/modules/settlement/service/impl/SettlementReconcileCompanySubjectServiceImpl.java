package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.common.utils.ListUtil;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.contract.entity.ContractBasicInfoEntity;
import com.mpolicy.manage.modules.contract.entity.ContractInsuranceProductMapEntity;
import com.mpolicy.manage.modules.contract.service.ContractBasicInfoService;
import com.mpolicy.manage.modules.contract.service.ContractInsuranceProductMapService;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductEntity;
import com.mpolicy.manage.modules.protocol.entity.ProtocolBasicInfoEntity;
import com.mpolicy.manage.modules.protocol.entity.ProtocolProductPremEntity;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductService;
import com.mpolicy.manage.modules.protocol.service.ProtocolBasicInfoService;
import com.mpolicy.manage.modules.protocol.service.ProtocolProductPremService;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanySubjectDao;
import com.mpolicy.manage.modules.settlement.entity.*;
import com.mpolicy.manage.modules.settlement.service.*;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanySubjectInfo;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanySubjectListOut;
import com.mpolicy.manage.modules.settlement.vo.SaveReconcileCompanySubject;
import com.mpolicy.manage.modules.settlement.vo.UpdateReconcileCompanySubject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("settlementReconcileCompanySubjectService")
public class SettlementReconcileCompanySubjectServiceImpl extends ServiceImpl<SettlementReconcileCompanySubjectDao, SettlementReconcileCompanySubjectEntity> implements SettlementReconcileCompanySubjectService {


    @Autowired
    private SettlementReconcileCompanyInfoService settlementReconcileCompanyInfoService;


    @Autowired
    private SettlementSubjectService settlementSubjectService;

    @Autowired
    private SettlementReconcileContractInfoService settlementReconcileContractInfoService;

    @Autowired
    private SettlementReconcileCompanySubjectProductService settlementReconcileCompanySubjectProductService;

    @Autowired
    private IEpProtocolInsuranceProductService protocolInsuranceProductService;

    @Autowired
    private ProtocolBasicInfoService protocolBasicInfoService;

    @Autowired
    private ContractBasicInfoService contractBasicInfoService;

    @Autowired
    private ContractInsuranceProductMapService contractInsuranceProductMapService;
    @Autowired
    private SettlementReconcileCompanySubjectProductService reconcileCompanySubjectProductService;

    @Autowired
    private SettlementReconcileCompanySubjectPolicyMethodService settlementReconcileCompanySubjectPolicyMethodService;

    @Autowired
    private ProtocolProductPremService protocolProductPremService;

    /**
     * 获取结算保司配置对应规则科目表列表
     *
     * @param reconcileCompanyCode
     * @return
     */
    @Override
    public List<ReconcileCompanySubjectListOut> findReconcileCompanySubjectList(String reconcileCompanyCode, Integer reconcileType, String mergeCode) {

        List<SettlementSubjectEntity> settlementSubjectList = settlementSubjectService.list();
        Map<String, String> settlementSubjectMapCode = settlementSubjectList.stream().collect(Collectors.toMap(SettlementSubjectEntity::getSubjectCode, SettlementSubjectEntity::getSubjectName));
        if (reconcileType == null || reconcileType == 0) {
            //小鲸
            SettlementReconcileCompanyInfoEntity settlementReconcileCompanyInfo = Optional.ofNullable(settlementReconcileCompanyInfoService.lambdaQuery()
                    .eq(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                    .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算保司信息不存在")));
            return lambdaQuery()
                    .eq(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, reconcileCompanyCode)
                    .eq(StrUtil.isNotBlank(mergeCode), SettlementReconcileCompanySubjectEntity::getMergeCode, mergeCode)
                    .list().stream()
                    .map(m -> {
                        ReconcileCompanySubjectListOut result = BeanUtil.copyProperties(m, ReconcileCompanySubjectListOut.class);
                        result.setSubjectScopeDesc(m.getSubjectScope() == 0 ? "通用" : "指定险种");
                        if (!settlementSubjectMapCode.containsKey(m.getReconcileSubjectCode())) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算科目编码" + m.getReconcileSubjectCode() + "没有配置"));
                        }
                        result.setReconcileSubjectName(settlementSubjectMapCode.get(m.getReconcileSubjectCode()));
                        result.setCompanyName(settlementReconcileCompanyInfo.getCompanyName());
                        return result;
                    })
                    .collect(Collectors.toList());
        } else {
            SettlementReconcileContractInfoEntity settlementReconcileContractInfo = Optional.ofNullable(settlementReconcileContractInfoService.lambdaQuery()
                    .eq(SettlementReconcileContractInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                    .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("合约信息不存在")));
            return lambdaQuery()
                    .eq(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, reconcileCompanyCode)
                    .eq(StrUtil.isNotBlank(mergeCode), SettlementReconcileCompanySubjectEntity::getMergeCode, mergeCode)
                    .list().stream()
                    .map(m -> {
                        ReconcileCompanySubjectListOut result = BeanUtil.copyProperties(m, ReconcileCompanySubjectListOut.class);
                        result.setSubjectScopeDesc(m.getSubjectScope() == 0 ? "通用" : "指定险种");
                        if (!settlementSubjectMapCode.containsKey(m.getReconcileSubjectCode())) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算科目编码" + m.getReconcileSubjectCode() + "没有配置"));
                        }
                        result.setReconcileSubjectName(settlementSubjectMapCode.get(m.getReconcileSubjectCode()));
                        result.setCompanyName(settlementReconcileContractInfo.getSettlementCompanyName());
                        return result;
                    })
                    .collect(Collectors.toList());
        }
    }

    @Override
    public ReconcileCompanySubjectInfo findReconcileCompanySubjectInfoById(Integer id) {
        SettlementReconcileCompanySubjectEntity reconcileCompanySubject = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目配置不存在")));
        ReconcileCompanySubjectInfo result = BeanUtil.copyProperties(reconcileCompanySubject, ReconcileCompanySubjectInfo.class);
        List<String> insuranceProductCodeList = reconcileCompanySubjectProductService.lambdaQuery()
                .eq(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode, reconcileCompanySubject.getSubjectRuleCode())
                .list().stream().map(SettlementReconcileCompanySubjectProductEntity::getInsuranceProductCode)
                .distinct().collect(Collectors.toList());
        result.setInsuranceProductCodeList(insuranceProductCodeList);
        List<Integer> policyMethodList = settlementReconcileCompanySubjectPolicyMethodService.lambdaQuery()
                .eq(SettlementReconcileCompanySubjectPolicyMethodEntity::getSubjectRuleCode, reconcileCompanySubject.getSubjectRuleCode())
                .list().stream().map(SettlementReconcileCompanySubjectPolicyMethodEntity::getPolicyMethod)
                .collect(Collectors.toList());
        result.setPolicyMethodList(policyMethodList);
        return result;
    }

    /**
     * 新增结算保司科目信息
     *
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReconcileCompanySubject(SaveReconcileCompanySubject params) {

        SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject = BeanUtil.copyProperties(params, SettlementReconcileCompanySubjectEntity.class);
        String subjectRuleCode = CommonUtils.createCodeLastNumber("R");


        //配置了指定保司险种
        if (StatusEnum.NORMAL.getCode().equals(params.getSubjectScope())) {
            if (CollUtil.isEmpty(params.getInsuranceProductCodeList())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("选择了指定险种,但是没有配置指定险种"));
            }
        }
        if (CollUtil.isNotEmpty(params.getInsuranceProductCodeList())) {
            Map<String, EpProtocolInsuranceProductEntity> protocolInsuranceProductMap = protocolInsuranceProductService.lambdaQuery()
                    .in(EpProtocolInsuranceProductEntity::getInsuranceProductCode, params.getInsuranceProductCodeList())
                    .list().stream().collect(Collectors.toMap(EpProtocolInsuranceProductEntity::getInsuranceProductCode, v -> v));
            //保存关联关系
            settlementReconcileCompanySubjectProductService.saveBatch(
                    params.getInsuranceProductCodeList()
                            .stream()
                            .map(insuranceProductCode -> SettlementReconcileCompanySubjectProductEntity.builder()
                                    .subjectRuleCode(subjectRuleCode)
                                    .insuranceProductCode(insuranceProductCode)
                                    .insuranceProductName(protocolInsuranceProductMap.get(insuranceProductCode).getInsuranceProductName())
                                    .build())
                            .collect(Collectors.toList())
            );
        }
        settlementReconcileCompanySubject.setSubjectRuleCode(subjectRuleCode);
        settlementReconcileCompanySubject.setMergeCode(subjectRuleCode);
        // 创建规则
        save(settlementReconcileCompanySubject);

        // 保单方式
        if (CollUtil.isNotEmpty(params.getPolicyMethodList())) {
            settlementReconcileCompanySubjectPolicyMethodService.saveBatch(params.getPolicyMethodList().stream().map(policyMethod -> {
                SettlementReconcileCompanySubjectPolicyMethodEntity settlementReconcileCompanySubjectPolicyMethod = new SettlementReconcileCompanySubjectPolicyMethodEntity();
                settlementReconcileCompanySubjectPolicyMethod.setPolicyMethod(policyMethod);
                settlementReconcileCompanySubjectPolicyMethod.setSubjectRuleCode(settlementReconcileCompanySubject.getSubjectRuleCode());
                return settlementReconcileCompanySubjectPolicyMethod;
            }).collect(Collectors.toList()));
        }
        Integer subjectRuleCount = lambdaQuery().eq(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, params.getReconcileCompanyCode()).count();
        if (params.getReconcileType() == null || params.getReconcileType() == 0) {
            // 规则数量+1
            settlementReconcileCompanyInfoService.lambdaUpdate()
                    .set(SettlementReconcileCompanyInfoEntity::getSubjectRuleCount, subjectRuleCount)
                    .eq(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, settlementReconcileCompanySubject.getReconcileCompanyCode())
                    .update();
        } else {
            // 规则数量+1
            /*settlementReconcileContractInfoService.lambdaUpdate()
                    .set(SettlementReconcileContractInfoEntity::getSubjectRuleCount, subjectRuleCount)
                    .eq(SettlementReconcileContractInfoEntity::getReconcileCompanyCode, settlementReconcileCompanySubject.getReconcileCompanyCode())
                    .update();*/
        }
    }

    /**
     * 修改结算保司科目信息
     *
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReconcileCompanySubject(UpdateReconcileCompanySubject params) {
        SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject = Optional.ofNullable(getById(params.getId()))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("没有获取到科目规则")));
        //配置了指定保司险种
        if (StatusEnum.NORMAL.getCode().equals(params.getSubjectScope())) {
            if (CollUtil.isEmpty(params.getInsuranceProductCodeList())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("选择了指定险种,但是没有配置指定险种"));
            }
        }
        //todo 省事先删除之前,在新增 . 后面改成判断的
        settlementReconcileCompanySubjectProductService.lambdaUpdate()
                .eq(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode, settlementReconcileCompanySubject.getSubjectRuleCode())
                .remove();
        if (CollUtil.isNotEmpty(params.getInsuranceProductCodeList())) {
            Map<String, EpProtocolInsuranceProductEntity> protocolInsuranceProductMap = protocolInsuranceProductService.lambdaQuery()
                    .eq(EpProtocolInsuranceProductEntity::getReconcileType,params.getReconcileType())
                    .in(EpProtocolInsuranceProductEntity::getInsuranceProductCode, params.getInsuranceProductCodeList())
                    .list().stream().collect(Collectors.toMap(EpProtocolInsuranceProductEntity::getInsuranceProductCode, v -> v));
            //保存关联关系
            settlementReconcileCompanySubjectProductService.saveBatch(
                    params.getInsuranceProductCodeList()
                            .stream()
                            .map(insuranceProductCode -> SettlementReconcileCompanySubjectProductEntity.builder()
                                    .subjectRuleCode(settlementReconcileCompanySubject.getSubjectRuleCode())
                                    .insuranceProductCode(insuranceProductCode)
                                    .insuranceProductName(protocolInsuranceProductMap.get(insuranceProductCode).getInsuranceProductName())
                                    .build())
                            .collect(Collectors.toList())
            );
        }

        SettlementReconcileCompanySubjectEntity update = BeanUtil.copyProperties(params, SettlementReconcileCompanySubjectEntity.class);
        //更新规则信息
        if (StrUtil.isBlank(params.getRemark())) {
            update.setRemark("");
        }
        updateById(update);

        // 保单方式
        if (CollUtil.isNotEmpty(params.getPolicyMethodList())) {
            List<Integer> dbPolicyMethod = settlementReconcileCompanySubjectPolicyMethodService.lambdaQuery()
                    .eq(SettlementReconcileCompanySubjectPolicyMethodEntity::getSubjectRuleCode, settlementReconcileCompanySubject.getSubjectRuleCode())
                    .list().stream().map(SettlementReconcileCompanySubjectPolicyMethodEntity::getPolicyMethod).collect(Collectors.toList());
            Map<String, List<Integer>> map = new ListUtil<Integer>().splitListToMap(dbPolicyMethod, params.getPolicyMethodList());
            //处理删除
            if (CollUtil.isNotEmpty(map.get(ListUtil.REMOVE_LIST_KEY))) {
                // 删除关联关系
                settlementReconcileCompanySubjectPolicyMethodService.lambdaUpdate()
                        .in(SettlementReconcileCompanySubjectPolicyMethodEntity::getPolicyMethod, map.get(ListUtil.REMOVE_LIST_KEY))
                        .eq(SettlementReconcileCompanySubjectPolicyMethodEntity::getSubjectRuleCode, settlementReconcileCompanySubject.getSubjectRuleCode())
                        .remove();
            }
            //处理新增
            if (CollUtil.isNotEmpty(map.get(ListUtil.SAVE_LIST_KEY))) {
                settlementReconcileCompanySubjectPolicyMethodService.saveBatch(map.get(ListUtil.SAVE_LIST_KEY).stream().map(policyMethod -> {
                    SettlementReconcileCompanySubjectPolicyMethodEntity settlementReconcileCompanySubjectPolicyMethod = new SettlementReconcileCompanySubjectPolicyMethodEntity();
                    settlementReconcileCompanySubjectPolicyMethod.setPolicyMethod(policyMethod);
                    settlementReconcileCompanySubjectPolicyMethod.setSubjectRuleCode(settlementReconcileCompanySubject.getSubjectRuleCode());
                    return settlementReconcileCompanySubjectPolicyMethod;
                }).collect(Collectors.toList()));
            }
        } else {
            // 删除关联关系
            settlementReconcileCompanySubjectPolicyMethodService.lambdaUpdate()
                    .eq(SettlementReconcileCompanySubjectPolicyMethodEntity::getSubjectRuleCode, settlementReconcileCompanySubject.getSubjectRuleCode())
                    .remove();
        }
    }

    @Override
    public void mergeSubject(List<Integer> idList) {
        String subjectRuleCode = CommonUtils.createCodeLastNumber("R");
        long count = lambdaQuery().in(SettlementReconcileCompanySubjectEntity::getId, idList)
                .list().stream().map(SettlementReconcileCompanySubjectEntity::getReconcileSubjectCode)
                .distinct().count();
        if (count > 1) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("存在不同科目,不允许合并"));
        }
        lambdaUpdate()
                .set(SettlementReconcileCompanySubjectEntity::getMergeCode, subjectRuleCode)
                .in(SettlementReconcileCompanySubjectEntity::getId, idList)
                .update();
    }

    @Override
    public void splitSubject(List<Integer> idList) {
        lambdaQuery()
                .in(SettlementReconcileCompanySubjectEntity::getId, idList)
                .list()
                .forEach(action -> {
                    lambdaUpdate()
                            .set(SettlementReconcileCompanySubjectEntity::getMergeCode, action.getSubjectRuleCode())
                            .eq(SettlementReconcileCompanySubjectEntity::getId, action.getId())
                            .update();
                });
    }

    /**
     * 获取指定保司产品列表
     *
     * @param reconcileCompanyCode 结算保司编码
     * @return
     */
    @Override
    public List<SelectOut> findSelectInsuranceProductList(String reconcileCompanyCode, Integer reconcileType) {
        List<String> insuranceProductCodeList;
        if (reconcileType == null || reconcileType == 0) {
            //1.获取结算保司配置信息
            SettlementReconcileCompanyInfoEntity settlementReconcileCompanyInfo =
                    Optional.ofNullable(settlementReconcileCompanyInfoService.lambdaQuery()
                            .eq(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                            .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保司配置不存在")));
            // 1.获取协议
            List<String> protocolCodeList = protocolBasicInfoService.lambdaQuery()
                    .eq(ProtocolBasicInfoEntity::getInnerSignatoryCode, settlementReconcileCompanyInfo.getInnerSignatoryCode())
                    .in(ProtocolBasicInfoEntity::getExternalSignatoryCode, StrUtil.split(settlementReconcileCompanyInfo.getExternalSignatoryCode(), ','))
                    .eq(ProtocolBasicInfoEntity::getExternalSignatoryType, settlementReconcileCompanyInfo.getExternalSignatoryType())
                    .list().stream().map(ProtocolBasicInfoEntity::getProtocolCode).collect(Collectors.toList());
            if (protocolCodeList.isEmpty()) {
                log.info("对账单没有匹配到对应的协议={}", reconcileCompanyCode);
                return Collections.emptyList();
            }

            insuranceProductCodeList = protocolProductPremService.lambdaQuery()
                    .in(ProtocolProductPremEntity::getCompanyCode, StrUtil.split(settlementReconcileCompanyInfo.getCompanyCode(), ','))
                    .in(ProtocolProductPremEntity::getProtocolCode, protocolCodeList)
                    .list().stream()
                    .map(ProtocolProductPremEntity::getInsuranceProductCode)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            //1.获取结算保司配置信息
            SettlementReconcileContractInfoEntity settlementReconcileContractInfo =
                    Optional.ofNullable(settlementReconcileContractInfoService.lambdaQuery()
                            .eq(SettlementReconcileContractInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                            .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("合约配置不存在")));
            ContractBasicInfoEntity contractBasicInfo = Optional.ofNullable(contractBasicInfoService.lambdaQuery()
                    .eq(ContractBasicInfoEntity::getExternalSignatoryCode, settlementReconcileContractInfo.getExternalSignatoryCode())
                    .eq(ContractBasicInfoEntity::getInnerSignatoryCode, settlementReconcileContractInfo.getInnerSignatoryCode())
                    .eq(ContractBasicInfoEntity::getExternalSignatoryType, settlementReconcileContractInfo.getExternalSignatoryType())
                    .eq(ContractBasicInfoEntity::getInnerSignatoryType, settlementReconcileContractInfo.getInnerSignatoryType())
                    .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("合约信息不存在")));

            insuranceProductCodeList = contractInsuranceProductMapService.lambdaQuery()
                    .eq(ContractInsuranceProductMapEntity::getContractCode, contractBasicInfo.getContractCode())
                    .list().stream().map(ContractInsuranceProductMapEntity::getInsuranceProductCode)
                    .distinct()
                    .collect(Collectors.toList());

        }
        if (insuranceProductCodeList.isEmpty()) {
            log.info("上传费率表");
            return Collections.emptyList();
        }

        // 4.获取保司产品信息
        return protocolInsuranceProductService.lambdaQuery()
                .in(EpProtocolInsuranceProductEntity::getInsuranceProductCode, insuranceProductCodeList)
                .eq(EpProtocolInsuranceProductEntity::getReconcileType, reconcileType)
                .list().stream().map(m -> {
                    SelectOut result = new SelectOut();
                    result.setValue(m.getInsuranceProductCode());
                    result.setLabel(m.getInsuranceProductName() + "-" + m.getInsuranceProductCode());
                    return result;
                })
                .collect(Collectors.toList());

    }

    /**
     * 删除结算保司科目信息
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReconcileCompanySubject(Integer id) {
        //删除科目信息
        SettlementReconcileCompanySubjectEntity reconcileCompanySubject = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目配置不存在")));


        removeById(id);

        //删除关联保司产品
        reconcileCompanySubjectProductService.lambdaUpdate()
                .eq(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode, reconcileCompanySubject.getSubjectRuleCode())
                .remove();

        if (reconcileCompanySubject.getReconcileType() == 0) {
            SettlementReconcileCompanyInfoEntity settlementReconcileCompanyInfo = Optional.ofNullable(settlementReconcileCompanyInfoService.lambdaQuery()
                    .eq(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, reconcileCompanySubject.getReconcileCompanyCode())
                    .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算保司配置不存在")));

            // 规则数量-1
            settlementReconcileCompanyInfoService.lambdaUpdate()
                    .set(SettlementReconcileCompanyInfoEntity::getSubjectRuleCount, Math.max(settlementReconcileCompanyInfo.getSubjectRuleCount() - 1, 0))
                    .eq(SettlementReconcileCompanyInfoEntity::getReconcileCompanyCode, reconcileCompanySubject.getReconcileCompanyCode())
                    .update();
        }

    }
}
