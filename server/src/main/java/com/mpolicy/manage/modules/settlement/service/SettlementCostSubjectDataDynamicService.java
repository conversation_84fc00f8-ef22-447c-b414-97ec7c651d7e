package com.mpolicy.manage.modules.settlement.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyInput;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyInfo;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyQuery;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyRecord;

import java.util.Map;

/**
 * 科目范围(动态科目)数据服务接口
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
public interface SettlementCostSubjectDataDynamicService {

    /**
     * 获取周期动态科目申请集合
     *
     * @param paramMap 查询map
     * @return 周期动态科目申请集合
     * <AUTHOR>
     * @since 2024/1/29
     */
    PageUtils<SubjectDataDynamicApplyInfo> dynamicApplyList(Map<String, Object> paramMap);

    PageUtils<SubjectDataDynamicApplyRecord> pageSubjectDataDynamicApply(SubjectDataDynamicApplyQuery query);

    /**
     * 获取周期动态科目申请信息
     *
     * @param applyCode 动态科目申请编号
     * @return 申请纪录信息
     * <AUTHOR>
     * @since 2024/1/29
     */
    SubjectDataDynamicApplyInfo querySubjectDataDynamicApply(String applyCode);

    /**
     * 动态科目申请
     *
     * @param apply    申请信息
     * @param userName 操作员
     * @return 申请单号
     * <AUTHOR>
     * @since 2023/12/21
     */
    String subjectDataDynamicApply(SubjectDataDynamicApplyInput apply, String userName);

    /**
     * 加载动态科目配置文件
     *
     * @param applyCode 动态科目申请编号
     * @param userName  操作员
     * <AUTHOR>
     * @since 2023/11/27 20:24
     */
    void loadDynamicSubject(String applyCode, String userName);
}

