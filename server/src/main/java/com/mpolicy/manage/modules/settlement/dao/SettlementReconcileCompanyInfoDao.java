package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanyInfoListInput;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanyInfoListOut;
import org.apache.ibatis.annotations.Param;

/**
 * 结算保司配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:32:26
 */
public interface SettlementReconcileCompanyInfoDao extends BaseMapper<SettlementReconcileCompanyInfoEntity> {

    /**
     * 分页获取数据
     * @param page
     * @param params
     * @return
     */
    IPage<ReconcileCompanyInfoListOut> queryPage(@Param("page") Page<ReconcileCompanyInfoListOut> page,
                                                 @Param("params") ReconcileCompanyInfoListInput params);
}
