package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcilePolicyDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcilePolicyEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcilePolicyService;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileDiffExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保司结算对账单关联数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcilePolicyService")
public class SettlementReconcilePolicyServiceImpl extends ServiceImpl<SettlementReconcilePolicyDao, SettlementReconcilePolicyEntity> implements SettlementReconcilePolicyService {

    @Override
    public List<SettlementReconcileDiffExcel> findSettlementReconcileDiffList(List<String> billCodeList) {

        return baseMapper.findSettlementReconcileDiffList(billCodeList);
    }
}
