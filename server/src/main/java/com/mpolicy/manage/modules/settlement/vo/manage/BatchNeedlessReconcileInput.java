package com.mpolicy.manage.modules.settlement.vo.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/21 10:26
 * @Version 1.0
 */
@Data
public class BatchNeedlessReconcileInput {
    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    @NotBlank(message = "对账唯一单号不能为空")
    private String reconcileCode;

    /**
     * 对账保单业务汇总编号
     */
    @ApiModelProperty(value = "对账保单业务汇总编号", example = "RC2023052302020200")
    @NotEmpty(message = "对账保单业务汇总编号不能为空")
    private List<String> billCodeList;

    @ApiModelProperty(value = "无需对账原因", example = "无需对账")
    private String reason;
}
