package com.mpolicy.manage.modules.settlement.pattern;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class ReconcileTemplateFactory {


    private static Map<ReconcileTemplateEnum, ReconcileTemplateHandler> reconcileTemplateMap = new ConcurrentHashMap<>();

    public static ReconcileTemplateHandler getInvokeStrategy(String code) {
        ReconcileTemplateEnum eventEnum = ReconcileTemplateEnum.matchSearchCode(code);
        if (eventEnum == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未找到符合条件的模版"));
        }
        ReconcileTemplateHandler handler = reconcileTemplateMap.get(eventEnum);
        if (handler == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("模版未做相应的处理那"));
        }
        handler.setReconcileTemplate(eventEnum);
        return handler;
    }

    public static void register(ReconcileTemplateEnum reconcileTemplateEnum,
                                ReconcileTemplateHandler handler) {
        if (null == reconcileTemplateEnum || null == handler) {
            return;
        }
        reconcileTemplateMap.put(reconcileTemplateEnum, handler);
    }
}
