package com.mpolicy.manage.modules.settlement.vo.settlement;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算合约配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-28 14:21:38
 */

@Data
public class SettlementReconcileContractInfoListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结算保司编码
     */
    private String reconcileCompanyCode;
    /**
     * 结算名称
     */
    private String reconcileCompanyName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 内部签署方类型
     */
    private String innerSignatoryType;
    /**
     * 结算规则编码
     */
    private String subjectRuleCode;
    private String subjectReconcileCompanyCode;
}
