package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("settlement_reconcile_company_subject_policy_method")
public class SettlementReconcileCompanySubjectPolicyMethodEntity implements Serializable {
    private static final long serialVersionUID = 3313296003669736138L;

    @TableId
    private Integer id;

    private String subjectRuleCode;

    private Integer policyMethod;
}
