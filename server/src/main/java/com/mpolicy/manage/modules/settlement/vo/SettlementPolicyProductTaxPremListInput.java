package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/3/5 10:54
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettlementPolicyProductTaxPremListInput extends BasePage {

    @ApiModelProperty(name = "reconcileType", value = "结算类型", example = "portrait")
    @NotNull(message = "结算类型不能为空")
    private Integer reconcileType;

    @ApiModelProperty(name = "batchCode", value = "批单号", example = "portrait")
    private String batchCode;

    @ApiModelProperty(name = "policyNo", value = "保单号", example = "portrait")
    private String policyNo;

}

