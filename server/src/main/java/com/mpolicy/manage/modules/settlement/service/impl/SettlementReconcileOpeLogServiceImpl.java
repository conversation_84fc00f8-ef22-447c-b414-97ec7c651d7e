package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileOpeLogDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileOpeLogEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileOpeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保司结算对账单操作纪录表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:33
 */
@Slf4j
@Service("settlementReconcileOpeLogService")
public class SettlementReconcileOpeLogServiceImpl extends ServiceImpl<SettlementReconcileOpeLogDao, SettlementReconcileOpeLogEntity> implements SettlementReconcileOpeLogService {

}
