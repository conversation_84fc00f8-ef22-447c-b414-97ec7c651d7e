package com.mpolicy.manage.modules.settlement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SelectInsuranceProductListVo implements Serializable {


    @ApiModelProperty(value = "外部签署方类型")
    private String companyCode;

    @ApiModelProperty(value = "外部签署方类型")
    private String externalSignatoryType;

    @ApiModelProperty(value = "外部签署方")
    private String externalSignatoryCode;

    @ApiModelProperty(value = "内部签署方")
    private String innerSignatoryCode;
}
