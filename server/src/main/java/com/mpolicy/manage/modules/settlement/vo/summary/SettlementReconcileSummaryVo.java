package com.mpolicy.manage.modules.settlement.vo.summary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 生成汇总单
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "生成汇总单", description = "生成汇总单")
public class SettlementReconcileSummaryVo implements Serializable {

    private static final long serialVersionUID = 1;

}
