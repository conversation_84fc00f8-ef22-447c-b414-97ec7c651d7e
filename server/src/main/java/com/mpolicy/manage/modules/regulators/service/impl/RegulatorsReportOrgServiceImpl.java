package com.mpolicy.manage.modules.regulators.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.modules.regulators.dao.RegulatorsReportOrgDao;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportStatusEnum;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportOrgService;

import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportData;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportList;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service("regulatorsReportOrgService")
public class RegulatorsReportOrgServiceImpl extends ServiceImpl<RegulatorsReportOrgDao, RegulatorsReportOrgEntity> implements RegulatorsReportOrgService {

    @Override
    public PageUtils<OrgRegulatorsReportList> queryOrgRegulatorsReportList(String regulatorsNo, Map<String, Object> params) {

        //  机构编码 + 报告类型
        String orgCode = (String) params.get("orgCode");
        String reportType = (String) params.get("reportType");
        //  最后上传时间
        String uploadDate = (String) params.get("uploadDate");
        // 分页查询
        IPage<RegulatorsReportOrgEntity> page = this.page(
                new Query<RegulatorsReportOrgEntity>().getPage(params),
                new LambdaQueryWrapper<RegulatorsReportOrgEntity>()
                        .eq(StringUtils.isNotBlank(orgCode), RegulatorsReportOrgEntity::getOrgCode, orgCode)
                        .eq(StringUtils.isNotBlank(regulatorsNo), RegulatorsReportOrgEntity::getRegulatorsNo, regulatorsNo)
                        .eq(StringUtils.isNotBlank(reportType), RegulatorsReportOrgEntity::getReportType, reportType)
                        .apply(StringUtils.isNotBlank(uploadDate), "date_format(update_time,'%Y-%m-%d') = {0}", uploadDate)
                        .orderByDesc(RegulatorsReportOrgEntity::getId)
        );


        // 构建返回的vo list
        List<OrgRegulatorsReportList> orgRegulatorsReportList = new ArrayList<>();

        page.getRecords().forEach(x -> {
            OrgRegulatorsReportList bean = new OrgRegulatorsReportList();
            BeanUtils.copyProperties(x, bean);
            // 报送月度说明
            bean.setOrgMonthDesc(StrUtil.format("{}年{}月",x.getRegulatorsYear(),x.getRegulatorsMonth()));
            // 报送状态
            bean.setReportStatus(RegulatorsReportStatusEnum.deCode(x.getReportStatus()).getName());
            bean.setReportStatusCode(x.getReportStatus());
            // 报告类型和状态
            RegulatorsReportTypeEnum regulatorsReportType = RegulatorsReportTypeEnum.deCode(x.getReportType());
            if(regulatorsReportType != null){
                bean.setReportType(regulatorsReportType.getCode());
                bean.setReportTypeName(regulatorsReportType.getName());
            }
            bean.setUpdateTime(DateUtil.formatDate(x.getUpdateTime()));
            // 文件编码 + 文件下载地址
            bean.setFileCode(x.getReportFileCode());
            bean.setDomainPath(DomainUtil.addOssDomainIfNotExist(x.getReportFilePath()));
            orgRegulatorsReportList.add(bean);
        });

        // 重新构建分页返回对象
        return new PageUtils(orgRegulatorsReportList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public OrgRegulatorsReportData queryOrgRegulatorsReport(String orgRegulatorsNo) {

        // 根据月度报备报告 + 机构报备报告唯一编号获取纪录
        RegulatorsReportOrgEntity orgReport = Optional.ofNullable(
                lambdaQuery()
                        .eq(RegulatorsReportOrgEntity::getOrgRegulatorsNo, orgRegulatorsNo)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("机构报备报告纪录不存在")));

        OrgRegulatorsReportData result = new OrgRegulatorsReportData();
        BeanUtils.copyProperties(orgReport, result);
        // 报送月度说明
        result.setOrgMonthDesc(StrUtil.format("{}年{}月",orgReport.getRegulatorsYear(),orgReport.getRegulatorsMonth()));
        // 报送状态
        result.setReportStatus(RegulatorsReportStatusEnum.deCode(orgReport.getReportStatus()).getName());
        result.setReportStatusCode(orgReport.getReportStatus());
        // 报告类型和状态
        RegulatorsReportTypeEnum regulatorsReportType = RegulatorsReportTypeEnum.deCode(orgReport.getReportType());
        if(regulatorsReportType != null){
            result.setReportType(regulatorsReportType.getCode());
            result.setReportTypeName(regulatorsReportType.getName());
        }
        // 报告内容
        result.setReportData(JSONObject.parseObject(orgReport.getReportData()));
        result.setReportTitle(orgReport.getReportTitle());
        // 文件下载地址
        result.setDomainPath(DomainUtil.addOssDomainIfNotExist(orgReport.getReportFilePath()));
        return result;
    }
}
