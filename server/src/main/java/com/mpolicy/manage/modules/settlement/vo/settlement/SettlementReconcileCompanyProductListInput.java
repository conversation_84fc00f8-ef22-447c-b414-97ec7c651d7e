package com.mpolicy.manage.modules.settlement.vo.settlement;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算保司配置对应规则科目表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 14:29:16
 */

@Data
public class SettlementReconcileCompanyProductListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    /**
     * 保司对账单产品名称
     */
    private String companyProductName;
    /**
     * 协议险种编码
     */
    private String insuranceProductCode;
    /**
     * 协议险种名称
     */
    private String insuranceProductName;
    /**
     * 备注
     */
    private String remark;

}
