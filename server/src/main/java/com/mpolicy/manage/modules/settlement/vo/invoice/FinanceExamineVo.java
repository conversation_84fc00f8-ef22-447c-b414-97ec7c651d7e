package com.mpolicy.manage.modules.settlement.vo.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinanceExamineVo implements Serializable {
    private static final long serialVersionUID = 2033062070337913941L;


    @NotBlank(message = "发票编码不能为空")
    @ApiModelProperty(value = "发票编码")
    private String invoiceCode;

    @ApiModelProperty(value = "财务审核状态")
    @NotNull(message = "财务审核状态不能为空")
    private Integer invoicingFinanceStatus;

    /**
     * 开票税率'
     */
    @ApiModelProperty(value = "开票税率")
    private BigDecimal invoicingTaxRate;


    /**
     * 开票项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String invoicingItemCode;

    @ApiModelProperty(value = "项目名称")
    private String invoicingItemName;

    /**
     * 有无时间要求
     */
    @ApiModelProperty(value = "有无时间要求")
    private Integer invoicingTimeAsk;

    /**
     * '开票时间'
     */
    @ApiModelProperty(value = "开票时间",example = "2023-04-05 00:00:00")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date invoicingTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String invoicingRemark;

    /**
     * 开票意见
     */
    @ApiModelProperty(value = "开票意见")
    private String invoicingOpinion;

}
