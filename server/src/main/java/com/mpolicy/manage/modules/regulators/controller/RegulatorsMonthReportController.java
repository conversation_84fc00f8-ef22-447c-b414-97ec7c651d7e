package com.mpolicy.manage.modules.regulators.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.regulators.service.RegulatorsMonthReportService;
import com.mpolicy.manage.modules.regulators.vo.CreateRegulatorsMonthReport;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportInput;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportOut;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@Api(tags = "监管报表")
@RequestMapping("regulators/month/report")
public class RegulatorsMonthReportController {

    @Autowired
    private RegulatorsMonthReportService regulatorsMonthReportService;


    /**
     * 获取数据列表
     * @param input 请求数据
     * @return 列表数据
     */
    @PostMapping("list")
    @RequiresPermissions(value = {"regulators:monthreport:list"})
    public Result<PageUtils<RegulatorsMonthReportOut>> list(@RequestBody @Valid RegulatorsMonthReportInput input) {
        PageUtils<RegulatorsMonthReportOut> page = regulatorsMonthReportService.queryPage(input);
        return Result.success(page);
    }


    /**
     * 创建监管报表
     * @param input 请求参数
     * @return 创建结果
     */
    @PostMapping("create")
    @RequiresPermissions(value = {"regulators:monthreport:list"})
    public Result create(@RequestBody @Valid CreateRegulatorsMonthReport input) {
         regulatorsMonthReportService.createRegulatorsMonthReport(input);
        return Result.success();
    }

}
