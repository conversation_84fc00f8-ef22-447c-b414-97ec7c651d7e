package com.mpolicy.manage.modules.settlement.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.policy.vo.EpPolicyExportVo;
import com.mpolicy.manage.modules.settlement.service.SettlementCostImportRecordService;
import com.mpolicy.manage.modules.settlement.service.SettlementCostService;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicService;
import com.mpolicy.manage.modules.settlement.service.SettlementEmployeeService;
import com.mpolicy.manage.modules.settlement.service.detail.impl.SettlementDetailManager;
import com.mpolicy.manage.modules.settlement.utils.AutoCostUtils;
import com.mpolicy.manage.modules.settlement.vo.dynamic.*;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.*;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.EmployeeInfoInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.EmployeeInfoVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.OrganizationCache;
import com.mpolicy.settlement.core.common.autocost.*;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import com.mpolicy.web.common.annotation.PassToken;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 农保员工佣金结算
 * @date 2023/11/28 9:17 上午
 * @Version 1.0
 */
@RestController
@RequestMapping("/settlement/cost")
@Api(tags = "农保员工佣金结算")
@Slf4j
public class SettlementCostController {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Autowired
    private SettlementCostService settlementCostService;
    @Autowired
    private SettlementEmployeeService settlementEmployeeService;

    @Autowired
    private SettlementDetailManager detailManager;

    /**
     * 动态科目数据
     */
    @Autowired
    private SettlementCostSubjectDataDynamicService settlementCostSubjectDataDynamicService;

    @Autowired
    private SettlementCostImportRecordService settlementCostImportRecordService;

    /**
     * 农保员工佣金结算--按结算机构展示
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/pageSettlementCostAutoInfo")
    @ApiOperation(value = "农保员工佣金结算--按结算机构展示", notes = "农保员工佣金结算--按结算机构展示", httpMethod = "POST")
    public Result<PageUtils<SettlementCostAutoInfoVo>> pageSettlementCostAutoInfo(@RequestBody SettlementCostAutoInput input) {
        PageUtils<SettlementCostAutoInfoVo> page = settlementCostService.pageSettlementCostAutoInfo(input);
        return Result.success(page);
    }

    @PostMapping("/auto/pageSettlementCostAutoInfo/export")
    @ApiOperation(value = "农保员工佣金结算--按结算机构展示导出", notes = "农保员工佣金结算--按结算机构展示导出", httpMethod = "POST")
    public void pageSettlementCostAutoInfoPage(@RequestBody SettlementCostAutoInput input, HttpServletResponse servletResponse) {
        settlementCostService.pageSettlementCostAutoInfoExport(input, servletResponse);
    }

    /**
     * 农保员工佣金结算--按汇总展示
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/pageSettlementCostAutoInfoBySummary/export")
    @ApiOperation(value = "农保员工佣金结算--按汇总展示导出", notes = "农保员工佣金结算--按汇总展示导出", httpMethod = "POST")
    public void pageSettlementCostAutoInfoBySummaryExport(@RequestBody SettlementCostAutoInput input, HttpServletResponse servletResponse) throws IOException {
        settlementCostService.pageSettlementCostAutoInfoBySummaryExport(input, servletResponse);
    }

    @PostMapping("/auto/pageSettlementCostAutoInfoBySummary")
    @ApiOperation(value = "农保员工佣金结算--按汇总展示", notes = "农保员工佣金结算--按汇总展示", httpMethod = "POST")
    public Result<PageUtils<SettlementCostAutoInfoVo>> pageSettlementCostAutoInfoBySummary(@RequestBody SettlementCostAutoInput input) {
        PageUtils<SettlementCostAutoInfoVo> page = settlementCostService.pageSettlementCostAutoInfoBySummary(input);
        return Result.success(page);
    }


    /**
     * 获取险种维度科目明细
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/listSubject")
    @ApiOperation(value = "获取险种维度科目明细", notes = "获取险种维度科目明细", httpMethod = "POST")
    public Result<PageUtils<SettlementCostAutoRecordVo>> listSubject(@RequestBody SettlementSubjectInput input) {
        PageUtils<SettlementCostAutoRecordVo> page = null;
        return Result.success(page);
    }

    /**
     * 获取险种维度科目明细
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/listSubjectProductDetail")
    @ApiOperation(value = "获取险种维度科目明细", notes = "获取险种维度科目明细", httpMethod = "POST")
    public Result<PageUtils<SettlementCostAutoProductVo>> listSubjectProductDetail(@RequestBody SettlementSubjectInput input) {
        PageUtils<SettlementCostAutoProductVo> page = null;
        return Result.success(page);
    }

    /**
     * 获取保单维度科目明细
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/listSubjectPolicyDetail")
    @ApiOperation(value = "获取保单维度科目明细", notes = "获取保单维度科目明细", httpMethod = "POST")
    public Result<PageUtils<SettlementCostAutoPolicyVo>> listSubjectPolicyDetail(@RequestBody SettlementSubjectInput input) {
        PageUtils<SettlementCostAutoPolicyVo> page = null;
        return Result.success(page);
    }

    /**
     * 获取保单险种维度科目明细
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/listSubjectPolicyProductDetail")
    @ApiOperation(value = "获取保单险种维度科目明细", notes = "获取保单险种维度科目明细", httpMethod = "POST")
    public Result<PageUtils<String>> listSubjectPolicyProductDetail(@RequestBody SettlementSubjectInput input) {
        PageUtils<String> page = null;
        return Result.success(page);
    }

    /**
     * 获取员工所有区域信息
     *
     * @return
     */
    @GetMapping("/auto/listAllRegion")
    @ApiOperation(value = "获取员工所有区域信息", notes = "获取员工所有区域信息", httpMethod = "GET")
    public Result<List<OrganizationCache>> listAllRegion() {
        List<OrganizationCache> list = settlementEmployeeService.listAllRegion();
        return Result.success(list);
    }
    /**
     * 查询员工信息
     *
     * @param input 请求参数
     * @return
     */
    @PostMapping("/auto/listEmployeeInfo")
    @ApiOperation(value = "查询员工信息", notes = "查询员工信息", httpMethod = "POST")
    public Result<List<EmployeeInfoVo>> listEmployeeInfo(@RequestBody EmployeeInfoInput input) {
        log.info("查询员工信息，入参：{}",input);
        List<EmployeeInfoVo> list = settlementEmployeeService.listEmployeeInfo(input);
        return Result.success(list);
    }


    /**
     * 动态科目-申请集合
     */
    @ApiOperation(value = "动态科目-申请集合", notes = "动态科目-申请集合")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "costSettlementCycle", dataType = "String", value = "所属周期"),
            @ApiImplicitParam(paramType = "query", name = "applyStatus", dataType = "int", value = "处理状态", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/dynamic_apply/list")
    public Result<PageUtils<SubjectDataDynamicApplyInfo>> dynamicApplyList(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("获取周期动态科目申请集合，查询条件={}", params);
        PageUtils<SubjectDataDynamicApplyInfo> page = settlementCostSubjectDataDynamicService.dynamicApplyList(params);
        return Result.success(page);
    }


    /**
     * 结算文件申请
     *
     * @param apply 申请信息
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2024/01/29
     */
    @ApiOperation(value = "结算文件-申请", notes = "结算文件导入功能")
    @PostMapping("/dynamic_subject/apply")
    public Result<String> uploadSubjectDataDynamicApply(@RequestBody @ApiParam(name = "apply", value = "动态科目申请信息", required = true) SubjectDataDynamicApplyInput apply) {
        // 1 校验参数
        ValidatorUtils.validateEntity(apply);
        // 2 执行申请
        return Result.success(settlementCostSubjectDataDynamicService.subjectDataDynamicApply(apply, ShiroUtils.getUserEntity().getUsername()));
    }

    /**
     * 结算文件删除
     * @param input
     * @return
     */
    @ApiOperation(value = "结算文件删除", notes = "结算文件导入功能")
    @PostMapping("/dynamic_subject/delete")
    public Result<String> deleteDynamicSubject(@RequestBody @ApiParam(name = "input", value = "动态科目申请信息", required = true) SettlementCostImportRecordDelete input) {
        input.setUserName(ShiroUtils.getUserEntity().getUsername());
        settlementCostImportRecordService.deleteImportRecord(input);
        return Result.success(Constant.DEFAULT_SUCCESS);
    }
    /**
     * 动态科目数据生成
     *
     * @param applyCode 动态科目申请编码
     * @param userName  操作员
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2024/01/29
     */
    @ApiOperation(value = "结算文件-数据生成", notes = "结算文件导入功能")
    @PostMapping("/dynamic_subject/load")
    public Result<String> loadDynamicSubject(@RequestParam(value = "applyCode") @ApiParam(name = "applyCode", value = "动态科目申请编码", example = "F001") String applyCode,
                                             @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {
        // 获取对账单
        SubjectDataDynamicApplyInfo applyInfo = settlementCostSubjectDataDynamicService.querySubjectDataDynamicApply(applyCode);
        if (applyInfo.getApplyStatus() == 3) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("动态科目数据生成中，请稍后重试")));
        }
        settlementCostSubjectDataDynamicService.loadDynamicSubject(applyCode, userName);
        return Result.success(Constant.DEFAULT_SUCCESS);
    }

    /**
     * 文件导入记录查询
     * @param query
     * @return
     */
    @ApiOperation(value = "文件导入记录查询", notes = "文件导入记录查询")
    @PostMapping("/import_record/pageSubjectDataDynamicApplyQuery")
    @PassToken
    public Result<PageUtils<SubjectDataDynamicApplyRecord>> pageSubjectDataDynamicApplyQuery(@RequestBody @ApiParam(name = "query", value = "结算文件导入记录查询", required = true) SubjectDataDynamicApplyQuery query) {

        return Result.success(settlementCostSubjectDataDynamicService.pageSubjectDataDynamicApply(query));
    }



    /**
     * 文件导入列表查询
     * @param query
     * @return
     */
    @ApiOperation(value = "文件导入列表查询", notes = "文件导入列表查询")
    @PostMapping("/import_record/pageSettlementCostImportRecord")
    @PassToken
    public Result<PageUtils<SettlementCostImportRecord>> pageSettlementCostImportRecord(@RequestBody @ApiParam(name = "query", value = "结算文件导入列表查询", required = true) SettlementCostImportRecordQuery query) {

        return Result.success(settlementCostImportRecordService.pageSettlementCostImportRecord(query));
    }
    /**
     * 文件导入操作记录查询
     * @param query
     * @return
     */
    @ApiOperation(value = "文件导入操作记录查询", notes = "文件导入操作记录查询")
    @PostMapping("/import_record/pageSettlementCostImportOperationRecord")
    @PassToken
    public Result<PageUtils<SettlementCostImportOperationRecord>> pageSettlementCostImportOperationRecord(@RequestBody @ApiParam(name = "query", value = "结算文件导入操作记录查询", required = true) SettlementCostImportOperationRecordQuery query) {

        return Result.success(settlementCostImportRecordService.pageSettlementCostImportOperationRecord(query));
    }

    @GetMapping("/import_record/exportImportErrorData")
    @ApiOperation(value = "导出导入异常记录", notes = "导出导入异常记录")
    @PassToken
    public void exportImportErrorData(@RequestParam(value = "applyCode") @ApiParam(name = "applyCode", value = "申请编码", example = "F001") String applyCode,
                       @RequestParam(value = "fileName") @ApiParam(name = "fileName", value = "文件编码", example = "F001") String fileName
            , HttpServletResponse response) {
        settlementCostImportRecordService.exportSettlementCostImportErrorData(applyCode,fileName,response);
    }



    @ApiOperation(value = "科目详情-列表", notes = "科目详情-列表")
    @PostMapping("/detail/page")
    public Result<PageUtils<?>> cosetSubjectDetailPage(@RequestBody PageSettlementDetailParams p) {
        return Result.success(detailManager.showDetailPage(p));
    }

    @ApiOperation(value = "科目详情-汇总", notes = "科目详情-汇总")
    @PostMapping("/detail/summary")
    public Result<Object> cosetSubjectDetailSummary(@RequestBody PageSettlementDetailParams p) {
        return Result.success(detailManager.summary(p));
    }

    /**
     * 重新结算
     *
     * @return
     */
    @GetMapping("/auto/recalculate")
    @ApiOperation(value = "重新结算", notes = "重新结算", httpMethod = "GET")
    public Result<String> recalculate(@RequestParam(value = "programmeCode") @ApiParam(name = "programmeCode", value = "方案编码", example = "F001") String programmeCode,
                                      @RequestParam(value = "costSettlementCycle") @ApiParam(name = "costSettlementCycle", value = "结算周期", example = "F001") String costSettlementCycle,
                                      @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        return Result.success("success");
    }

    /**
     * 确认结算
     *
     * @return
     */
    @GetMapping("/auto/confirmSettlement")
    @ApiOperation(value = "确认结算", notes = "确认结算", httpMethod = "GET")
    public Result<String> confirmSettlement(@RequestParam(value = "programmeCode") @ApiParam(name = "programmeCode", value = "方案编码", example = "F001") String programmeCode,
                                            @RequestParam(value = "costSettlementCycle") @ApiParam(name = "costSettlementCycle", value = "结算周期", example = "F001") String costSettlementCycle,
                                            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {
        settlementCostService.confirmSettlement(programmeCode,costSettlementCycle,userName);
        return Result.success("success");
    }

    /**
     * 是否已确认结算
     *
     * @return
     */
    @GetMapping("/auto/validateConfirmed")
    @ApiOperation(value = "是否已确认结算，已确认，返回true，未确认返回false", notes = "是否已确认结算", httpMethod = "GET")
    public Result<Boolean> validateConfirmed(@RequestParam(value = "programmeCode") @ApiParam(name = "programmeCode", value = "方案编码", example = "F001") String programmeCode,
                                            @RequestParam(value = "costSettlementCycle") @ApiParam(name = "costSettlementCycle", value = "导入数据周期", example = "F001") String costSettlementCycle) {

        return Result.success(settlementCostService.validateConfirmed(programmeCode, AutoCostUtils.getReissueCostSettlementCycle(costSettlementCycle, 1)));
    }


    @ApiOperation(value = "科目详情-科目", notes = "科目详情-科目")
    @PostMapping("/detail/subject")
    public Result<List<SettlementDetailAllSubjectVO>> queryAllSubject(@RequestBody SettlementCostAutoInput input) {
        return Result.success(
                settlementCostService.querySettlementDynamicSubjectVOS(input)
        );
    }

    @ApiOperation(value = "农保员工佣金结算-列表", notes = "农保员工佣金结算-列表")
    @PostMapping("/month/confirm/page")
    @RequiresPermissions(value = {"settlement:costmonth:all"})
    public Result<PageUtils<SettlementConfirmPageVO>> cosetSubjectDetailPage(@RequestBody SettlementConfirmInput p) {
        return Result.success(settlementCostService.pageSettlementConfirmPage(p));
    }


    /**
     * 自动结算上传文件-导入申请
     *
     * @param apply 申请信息
     * @return com.mpolicy.common.result.Result<java.lang.String>
     */
    @ApiOperation(value = "自动结算上传文件-导入申请", notes = "自动结算上传文件-导入申请")
    @PostMapping("/cost_upload/upload")
    @PassToken
    public Result<String> uploadHistoryLongCostApply(@RequestBody @ApiParam(name = "apply", value = "自动结算上传文件-导入申请信息", required = true) CostUploadApplyInput apply) {
        apply.setUserName(ShiroUtils.getUserEntity().getUsername());
        return Result.success(settlementCostService.uploadHistoryLongCostApply(apply));

    }



    @ApiOperation(value = "自动结算上传文件-数据生成", notes = "自动结算上传文件-数据生成")
    @PostMapping("/cost_upload/loadCost")
    @PassToken
    public Result<String> loadCost(@RequestParam(value = "applyCode") @ApiParam(name = "applyCode", value = "结算上传文件申请编码", example = "F001") String applyCode,
                                   @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName) {

        return Result.success(settlementCostService.loadCost(applyCode, userName));
    }


    @ApiOperation(value = "根据动态科目名称模糊查询科目定义信息")
    @GetMapping("/cost_upload/listSettlementDynamicSubject")
    public Result<List<SettlementDynamicSubjectDefinition>> listSettlementDynamicSubject(@RequestParam(value = "dynamicSubjectName") @ApiParam(name = "dynamicSubjectName", value = "动态科目名称模糊信息", example = "F001") String dynamicSubjectName) {
        log.info("根据动态科目名称模糊查询科目定义信息,入参:{}",dynamicSubjectName);
        return Result.success(settlementCostService.listSettlementDynamicSubject(dynamicSubjectName));
    }

    @ApiOperation(value = "保存动态科目定义信息")
    @PostMapping("/cost_upload/saveSettlementDynamicSubjectDefinition")
    public Result<String> saveSettlementDynamicSubjectDefinition(@RequestBody SettlementDynamicSubjectDefinition definition) {
        log.info("存储动态科目定义信息,入参:{}", JSON.toJSONString(definition));
        definition.setUpdateUser(ShiroUtils.getUserEntity().getUsername());
        return Result.success(settlementCostService.saveSettlementDynamicSubjectDefinition(definition));
    }

    @ApiOperation(value = "主任寻人规则不匹配日志记录-列表", notes = "主任寻人规则不匹配日志记录-列表")
    @PostMapping("/auto/branchDirector/pageNotMatchLog")
    public Result<PageUtils<SettlementDirectorNotMatchRetDto>> pageNotMatchLog(@RequestBody SettlementDirectorNotMatchLogQuery p) {
        return Result.success(settlementCostService.pageNotMatchLog(p));
    }

    @ApiOperation(value = "结算生服对接人导出", notes = "结算生服对接人导出")
    @PostMapping("/auto/pco/exportSettlementPcoInfo")
    public Result exportSettlementPcoInfo(HttpServletResponse response,@RequestParam(value = "settlementCycle",required = true)  String settlementCycle) throws IOException {
        settlementCostService.exportSettlementPcoInfo(response,settlementCycle);
        return Result.success();
    }

}
