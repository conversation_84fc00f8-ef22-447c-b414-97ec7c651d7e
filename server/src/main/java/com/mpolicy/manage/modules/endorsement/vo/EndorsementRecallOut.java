package com.mpolicy.manage.modules.endorsement.vo;

import com.mpolicy.manage.modules.insure.vo.InsureRecallList;
import com.mpolicy.order.common.endorsement.EndorsementBasicInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 保全回溯详情
 *
 * <AUTHOR>
 * @date 2022-05-29 13:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "投保回溯详情")
public class EndorsementRecallOut extends EndorsementBasicInfo {

    @ApiModelProperty(value = "订单回溯集合信息")
    private List<InsureRecallList> recallList;
}
