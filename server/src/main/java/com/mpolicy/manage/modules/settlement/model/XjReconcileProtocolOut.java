package com.mpolicy.manage.modules.settlement.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class XjReconcileProtocolOut implements Serializable {
    private static final long serialVersionUID = -7498499959439315360L;

    private String externalSignatoryType;

    private String externalSignatoryName;
    private String externalSignatoryCode;

    private String protocolCode;

    private Integer settlementPeriodType;

    private String settlementUnderwritingStartDimension;

    private Integer settlementUnderwritingStartDay;

    private String settlementUnderwritingEndDimension;

    private Integer settlementUnderwritingEndDay;

    private String settlementReceiptDimension;

    private Integer settlementReceiptDay;

    private String settlementRevisitDimension;

    private Integer settlementRevisitDay;

    private String companyType;

    private String companyShortName;

    private String companyCode;
}
