package com.mpolicy.manage.modules.settlement.enums;

import lombok.Getter;

import java.util.Arrays;
@Getter
public enum CommissionTypeEnum {
    COMMON("common","新契约/续保佣金"),
    RENEWAL("renewal","续期佣金"),
    CORRECT_INC_OR_DEC("correct_inc_or_dec","保全增员/减员"),
    CORRECT_SURRENDER("correct_surrender","保全退保"),
    UNKNOWN("unknown","未知")
    ;

    private final String code;

    private final String desc;

    CommissionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CommissionTypeEnum getCommissionTypeEnum(String code) {
        return Arrays.stream(CommissionTypeEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }
}
