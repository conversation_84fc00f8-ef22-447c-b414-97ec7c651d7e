package com.mpolicy.manage.modules.settlement.vo.invoice;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
@Data
public class InvoiceEnclosureItem implements Serializable {
    private static final long serialVersionUID = -4186123971708464920L;

    @NotBlank(message = "文件编码不能为空")
    private String fileCode;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 文件类型
     */
    private String fileType;
}
