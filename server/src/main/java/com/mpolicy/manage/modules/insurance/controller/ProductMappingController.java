package com.mpolicy.manage.modules.insurance.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoMappingMapService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoMappingService;
import com.mpolicy.manage.modules.insurance.vo.ProductInfoMappingInput;
import com.mpolicy.manage.modules.insurance.vo.ProductInfoMappingOut;
import com.mpolicy.manage.modules.insurance.vo.ProductInfoMappingVO;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.websocket.server.PathParam;

/**
 * Created by 陈杰生
 * 需求文档：<a href="https://cfpamf.yuque.com/g/xg4y3f/ulb6rg/wnxnmxlzen43rsdh/collaborator/join?token=YqHXcx1Kw8qAqibP&source=doc_collaborator#">...</a> 《增加产品与险种映射配置功能》
 * 作者开发文档：<a href="https://www.yuque.com/u34565069/fpgykd/lchmlyo8f1ieh2az">...</a>
 * 2024/1/17 9:27
 * <p>
 * 产品与险种映射配置功能
 */
@Validated
@RestController
@RequestMapping("insurance/productMapping")
@Api(tags = "产品与险种映射配置功能")
@Slf4j
public class ProductMappingController {

    @Autowired
    private InsuranceProductInfoMappingService productInfoMappingService;

    @Autowired
    private InsuranceProductInfoMappingMapService productInfoMappingMapService;


    @ApiOperation(value = "保存产品与险种映射配置", notes = "保存产品与险种映射配置")
    @PostMapping("/saveOrUpdate")
    @Transactional
    public Result<String> saveOrUpdate(@RequestBody @ApiParam(name = "ProductInfoMappingVO", value = "保存产品与险种映射配置信息") ProductInfoMappingVO productInfoMappingVO) {
        // 1. 校验VO类
        ValidatorUtils.validateEntity(productInfoMappingVO);
        // 2. saveProductInfoMapping
        productInfoMappingService.saveOrUpdateProductInfoMapping(productInfoMappingVO);
        // 3. saveProductInfoMappingMap
        productInfoMappingMapService.saveOrUpdateProductInfoMappingMap(productInfoMappingVO);
        // 4.无报错，响应成功
        return Result.success();
    }

    @ApiOperation(value = "删除产品与险种映射配置", notes = "删除产品与险种映射配置")
    @GetMapping("/delete")
    @Transactional
    public Result<String> delete(@PathParam("productCode") String productCode) {
        // 1. deleteProductInfoMapping
        productInfoMappingService.deleteProductInfoMapping(productCode);
        // 2. deleteProductInfoMappingMap
        productInfoMappingMapService.deleteProductInfoMappingMap(productCode);
        // 3. 无报错，响应成功
        return Result.success();
    }

    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    @GetMapping("/list")
    public Result<PageUtils<ProductInfoMappingOut>> list(ProductInfoMappingInput productInfoDisclosuresInput) {
        // 1. 获取分页数据
        PageUtils<ProductInfoMappingOut> page = productInfoMappingService.queryPage(productInfoDisclosuresInput);
        // 2. 返回数据
        return Result.success(page);
    }

    @ApiOperation(value = "获取产品与险种映射配置", notes = "获取产品与险种映射配置")
    @GetMapping("/query")
    public Result<ProductInfoMappingVO> query(@PathParam("productCode") String productCode) {
        return Result.success(productInfoMappingService.queryProductInfoMappingDetail(productCode));
    }

    @ApiOperation(value = "获取险种名称", notes = "获取险种名称")
    @GetMapping("/queryInsuranceTypeName")
    public Result<String> queryInsuranceTypeName(@PathParam("productCode") String productCode,@RequestParam("companyCode") String companyCode) {
        return Result.success(productInfoMappingService.queryInsuranceTypeName(productCode,companyCode));
    }


    @ApiOperation(value = "导出产品与险种映射配置", notes = "导出产品与险种映射配置")
    @PostMapping("/export")
    public void export(@RequestBody ProductInfoMappingInput productInfoDisclosuresInput, HttpServletResponse response) {
        productInfoMappingService.exportProductInfoMappingDetail(productInfoDisclosuresInput, response);
    }

}
