package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyReviseService;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseListOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 基础佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */
@RestController
@RequestMapping("settlement/policy/revise")
@Api(tags = "结算保单变更记录")
public class SettlementPolicyReviseController {

    @Autowired
    private SettlementPolicyReviseService settlementPolicyReviseService;


    @GetMapping("list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<SettlementPolicyReviseListOut>> list(SettlementPolicyReviseInput input) {
        PageUtils<SettlementPolicyReviseListOut> page = settlementPolicyReviseService.findSettlementPolicyReviseList(input);
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<SettlementPolicyReviseOut> info(@PathVariable(value = "id", required = false)
                                                   @NotNull(message = "操作的数据id不能为空")
                                                   @ApiParam(value = "详情ID") Integer id) {
        SettlementPolicyReviseOut settlementPolicyRevise = settlementPolicyReviseService.findSettlementPolicyReviseById(id);
        return Result.success(settlementPolicyRevise);
    }
}
