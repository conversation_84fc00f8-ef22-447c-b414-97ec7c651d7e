package com.mpolicy.manage.modules.agentApply.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinApplyEntity;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyBasicInfoOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExportVo;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyPageListOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyPageListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代理人线上入职用户申请信息表
 * 
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
public interface BlAgentOnlineJoinApplyDao extends BaseMapper<BlAgentOnlineJoinApplyEntity> {
    /**
     * 代理人申请分页查询
     * @param page
     * @param input
     * @return
     */
    IPage<AgentApplyPageListOut> pageList(@Param("page") IPage page, @Param("input") AgentApplyPageListVO input);

    /**
     * 代理人申请导出信息查询
     * @param input
     * @return
     */
    List<AgentApplyExportVo> export(@Param("input") AgentApplyPageListVO input);

    AgentApplyBasicInfoOut findArea(@Param("agentCode") String agentCode);
}
