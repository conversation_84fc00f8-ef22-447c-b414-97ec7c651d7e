package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileDiffBacklogEntity;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogInfo;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogOut;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogVo;

import java.util.Map;

/**
 * 保司结算对账单差异待办
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcileDiffBacklogService extends IService<SettlementReconcileDiffBacklogEntity> {

    /**
     * 分页查询结算待办事项列表
     *
     * @param params:
     * @return : com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.confirm.SettlementReconcileConfirmInfo>
     * <AUTHOR>
     * @date 2023/5/26 15:13
     */
    PageUtils<SettlementReconcileBacklogInfo> querySettlementBacklogInfoServiceList(Map<String, Object> params);

    /**
     * 查看待办
     *
     * @param backlogCode:
     * @return : void
     * <AUTHOR>
     * @date 2023/5/29 14:58
     */
    SettlementReconcileBacklogOut querySettlementBacklogInfo(String backlogCode);

    /**
     * 删除待办
     *
     * @param backlogCode:
     * @return : void
     * <AUTHOR>
     * @date 2023/5/29 14:59
     */
    void deleteSettlementBacklogInfo(String backlogCode);

    /**
     * 处理待办
     *
     * @param settlementReconcileBacklogVo:
     * @return : java.lang.String
     * <AUTHOR>
     * @date 2023/5/29 15:44
     */
    String handleDiff(SettlementReconcileBacklogVo settlementReconcileBacklogVo);
}

