package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyInfoEntity;
import lombok.Data;

import java.io.Serializable;
@Data
public class SettlementPolicyReviseOut implements Serializable {
    private static final long serialVersionUID = -2258251382716023262L;

    private SettlementPolicyInfoEntity beforeData; // 修改前数据

    private SettlementPolicyInfoEntity afterData; // 修改后数据
}
