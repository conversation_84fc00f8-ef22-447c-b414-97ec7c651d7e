package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcilePolicyEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileDiffExcel;

import java.util.List;

/**
 * 保司结算对账单关联数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcilePolicyService extends IService<SettlementReconcilePolicyEntity> {

    List<SettlementReconcileDiffExcel> findSettlementReconcileDiffList(List<String> billCodeList);
}

