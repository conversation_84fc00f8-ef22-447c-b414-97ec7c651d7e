package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class HandleRefreshVo implements Serializable {
    private static final long serialVersionUID = 6103884215777214607L;

    @NotNull(message = "ID不能为空")
    private Integer id;

    /**
     * 事件状态
     */
    private Integer eventStatus;
    /**
     * 事件信息
     */
    private String eventMessage;
    /**
     * 收入处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer incomeEventStatus;
    /**
     * 小鲸收入事件响应信息
     */
    private String incomeEventMessage;
    /**
     * 支出处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer costEventStatus;
    /**
     * 支出事件响应信息
     */
    private String costEventMessage;
    /**
     * 混合单收入处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer contractIncomeEventStatus;
    /**
     * 混合收入事件响应信息
     */
    private String contractIncomeEventMessage;
}
