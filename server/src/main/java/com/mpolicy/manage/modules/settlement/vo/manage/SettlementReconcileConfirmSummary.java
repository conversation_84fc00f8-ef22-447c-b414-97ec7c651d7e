package com.mpolicy.manage.modules.settlement.vo.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 对账单保单科目汇总信息
 *
 * <AUTHOR>
 * @since 2023/6/1 00:44
 */
@Data
@ApiModel(value = "对账单对账汇总信息", description = "对账单对账汇总信息")
public class SettlementReconcileConfirmSummary implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 总保费
     */
    @ApiModelProperty(value = "对账单总保费", example = "529.00")
    private BigDecimal reconcileTotalPremium = BigDecimal.ZERO;

    /**
     * 总保费
     */
    @ApiModelProperty(value = "对账单结算金额", example = "529.00")
    private BigDecimal reconcileSettlementAmount = BigDecimal.ZERO;

    /**
     * 线上对账单汇总
     */
    @ApiModelProperty(value = "线上对账单汇总")
    private List<SettlementReconcileConfirmSubject> onlineSummary;

    /**
     * 线下对账单汇总
     */
    @ApiModelProperty(value = "线下对账单汇总")
    private List<SettlementReconcileConfirmSubject>  offlineSummary;
}
