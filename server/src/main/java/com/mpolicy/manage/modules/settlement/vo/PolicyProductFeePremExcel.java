package com.mpolicy.manage.modules.settlement.vo;

import cn.hutool.core.annotation.Alias;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/2/27 15:53
 * @Version 1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class PolicyProductFeePremExcel extends BaseRowModel {

    /**
     * "保单号"
     */
    @Alias("保单号")
    @ExcelProperty(value = "保单号", index = 0)
    private String policyNo;

    /**
     * "批单号"
     */
    @Alias("批单号")
    @ExcelProperty(value = "批单号", index = 1)
    private String batchCode;

    /**
     * "保司协议险种编码"
     */
    @Alias("保司协议险种编码")
    @ExcelProperty(value = "保司协议险种编码", index = 2)
    private String insuranceProductCode;

    /**
     * "小鲸险种编码"
     */
    @Alias("小鲸险种编码")
    @ExcelProperty(value = "小鲸险种编码", index = 3)
    private String productCode;

    /**
     * "税前保费（元）"
     */
    @Alias("税前保费（元）")
    @ExcelProperty(value = "税前保费（元）", index = 4)
    private String premium;

    /**
     * "税后保费（元）"
     */
    @Alias("税后保费（元）")
    @ExcelProperty(value = "税后保费（元）", index = 5)
    private String taxAfterPremium;

    /**
     * "税率"
     */
    @Alias("税率")
    @ExcelProperty(value = "税率", index = 6)
    private String taxRate;

    /**
     * "税率编码"
     */
//    @Alias("税率编码")
//    @ExcelProperty(value = "税率编码", index = 7)
    private String premCode;

    @ExcelProperty(value = "校验信息", index = 7)
//    @Alias("校验信息")
    private String errorMsg;

}
