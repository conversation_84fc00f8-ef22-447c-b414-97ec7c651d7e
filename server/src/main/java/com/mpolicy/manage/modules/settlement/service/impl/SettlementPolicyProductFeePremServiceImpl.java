package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.enums.PremChangeTypeEnum;
import com.mpolicy.manage.enums.SettlementMethodEnum;
import com.mpolicy.manage.helper.AdminBaseHelper;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductEntity;
import com.mpolicy.manage.modules.protocol.enums.SettlementProtocolEventEnum;
import com.mpolicy.manage.modules.protocol.helper.SettlementProtocolHelper;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductService;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementPremChangeLogService;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.manage.utils.ProductPremUtil;
import com.mpolicy.manage.utils.TxUtil;
import com.mpolicy.policy.common.ep.policy.OptUserInfoVo;
import com.mpolicy.policy.common.ep.policy.common.PolicyFileImportApplyResult;
import com.mpolicy.policy.common.ep.policy.common.PolicyFileImportApplyVo;
import generator.domain.SettlementPolicyProductFeePrem;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductFeePremService;
import com.mpolicy.manage.modules.settlement.dao.SettlementPolicyProductFeePremMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【settlement_policy_product_fee_prem】的数据库操作Service实现
 * @createDate 2025-02-27 14:16:04
 */

@Slf4j
@Service
public class SettlementPolicyProductFeePremServiceImpl extends ServiceImpl<SettlementPolicyProductFeePremMapper, SettlementPolicyProductFeePrem>
        implements SettlementPolicyProductFeePremService {


    @Autowired
    private IEpProtocolInsuranceProductService protocolInsuranceProductService;

    @Autowired
    private SysDocumentService sysDocumentService;


    @Autowired
    private SettlementPremChangeLogService settlementPremChangeLogService;


    @Override
    public PageUtils<SettlementPolicyProductTaxPremListOut> pageList(SettlementPolicyProductTaxPremListInput input) {

        IPage<SettlementPolicyProductTaxPremListOut> page = baseMapper.findSettlementPolicyProductPremList(
                new Page<SettlementPolicyProductTaxPremListOut>(input.getPage(), input.getLimit()), input);
        page.getRecords().forEach(action -> {
            action.setSettlementMethod(SettlementMethodEnum.matchSearchCode(action.getSettlementMethod()).getDesc());
        });
        return new PageUtils(page);

    }

    @Override
    public void updatePolicyTaxPremList(List<SettlementPolicyProductPremEntity> list, SysUserEntity userEntity) {


        if (CollectionUtil.isEmpty(list)) {
            return;
        }

        List<SettlementPolicyProductFeePrem> taxPolicyProductFeePremList = list.stream()
                .filter(x -> Objects.equals(x.getSettlementMethod(), SettlementMethodEnum.NET_PREMIUM.getCode()) )
                .map(
                        x -> {
                            SettlementPolicyProductFeePrem r = new SettlementPolicyProductFeePrem();
                            r.setPolicyNo(x.getPolicyNo());
                            r.setBatchCode(x.getBatchCode());
                            r.setReconcileType(x.getReconcileType());
                            r.setProductCode(x.getProductCode());
                            r.setInsuranceProductCode(x.getInsuranceProductCode());
                            r.setPremium(x.getPremium());
                            r.setTaxAfterPremium(x.getTaxAfterPremium());
                            r.setTaxRate(x.getTaxRate());
                            return r;
                        }
                ).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(taxPolicyProductFeePremList)) {
            return;
        }

        //生成费率编码
        List<String> policyNos = taxPolicyProductFeePremList
                .stream()
                .map(SettlementPolicyProductFeePrem::getPolicyNo)
                .distinct()
                .collect(Collectors.toList());

        Map<String, SettlementPolicyProductFeePrem> policyPremMap =
                this.lambdaQuery().in(SettlementPolicyProductFeePrem::getPolicyNo, policyNos)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(ProductPremUtil::getTaxFeeFactor
                                , v -> v
                                , (v1, v2) -> {
                                    log.warn(StrUtil.format("数据库中存在相同premCode他们的ID是[{}-{}]的数据,请联系开发人员检查") );
                                    return v1;
                                }
                        ));
        // 新增费率信息
        List<SettlementPolicyProductFeePrem> insertPolicyPremList = new ArrayList<>();
        List<SettlementPolicyProductFeePrem> updatePolicyPremList = new ArrayList<>();
        List<String> removePolicyPremList = new ArrayList<>();
        taxPolicyProductFeePremList.forEach(action -> {
            action.setReconcileType(action.getReconcileType());
            action.setPremCode(ProductPremUtil.getTaxFeeFactor(action));
            // 费率的匹配因子一样
            if (policyPremMap.containsKey(action.getPremCode())) {
                SettlementPolicyProductFeePrem settlementPolicyProductPrem = policyPremMap.get(action.getPremCode());
                //判断他们的费率信息是否一样,如果不一样才处理
                if (!ProductPremUtil.getPolicyTaxRateCode(action)
                        .equals(ProductPremUtil.getPolicyTaxRateCode(settlementPolicyProductPrem))) {
                    action.setId(settlementPolicyProductPrem.getId());
                    String premCode = ProductPremUtil.getTaxFeeFactor(action);
                    action.setPremCode(premCode);
                    updatePolicyPremList.add(action);
                    removePolicyPremList.add(settlementPolicyProductPrem.getPremCode());
                }
            } else {
                // 匹配因子不一样直接进行追加
                String premCode = ProductPremUtil.getTaxFeeFactor(action);
                action.setPremCode(premCode);
                insertPolicyPremList.add(action);
            }
        });


        //批量插入
        if (!insertPolicyPremList.isEmpty()) {
            baseMapper.insertBatchSomeColumn(insertPolicyPremList);
        }
        if (!updatePolicyPremList.isEmpty()) {
            // 批量更新数据
            this.updateBatchById(updatePolicyPremList);
        }
        if (removePolicyPremList.isEmpty() && updatePolicyPremList.isEmpty() && insertPolicyPremList.isEmpty()) {
            log.info("数据没有变更不处理.....");
            return;
        }
    }

}
