package com.mpolicy.manage.modules.agentApply.controller;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineQuestionService;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamPageListOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamPageListVO;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamSourceOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyQuestionOut;
import com.mpolicy.manage.modules.sys.entity.SysConfigEntity;
import com.mpolicy.manage.modules.sys.service.SysConfigService;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.web.common.annotation.Lock;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

/**
 * ClassName: BlAgentOnlineApplyAnswerController
 * Description:
 * date: 2022/12/7 13:47
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("agent/apply/exam")
@Api(tags = "代理人线上入职考试信息")
public class BlAgentOnlineApplyExamController {

    @Autowired
    private BlAgentOnlineQuestionService blAgentOnlineQuestionService;
    @Autowired
    private SysConfigService sysConfigService;

    /**
     * 分页获取代理人入职申请信息
     * @param input
     * @return
     */
    @GetMapping("pageList")
    @RequiresPermissions(value = {"agent:exam:all"})
    @ApiOperation(value = "分页获取代理人入职考试信息列表", notes = "分页获取代理人入职考试信息列表")
    public Result<PageUtils<AgentApplyExamPageListOut>> pageList(AgentApplyExamPageListVO input) {
        PageUtils<AgentApplyExamPageListOut> page = blAgentOnlineQuestionService.pageList(input);
        return Result.success(page);
    }

    /**
     * 获取考试合格分数
     * @return
     */
    @ApiOperation(value = "获取考试合格分数")
    @GetMapping("/passSource")
    public Result<AgentApplyExamSourceOut> passSource() {
        AgentApplyExamSourceOut out = new AgentApplyExamSourceOut();
        out.setScore(ConstantCacheHelper.getValue(Constant.AGENT_ONLINE_APPLY_PASS_SCORE,"60"));
        return Result.success(out);
    }

    /**
     * 更新考试合格分数
     * @return
     */
    @ApiOperation(value = "更新考试合格分数")
    @PostMapping("/updatePassSource")
    public Result updatePassSource(@RequestBody @Valid AgentApplyExamSourceOut vo) {
        SysConfigEntity sysConfigEntity = sysConfigService.lambdaQuery().eq(SysConfigEntity::getParamKey,Constant.AGENT_ONLINE_APPLY_PASS_SCORE).one();
        if(Objects.isNull(sysConfigEntity)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("考试合格分数未配置"));
        }
        Integer standard = Integer.valueOf(vo.getScore());
        if(standard <= Constant.AGENT_ONLINE_APPLY_PASS_SCORE_MIN || standard>Constant.AGENT_ONLINE_APPLY_PASS_SCORE_MAX){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("考试合格分数区间错误"));
        }
        sysConfigEntity.setParamValue(vo.getScore());
        sysConfigService.update(sysConfigEntity);
        return Result.success();
    }

    /**
     * 根据code获取题目及答案信息
     * @param code
     * @return
     */
    @ApiOperation(value = "根据code获取题目及答案信息")
    @GetMapping("/info/{code}")
    @RequiresPermissions(value = {"agent:exam:all"})
    public Result<AgentApplyQuestionOut> info(@ApiParam(value = "题目编码", required = true)
                                          @PathVariable("code") String code) {
        AgentApplyQuestionOut out = blAgentOnlineQuestionService.info(code);
        return Result.success(out);
    }

    /**
     * 根据题目code删除题目信息
     * @param code
     * @return
     */
    @ApiOperation(value = "根据code获取删除题目信息")
    @GetMapping("/delete/{code}")
    @RequiresPermissions(value = {"agent:exam:all"})
    public Result delete(@ApiParam(value = "题目编码", required = true)
                                              @PathVariable("code") String code) {
        blAgentOnlineQuestionService.delete(code);
        return Result.success();
    }

    /**
     * 新增考试题目
     * @param input
     * @return
     */
    @ApiOperation(value = "新增题目")
    @PostMapping("/save")
    @RequiresPermissions(value = {"agent:exam:all"})
    @Lock(keys = "#input.title", attemptTimeout = 10)
    public Result save(@RequestBody @Valid AgentApplyQuestionOut input) {
        blAgentOnlineQuestionService.save(input);
        return Result.success();
    }

    /**
     * 修改考试题目
     * @param input
     * @return
     */
    @ApiOperation(value = "修改考试题目")
    @PostMapping("/update")
    @RequiresPermissions(value = {"agent:exam:all"})
    @Lock(keys = "#input.title", attemptTimeout = 10)
    public Result update(@RequestBody @Valid AgentApplyQuestionOut input) {
        ValidatorUtils.validateEntity(input, UpdateGroup.class);
        blAgentOnlineQuestionService.update(input);
        return Result.success();
    }
}
