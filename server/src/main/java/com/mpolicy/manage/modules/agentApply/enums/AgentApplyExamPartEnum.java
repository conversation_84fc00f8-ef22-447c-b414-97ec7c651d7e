package com.mpolicy.manage.modules.agentApply.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * ClassName: AgentApplyStatusEnum
 * Description: 代理人线上考试题目所属部分枚举
 * date: 2022/11/25 15:56
 *
 * <AUTHOR>
 */
@Getter
public enum AgentApplyExamPartEnum {
    //所属部分(1:一 2:二 3:三)
    PART1(1, "一"),
    PART2(2, "二"),
    PART3(3, "三"),
    ;

    private final Integer code;
    private final String name;

    AgentApplyExamPartEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AgentApplyExamPartEnum getEnumByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        AgentApplyExamPartEnum examPartEnum = Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
        return Objects.isNull(examPartEnum)?null:examPartEnum.getName();
    }
}
