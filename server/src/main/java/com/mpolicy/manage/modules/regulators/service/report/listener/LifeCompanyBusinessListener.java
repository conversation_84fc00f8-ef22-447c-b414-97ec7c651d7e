package com.mpolicy.manage.modules.regulators.service.report.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.modules.regulators.service.report.data.LifeCompanyBusinessData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 代理人身险公司业务表报告数据解析
 *
 * <AUTHOR>
 * @date 2022-01-21 18:48
 */
@Slf4j
public class LifeCompanyBusinessListener extends ReportObjectEventListener<Object, LifeCompanyBusinessData> {

    List<LifeCompanyBusinessData> datas = new ArrayList<>();

    @Override
    public List<LifeCompanyBusinessData> getReadData() {
        return datas;
    }

    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.debug("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);

        // 暂时用判断 超过14行，不解析
        JSONArray lineData = JSON.parseArray(xlsLineData);
        if(lineData.size() < 15){
            return;
        }
        // 解析赋值对象
        LifeCompanyBusinessData bean = new LifeCompanyBusinessData();
        bean.setProjectName(lineData.getString(0));
        bean.setLineNumber(lineData.getString(1));
        bean.setNewPolicyPrem(lineData.getString(2));
        bean.setRenewalPolicyPrem(lineData.getString(3));
        bean.setPayablePrem(lineData.getString(4));
        bean.setNewPolicyProxyCommission(lineData.getString(5));
        bean.setRenewalPolicyProxyCommission(lineData.getString(6));

        bean.setInNewPolicyPrem(lineData.getString(7));
        bean.setInRenewalPolicyPrem(lineData.getString(8));
        bean.setInNewPolicyProxyCommission(lineData.getString(9));
        bean.setInRenewalPolicyProxyCommission(lineData.getString(10));

        bean.setEscrowNewPolicyPrem(lineData.getString(11));
        bean.setEscrowRenewalPolicyPrem(lineData.getString(12));
        bean.setEscrowNewPolicyProxyCommission(lineData.getString(13));
        bean.setEscrowRenewalPolicyProxyCommission(lineData.getString(14));
        // 父节点逻辑写死
        bean.setParentLineNumber(builderParentLineNumber(bean.getLineNumber()));
        datas.add(bean);
    }


    /**
     * <p>
     * 结合导入的模板文件，进行父节点写死配置
     * </p>
     *
     * @param lineNumber lineNumber
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/21
     */
    private String builderParentLineNumber(String lineNumber) {
        if (StringUtils.isBlank(lineNumber)) {
            return StrUtil.EMPTY;
        }
        int number = Integer.parseInt(lineNumber);
        if (number == 1 || number == 16 || number == 20 || number == 23 || number == 24) {
            return "0";
        }
        if(number == 2 || number == 7 || number == 12 || number == 14){
            return "1";
        }
        // 3~6 > 2
        if (number > 2 && number < 7) {
            return "2";
        }
        // 8~11 > 7
        if (number > 7 && number < 12) {
            return "7";
        }
        if (number == 13) {
            return "12";
        }
        if (number == 15) {
            return "14";
        }
        // 17~19 > 16
        if (number > 16 && number < 20) {
            return "16";
        }
        return "20";
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
