package com.mpolicy.manage.modules.settlement.entity;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class ApplyForInvoicingVo implements Serializable {
    private static final long serialVersionUID = -9140800492642421630L;

    @NotEmpty(message = "请选择开票对账单")
    private List<ApplyForInvoicingReconcileItem> applyForInvoicingReconcileList;


    private List<String> file;
}
