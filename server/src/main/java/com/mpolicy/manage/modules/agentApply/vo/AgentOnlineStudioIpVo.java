package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 线上代理人入职申请工作室信息
 * @Date 15:38 2024/4/8
 * @Param 
 * <AUTHOR>
 * @return 
 **/
@Data
public class AgentOnlineStudioIpVo implements Serializable {
    /**
     * 工作室勋章
     */
    @ApiModelProperty(value = "工作室勋章")
    private String studioTag;
    /**
     * 知乎名称
     */
    @ApiModelProperty(value = "知乎名称")
    private String zhihuName;
    /**
     * 知乎粉丝数
     */
    @ApiModelProperty(value = "知乎粉丝数")
    private String zhihuFans;
    /**
     * 知乎认证
     */
    @ApiModelProperty(value = "知乎认证")
    private String zhihuVerified;
    /**
     * 知乎标签
     */
    @ApiModelProperty(value = "知乎标签")
    private String zhihuTag;
    /**
     * 知乎文章数
     */
    @ApiModelProperty(value = "知乎文章数")
    private Integer zhihuCount;
    /**
     * 微博名
     */
    @ApiModelProperty(value = "微博名")
    private String weiboName;
    /**
     * 微博粉丝数
     */
    @ApiModelProperty(value = "微博粉丝数")
    private String weiboFans;
    /**
     * 微博认证
     */
    @ApiModelProperty(value = "微博认证")
    private String weiboVerified;
    /**
     * 微博介绍
     */
    @ApiModelProperty(value = "微博介绍")
    private String weiboIntro;
    /**
     * 微博文章数
     */
    @ApiModelProperty(value = "微博文章数")
    private Integer weiboCount;
    /**
     * 公众号账号
     */
    @ApiModelProperty(value = "公众号账号")
    private String wechatPublicPlatformName;
    /**
     * 公众号账号介绍
     */
    @ApiModelProperty(value = "公众号账号介绍")
    private String wechatPublicPlatformIntro;
    /**
     * 公众号账号文章数
     */
    @ApiModelProperty(value = "公众号账号文章数")
    private Integer wechatPublicPlatformCount;
    /**
     * 抖音名
     */
    @ApiModelProperty(value = "抖音名")
    private String douyinName;
    /**
     * 抖音粉丝数
     */
    @ApiModelProperty(value = "抖音粉丝数")
    private String douyinFans;
    /**
     * 抖音点赞数
     */
    @ApiModelProperty(value = "抖音点赞数")
    private String douyinLikeNum;
    /**
     * 抖音认证
     */
    @ApiModelProperty(value = "抖音认证")
    private String douyinVerified;
    /**
     * 抖音介绍
     */
    @ApiModelProperty(value = "抖音介绍")
    private String douyinIntro;
    /**
     * 抖音文章数
     */
    @ApiModelProperty(value = "抖音文章数")
    private Integer douyinCount;
}
