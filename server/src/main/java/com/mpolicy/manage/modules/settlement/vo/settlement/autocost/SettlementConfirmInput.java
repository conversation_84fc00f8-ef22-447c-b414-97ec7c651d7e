package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/19 10:23
 * @Version 1.0
 */
@Data
public class SettlementConfirmInput extends BasePage {

    @ApiModelProperty("结佣月份")
    private String costSettlementCycle;

    @ApiModelProperty("结算状态")
    private String programmeRecordStatus;

}
