package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算保司配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:32:26
 */
@TableName("settlement_reconcile_company_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileCompanyInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 结算保司编码
     */
    private String reconcileCompanyCode;
    /**
     * 结算保司名称
     */
    private String reconcileCompanyName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 结算保司编码
     */
    private String companyCode;
    /**
     * 结算保司名称
     */
    private String companyName;
    /**
     * 产品所属分公司编码
     */
    private String settlementCompanyCode;
    /**
     * 产品所属分公司名称
     */
    private String settlementCompanyName;
    /**
     * 规则科目数量
     */
    private Integer subjectRuleCount;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
