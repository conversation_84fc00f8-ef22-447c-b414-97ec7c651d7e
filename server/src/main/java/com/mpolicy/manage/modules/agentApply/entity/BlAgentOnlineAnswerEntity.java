package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人入职申请题目答案表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-07 13:40:42
 */
@TableName("bl_agent_online_answer")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineAnswerEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 所属题目code
     */
    private String qCode;
    /**
     * 答案
     */
    private String answer;
    /**
     * 是否正确(0:不正确 1:正确)
     */
    private Integer isRight;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
