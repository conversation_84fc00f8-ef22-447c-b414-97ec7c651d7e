package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人线上入职审核表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
@TableName("bl_agent_online_join_history")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineJoinHistoryEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    private String agentCode;
    /**
     * 驳回原因
     */
    private String reason;
    /**
     * 审核状态(0:通过 -1:不通过)
     */
    private Integer status;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
}
