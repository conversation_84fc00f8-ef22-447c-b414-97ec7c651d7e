package com.mpolicy.manage.modules.settlement.dao;

import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeSubjectEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeSubjectVo;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 10:39 上午
 * @Version 1.0
 */
public interface SettlementProgrammeSubjectDao extends ImsBaseMapper<SettlementCostProgrammeSubjectEntity> {
    /**
     * 根据方案查询科目名称
     * @param programmeCode
     * @return
     */
    List<SettlementProgrammeSubjectVo> listProgrammeSubject(@Param("programmeCode")String programmeCode);
}
