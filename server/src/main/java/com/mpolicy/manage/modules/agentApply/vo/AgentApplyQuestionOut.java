package com.mpolicy.manage.modules.agentApply.vo;

import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentApplyInfoOut
 * Description: 代理人考试信息详情
 * date: 2022/11/29 10:56
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyQuestionOut implements Serializable {

    @ApiModelProperty("id")
    private Integer id;
    /**
     * 题目编码
     */
    @ApiModelProperty("题目编码")
    @NotBlank(message = "题目编码不能为空",groups = UpdateGroup.class)
    private String code;
    /**
     * 所属部分(1:一 2:二 3:三)
     */
    @ApiModelProperty("所属部分(1:一 2:二 3:三)")
    @NotNull(message = "所属不能不能为空")
    @Min(value = 1,message = "所属部分传值不正确")
    @Max(value = 3,message = "所属部分传值不正确")
    private Integer part;
    @ApiModelProperty("所属部分(1:一 2:二 3:三)")
    private String partName;
    /**
     * 题型(1:单选题 2:多选题 3:判断题)
     */
    @ApiModelProperty("题型(1:单选题 2:多选题 3:判断题)")
    @NotNull(message = "题型不能为空")
    @Min(value = 1,message = "题型传值不正确")
    @Max(value = 3,message = "题型传值不正确")
    private Integer type;
    @ApiModelProperty("题型(1:单选题 2:多选题 3:判断题)")
    private String typeName;
    /**
     * 分值
     */
    @ApiModelProperty("分值")
    @NotBlank(message = "分值不能为空")
    @Pattern(regexp = "^[0-9]*$", message = "合格分数只能为数字")
    private String score;
    /**
     * 题目
     */
    @ApiModelProperty("题目")
    @NotBlank(message = "题目不能为空")
    private String title;

    @ApiModelProperty("题目答案")
    @NotEmpty(message = "答案信息不能为空")
    @Valid
    private List<AgentApplyAnswerOut> answer;
}
