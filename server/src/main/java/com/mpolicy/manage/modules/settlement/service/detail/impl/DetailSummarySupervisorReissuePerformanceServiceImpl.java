package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsProductDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummarySupervisorPerformance;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/2/3 23:58
 * @Version 1.0
 */
@Service
public class DetailSummarySupervisorReissuePerformanceServiceImpl extends AbsProductDimensionService<List<DetailSummarySupervisorPerformance>> {
    @Override
    public List<DetailSummarySupervisorPerformance> querySummary(PageSettlementDetailParams p) {

        DetailSummarySupervisorPerformance summaryLongReissuePromotion = costAutoInfoDao.supervisorPerformanceSummary(p);

        DetailSummarySupervisorPerformance supervisorPromotion = null;
        if (Objects.nonNull(summaryLongReissuePromotion)) {
            summaryLongReissuePromotion.setMonth(calMonthBeforeBeforeLastMonth(p) + "（补发）");

            //查询上个周期的暂发
            PageSettlementDetailParams lastCycleParams = JSONObject.parseObject(JSONObject.toJSONString(p), PageSettlementDetailParams.class);
            lastCycleParams.setSettlementMonth(calLastMonthCycle(p));
             supervisorPromotion = costAutoInfoDao.supervisorPerformanceSummary(lastCycleParams);
             
        }
        DetailSummarySupervisorPerformance totalSummary = new DetailSummarySupervisorPerformance();
        totalSummary.setMonth(calMonthBeforeBeforeLastMonth(p));
        Optional<DetailSummarySupervisorPerformance> summaryLongReissuePromotionOpt = Optional.ofNullable(summaryLongReissuePromotion);
        Optional<DetailSummarySupervisorPerformance> longPromotionOpt = Optional.ofNullable(supervisorPromotion);

        totalSummary.setPromotion(
                summaryLongReissuePromotionOpt.map(DetailSummarySupervisorPerformance::getPromotion).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummarySupervisorPerformance::getPromotion).orElse(BigDecimal.ZERO))
        );

        totalSummary.setGrantAmount(
                summaryLongReissuePromotionOpt.map(DetailSummarySupervisorPerformance::getGrantAmount).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummarySupervisorPerformance::getGrantAmount).orElse(BigDecimal.ZERO))
        );

        totalSummary.setCommissionRate(
                summaryLongReissuePromotionOpt.map(DetailSummarySupervisorPerformance::getCommissionRate).orElse(BigDecimal.ZERO)
        );
        totalSummary.setGrantRate(
                summaryLongReissuePromotionOpt.map(DetailSummarySupervisorPerformance::getGrantRate).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummarySupervisorPerformance::getGrantRate).orElse(BigDecimal.ZERO))
        );

        return Lists.newArrayList(totalSummary, supervisorPromotion, summaryLongReissuePromotion);


    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.SUPERVISOR_REISSUE_PERFORMANCE);
    }
}
