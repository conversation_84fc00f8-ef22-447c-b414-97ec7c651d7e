package com.mpolicy.manage.modules.regulators.service.report.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.modules.regulators.service.report.data.PropertyCompanyBusinessData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 代理产险公司业务表报告数据解析
 *
 * <AUTHOR>
 * @date 2022-01-21 18:48
 */
@Slf4j
public class PropertyCompanyBusinessListener extends ReportObjectEventListener<Object, PropertyCompanyBusinessData> {

    List<PropertyCompanyBusinessData> datas = new ArrayList<>();

    @Override
    public List<PropertyCompanyBusinessData> getReadData() {
        return datas;
    }

    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.info("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);

        // 暂时用判断 超过14行，不解析
        JSONArray lineData = JSON.parseArray(xlsLineData);
        if(lineData.size() < 9){
            return;
        }
        // 解析赋值对象
        PropertyCompanyBusinessData bean = new PropertyCompanyBusinessData();
        bean.setProjectName(lineData.getString(0));
        bean.setLineNumber(lineData.getString(1));
        bean.setPolicyPrem(lineData.getString(2));
        bean.setPayablePrem(lineData.getString(3));
        bean.setProxyCommission(lineData.getString(4));


        bean.setInPolicyPrem(lineData.getString(5));
        bean.setInProxyCommission(lineData.getString(6));
        bean.setEscrowPolicyPrem(lineData.getString(7));
        bean.setEscrowProxyCommission(lineData.getString(8));

        // 父节点逻辑写死
        bean.setParentLineNumber(builderParentLineNumber(bean.getLineNumber()));
        datas.add(bean);
    }


    /**
     * <p>
     * 结合导入的模板文件，进行父节点写死配置
     * </p>
     *
     * @param lineNumber lineNumber
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/21
     */
    private String builderParentLineNumber(String lineNumber) {
        if (StringUtils.isBlank(lineNumber)) {
            return StrUtil.EMPTY;
        }
        int number = Integer.parseInt(lineNumber);
        if (number == 3) {
            return "2";
        }
        if (number == 5) {
            return "4";
        }
        // 10/11 > 9
        if (number == 10 || number == 11) {
            return "9";
        }

        if (number == 17) {
            return "16";
        }
        if (number == 19) {
            return "18";
        }
        return "0";
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
