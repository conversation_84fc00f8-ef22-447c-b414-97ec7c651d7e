package com.mpolicy.manage.modules.regulators.service.report.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 代理人身险公司业务表报告数据
 *
 * <AUTHOR>
 * @date 2022-01-21 13:26
 */
@Data
@ApiModel(value = "代理人身险公司业务表报告数据")
public class LifeCompanyBusinessData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目", example = "寿险小计")
    private String projectName;

    @ApiModelProperty(value = "行次", example = "1")
    private String lineNumber;

    @ApiModelProperty(value = "父节点行次", example = "1")
    private String parentLineNumber;

    @ApiModelProperty(value = "新保单保费-累计", example = "1")
    private String newPolicyPrem;

    @ApiModelProperty(value = "续期保单保费-累计", example = "1")
    private String renewalPolicyPrem;

    @ApiModelProperty(value = "应付保费-累计", example = "1")
    private String payablePrem;

    @ApiModelProperty(value = "新的代理佣金累计-累计", example = "1")
    private String newPolicyProxyCommission;

    @ApiModelProperty(value = "续期代理佣金累计-累计", example = "1")
    private String renewalPolicyProxyCommission;


    @ApiModelProperty(value = "自营新保单保费", example = "1")
    private String inNewPolicyPrem;

    @ApiModelProperty(value = "自营续期保单保费", example = "1")
    private String inRenewalPolicyPrem;

    @ApiModelProperty(value = "自营新的代理佣金累计", example = "1")
    private String inNewPolicyProxyCommission;

    @ApiModelProperty(value = "自营续期代理佣金累计", example = "1")
    private String inRenewalPolicyProxyCommission;


    @ApiModelProperty(value = "第三方自营新保单保费", example = "1")
    private String escrowNewPolicyPrem;

    @ApiModelProperty(value = "第三方自营续期保单保费", example = "1")
    private String escrowRenewalPolicyPrem;

    @ApiModelProperty(value = "第三方自营新的代理佣金累计", example = "1")
    private String escrowNewPolicyProxyCommission;

    @ApiModelProperty(value = "第三方自营续期代理佣金累计", example = "1")
    private String escrowRenewalPolicyProxyCommission;
}
