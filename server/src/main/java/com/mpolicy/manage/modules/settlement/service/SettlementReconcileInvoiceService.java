package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.invoice.vo.third.FttmInvoiceOrderInfoVo;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceEntity;
import com.mpolicy.manage.modules.settlement.vo.invoice.*;

import java.util.List;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */
public interface SettlementReconcileInvoiceService extends IService<SettlementReconcileInvoiceEntity> {

    /**
     * 获取对账单发票信息列表
     *
     * @param input
     * @return
     */
    PageUtils<SettlementReconcileInvoiceListOut> findSettlementReconcileInvoiceList(SettlementReconcileInvoiceListInput input);

    /**
     * 获取对账单发票信息详情
     *
     * @param invoiceCode 发票编码
     * @return
     */
    SettlementReconcileInvoiceInfoOut findSettlementReconcileInvoiceByInvoiceCode(String invoiceCode);

    /**
     * 新增对账单发票信息数据
     *
     * @param input
     * @return
     */
    void saveSettlementReconcileInvoice(SettlementReconcileInvoiceSaveInput input);

    /**
     * 修改对账单发票信息数据
     *
     * @param input
     * @return
     */
    void updateSettlementReconcileInvoiceById(SettlementReconcileInvoiceUpdateInput input);

    /**
     * 开票财务审核
     * @param input 审核信息
     */
    void financeExamine(FinanceExamineVo input);
    /**
     * 系统回退
     * @param input 系统回退请求参数
     */
    void systemRollback(SystemRollbackVo input);
    /**
     * 发送至保司
     * @param input 发送至保司信息
     */
    void sendToCompany(SendToCompanyVo input);

    /**
     * 更新快递信息
     * @param input 快递信息
     */
    void updateExpress(UpdateExpressVo input);

    /**
     * 查询开票轨迹信息
     * @param invoiceCode 申请编码
     * @return 轨迹列表
     */
    List<ReconcileInvoiceTrackListOut> findReconcileInvoiceTrackList(String invoiceCode);

    void invoiceToFttmRetry(String invoiceNum);

    void invoiceToFttm(String invoiceNum);

    /**
     * 根据发票编码获取发票关联对账单信息
     * @param invoiceCode 发票编码
     * @return 列表数据
     */
    List<ReconcileInvoiceSettlementReconcileOut> findReconcileInvoiceSettlementReconcileList(String invoiceCode);


    void handleBlueCallback(com.mpolicy.manage.modules.invoice.vo.third.Result<FttmInvoiceOrderInfoVo> vo);

    void releaseOccupationMoney(String invoiceCode);

    List<InvoiceReturnDetail> findSettlementReconcileInvoiceByReconcileCode(String reconcileCode);

    <T> void sendDingTalkMessage(Integer templateType, T t);
}

