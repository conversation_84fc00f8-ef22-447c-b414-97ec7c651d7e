package com.mpolicy.manage.modules.settlement.vo.backlog;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 保司结算对账单差异待办列表信息
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "保司结算对账单差异待办列表信息", description = "保司结算对账单差异待办列表信息")
public class SettlementReconcileBacklogInfo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 差异受理编号
     */
    @ApiModelProperty(value = "差异受理编号", example = "CY2023052302020200")
    private String backlogCode;
    /**
     * 汇总对账纪录编号
     */
    @ApiModelProperty(value = "汇总对账纪录编号", example = "CY2023052302020200")
    private String billCode;
    /**
     * 差异类型名称
     */
    @ApiModelProperty(value = "差异类型名称", example = "我司漏单")
    private String diffName;
    /**
     * 差异保单编号
     */
    @ApiModelProperty(value = "差异保单编号", example = "ASDF23456789")
    private String policyNo;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要", example = "修改完成")
    private String diffDesc;
    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人", example = "张三")
    private String createUser;
    /**
     * 代办人/处理人
     */
    @ApiModelProperty(value = "代办人/处理人", example = "李四")
    private String acceptUserName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2023-5-12 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间", example = "2023-5-12 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date diffFinishTime;

}
