package com.mpolicy.manage.modules.agentApply.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinApplyExtendEntity;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExtendInfoOut;
import org.apache.ibatis.annotations.Param;

/**
 * 代理人线上入职扩展信息
 * 
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
public interface BlAgentOnlineJoinApplyExtendDao extends BaseMapper<BlAgentOnlineJoinApplyExtendEntity> {


    AgentApplyExtendInfoOut findArea(@Param("agentCode") String agentCode);
}
