package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SettlementPolicyReviseListOut implements Serializable {
    private static final long serialVersionUID = 5944196409014586060L;

    private Integer id; // 主键id
    private String settlementCode; // 结算编码
    private String createUser; // 创建人
    private Date createTime; // 创建时间
}
