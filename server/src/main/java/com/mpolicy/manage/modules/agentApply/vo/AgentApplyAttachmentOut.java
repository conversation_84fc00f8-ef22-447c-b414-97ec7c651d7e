package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: AgentApplyAttachmentOut
 * Description:
 * date: 2022/11/30 11:20
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyAttachmentOut implements Serializable {
    private static final long serialVersionUID = -4051013835901841705L;

    @ApiModelProperty("文件的id")
    private Integer id;
    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型")
    private String identificationType;
    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String fileName;
    /**
     * 附件格式
     */
    @ApiModelProperty("附件格式")
    private String suffix;
    @ApiModelProperty("文件编码")
    private String fileCode;
    /**
     * 文件路径
     */
    @ApiModelProperty("文件地址")
    private String filePath;
}
