package com.mpolicy.manage.modules.settlement.controller;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyInfoService;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyInfoOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyInfoReviseVo;
import com.mpolicy.manage.modules.settlement.vo.policy.SettlementReconcilePolicyInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;

/**
 * 结算保单明细管理
 * 对账明细清单
 *
 * <AUTHOR>
 * @since 2023-05-24 15:59
 */
@RestController
@RequestMapping("/settlement/reconcile/policy")
@Api(tags = "结算保单明细管理")
@Slf4j
public class ReconcilePolicyController extends ReconcileBaseController {

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @ApiOperation(value = "分页查询保单信息列表", notes = "分页查询保单信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "settlementTimeStart", dataType = "String", value = "记账时间开始"),
            @ApiImplicitParam(paramType = "query", name = "settlementTimeEnd", dataType = "String", value = "记账时间结束"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "产品名称"),
            @ApiImplicitParam(paramType = "query", name = "approvedTimeStart", dataType = "String", value = "承保时间开始"),
            @ApiImplicitParam(paramType = "query", name = "approvedTimeEnd", dataType = "String", value = "承保时间结束"),
            @ApiImplicitParam(paramType = "query", name = "insuredNameMobileCard", dataType = "String", value = "被保人姓名/证件号/手机号"),
            @ApiImplicitParam(paramType = "query", name = "applicantNameMobileCard", dataType = "String", value = "投保人姓名/证件号/手机号"),
            @ApiImplicitParam(paramType = "query", name = "commodityType", dataType = "String", value = "产品类型"),
            @ApiImplicitParam(paramType = "query", name = "enforceTimeStart", dataType = "String", value = "生效时间开始"),
            @ApiImplicitParam(paramType = "query", name = "enforceTimeEnd", dataType = "String", value = "生效时间结束"),
            @ApiImplicitParam(paramType = "query", name = "policyStatus", dataType = "String", value = "保单状态"),
            @ApiImplicitParam(paramType = "query", name = "settlementCode", dataType = "String", value = "单据编号"),
            @ApiImplicitParam(paramType = "query", name = "serialNumber", dataType = "String", value = "订单号"),
            @ApiImplicitParam(paramType = "query", name = "settlementMonth", dataType = "String", value = "结算月份"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "销售渠道编码"),
            @ApiImplicitParam(paramType = "query", name = "referrerCodeName", dataType = "String", value = "渠道推荐人编码名称"),
            @ApiImplicitParam(paramType = "query", name = "mainAgentCode", dataType = "String", value = "代理人编码名称"),
            @ApiImplicitParam(paramType = "query", name = "reconcileStatus", dataType = "String", value = "对账结果/状态：0未对账1对账中2已完成对账"),
            @ApiImplicitParam(paramType = "query", name = "protocolCode", dataType = "String", value = "协议编码"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions("settlement:reconcile:policy:list")
    public Result<PageUtils<SettlementReconcilePolicyInfo>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("获取保单信息列表，查询条件={}", params);
        PageUtils<SettlementReconcilePolicyInfo> page = settlementPolicyInfoService.querySettlementPolicyInfoServiceList(params);
        return Result.success(page);
    }

    /**
     * 对账明细清单导出
     *
     * @param response response返回
     * @param params   map参数
     * @return
     */
    @ApiOperation(value = "对账明细清单导出", notes = "对账明细清单导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "settlementTimeStart", dataType = "String", value = "记账时间开始"),
            @ApiImplicitParam(paramType = "query", name = "settlementTimeEnd", dataType = "String", value = "记账时间结束"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "产品名称"),
            @ApiImplicitParam(paramType = "query", name = "approvedTimeStart", dataType = "String", value = "承保时间开始"),
            @ApiImplicitParam(paramType = "query", name = "approvedTimeEnd", dataType = "String", value = "承保时间结束"),
            @ApiImplicitParam(paramType = "query", name = "insuredNameMobileCard", dataType = "String", value = "被保人姓名/证件号/手机号"),
            @ApiImplicitParam(paramType = "query", name = "applicantNameMobileCard", dataType = "String", value = "投保人姓名/证件号/手机号"),
            @ApiImplicitParam(paramType = "query", name = "commodityType", dataType = "String", value = "产品类型"),
            @ApiImplicitParam(paramType = "query", name = "enforceTimeStart", dataType = "String", value = "生效时间开始"),
            @ApiImplicitParam(paramType = "query", name = "enforceTimeEnd", dataType = "String", value = "生效时间结束"),
            @ApiImplicitParam(paramType = "query", name = "policyStatus", dataType = "String", value = "保单状态"),
            @ApiImplicitParam(paramType = "query", name = "settlementCode", dataType = "String", value = "单据编号"),
            @ApiImplicitParam(paramType = "query", name = "serialNumber", dataType = "String", value = "订单号"),
            @ApiImplicitParam(paramType = "query", name = "settlementMonth", dataType = "String", value = "结算月份"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "销售渠道编码"),
            @ApiImplicitParam(paramType = "query", name = "referrerCodeName", dataType = "String", value = "渠道推荐人编码名称"),
            @ApiImplicitParam(paramType = "query", name = "mainAgentCodeName", dataType = "String", value = "渠道推荐人编码名称"),
            @ApiImplicitParam(paramType = "query", name = "reconcileStatus", dataType = "String", value = "对账结果/状态：0未对账1对账中2已完成对账"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    @RequiresPermissions("settlement:reconcile:policy:export")
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode("对账单明细.xlsx"));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, SettlementReconcilePolicyInfo.class);
            sheet.setSheetName("sheet1");

            int page = 1;
            params.put("limit", "2000");
            while (true) {
                // 分批查询
                params.put("page", String.valueOf(page));
                PageUtils<SettlementReconcilePolicyInfo> sourceData = settlementPolicyInfoService.querySettlementPolicyInfoServiceList(params);
                if (sourceData.getList() != null && !sourceData.getList().isEmpty()) {
                    writer.write(sourceData.getList(), sheet);
                    log.info("对账单明细，page={}, dataSize={}", page, sourceData.getList().size());
                    // 赋值maxId ： 需要+1
                    page++;
                } else {
                    log.info("对账单明细，退出构建，执行导出");
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("对账单明细生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.success();
    }


    /**
     * 获取详情信息
     * @param settlementCode 结算编码
     * @return
     * @throws IOException
     */
    @GetMapping("info/{settlementCode}")
    public Result<SettlementPolicyInfoOut> info(@PathVariable(value = "settlementCode") String settlementCode) throws IOException {
        SettlementPolicyInfoOut result =  settlementPolicyInfoService.findSettlementPolicyInfo(settlementCode);
        return Result.success(result);
    }


    /**
     * 修正数据
     * @param vo
     * @return
     * @throws IOException
     */
    @PostMapping("revise")
    public Result revise(@RequestBody @Valid SettlementPolicyInfoReviseVo vo) throws IOException {
        settlementPolicyInfoService.reviseSettlementPolicyInfo(vo);
        return Result.success();
    }

    /**
     * 刷新费率信息
     * @param settlementCode
     * @return
     */
    @PostMapping("refreshCommission/{settlementCode}")
    public Result refreshCommission(@PathVariable(value = "settlementCode")String settlementCode) {
        settlementPolicyInfoService.refreshCommission(settlementCode);
        return Result.success();
    }
}
