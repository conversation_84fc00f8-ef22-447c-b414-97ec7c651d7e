package com.mpolicy.manage.modules.regulators.service.report.listener;

import com.alibaba.excel.event.AnalysisEventListener;

import java.util.List;

/**
 * 监管报备监听基础
 *
 * <AUTHOR>
 * @date 2022-01-21 11:57
 */
public abstract class ReportObjectEventListener<T,R> extends AnalysisEventListener<T> {

    /**
     * <p>
     * 读取扩展
     * </p>
     *
     * @return java.util.List<T>
     * <AUTHOR>
     * @since 2022/01/21
     */
    public abstract List<R> getReadData();
}
