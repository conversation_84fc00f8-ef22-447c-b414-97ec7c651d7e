package com.mpolicy.manage.modules.settlement.vo.invoice;

import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceEnclosureEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInvoiceInfoOut implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 发票编码
     */
    private String invoiceCode;
    /**
     * 开票进展0:开票申请
     */
    private Integer invoiceStep;
    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 开票申请信息/提前开票原因
     */
    private String invoiceMessage;
    /**
     * 开票类型，1:普票，2:专票
     */
    private String invoiceType;
    /**
     * 抬头类型；1:企业，2:机关事业单位，3:个人，4:其他'
     */
    private String invoiceTitleType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 纳税人识别号
     */
    private String taxpayerNum;
    /**
     * 开户银行
     */
    private String depositBank;
    /**
     * 银行账户
     */
    private String bankAccount;
    /**
     * 公司电话
     */
    private String companyPhone;
    /**
     * 公司地址
     */
    private String companyAddress;
    /**
     * 邮箱地址
     */
    private String mailAddress;
    /**
     * 是否需要邮寄；0:否，1:是
     */
    private String needMail;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人电话
     */
    private String receiverPhone;
    /**
     * 收件人地址
     */
    private String receiverAddress;
    /**
     * PDF;OFD;XML
     */
    private String invoiceFileType;
    /**
     * 开票时效要求
     */
    private String invoiceTimeRequire;
    /**
     * 备注
     */
    private String remark;

    /**
     * 财务审核状态
     */
    private Integer invoicingFinanceStatus;

    /**
     * 开票组织编码
     */
    private String invoicingOrgCode;
    /**
     * 开票组织名称
     */
    private String invoicingOrgName;

    /**
     * 开票税率'
     */
    private BigDecimal invoicingTaxRate;


    /**
     * 开票项目编码
     */
    private String invoicingItemCode;

    /**
     * 开票项目名称
     */
    private String invoicingItemName;

    /**
     * 有无时间要求
     */
    private Integer invoicingTimeAsk;

    /**
     * '开票时间'
     */
    private Date invoicingTime;

    /**
     * 备注
     */
    private String invoicingRemark;

    /**
     * 开票意见
     */
    private String invoicingOpinion;

    /**
     * 操作人
     */
    private String invoicingOperateUser;

    /**
     * 操作时间
     */
    private Date invoicingOperateTime;

    /**
     * 开票失败原因
     */
    private String invoicingSystemResult;
    /**
     * 开票回退
     */
    private String invoicingFallbackReason;
    /**
     * 系统开票状态
     */
    private String invoicingSystemStatus;

    /**
     * 邮箱主题
     */
    private String mailSubject;

    /**
     * 邮件内容
     */
    private String mailContent;
    /**
     * 申请状态
     */
    private Integer applyStatus;
    /**
     * 申请人
     */
    private String applyUserName;

    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 发送保司状态
     */
    private Integer pushCompanyStatus;

    /**
     * 开票方式:0正常开票 1:提前开票
     */
    private Integer invoiceMode;
    /**
     * 对账单类型
     */
    private Integer reconcileType;
    /**
     * 是否需要发送邮件
     */
    private Integer isSendMailbox;
    /**
     * 红冲状态
     */
    private Integer redFlushStatus;

    private String expressName;

    private String expressWaybillNumber;

    /**
     * 数电号
     */
    @ApiModelProperty(value = "数电号")
    private String blueElectricCode;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    private String invoiceDate;

    /**
     * 发票附件
     */
    List<SettlementReconcileInvoiceEnclosureEntity> invoiceEnclosureList;

    /**
     * 邮件附件
     */
    List<SettlementReconcileInvoiceEnclosureEntity> mailEnclosureList;
    /**
     * 邮箱地址
     */
    List<InvoiceMailboxVo> invoiceMailboxList;

    /**
     * 申请开票时对账单信息
     */
    List<ReconcileInvoiceSettlementReconcileOut> invoicingReconcileList;

    /**
     * 发票附件
     */
    @ApiModelProperty(name = "发票附件")
    List<SettlementReconcileInvoiceEnclosureEntity> invoiceList;

}
