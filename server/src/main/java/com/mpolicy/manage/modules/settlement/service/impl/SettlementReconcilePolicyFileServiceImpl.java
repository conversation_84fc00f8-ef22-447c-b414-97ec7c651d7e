package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcilePolicyFileDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileFileEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcilePolicyFileEntity;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateFactory;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateHandler;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcilePolicyFileService;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("settlementReconcilePolicyFileService")
public class SettlementReconcilePolicyFileServiceImpl extends ServiceImpl<SettlementReconcilePolicyFileDao, SettlementReconcilePolicyFileEntity> implements SettlementReconcilePolicyFileService {

    @Autowired
    private SysDocumentService sysDocumentService;

    @Autowired
    private StorageService storageService;
    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Override
    public void saveReconcileRuleFileTemplate(SettlementReconcileFileEntity settlementReconcileFile, SysDocumentEntity document) {
        try {
            // 生成到本地
            String localFilePath = tempPath.concat(settlementReconcileFile.getFileName());
            storageService.downloadFileToLocal(document.getFilePath(), localFilePath);
            ReconcileTemplateHandler handler = ReconcileTemplateFactory.getInvokeStrategy(settlementReconcileFile.getReconcileFileType());
            List<ReconcileRuleFileTemplate> readFile = handler.readFile(Files.newInputStream(Paths.get(localFilePath)), settlementReconcileFile.getReconcileCode());
            if (CollUtil.isEmpty(readFile)) {
                return;
            }
            Integer fileType = ReconcileTemplateEnum.getFileType(settlementReconcileFile.getReconcileFileType());
            CollUtil.split(readFile, 5000).forEach(action -> {
                List<SettlementReconcilePolicyFileEntity> settlementReconcilePolicyFileList = action.stream().map(m -> {
                    SettlementReconcilePolicyFileEntity settlementReconcilePolicyFile = BeanUtil.copyProperties(m, SettlementReconcilePolicyFileEntity.class);
                    settlementReconcilePolicyFile.setReconcileCode(settlementReconcileFile.getReconcileCode());
                    settlementReconcilePolicyFile.setFileCode(settlementReconcileFile.getReconcileFileCode());
                    settlementReconcilePolicyFile.setFileType(fileType);
                    settlementReconcilePolicyFile.setCompanyAmount(m.getCompanyAmount().setScale(2, RoundingMode.HALF_UP));
                    return settlementReconcilePolicyFile;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(settlementReconcilePolicyFileList)) {
                    baseMapper.insertBatchSomeColumn(settlementReconcilePolicyFileList);
                }
            });
        } catch (Exception e) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件入库失败"));
        }
    }
}
