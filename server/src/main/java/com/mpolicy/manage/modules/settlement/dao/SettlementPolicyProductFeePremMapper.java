package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductPremListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductPremListOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductTaxPremListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductTaxPremListOut;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import generator.domain.SettlementPolicyProductFeePrem;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【settlement_policy_product_fee_prem】的数据库操作Mapper
 * @createDate 2025-02-27 14:16:04
 * @Entity generator.domain.SettlementPolicyProductFeePrem
 */
public interface SettlementPolicyProductFeePremMapper extends ImsBaseMapper<SettlementPolicyProductFeePrem> {


    IPage<SettlementPolicyProductTaxPremListOut> findSettlementPolicyProductPremList(
            @Param("page") Page<SettlementPolicyProductTaxPremListOut> page
            , @Param("input") SettlementPolicyProductTaxPremListInput input
    );

}
