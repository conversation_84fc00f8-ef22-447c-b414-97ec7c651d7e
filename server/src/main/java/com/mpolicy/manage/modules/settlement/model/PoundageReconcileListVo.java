package com.mpolicy.manage.modules.settlement.model;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PoundageReconcileListVo extends BasePage implements Serializable {
    private static final long serialVersionUID = 4782497232744416191L;

    @ApiModelProperty(value = "保司编码", example = "12121")
    private String companyCode;

    @ApiModelProperty(value = "外部签署方类型", example = "12121")
    private String externalSignatoryType;

    @ApiModelProperty(value = "外部签署方", example = "2002年8月")
    private String externalSignatoryName;

    @ApiModelProperty(value = "对账月度", example = "2002年8月")
    private String reconcileMonth;
}
