package com.mpolicy.manage.modules.settlement.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileMonthEnum;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 自动科目结算工具类
 *
 * <AUTHOR>
 * @since 2023-10-22 12:19
 */
@Slf4j
public class AutoCostUtils {

    public static String AMOUNT_RULE = "^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$";

    public static List<String> LIST_RESET_SUBJECT_CODE = Arrays.asList(CostSubjectEnum.AGRICULTURAL_MACHINERY_PERFORMANCE.getCode(),CostSubjectEnum.ISSUING_REGIONAL_MARKETING_FEE.getCode(),CostSubjectEnum.LONG_RESTATEMENT_REISSUE_COMM.getCode(),CostSubjectEnum.DYNAMIC_SUBJECT.getCode());

    /**
     * 获取结算快照月份
     *
     * @return 快照月份[yyyyMM]
     * <AUTHOR>
     * @since 2023/11/9 16:42
     */
    public static String costSettlementMonthSnapshot() {
        return DateUtil.format(new Date(), "yyyyMM");
    }

    /**
     * 根据当前对账周期获取需补发的对账周期
     *
     * @param monthSnapshot 快照周期
     * @param month         迁移周期
     * @return 补发对账周期[方案编码_yyyyMM]
     * <AUTHOR>
     * @since 2023/11/20 22:29
     */
    public static String getReissueCostSettlementCycle(String monthSnapshot, int month) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return DateUtil.format(DateUtil.offset(date, DateField.MONTH, month), "yyyyMM");
    }

    /**
     * 根据配置截止类型 + 截止日
     * @param monthSnapshot      类型
     * @param reconcileMonthEnum 截止日
     * @param timeEndDay         相隔天数
     * @param beginFlag          是否开始时间
     * @return
     */
    public static DateTime builderDateTime(String monthSnapshot, ReconcileMonthEnum reconcileMonthEnum, Integer timeEndDay,boolean beginFlag) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        DateTime nowTime = DateUtil.endOfDay(date);
        if(beginFlag){
            nowTime = DateUtil.beginOfDay(date);
        }

        DateTime dateTime = DateUtil.date(nowTime)
                .offset(DateField.MONTH, -reconcileMonthEnum.getMonth());

        // 获取日期的月最后日
        DateTime dayOfMonth = DateUtil.endOfMonth(dateTime);
        if (timeEndDay > dayOfMonth.dayOfMonth()) {
            return dayOfMonth;
        } else {
            return DateUtil.date(dateTime).setField(DateField.DAY_OF_MONTH, timeEndDay);
        }
    }



    /**
     * 根据配置截止类型 + 截止日
     *
     * @param reconcileMonthEnum 类型
     * @param timeEndDay         截止日
     * @return cn.hutool.core.date.DateTime
     * <AUTHOR>
     * @since 2023/11/3 17:20
     */
    public static DateTime builderDateTime(ReconcileMonthEnum reconcileMonthEnum, Integer timeEndDay) {
        DateTime nowTime = DateUtil.endOfDay(new Date());
        DateTime dateTime = DateUtil.date(nowTime)
                .offset(DateField.MONTH, -reconcileMonthEnum.getMonth());

        // 获取日期的月最后日
        DateTime dayOfMonth = DateUtil.endOfMonth(dateTime);
        if (timeEndDay > dayOfMonth.dayOfMonth()) {
            return dayOfMonth;
        } else {
            return DateUtil.date(dateTime).setField(DateField.DAY_OF_MONTH, timeEndDay);
        }
    }

    /**
     * yyyyMM转yyyy-MM
     *
     * @param str yyyyMM
     * 格式为：yyyy-MM
     * <AUTHOR>
     * @since 2023/12/20
     */
    public static String yyyyMMToyyyyMM(String str) {
        return str.substring(0, 4) + "-" + str.substring(4);
    }

    public static boolean validateAmount(String amount){
        Pattern pattern= Pattern.compile(AMOUNT_RULE);
        Matcher match=pattern.matcher(amount);
        if(match.matches()==false){
            return false;
        }else{
            return true;
        }
    }



    public static void main(String[] args) {
        System.out.println(validateAmount("0.01"));
        System.out.println(validateAmount("0"));
        System.out.println(validateAmount("0.99"));
        System.out.println(validateAmount("0.991"));
        System.out.println(validateAmount("0.1"));

    }

}