package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementProgrammeSubjectDao;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeSubjectEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyInfoEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostProgrammeSubjectService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeSubjectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 2:45 下午
 * @Version 1.0
 */
@Slf4j
@Service
public class SettlementCostProgrammeSubjectServiceImpl extends ServiceImpl<SettlementProgrammeSubjectDao, SettlementCostProgrammeSubjectEntity> implements SettlementCostProgrammeSubjectService {

    @Override
    public List<SettlementProgrammeSubjectVo> listProgrammeSubject(String programmeCode){
        return this.baseMapper.listProgrammeSubject(programmeCode);
    }
}
