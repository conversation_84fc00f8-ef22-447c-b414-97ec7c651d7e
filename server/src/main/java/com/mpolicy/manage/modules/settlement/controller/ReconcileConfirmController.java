package com.mpolicy.manage.modules.settlement.controller;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.insure.vo.insureOrderInfoOut;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileConfirmService;
import com.mpolicy.manage.modules.settlement.vo.confirm.SettlementReconcileConfirmInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 保司对账单确认单
 * 手续费结算确认单
 * <AUTHOR>
 * @since 2023-05-24 15:59
 */
@RestController
@RequestMapping("/settlement/reconcile/confirm")
@Api(tags = "保司对账单确认单")
@Slf4j
public class ReconcileConfirmController extends ReconcileBaseController {

    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;

    @ApiOperation(value = "分页查询结算确认信息列表", notes = "分页查询结算确认信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "reconcileMonth", dataType = "String", value = "结算月份"),
            @ApiImplicitParam(paramType = "query", name = "reconcileSubjectCode", dataType = "String", value = "科目编码"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query", name = "innerSignatoryCode", dataType = "String", value = "内部签署方编码"),
            @ApiImplicitParam(paramType = "query", name = "innerSignatoryCode", dataType = "String", value = "外部签署方编码"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions("settlement:reconcile:confirm:list")
    public Result<PageUtils<SettlementReconcileConfirmInfo>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("获取结算确认信息列表，查询条件={}", params);
        PageUtils<SettlementReconcileConfirmInfo> page = settlementReconcileConfirmService.querySettlementConfirmInfoServiceList(params);
        return Result.success(page);
    }


    /**
     * 结算确认信息导出
     * @param response response返回
     * @param params map参数
     * @return
     */
    @ApiOperation(value = "结算确认信息导出", notes = "结算确认信息导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "reconcileMonth", dataType = "String", value = "结算月份"),
            @ApiImplicitParam(paramType = "query", name = "reconcileSubjectCode", dataType = "String", value = "科目编码"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query", name = "innerSignatoryCode", dataType = "String", value = "内部签署方编码"),
            @ApiImplicitParam(paramType = "query", name = "innerSignatoryCode", dataType = "String", value = "外部签署方编码"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    @RequiresPermissions("settlement:reconcile:confirm:export")
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        log.info("手续费结算确认单导出生成 .....开始");
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode("手续费结算确认单.xlsx"));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, SettlementReconcileConfirmInfo.class);
            sheet.setSheetName("sheet1");

            int page = 1;
            params.put("limit", "2000");
            while (true) {
                // 分批查询
                params.put("page", String.valueOf(page));
                PageUtils<SettlementReconcileConfirmInfo> sourceData = settlementReconcileConfirmService.querySettlementConfirmInfoServiceList(params);
                if (sourceData.getList() != null && !sourceData.getList().isEmpty()) {
                    writer.write(sourceData.getList(), sheet);
                    log.info("手续费结算确认单导出生成，page={}, dataSize={}", page, sourceData.getList().size());
                    // 赋值maxId ： 需要+1
                    page++;
                } else {
                    log.info("手续费结算确认单导出生成，退出构建，执行导出");
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("手续费结算确认单导出生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.success();
    }
}
