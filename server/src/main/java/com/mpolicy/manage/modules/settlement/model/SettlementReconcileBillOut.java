package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SettlementReconcileBillOut implements Serializable {
    private static final long serialVersionUID = -7115563221765312240L;

    @ApiModelProperty(value = "编码", example = "PR000001")
    private String reconcileBillCode;

    @ApiModelProperty(value = "保险公司", example = "xx")
    private String companyName;

    @ApiModelProperty(value = "保险公司简称", example = "xx")
    private String companyShortName;

    @ApiModelProperty(value = "外部签署方名称", example = "xx")
    private String externalSignatoryName;

    @ApiModelProperty(value = "外部签署方类型", example = "xx")
    private String externalSignatoryType;

    @ApiModelProperty(value = "内部签署方分支名称", example = "xx")
    private String innerSignatoryBranchName;

    @ApiModelProperty(value = "对账月度", example = "xx")
    private String reconcileMonth;

    @ApiModelProperty(value = "保司对账单数", example = "9")
    private Integer companyBillNum;

    @ApiModelProperty(value = "保司手续金额", example = "100.00")
    private BigDecimal poundageAmount;

    @ApiModelProperty(value = "我司手续金额", example = "100.00")
    private BigDecimal xjPoundageAmount;

    @ApiModelProperty(value = "未平账差额", example = "0.00")
    private BigDecimal difference;

    @ApiModelProperty(value = "保司对账单下载地址", example = "http://xxx.zip")
    private String companyReconcileUrl;

    @ApiModelProperty(value = "对账单状态", example = "0")
    private Integer reconcileStatus;
}
