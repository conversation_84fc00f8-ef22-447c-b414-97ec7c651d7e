package com.mpolicy.manage.modules.settlement.vo.settlement;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class ReconcileJobOut implements Serializable {

    /**
     * 事件描述
     */
    private String eventDesc;
    /**
     * 业务编码
     */
    private String policyNo;
    /**
     * 保全编码
     */
    private String endorsementNo;
    /**
     * push事件唯一凭证编码
     */
    private String pushEventCode;
    /**
     * 请求响应消息
     */
    private String eventMessage;
    /**
     * 事件状态;0代处理1处理中2处理完成
     */
    private Integer eventStatus;
    /**
     * 事件处理完成时间
     */
    private Date eventFinishTime;
}
