package com.mpolicy.manage.modules.settlement.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.*;
import com.mpolicy.settlement.core.common.autocost.SettlementDirectorNotMatchLogQuery;
import com.mpolicy.settlement.core.common.autocost.SettlementDirectorNotMatchRetDto;
import com.mpolicy.settlement.core.common.autocost.SettlementDynamicSubjectDefinition;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface SettlementCostService {
    /**
     * 分页查询自动结算金额
     * @param input
     * @return
     */
    PageUtils<SettlementCostAutoInfoVo> pageSettlementCostAutoInfo(SettlementCostAutoInput input);

    void pageSettlementCostAutoInfoExport(SettlementCostAutoInput input, HttpServletResponse response);

    /**
     * 分页获取每个人每个机构下的汇总数据
     * @param input
     * @return
     */
    PageUtils<SettlementCostAutoInfoVo> pageSettlementCostAutoInfoBySummary(SettlementCostAutoInput input);

    List<SettlementDetailAllSubjectVO> querySettlementDynamicSubjectVOS(SettlementCostAutoInput input);

    void pageSettlementCostAutoInfoBySummaryExport(SettlementCostAutoInput input, HttpServletResponse response) throws IOException;

    /**
     * 确认结算
     * @param programmeCode
     * @param settlementCycle
     */
    void confirmSettlement(String programmeCode,String settlementCycle,String confirmUser);

    Boolean validateConfirmed(String programmeCode, String settlementCycle);

    PageUtils<SettlementConfirmPageVO> pageSettlementConfirmPage(SettlementConfirmInput input);

    String uploadHistoryLongCostApply(CostUploadApplyInput apply);

    String loadCost(String applyCode, String userName);

    List<SettlementDynamicSubjectDefinition> listSettlementDynamicSubject(String subjectName);
    String saveSettlementDynamicSubjectDefinition(SettlementDynamicSubjectDefinition definition);

    PageUtils<SettlementDirectorNotMatchRetDto> pageNotMatchLog(SettlementDirectorNotMatchLogQuery query);

    void exportSettlementPcoInfo(HttpServletResponse response,String settlementCycle)throws IOException;
}
