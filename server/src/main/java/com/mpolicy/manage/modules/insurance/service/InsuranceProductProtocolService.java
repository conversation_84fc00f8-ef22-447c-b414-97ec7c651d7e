package com.mpolicy.manage.modules.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductProtocolEntity;

import java.util.Map;

/**
 * 产品险种协议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-26 16:29:21
 */
public interface InsuranceProductProtocolService extends IService<InsuranceProductProtocolEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

