package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileConfirmDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileConfirmEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileConfirmService;
import com.mpolicy.manage.modules.settlement.vo.confirm.SettlementReconcileConfirmInfo;
import com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileDiffTypeEnum;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 保司结算对账单汇总表
 *
 * <AUTHOR>
 * @date 2023-05-23 14:32:21
 */
@Slf4j
@Service("settlementReconcileConfirmService")
public class SettlementReconcileConfirmServiceImpl extends ServiceImpl<SettlementReconcileConfirmDao, SettlementReconcileConfirmEntity> implements SettlementReconcileConfirmService {


    @Override
    public PageUtils<SettlementReconcileConfirmInfo> querySettlementConfirmInfoServiceList(Map<String, Object> params) {
        // 条件参数获取
        //结算月份
        String reconcileMonth = RequestUtils.objectValueToString(params, "reconcileMonth");
        //科目编码
        String reconcileSubjectCode = RequestUtils.objectValueToString(params, "reconcileSubjectCode");
        //保险公司编码
        String companyCode = RequestUtils.objectValueToString(params, "companyCode");
        //【内部签署方编码】
        String innerSignatoryCode = RequestUtils.objectValueToString(params, "innerSignatoryCode");
        //【外部签署方编码】
        String externalSignatoryCode = RequestUtils.objectValueToString(params, "externalSignatoryCode");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //【产品名称】是否等于【协议产品名称】
        String protocolProductCode = RequestUtils.objectValueToString(params, "protocolProductCode");
        // 分页查询基础数据
        IPage<SettlementReconcileConfirmEntity> pageResult = this.page(
                new Query<SettlementReconcileConfirmEntity>().getPage(params),
                new LambdaQueryWrapper<SettlementReconcileConfirmEntity>()
                        .eq(StringUtils.isNotBlank(policyNo), SettlementReconcileConfirmEntity::getPolicyNo, policyNo)
                        .eq(StringUtils.isNotBlank(companyCode), SettlementReconcileConfirmEntity::getCompanyCode, companyCode)
                        .eq(StringUtils.isNotBlank(reconcileMonth), SettlementReconcileConfirmEntity::getReconcileMonth, reconcileMonth)
                        .eq(StringUtils.isNotBlank(reconcileSubjectCode), SettlementReconcileConfirmEntity::getReconcileSubjectCode, reconcileSubjectCode)
                        .orderByDesc(SettlementReconcileConfirmEntity::getCreateTime)
        );
        // 构建vo
        // todo 时间处理  枚举字典处理
        List<SettlementReconcileConfirmInfo> resultData = pageResult.getRecords().stream().map(x -> {
            SettlementReconcileConfirmInfo bean = new SettlementReconcileConfirmInfo();
            BeanUtils.copyProperties(x, bean);
            return bean;
        }).collect(Collectors.toList());

        // 重新构建分页返回对象
        return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(), (int) pageResult.getCurrent());
    }

    @Override
    public SettlementReconcileDiff querySettlementReconcileDiff(String billCode) {
        SettlementReconcileConfirmEntity reconcileConfirm = Optional.ofNullable(lambdaQuery()
                .eq(SettlementReconcileConfirmEntity::getBillCode, billCode)
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("{}纪录不存在", billCode))));

        SettlementReconcileDiff result = new SettlementReconcileDiff();
        BeanUtils.copyProperties(reconcileConfirm, result);
        result.setDiffStatusDesc(reconcileConfirm.getDiffStatus() == 0 ? "待处理" : "已处理");
        ReconcileDiffTypeEnum reconcileDiffType = ReconcileDiffTypeEnum.getReconcileDiffType(reconcileConfirm.getDiffType());
        result.setDiffName(reconcileDiffType != null ? reconcileDiffType.getName() : "未知");
        // 扩展其他
        return result;
    }
}
