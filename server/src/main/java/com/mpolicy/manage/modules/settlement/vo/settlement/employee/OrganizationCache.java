package com.mpolicy.manage.modules.settlement.vo.settlement.employee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/30 1:45 上午
 * @Version 1.0
 */
@Data
@ApiModel(value = "区域分支机构信息缓存", description = "区域分支机构信息缓存")
public class OrganizationCache implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(name = "分支名称")
    private String branchName;

    @ApiModelProperty(name = "分支ID")
    private Long branchId;

    @ApiModelProperty(name = "任职部门编码，分支员工为分支编码")
    private String branchCode;

    @ApiModelProperty(name = "所属片区，总部员工为空，如果分支挂在区域也为空")
    private String zoneName;

    @ApiModelProperty(name = "所属片区ID，总部员工为空，如果分支挂在区域也为空")
    private Long zoneId;

    @ApiModelProperty(name = "所属片区编码，总部员工为空，如果分支挂在区域也为空")
    private String zoneCode;

    @ApiModelProperty(name = "所属区域ID，总部员工默认为【总部】")
    private Long regionId;

    @ApiModelProperty(name = "所属区域编码，总部员工默认为【总部】")
    private String regionCode;

    @ApiModelProperty(name = "所属区域，总部员工默认为【总部】，分支员工为各区域")
    private String regionName;
}
