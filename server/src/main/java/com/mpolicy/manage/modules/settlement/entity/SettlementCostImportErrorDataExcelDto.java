package com.mpolicy.manage.modules.settlement.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class SettlementCostImportErrorDataExcelDto extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号", example = "KMH")
    @ExcelProperty(value = "申请编号")
    private String applyCode;

    /**
     * 所属周期
     */
    @ApiModelProperty(value = "月份", example = "202311")
    @ExcelProperty(value = "月份")
    private String costSettlementCycle;

    @ApiModelProperty(value = "sheet下标")
    @ExcelProperty(value = "sheet下标")
    private Integer sheetIndex;
    @ApiModelProperty(value = "sheet名字")
    @ExcelProperty(value = "sheet名字")
    private String sheetName;

    @ApiModelProperty(value = "行数")
    @ExcelProperty(value = "第几行")
    private Integer rowNum;

    @ApiModelProperty(value = "原始记录的序号")
    @ExcelProperty(value = "原始记录的序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "错误描述")
    @ExcelProperty(value = "错误描述")
    private String errorMsg;
}
