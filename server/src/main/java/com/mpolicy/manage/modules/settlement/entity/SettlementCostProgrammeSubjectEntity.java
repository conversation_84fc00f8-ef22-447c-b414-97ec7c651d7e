package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 支出结算方案科目关系表
 * 
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
@TableName("settlement_cost_programme_subject")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostProgrammeSubjectEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 方案编码
	 */
	private String programmeCode;
	/**
	 * 科目编码
	 */
	private String subjectCode;
	/**
	 * 科目优先级
	 */
	private Integer orderNumber;
	/**
	 * 绑定说明
	 */
	private String bindDesc;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
