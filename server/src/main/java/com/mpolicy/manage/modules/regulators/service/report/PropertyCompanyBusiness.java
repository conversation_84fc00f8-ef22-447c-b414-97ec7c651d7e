package com.mpolicy.manage.modules.regulators.service.report;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.regulators.common.RegulatorsKeys;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.report.data.LifeCompanyBusinessData;
import com.mpolicy.manage.modules.regulators.service.report.data.PropertyCompanyBusinessData;
import com.mpolicy.manage.modules.regulators.service.report.listener.LifeCompanyBusinessListener;
import com.mpolicy.manage.modules.regulators.service.report.listener.PropertyCompanyBusinessListener;
import com.mpolicy.manage.modules.regulators.utils.RegulatorsExcelUtil;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportData;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 代理产险公司业务表
 *
 * <AUTHOR>
 * @date 2022-01-21 14:44
 */
@Slf4j
@Service
public class PropertyCompanyBusiness extends AbsRegulatorsReport<RegulatorsReportData> {


    @Override
    public RegulatorsReportTypeEnum getRegulatorsReportType() {
        return RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS;
    }

    @Override
    public boolean checkRegulatorsReportFile(String fileCode, Boolean insertDataRedis) {
        // 检测fileCode
        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("责任定义文件不存在")));


        return checkRegulatorsReportFile(document, insertDataRedis);
    }

    @Override
    public boolean checkRegulatorsReportFile(SysDocumentEntity document, Boolean insertDataRedis) {

        // 1 生成到本地
        String localFilePath = tempPath.concat(document.getFileName());
        storageService.downloadFileToLocal(document.getFilePath(), localFilePath);

        // 2 解析xls
        List<PropertyCompanyBusinessData> reportData;
        try {
            reportData = RegulatorsExcelUtil.readExcelByList(localFilePath, 1, 3, new PropertyCompanyBusinessListener());
        }catch (Exception e){
            log.warn("代理产险公司业务表模板解析异常：",e);
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("代理产险公司业务表模板错误，请核查"));
        }
        // 3 todo 校验数据

        // 4 写入redis
        if (insertDataRedis) {
            JSONObject data = new JSONObject();
            data.put("data", JSON.toJSON(reportData));
            data.put("reportType", RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getCode());
            data.put("reportTypeName", RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getName());
            redisService.set(RegulatorsKeys.REGULATORS_REPORT, StrUtil.format("{}-{}", RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getCode(), document.getFileCode()), data.toJSONString());
        }
        return true;
    }

    @Override
    void generatorReport(RegulatorsReportInfoEntity reportInfo, RegulatorsReportData regulatorsReportData, SysDocumentEntity document) {

        // 根据报文中的机构报备编号获取是否存在纪录
        RegulatorsReportOrgEntity bean = Optional.ofNullable(
                regulatorsReportOrgService.lambdaQuery()
                        .eq(RegulatorsReportOrgEntity::getOrgRegulatorsNo, regulatorsReportData.getOrgRegulatorsNo())
                        .one()
        ).orElse(new RegulatorsReportOrgEntity());

        BeanUtils.copyProperties(regulatorsReportData, bean);
        // 赋值机构编码
        bean.setOrgRegulatorsNo(regulatorsReportData.getOrgRegulatorsNo());
        // 文件path
        bean.setReportFilePath(document.getFilePath());
        // 文件名称
        bean.setReportFileName(document.getFileName());
        // 报告文件类型
        bean.setReportType(regulatorsReportData.getReportType().getCode());
        bean.setReportName(regulatorsReportData.getReportType().getName());
        bean.setReportStatus(0);
        // 报告内容
        String reportData = redisService.get(RegulatorsKeys.REGULATORS_REPORT, StrUtil.format("{}-{}", RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getCode(), document.getFileCode()), String.class);
        if(StringUtils.isBlank(reportData)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取缓存报告内容缺失"));
        }
        bean.setReportData(reportData);
        bean.setReportTitle(StrUtil.format("{}-{}", reportInfo.getRegulatorsName(), RegulatorsReportTypeEnum.PROPERTY_COMPANY_BUSINESS.getName()));
        regulatorsReportOrgService.saveOrUpdate(bean);

        log.info("机构报备[代理产险公司业务表] 操作完成");
    }
}
