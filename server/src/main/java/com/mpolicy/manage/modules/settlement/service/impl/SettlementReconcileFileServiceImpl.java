package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileFileDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileFileEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保司结算对账单关联文件
 *
 * <AUTHOR>
 * @since  2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileFileService")
public class SettlementReconcileFileServiceImpl extends ServiceImpl<SettlementReconcileFileDao, SettlementReconcileFileEntity> implements SettlementReconcileFileService {

}
