package com.mpolicy.manage.modules.agentApply.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * ClassName: AgentApplyStatusEnum
 * Description: 代理人线上考试题目类型枚举
 * date: 2022/11/25 15:56
 *
 * <AUTHOR>
 */
@Getter
public enum AgentApplyExamTypeEnum {
    //题型(1:单选题 2:多选题 3:判断题)
    TYPE1(1, "单选题"),
    TYPE2(2, "多选题"),
    TYPE3(3, "判断题"),
    ;

    private final Integer code;
    private final String name;

    AgentApplyExamTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AgentApplyExamTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        AgentApplyExamTypeEnum examTypeEnum = Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
        return Objects.isNull(examTypeEnum)?null:examTypeEnum.getName();
    }
}
