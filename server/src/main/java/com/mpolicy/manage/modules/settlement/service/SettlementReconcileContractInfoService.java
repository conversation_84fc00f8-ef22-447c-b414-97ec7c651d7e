package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileContractInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;

/**
 * 结算合约配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-28 14:21:38
 */
public interface SettlementReconcileContractInfoService extends IService<SettlementReconcileContractInfoEntity> {

    /**
     * 获取结算合约配置表列表
     * @param input
     * @return
     */
    PageUtils<SettlementReconcileContractInfoListOut> findSettlementReconcileContractInfoList(SettlementReconcileContractInfoListInput input);

    /**
     * 获取结算合约配置表详情
     * @param id
     * @return
     */
        SettlementReconcileContractInfoInfoOut findSettlementReconcileContractInfoById(Integer id);

    /**
     * 新增结算合约配置表数据
     * @param input
     * @return
     */
    void saveSettlementReconcileContractInfo(SettlementReconcileContractInfoSaveInput input);

    /**
     * 修改结算合约配置表数据
     * @param input
     * @return
     */
    void updateSettlementReconcileContractInfoById(SettlementReconcileContractInfoUpdateInput input);
}

