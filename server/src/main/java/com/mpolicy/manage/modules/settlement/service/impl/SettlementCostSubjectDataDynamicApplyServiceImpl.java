package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostSubjectDataDynamicApplyDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApplyEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 科目范围(动态科目)数据申请记录
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
@Slf4j
@Service
public class SettlementCostSubjectDataDynamicApplyServiceImpl extends ServiceImpl<SettlementCostSubjectDataDynamicApplyDao, SettlementCostSubjectDataDynamicApplyEntity> implements SettlementCostSubjectDataDynamicApplyService {


}
