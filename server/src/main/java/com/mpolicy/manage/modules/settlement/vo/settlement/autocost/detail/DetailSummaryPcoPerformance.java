package com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/2/2 03:26
 * @Version 1.0
 */
@Data
public class DetailSummaryPcoPerformance {

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("机构推广费")
    private BigDecimal orgPromotion;

    @ApiModelProperty("pco等级")
    private BigDecimal pcoLevel;

    @ApiModelProperty("pco绩效提成比例")
    private BigDecimal pcoPerformanceRate;

    @ApiModelProperty("机构长期险续期率")
    private BigDecimal orgLongRenewalRate;

    @ApiModelProperty("发放比例")
    private BigDecimal costRate;

    @ApiModelProperty("发放金额")
    private BigDecimal costAmount;

}
