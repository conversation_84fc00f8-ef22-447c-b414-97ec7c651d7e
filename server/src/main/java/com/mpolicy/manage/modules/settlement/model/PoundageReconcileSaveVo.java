package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PoundageReconcileSaveVo implements Serializable {
    private static final long serialVersionUID = -3212044807841529011L;

    @ApiModelProperty(value = "保司编码", example = "0001")
    private String companyCode;

    @ApiModelProperty(value = "结算保司编码", example = "0001")
    private String targetCompanyCode;

    @ApiModelProperty(value = "模版编码", example = "0001")
    private String templateCode;

    @ApiModelProperty(value = "上传的文件列表")
    private List<PoundageReconcileFileVo> fileList;
}
