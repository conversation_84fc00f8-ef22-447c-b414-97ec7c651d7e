package com.mpolicy.manage.modules.settlement.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SettlementPolicyProductPremListOut implements Serializable {
    private static final long serialVersionUID = -1743896459898978435L;
    /**
     * id
     */
    private Integer id;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 批单号
     */
    private Integer year;
    private Integer period;
    private String batchCode;
    private String productCode;
    private String productName;
    private String insuranceProductCode;
    private String insuranceProductName;
    /**
     * 保费
     */
    private BigDecimal premium;

    /**
     * 结算方式 按全保费(含税)结算 按净保费(税后)结算
     */
    private String settlementMethod;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 税后保费
     */
    private BigDecimal taxAfterPremium;
    /**
     * 基础佣金费率
     */
    private BigDecimal yearRate;
    /**
     * 操作人
     */
    private String updateUser;
    /**
     * 操作时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
