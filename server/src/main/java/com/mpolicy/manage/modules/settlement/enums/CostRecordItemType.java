package com.mpolicy.manage.modules.settlement.enums;

import com.mpolicy.manage.enums.ProgrammeStatusEnum;
import lombok.Getter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023-11-08 14:40
 * @description: 权限枚举
 */
@Getter
public enum CostRecordItemType {
    POLICY("policy", "保单维度"),
    PRODUCT("product", "险种维度"),
    POLICY_PRODUCT("policy_product", "保单险种维度"),
    CUSTOM("custom", "自定义"),
    NULL("", "未知")
    ;

    private String code;

    private String desc;

    CostRecordItemType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 匹配操作码
     *
     * @param code
     * @return
     */
    public static CostRecordItemType matchItemType(String code) {
        for (CostRecordItemType searchEnum : CostRecordItemType.values()) {
            if (searchEnum.code.equals(code)) {
                return searchEnum;
            }
        }
        return CostRecordItemType.NULL;
    }



}
