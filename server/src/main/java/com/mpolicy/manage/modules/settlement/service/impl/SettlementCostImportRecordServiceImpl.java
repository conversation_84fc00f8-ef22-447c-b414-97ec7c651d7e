package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.policy.vo.EpPolicyExportVo;
import com.mpolicy.manage.modules.policy.vo.EpV2PolicyExportVo;
import com.mpolicy.manage.modules.settlement.common.SettlementAutoCostBaseService;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostImportErrorDataExcelDto;
import com.mpolicy.manage.modules.settlement.service.SettlementCostImportRecordService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementCostAutoInfoVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementCostAutoInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDynamicSubjectVO;
import com.mpolicy.settlement.core.common.autocost.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SettlementCostImportRecordServiceImpl implements SettlementCostImportRecordService {

    @Autowired
    private SettlementAutoCostBaseService settlementAutoCostBaseService;

    public PageUtils<SettlementCostImportOperationRecord> pageSettlementCostImportOperationRecord(SettlementCostImportOperationRecordQuery query) {

        return settlementAutoCostBaseService.pageCostImportOperationRecord(query,true);
    }

    public PageUtils<SettlementCostImportRecord> pageSettlementCostImportRecord(SettlementCostImportRecordQuery query) {

        return settlementAutoCostBaseService.pageCostImportRecord(query,true);
    }

    public String deleteImportRecord(SettlementCostImportRecordDelete recordDelete){
        return settlementAutoCostBaseService.deleteImportRecord(recordDelete,true);
    }

    @Override
    public void exportSettlementCostImportErrorData(String applyCode,String name, HttpServletResponse response) {
        //按照结算机构展示，查询所有的动态科目
        List<SettlementCostImportErrorData> data = settlementAutoCostBaseService.listImportErrorDataByApplyCode(applyCode,true);
        List<SettlementCostImportErrorDataExcelDto> excelDtos = data.stream().map(d->{
            SettlementCostImportErrorDataExcelDto excelDto = new SettlementCostImportErrorDataExcelDto();
            BeanUtils.copyProperties(d,excelDto);
            return excelDto;
        }).collect(Collectors.toList());
        //ExcelUtil.writeExcel(response, excelDtos, URLUtil.encode("结算文件导入异常明细列表", CharsetUtil.CHARSET_UTF_8), "sheet1", new EpPolicyExportVo());

        try (OutputStream out = response.getOutputStream()){
            // 生成保单文件
            String fileName = URLEncoder.encode(name.concat(applyCode).concat("异常数据.xlsx"), StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
//            String fileName = URLEncoder.encode(name.concat(applyCode).concat("异常数据.xlsx"), StandardCharsets.UTF_8.name());
//            response.setCharacterEncoding("UTF-8");
//            response.setContentType("application/vnd.ms-excel;charset=utf-8");
//            response.setHeader(
//                    "Content-Disposition",
//                    StrUtil.format("attachment;filename={}", fileName, StandardCharsets.UTF_8)
//            );
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            Sheet sheet = new Sheet(1, 0, SettlementCostImportErrorDataExcelDto.class);
            sheet.setSheetName("异常数据列表");
            if (!excelDtos.isEmpty()) {
                writer.write(excelDtos, sheet);
            }
            writer.finish();
            out.flush();
        }catch (Exception e){
            e.printStackTrace();
        }

    }



}
