package com.mpolicy.manage.modules.settlement.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 佣金确认状态枚举
 *
 * <AUTHOR>
 * @version 2023/11/05
 */
public enum ConfirmStatusEnum {
    /**
     *
     */
    UNCONFIRMED(0,"未确认"),
    CONFIRMING(1,"确认中"),
    CONFIRMED(2,"已确认")
    ;

    @Getter
    private final Integer code;

    @Getter
    private final String name;


    ConfirmStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    public static ConfirmStatusEnum deCode(Integer code) {
        return Arrays.stream(ConfirmStatusEnum.values()).filter(x -> Objects.equals(x.code,code)).findFirst().orElse(null);
    }
}
