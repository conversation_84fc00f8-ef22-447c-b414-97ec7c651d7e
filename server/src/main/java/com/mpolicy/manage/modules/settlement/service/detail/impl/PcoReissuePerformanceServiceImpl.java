package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsProductDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryPcoPerformance;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryPcoPerformance;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/2/4 00:27
 * @Version 1.0
 */

@Service
public class PcoReissuePerformanceServiceImpl extends AbsProductDimensionService<List<DetailSummaryPcoPerformance>> {


    @Override
    public List<DetailSummaryPcoPerformance> querySummary(PageSettlementDetailParams p) {
        DetailSummaryPcoPerformance summaryLongReissuePromotion = costAutoInfoDao.pcoPerformanceSummary(p);

        DetailSummaryPcoPerformance supervisorPromotion = null;
        if (Objects.nonNull(summaryLongReissuePromotion)) {
            summaryLongReissuePromotion.setMonth(calMonthBeforeBeforeLastMonth(p) + "（补发）");

            //查询上个周期的暂发
            PageSettlementDetailParams lastCycleParams = JSONObject.parseObject(JSONObject.toJSONString(p), PageSettlementDetailParams.class);
            lastCycleParams.setSettlementMonth(calLastMonthCycle(p));
            supervisorPromotion = costAutoInfoDao.pcoPerformanceSummary(lastCycleParams);

        }
        DetailSummaryPcoPerformance totalSummary = new DetailSummaryPcoPerformance();
        totalSummary.setMonth(calMonthBeforeBeforeLastMonth(p));
        Optional<DetailSummaryPcoPerformance> summaryLongReissuePromotionOpt = Optional.ofNullable(summaryLongReissuePromotion);
        Optional<DetailSummaryPcoPerformance> longPromotionOpt = Optional.ofNullable(supervisorPromotion);

        totalSummary.setOrgPromotion(
                summaryLongReissuePromotionOpt.map(DetailSummaryPcoPerformance::getOrgPromotion).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummaryPcoPerformance::getOrgPromotion).orElse(BigDecimal.ZERO))
        );

        totalSummary.setCostAmount(
                summaryLongReissuePromotionOpt.map(DetailSummaryPcoPerformance::getCostAmount).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummaryPcoPerformance::getCostAmount).orElse(BigDecimal.ZERO))
        );

        totalSummary.setOrgLongRenewalRate(
                summaryLongReissuePromotionOpt.map(DetailSummaryPcoPerformance::getOrgLongRenewalRate).orElse(BigDecimal.ZERO)
        );
        totalSummary.setCostRate(
                summaryLongReissuePromotionOpt.map(DetailSummaryPcoPerformance::getCostRate).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummaryPcoPerformance::getCostRate).orElse(BigDecimal.ZERO))
        );

        return Lists.newArrayList(totalSummary, supervisorPromotion, summaryLongReissuePromotion)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.PCO_REISSUE_PERFORMANCE);
    }
}
