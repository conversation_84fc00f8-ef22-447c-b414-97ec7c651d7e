package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.common.SettlementReconcileBaseService;
import com.mpolicy.manage.modules.settlement.dao.SettlementEventJobDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementEventJobEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementEventJobService;
import com.mpolicy.manage.modules.settlement.vo.HandleRefreshVo;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobInfoOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListOut;
import com.mpolicy.settlement.core.common.reconcile.RectificationOneVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service("settlementEventJobService")
public class SettlementEventJobServiceImpl extends ServiceImpl<SettlementEventJobDao, SettlementEventJobEntity> implements SettlementEventJobService {

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private SettlementReconcileBaseService settlementReconcileBaseService;

    /**
     * 获取结算交互事件受理表列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<SettlementEventJobListOut> findSettlementEventJobList(SettlementEventJobListInput input) {
        IPage<SettlementEventJobListOut> page = baseMapper.findSettlementEventJobList(new Page<SettlementEventJobListOut>(input.getPage(), input.getLimit()), input);
        return new PageUtils(page);
    }

    /**
     * 获取结算交互事件受理表详情
     *
     * @param id
     * @return
     */
    @Override
    public SettlementEventJobInfoOut findSettlementEventJobById(Integer id) {
        SettlementEventJobEntity settlementEventJob = baseMapper.selectById(id);
        if (settlementEventJob == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在"));
        }
        SettlementEventJobInfoOut out = new SettlementEventJobInfoOut();
        BeanUtil.copyProperties(settlementEventJob, out);
        return out;
    }

    /**
     * 处理任务的刷新和重置
     *
     * @param vo 请求参数
     */
    @Override
    public void handleRefresh(HandleRefreshVo vo) {
        SettlementEventJobEntity settlementEventJob = Optional.ofNullable(getById(vo.getId()))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在")));
        Boolean execute = transactionTemplate.execute((status) -> {
            try {
                BeanUtil.copyProperties(vo, settlementEventJob);
                // 更新数据
                updateById(settlementEventJob);
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                return false;
            }
        });
        if (Boolean.TRUE.equals(execute)) {
            settlementReconcileBaseService.handleSettlementEventJob(CollUtil.newArrayList(settlementEventJob.getPushEventCode()));
        }
    }

    /**
     * 冲正处理
     *
     * @param id 数据id
     */
    @Override
    public void rectificationById(Integer id) {
        SettlementEventJobEntity settlementEventJob = Optional.ofNullable(getById(id)).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在")));
        RectificationOneVo vo = new RectificationOneVo();
        vo.setEventSourceCode(settlementEventJob.getPushEventCode());
        // 不会立即执行冲证数据的刷新
        vo.setRunJob(0);
        settlementReconcileBaseService.rectificationOne(vo);
    }
}
