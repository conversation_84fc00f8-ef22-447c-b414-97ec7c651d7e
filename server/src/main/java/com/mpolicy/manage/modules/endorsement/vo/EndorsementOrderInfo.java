package com.mpolicy.manage.modules.endorsement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保全订单列表
 *
 * <AUTHOR>
 * @date 2024/1/11 15:00
 */
@Data
@ApiModel(value = "保全订单列表")
public class EndorsementOrderInfo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 保全编号
     */
    @ApiModelProperty(value = "保全编号", example = "ENS20220506103627429095")
    @ExcelProperty(value = "保全编号")
    private String endorsementCode;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "H20506103627429095")
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 保全类型
     */
    @ApiModelProperty(value = "保全类型", example = "增减员")
    @ExcelProperty(value = "保全类型")
    private String endorsementTypeDesc;
    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号", example = "H20506103627429095")
    @ExcelProperty(value = "批单号")
    private String policyCode;
    /**
     * 保险公司
     */
    @ApiModelProperty(value = "保险公司", example = "泰康")
    @ExcelProperty(value = "保险公司")
    private String companyName;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", example = "惊喜一生")
    @ExcelProperty(value = "商品名称")
    private String commodityName;
    /**
     * 投保人
     */
    @ApiModelProperty(value = "投保人", example = "张三")
    @ExcelProperty(value = "投保人")
    private String holderName;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型", example = "SYS:JJH")
    @ExcelProperty(value = "证件类型")
    private String holderCertiCodDesc;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码", example = "142628468468456456456")
    @ExcelProperty(value = "证件号码")
    private String holderIdNo;
    /**
     * 保全金额
     */
    @ApiModelProperty(value = "保全金额", example = "100.00")
    @ExcelProperty(value = "保全金额")
    private BigDecimal premium;
    /**
     * 保全状态
     */
    @ApiModelProperty(value = "保全状态", example = "待核保")
    @ExcelProperty(value = "保全状态")
    private String endorsementStatusDesc;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-01-01")
    @ExcelProperty(value = "创建时间")
    private String createTime;
    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间", example = "2024-01-01")
    @ExcelProperty(value = "支付时间")
    private String payTime;
    /**
     * 生效日期（增员）
     */
    @ApiModelProperty(value = "生效日期（增员）", example = "2024-01-01")
    @ExcelProperty(value = "生效日期（增员）")
    private String addEffectiveDate;
    /**
     * 生效日期（减员）
     */
    @ApiModelProperty(value = "生效日期（减员）", example = "2024-01-01")
    @ExcelProperty(value = "生效日期（减员）")
    private String reduceEffectiveDate;
    /**
     * C端客户编码
     */
    @ApiModelProperty(value = "C端客户编码", example = "张三")
    @ExcelProperty(value = "C端客户编码")
    private String userNo;
    /**
     * C端客户名
     */
    @ApiModelProperty(value = "C端客户名", example = "C46546545645456")
    @ExcelProperty(value = "C端客户名")
    private String userName;
    /**
     * 渠道推荐人
     */
    @ApiModelProperty(value = "渠道推荐人", example = "张三")
    @ExcelProperty(value = "渠道推荐人")
    private String referrerName;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道", example = "中和农信")
    @ExcelProperty(value = "渠道")
    private String userChannelName;
    /**
     * 渠道推荐人
     */
    @ApiModelProperty(value = "代理人", example = "张三")
    @ExcelProperty(value = "渠道推荐人")
    private String agentName;
}
