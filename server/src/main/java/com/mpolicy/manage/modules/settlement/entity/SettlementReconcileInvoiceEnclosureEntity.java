package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 对账单附件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:51
 */
@TableName("settlement_reconcile_invoice_enclosure")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInvoiceEnclosureEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 发票编码
     */
    private String invoiceCode;
    /**
     * 文件类型 0:发票附件 1:邮件附件
     */
    private Integer invoiceType;
    /**
     * 文件编码
     */
    private String fileCode;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件后缀
     */
    private String fileExt;
    /**
     * 文件大小
     */
    private Integer fileSize;
    /**
     * oss存储路径
     */
    private String filePath;
    /**
     * 外部访问路径
     */
    private String domainPath;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
