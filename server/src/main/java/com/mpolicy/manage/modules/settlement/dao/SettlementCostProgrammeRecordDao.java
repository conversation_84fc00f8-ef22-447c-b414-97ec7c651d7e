package com.mpolicy.manage.modules.settlement.dao;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeRecordEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementConfirmInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementConfirmPageVO;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailPolicyDimensionVo;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 支出结算方案记录表
 * 
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
public interface SettlementCostProgrammeRecordDao extends ImsBaseMapper<SettlementCostProgrammeRecordEntity> {


}
