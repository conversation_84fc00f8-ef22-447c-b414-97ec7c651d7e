package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.alibaba.fastjson.JSONObject;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoRecordEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/1/25 15:44
 * @Version 1.0
 */
@Data
public class SettlementDynamicSubjectVO {

    @ApiModelProperty(value = "自定义科目编码")
    private String subjectCode;

    @ApiModelProperty(value = "自定义科目名称")
    private String subjectName;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;


    public static SettlementDynamicSubjectVO fromAutoRecord(SettlementCostAutoRecordEntity autoRecordEntity) {

        SettlementDynamicSubjectVO dynamicSubjectVO = new SettlementDynamicSubjectVO();
        dynamicSubjectVO.setSubjectCode(autoRecordEntity.getSubjectCode());
        dynamicSubjectVO.setSubjectName(autoRecordEntity.getSubjectName());
        dynamicSubjectVO.setAmount(autoRecordEntity.getGrantAmount());

        return dynamicSubjectVO;
    }

    public SettlementDynamicSubjectVO copy() {
        return JSONObject.parseObject(JSONObject.toJSONString(this),  SettlementDynamicSubjectVO.class) ;
    }

}
