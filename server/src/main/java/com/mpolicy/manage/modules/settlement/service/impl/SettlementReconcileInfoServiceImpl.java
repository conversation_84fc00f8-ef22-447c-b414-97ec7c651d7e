package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.redis.redisson.RedissLockUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.common.utils.thread.ThreadUtils;
import com.mpolicy.manage.common.utils.ListUtil;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.enums.SettlementInvoiceStatusEnum;
import com.mpolicy.manage.enums.SettlementMethodEnum;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductProductService;
import com.mpolicy.manage.modules.protocol.vo.InsuranceProductProductMapOut;
import com.mpolicy.manage.modules.settlement.common.SettlementReconcileBaseService;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInfoDao;
import com.mpolicy.manage.modules.settlement.entity.*;
import com.mpolicy.manage.modules.settlement.service.*;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.manage.modules.settlement.vo.manage.*;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.settlement.core.common.reconcile.HandleSettlementPolicyInfoVo;
import com.mpolicy.settlement.core.common.reconcile.RefreshSettlementEventJobVo;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import com.mpolicy.settlement.core.common.reconcile.enums.*;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 保司结算对账单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
@Slf4j
@Service("settlementReconcileInfoService")
public class SettlementReconcileInfoServiceImpl
        extends ServiceImpl<SettlementReconcileInfoDao, SettlementReconcileInfoEntity>
        implements SettlementReconcileInfoService {

    @Autowired
    private SettlementReconcileDiffBacklogService settlementReconcileDiffBacklogService;

    @Autowired
    private SettlementSubjectService settlementSubjectService;
    @Autowired
    private SettlementReconcileBaseService settlementReconcileBaseService;

    @Autowired
    private StorageService storageService;
    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;
    @Autowired
    private SettlementReconcilePolicyFileService settlementReconcilePolicyFileService;

    @Autowired
    private SettlementReconcilePolicyService settlementReconcilePolicyService;

    @Autowired
    private SettlementReconcileCompanyService settlementReconcileCompanyService;
    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;
    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;
    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;
    @Autowired
    private SettlementReconcileSubjectService settlementReconcileSubjectService;
    @Autowired
    private SettlementReconcileFileService settlementReconcileFileService;
    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;
    @Autowired
    private SettlementEventJobService settlementEventJobService;
    @Autowired
    private IEpProtocolInsuranceProductProductService protocolInsuranceProductProductService;
    @Autowired
    private SysDocumentService sysDocumentService;
    @Autowired
    private SettlementReconcileCompanySubjectProductService settlementReconcileCompanySubjectProductService;
    @Value("${mp.download.folder:logs/}")
    String destPath;


    @Override
    public SettlementReconcileInfo querySettlementReconcileInfo(String reconcileCode) {
        SettlementReconcileInfoEntity reconcileInfo = Optional.ofNullable(
                        this.lambdaQuery().eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单纪录不存在")));

        SettlementReconcileInfo result = new SettlementReconcileInfo();
        BeanUtils.copyProperties(reconcileInfo, result);
        return result;
    }

    @Override
    public PageUtils<SettlementReconcileInfo> querySettlementReconcileInfoList(Map<String, Object> params) {
        // 条件参数获取[保险公司编码 + 外部签署方类型字典 + 对账月度]
        String companyCode = RequestUtils.objectValueToString(params, "companyCode");//结算保司
        // 结算保司编码集合
        List<String> companyCodeList =
                JSONUtil.parseArray(RequestUtils.objectValueToString(params, "companyCodeList")).toList(String.class);
        String companyName = RequestUtils.objectValueToString(params, "companyName");
        String externalSignatoryType = RequestUtils.objectValueToString(params, "externalSignatoryType");
        String reconcileMonth = RequestUtils.objectValueToString(params, "reconcileMonth");
        Integer reconcileDay = RequestUtils.objectValueToInteger(params, "reconcileDay");
        String reconcileCompanyCode = RequestUtils.objectValueToString(params, "reconcileCompanyCode");
        String reconcileCode = RequestUtils.objectValueToString(params, "reconcileCode");
        String subjectRuleCode = RequestUtils.objectValueToString(params, "subjectRuleCode");
        String reconcileType = RequestUtils.objectValueToString(params, "reconcileType");
        Integer invoiceStatus = RequestUtils.objectValueToInteger(params, "invoiceStatus");
        String headName = RequestUtils.objectValueToString(params, "headName");
        String beginReconcileTime = RequestUtils.objectValueToString(params, "beginReconcileTime");
        String endReconcileTime = RequestUtils.objectValueToString(params, "endReconcileTime");
        List<Integer> reconcileStatusList =
                JSONUtil.parseArray(RequestUtils.objectValueToString(params, "reconcileStatusList")).toList(Integer.class);
        List<String> reconcileCodeList =
                JSONUtil.parseArray(RequestUtils.objectValueToString(params, "reconcileCodeList")).toList(String.class);
        if (StrUtil.isNotBlank(subjectRuleCode)) {
            reconcileCodeList = settlementReconcileSubjectService.lambdaQuery()
                    .eq(SettlementReconcileSubjectEntity::getSubjectRuleCode, subjectRuleCode).list().stream()
                    .map(SettlementReconcileSubjectEntity::getReconcileCode).collect(Collectors.toList());
        }
        // 分页查询基础数据
        IPage<SettlementReconcileInfoEntity> pageResult =
                this.page(new Query<SettlementReconcileInfoEntity>().getPage(params),
                        new LambdaQueryWrapper<SettlementReconcileInfoEntity>().eq(StringUtils.isNotBlank(companyCode),
                                        SettlementReconcileInfoEntity::getCompanyCode, companyCode)
                                .in(CollUtil.isNotEmpty(companyCodeList), SettlementReconcileInfoEntity::getCompanyCode, companyCodeList)
                                .like(StringUtils.isNotBlank(companyName), SettlementReconcileInfoEntity::getCompanyName,
                                        companyName).eq(StringUtils.isNotBlank(reconcileCompanyCode),
                                        SettlementReconcileInfoEntity::getReconcileCompanyCode, reconcileCompanyCode)
                                .eq(StringUtils.isNotBlank(reconcileCode), SettlementReconcileInfoEntity::getReconcileCode,
                                        reconcileCode)
                                .eq(StringUtils.isNotBlank(reconcileType), SettlementReconcileInfoEntity::getReconcileType,
                                        reconcileType).eq(StringUtils.isNotBlank(externalSignatoryType),
                                        SettlementReconcileInfoEntity::getExternalSignatoryType, externalSignatoryType)
                                .eq(StringUtils.isNotBlank(reconcileMonth), SettlementReconcileInfoEntity::getReconcileMonth,
                                        reconcileMonth)
                                .eq(StringUtils.isNotBlank(headName), SettlementReconcileInfoEntity::getHeadName, headName)
                                .ge(StringUtils.isNotBlank(beginReconcileTime), SettlementReconcileInfoEntity::getReconcileTime,
                                        beginReconcileTime)
                                .le(StringUtils.isNotBlank(endReconcileTime), SettlementReconcileInfoEntity::getReconcileTime,
                                        endReconcileTime)
                                .eq(reconcileDay != null, SettlementReconcileInfoEntity::getReconcileDay, reconcileDay)
                                .in(CollUtil.isNotEmpty(reconcileStatusList), SettlementReconcileInfoEntity::getReconcileStatus,
                                        reconcileStatusList)
                                .eq(invoiceStatus != null, SettlementReconcileInfoEntity::getInvoiceStatus, invoiceStatus)
                                .in(CollUtil.isNotEmpty(reconcileCodeList), SettlementReconcileInfoEntity::getReconcileCode,
                                        reconcileCodeList));
        if (CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageUtils(Collections.emptyList(), (int) pageResult.getTotal(), (int) pageResult.getSize(),
                    (int) pageResult.getCurrent());
        }
        List<String> newReconcileCodeList =
                pageResult.getRecords().stream().map(SettlementReconcileInfoEntity::getReconcileCode)
                        .collect(Collectors.toList());

        Map<String, List<String>> settlementReconcileSubjectMap = settlementReconcileSubjectService.lambdaQuery()
                .in(SettlementReconcileSubjectEntity::getReconcileCode, newReconcileCodeList).list().stream()
                .collect(Collectors.groupingBy(SettlementReconcileSubjectEntity::getReconcileCode)).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(SettlementReconcileSubjectEntity::getSubjectRuleCode).distinct()
                                .sorted().collect(Collectors.toList())));
        List<String> subjectRuleCodeList =
                settlementReconcileSubjectMap.values().stream().flatMap(Collection::stream).distinct()
                        .collect(Collectors.toList());
        Map<String, String> subjectMap = new HashMap<>();
        if (!subjectRuleCodeList.isEmpty()) {
            subjectMap.putAll(settlementReconcileCompanySubjectService.lambdaQuery()
                    .in(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCodeList).list().stream()
                    .filter(f -> StrUtil.isNotBlank(f.getRemark())).collect(
                            Collectors.toMap(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode,
                                    SettlementReconcileCompanySubjectEntity::getRemark, (v1, v2) -> v1)));
        }

        // 构建vo
        List<SettlementReconcileInfo> resultData = pageResult.getRecords().stream().map(x -> {
            SettlementReconcileInfo bean = new SettlementReconcileInfo();
            BeanUtils.copyProperties(x, bean);
            if (!subjectMap.isEmpty()) {
                String subjectRemark =
                        settlementReconcileSubjectMap.get(x.getReconcileCode()).stream().filter(subjectMap::containsKey)
                                .map(subjectMap::get).distinct().collect(Collectors.joining(","));
                bean.setSubjectRemark(subjectRemark);
            }
            //处理开票状态
            bean.setInvoiceStatusDesc(SettlementInvoiceStatusEnum.matchSearchCode(x.getInvoiceStatus()).getDesc());
            return bean;
        }).collect(Collectors.toList());

        // 重新构建分页返回对象
        return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(),
                (int) pageResult.getCurrent());
    }

    @Override
    public PageUtils<SettlementReconcileDiff> querySettlementReconcileDiffList(String reconcileCode,
                                                                               Map<String, Object> paramMap) {
        String diffType = RequestUtils.objectValueToString(paramMap, "diffType");
        Integer diffStatus = RequestUtils.objectValueToInteger(paramMap, "diffStatus");
        Integer followStatus = RequestUtils.objectValueToInteger(paramMap, "followStatus");
        String policyNo = RequestUtils.objectValueToString(paramMap, "policyNo");
        String endorsementNo = RequestUtils.objectValueToString(paramMap, "endorsementNo");
        Integer signChange = RequestUtils.objectValueToInteger(paramMap, "signChange");
        String zeroPremium = RequestUtils.objectValueToString(paramMap, "zeroPremium");
        // 分页查询基础数据
        IPage<SettlementReconcileConfirmEntity> pageResult =
                settlementReconcileConfirmService.page(new Query<SettlementReconcileConfirmEntity>().getPage(paramMap),
                        new LambdaQueryWrapper<SettlementReconcileConfirmEntity>().eq(StringUtils.isNotBlank(reconcileCode),
                                        SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                                .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1)
                                .eq(diffStatus != null, SettlementReconcileConfirmEntity::getDiffStatus, diffStatus)
                                .eq(StringUtils.isNotBlank(diffType), SettlementReconcileConfirmEntity::getDiffType, diffType)
                                .like(StringUtils.isNotBlank(policyNo), SettlementReconcileConfirmEntity::getPolicyNo, policyNo)
                                .like(StringUtils.isNotBlank(endorsementNo), SettlementReconcileConfirmEntity::getEndorsementNo, endorsementNo)
                                .eq(Objects.nonNull(signChange), SettlementReconcileConfirmEntity::getMarkStatus, signChange)
                                .ne(Objects.equals(zeroPremium, "1"), SettlementReconcileConfirmEntity::getPremium, 0)
                                .eq(Objects.equals(zeroPremium, "0"), SettlementReconcileConfirmEntity::getPremium, 0)
                                .orderByDesc(SettlementReconcileConfirmEntity::getDiffType));
        if (pageResult.getRecords().isEmpty()) {
            return new PageUtils<>(Collections.emptyList(), 0, 0, 1);
        }
        List<String> billCodeList = pageResult.getRecords().stream().map(SettlementReconcileConfirmEntity::getBillCode)
                .collect(Collectors.toList());

        Map<String, SettlementReconcilePolicyEntity> reconcilePolicyMap = settlementReconcilePolicyService.lambdaQuery()
                .in(SettlementReconcilePolicyEntity::getBillCode, billCodeList).list().stream()
                .collect(Collectors.toMap(SettlementReconcilePolicyEntity::getBillCode, Function.identity(), (x, y) -> x));
        Map<String, SettlementPolicyInfoEntity> settlementPolicyInfoMap = new HashMap<>(1);
        if (!reconcilePolicyMap.isEmpty()) {
            List<String> settlementCodeList =
                    reconcilePolicyMap.values().stream().map(SettlementReconcilePolicyEntity::getSettlementCode).distinct()
                            .collect(Collectors.toList());
            settlementPolicyInfoService.lambdaQuery()
                    .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                    .in(SettlementPolicyInfoEntity::getSettlementCode, settlementCodeList).list().forEach(action -> {
                        settlementPolicyInfoMap.put(action.getSettlementCode(), action);
                    });

        }
        // 构建vo
        List<SettlementReconcileDiff> resultData = pageResult.getRecords().stream().map(x -> {
            SettlementReconcileDiff bean = new SettlementReconcileDiff();
            BeanUtils.copyProperties(x, bean);
            // 小鲸保单存在 或者 小鲸和保司的保单都有
            if (reconcilePolicyMap.containsKey(x.getBillCode())) {
                SettlementReconcilePolicyEntity settlementReconcilePolicy = reconcilePolicyMap.get(x.getBillCode());
                SettlementPolicyInfoEntity settlementPolicyInfo =
                        settlementPolicyInfoMap.get(settlementReconcilePolicy.getSettlementCode());
                if (settlementPolicyInfo != null) {
                    bean.setApplicantTime(settlementPolicyInfo.getApplicantTime());
                    bean.setApplicantName(settlementPolicyInfo.getApplicantName());
                    bean.setSettlementRateTax(settlementPolicyInfo.getSettlementRateTax() + "");
                    bean.setSettlementRateMethod(
                            SettlementMethodEnum.matchSearchCode(settlementPolicyInfo.getSettlementRateMethod() + "")
                                    .getDesc());
                    bean.setOrderTime(settlementPolicyInfo.getOrderTime());
                    bean.setApprovedTime(settlementPolicyInfo.getApprovedTime());
                    bean.setEnforceTime(settlementPolicyInfo.getEnforceTime());
                }
            } else {
                // 小鲸缺失 这个时候需要计算一下 他是税前还是税后了,可能不准 [保费 / 税率 * 费率 = 手续费]
                // 判断[ 保费 / (手续费 / 费率)  >= 1.01]
                BigDecimal companySettlementRate;
                try {
                    companySettlementRate = new BigDecimal(x.getCompanySettlementRate());
                } catch (Exception e) {
                    log.error("保司对账单费率解析失败保单号:{}", x.getPolicyNo(), e);
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(
                            "保司对账单费率解析失败保单号=[" + x.getPolicyNo() + "],费率=[" + x.getCompanySettlementRate() + "]，请核查"));
                }

                if (x.getCompanyPremium().compareTo(BigDecimal.ZERO) == 0 || x.getCompanyAmount()
                        .compareTo(BigDecimal.ZERO) == 0 || companySettlementRate.compareTo(BigDecimal.ZERO) == 0) {
                    // 默认是税前
                    bean.setSettlementRateTax("1");
                    bean.setSettlementRateMethod(SettlementMethodEnum.FULL_PREMIUM.getDesc());
                } else {
                    BigDecimal taxRate = x.getCompanyPremium().divide(x.getCompanyAmount()
                                    .divide(new BigDecimal(x.getCompanySettlementRate()), 4, RoundingMode.HALF_UP), 4,
                            RoundingMode.HALF_UP);
                    if (taxRate.compareTo(new BigDecimal("1.01")) < 0) {
                        bean.setSettlementRateTax("1");
                        bean.setSettlementRateMethod(SettlementMethodEnum.FULL_PREMIUM.getDesc());
                    } else {
                        bean.setSettlementRateTax(taxRate.setScale(2, RoundingMode.HALF_UP).toString());
                        bean.setSettlementRateMethod(SettlementMethodEnum.NET_PREMIUM.getDesc());
                    }
                }

            }
            bean.setDiffStatusDesc(x.getDiffStatus() == 0 ? "待处理" : "已处理");
            bean.setDiffStatus(x.getDiffStatus());
            ReconcileDiffWhyEnum reconcileDiffWhy = ReconcileDiffWhyEnum.getReconcileDiffWhy(x.getDiffWhy());
            bean.setDiffWhyDesc(reconcileDiffWhy != null ? reconcileDiffWhy.getName() : "未知");
            ReconcileDiffTypeEnum reconcileDiffType = ReconcileDiffTypeEnum.getReconcileDiffType(x.getDiffType());
            bean.setDiffName(reconcileDiffType != null ? reconcileDiffType.getName() : "未知");
            return bean;
        }).collect(Collectors.toList());
        // 重新构建分页返回对象
        return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(),
                (int) pageResult.getCurrent());
    }

    /**
     * 获取差异详情
     *
     * @param billCode
     * @return
     */
    @Override
    public SettlementReconcileDiff querySettlementReconcileDiffInfo(String billCode) {
        //差异明细
        SettlementReconcileConfirmEntity settlementReconcileConfirm = Optional.ofNullable(
                settlementReconcileConfirmService.lambdaQuery().eq(SettlementReconcileConfirmEntity::getBillCode, billCode)
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("编码不存在")));
        // 代办事项
        SettlementReconcileDiffBacklogEntity settlementReconcileDiffBacklog =
                settlementReconcileDiffBacklogService.lambdaQuery()
                        .eq(SettlementReconcileDiffBacklogEntity::getBillCode, billCode).one();

        SettlementReconcileDiff bean = new SettlementReconcileDiff();
        BeanUtils.copyProperties(settlementReconcileConfirm, bean);
        bean.setDiffStatusDesc(settlementReconcileConfirm.getDiffStatus() == 0 ? "待处理" : "已处理");
        bean.setDiffStatus(settlementReconcileConfirm.getDiffStatus());
        ReconcileDiffTypeEnum reconcileDiffType =
                ReconcileDiffTypeEnum.getReconcileDiffType(settlementReconcileConfirm.getDiffType());
        bean.setDiffName(reconcileDiffType != null ? reconcileDiffType.getName() : "未知");
        ReconcileDiffWhyEnum reconcileDiffWhy =
                ReconcileDiffWhyEnum.getReconcileDiffWhy(settlementReconcileConfirm.getDiffWhy());
        bean.setDiffWhyDesc(reconcileDiffWhy != null ? reconcileDiffWhy.getName() : "未知");
        if (settlementReconcileDiffBacklog != null) {
            BeanUtils.copyProperties(settlementReconcileDiffBacklog, bean);
            bean.setFollowStatus(ReconcileDiffFollowStatusEnum.getReconcileDiffFollowStatus(
                    settlementReconcileDiffBacklog.getFollowStatus()).getName());
        }
        return bean;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startReconcile(StartReconcileVo input) {
        //如果上传的文件为空的时候,删除上传的对账单文件
        if (CollUtil.isEmpty(input.getSettlementReconcileFileList())) {
            // 删除文件
            settlementReconcileFileService.lambdaUpdate()
                    .eq(SettlementReconcileFileEntity::getReconcileCode, input.getReconcileCode()).remove();
            settlementReconcilePolicyFileService.lambdaUpdate()
                    .eq(SettlementReconcilePolicyFileEntity::getReconcileCode, input.getReconcileCode()).remove();
            //更新对账单对账文件数量
            settlementReconcileInfoService.lambdaUpdate().set(SettlementReconcileInfoEntity::getCompanyBillCount, 0)
                    .eq(SettlementReconcileInfoEntity::getReconcileCode, input.getReconcileCode()).update();
        } else {
            List<String> fileCodeList =
                    input.getSettlementReconcileFileList().stream().map(SettlementReconcileFileEntity::getReconcileFileCode)
                            .collect(Collectors.toList());
            //更新对账单文件
            List<String> dbFileCodeList = settlementReconcileFileService.lambdaQuery()
                    .eq(SettlementReconcileFileEntity::getReconcileCode, input.getReconcileCode()).list().stream()
                    .map(SettlementReconcileFileEntity::getReconcileFileCode).collect(Collectors.toList());
            if (CollUtil.isEmpty(dbFileCodeList)) {
                // 将文件入库
                this.saveSettlementReconcileFile(input.getReconcileCode(), input.getSettlementReconcileFileList());
            } else {
                ListUtil<String> listUtil = new ListUtil<>();
                Map<String, List<String>> map = listUtil.splitListToMap(dbFileCodeList, fileCodeList);
                List<String> removeList = map.get(ListUtil.REMOVE_LIST_KEY);
                List<String> saveList = map.get(ListUtil.SAVE_LIST_KEY);
                //删除文件
                if (CollUtil.isNotEmpty(removeList)) {
                    settlementReconcileFileService.remove(new LambdaQueryWrapper<SettlementReconcileFileEntity>().eq(
                                    SettlementReconcileFileEntity::getReconcileCode, input.getReconcileCode())
                            .in(SettlementReconcileFileEntity::getReconcileFileCode, removeList));
                    settlementReconcilePolicyFileService.lambdaUpdate()
                            .eq(SettlementReconcilePolicyFileEntity::getReconcileCode, input.getReconcileCode())
                            .in(SettlementReconcilePolicyFileEntity::getFileCode, removeList).remove();
                }
                if (CollUtil.isNotEmpty(saveList)) {
                    List<SettlementReconcileFileEntity> collect = input.getSettlementReconcileFileList().stream()
                            .filter(f -> saveList.contains(f.getReconcileFileCode())).collect(Collectors.toList());
                    this.saveSettlementReconcileFile(input.getReconcileCode(), collect);
                }
            }
            //更新对账单对账文件数量
            settlementReconcileInfoService.lambdaUpdate()
                    .set(SettlementReconcileInfoEntity::getCompanyBillCount, input.getSettlementReconcileFileList().size())
                    .eq(SettlementReconcileInfoEntity::getReconcileCode, input.getReconcileCode()).update();
        }
        // 执行开始对账
        return settlementReconcileBaseService.startReconcile(input.getReconcileCode(),
                ShiroUtils.getUserEntity().getUsername());
    }

    private void saveSettlementReconcileFile(String reconcileCode,
                                             List<SettlementReconcileFileEntity> settlementReconcileFileList) {
        if (CollUtil.isNotEmpty(settlementReconcileFileList)) {
            List<String> fileCodeList =
                    settlementReconcileFileList.stream().map(SettlementReconcileFileEntity::getReconcileFileCode)
                            .collect(Collectors.toList());
            Map<String, SysDocumentEntity> fileMap =
                    sysDocumentService.lambdaQuery().in(SysDocumentEntity::getFileCode, fileCodeList).list().stream()
                            .collect(Collectors.toMap(SysDocumentEntity::getFileCode, v -> v));
            settlementReconcileFileList.forEach(subjectFile -> {
                subjectFile.setReconcileCode(reconcileCode);
                if (fileMap.containsKey(subjectFile.getReconcileFileCode())) {
                    SysDocumentEntity documentInfo = fileMap.get(subjectFile.getReconcileFileCode());
                    subjectFile.setFileName(documentInfo.getFileName());
                    subjectFile.setFileUrl(documentInfo.getDomainPath());
                    settlementReconcileFileService.save(subjectFile);
                    // 将文件保存到数据库中
                    settlementReconcilePolicyFileService.saveReconcileRuleFileTemplate(subjectFile, documentInfo);
                }
            });
        }
    }

    @Override
    public List<SettlementReconcileFile> reconcileFileList(String reconcileCode) {
        List<SettlementReconcileFileEntity> reconcileList = settlementReconcileFileService.lambdaQuery()
                .eq(SettlementReconcileFileEntity::getReconcileCode, reconcileCode).list();
        return reconcileList.stream().map(x -> {
            SettlementReconcileFile bean = new SettlementReconcileFile();
            BeanUtils.copyProperties(x, bean);
            bean.setReconcileFileName(ReconcileTemplateEnum.matchSearchCode(x.getReconcileFileType()).getLabel());
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public void uploadCompanyReconcileFile(String reconcileCode, ReconcileTemplateEnum reconcileTemplate,
                                           String fileCode, String userName) {
        settlementReconcileBaseService.uploadReconcileFile(reconcileCode, reconcileTemplate.getCode(), fileCode,
                userName);
    }

    @Override
    public void retryUploadCompanyReconcileFile(String reconcileCode, ReconcileTemplateEnum reconcileTemplate,
                                                String sourceFileCode, String fileCode, String userName) {
        settlementReconcileBaseService.retryUploadReconcileFile(reconcileCode, reconcileTemplate.getCode(),
                sourceFileCode, fileCode, userName);
    }

    @Override
    public void removeReconcileFile(String reconcileCode, String reconcileFileCode, String userName) {
        settlementReconcilePolicyFileService.lambdaUpdate()
                .eq(SettlementReconcilePolicyFileEntity::getFileCode, reconcileFileCode).remove();
        settlementReconcileBaseService.removeReconcileFile(reconcileCode, reconcileFileCode, userName);
    }

    @Override
    public String reconcileAmountAccuracy(AmountAccuracyInput input, String userName) {
        ReconcileAmountAccuracyInput requestData = new ReconcileAmountAccuracyInput();
        BeanUtils.copyProperties(input, requestData);
        requestData.setOpeUserName(userName);
        String result = settlementReconcileBaseService.reconcileAmountAccuracy(requestData);
        log.info("执行批量处理对账单精度信息，msg={}", result);
        return result;
    }

    @Override
    public String retryStartReconcile(String reconcileCode, String userName) {
        return settlementReconcileBaseService.retryStartReconcile(reconcileCode, userName);
    }

    @Override
    public String finishReconcile(String reconcileCode, String userName) {
        return settlementReconcileBaseService.finishReconcile(reconcileCode, userName);
    }

    @Override
    public void diffBacklog(ReconcileDiffBacklogInput diffBacklogInput, String userName) {
        DiffBacklogInput input = new DiffBacklogInput();
        BeanUtils.copyProperties(diffBacklogInput, input);
        input.setOpeUserName(userName);
        settlementReconcileBaseService.diffBacklog(input);
    }

    @Override
    public PageUtils<SettlementReconcileConfirm> confirmReconcileList(String reconcileCode,
                                                                      Map<String, Object> paramMap) {
        // 保单号
        String policyNo = RequestUtils.objectValueToString(paramMap, "policyNo");
        // 分页查询基础数据
        IPage<SettlementReconcileConfirmEntity> pageResult =
                settlementReconcileConfirmService.page(new Query<SettlementReconcileConfirmEntity>().getPage(paramMap),
                        new LambdaQueryWrapper<SettlementReconcileConfirmEntity>().eq(StringUtils.isNotBlank(reconcileCode),
                                        SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                                .eq(StringUtils.isNotBlank(policyNo), SettlementReconcileConfirmEntity::getPolicyNo, policyNo));
        if (pageResult.getRecords().isEmpty()) {
            return new PageUtils<>(Collections.emptyList(), 0, 0, 1);
        }
        // 构建vo
        List<SettlementReconcileConfirm> resultData = pageResult.getRecords().stream().map(x -> {
            SettlementReconcileConfirm bean = new SettlementReconcileConfirm();
            BeanUtils.copyProperties(x, bean);
            return bean;
        }).collect(Collectors.toList());
        // 重新构建分页返回对象
        return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(),
                (int) pageResult.getCurrent());
    }

    @Override
    public SettlementReconcileConfirmSummary reconcileOverview(String reconcileCode) {
        Map<String, String> settlementSubjectMapCode = settlementSubjectService.list().stream().collect(Collectors.toMap(SettlementSubjectEntity::getSubjectCode, SettlementSubjectEntity::getSubjectName));
        // 获取集合信息
        List<SettlementReconcileConfirmEntity> list = settlementReconcileConfirmService.lambdaQuery()
                .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                .eq(SettlementReconcileConfirmEntity::getBillEnable, 1).list();

        Map<String, Map<String, List<SettlementReconcileConfirmEntity>>> confirm = list.stream()
                .collect(Collectors.groupingBy(SettlementReconcileConfirmEntity::getReconcileModel, // 一级分组，按模式分组
                        Collectors.groupingBy(SettlementReconcileConfirmEntity::getReconcileSubjectCode))); // 二级分组，按科目

        SettlementReconcileConfirmSummary result = new SettlementReconcileConfirmSummary();

        // 1.总计
        list.stream().filter(f -> ReconcileModelEnum.ONLINE.getCode().equals(f.getReconcileModel()))
                .map(SettlementReconcileConfirmEntity::getSettlementPremium).reduce(BigDecimal::add)
                .ifPresent(result::setReconcileTotalPremium);

        list.stream().map(SettlementReconcileConfirmEntity::getSettlementAmount).reduce(BigDecimal::add)
                .ifPresent(result::setReconcileSettlementAmount);

        // 2.线上
        List<SettlementReconcileConfirmSubject> onlineSummary = new ArrayList<>();
        if (confirm.containsKey(ReconcileModelEnum.ONLINE.getCode())) {
            confirm.get(ReconcileModelEnum.ONLINE.getCode()).forEach((k, v) -> {
                SettlementReconcileConfirmSubject bean = new SettlementReconcileConfirmSubject();
                bean.setReconcileSubjectName(settlementSubjectMapCode.get(k));
                v.stream().map(SettlementReconcileConfirmEntity::getSettlementPremium).reduce(BigDecimal::add)
                        .ifPresent(bean::setTotalPremium);
                v.stream().map(SettlementReconcileConfirmEntity::getSettlementAmount).reduce(BigDecimal::add)
                        .ifPresent(bean::setSettlementAmount);
                onlineSummary.add(bean);
            });
        }
        result.setOnlineSummary(onlineSummary);

        // 3.线下
        List<SettlementReconcileConfirmSubject> offlineSummary = new ArrayList<>();
        if (confirm.containsKey(ReconcileModelEnum.OFFLINE.getCode())) {
            confirm.get(ReconcileModelEnum.OFFLINE.getCode()).forEach((k, v) -> {
                SettlementReconcileConfirmSubject bean = new SettlementReconcileConfirmSubject();
                bean.setReconcileSubjectName(settlementSubjectMapCode.get(k));
                v.stream().map(SettlementReconcileConfirmEntity::getSettlementPremium).reduce(BigDecimal::add)
                        .ifPresent(bean::setTotalPremium);
                v.stream().map(SettlementReconcileConfirmEntity::getSettlementAmount).reduce(BigDecimal::add)
                        .ifPresent(bean::setSettlementAmount);
                offlineSummary.add(bean);
            });
        }
        result.setOfflineSummary(offlineSummary);
        return result;
    }

    @Override
    public String reconcileRefreshPolicy(String reconcileCode, String userName) {
        return settlementReconcileBaseService.reconcileRefreshPolicy(reconcileCode, userName);
    }

    @Override
    public void closeReconcile(String reconcileCode, String userName) {
        settlementReconcileBaseService.closeReconcile(reconcileCode, userName);
    }

    /**
     * 打包导出对账单
     *
     * @param reconcileCode 对账编码
     * @param response
     */
    @Override
    public void exportReconcileFile(String reconcileCode, HttpServletResponse response) {
        List<String> collect = settlementReconcileFileService.lambdaQuery()
                .eq(SettlementReconcileFileEntity::getReconcileCode, reconcileCode).list().stream()
                .map(m -> DomainUtil.removeDomain(m.getFileUrl())).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            String fileName = reconcileCode + ".zip";
            // 将文件打包单临时文件
            storageService.downloadAsZip(collect, destPath, fileName, true);
            // 读取文件内容并输出到浏览器
            File file = new File(destPath + File.separator + fileName);
            try (InputStream inputStream = new FileInputStream(file)) {
                // 创建一个字节数组，将文件内容转换成字节数组
                byte[] bytes = new byte[(int) file.length()];
                inputStream.read(bytes);

                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
                response.addHeader("Content-Length", "" + bytes.length);
                response.setContentType("application/octet-stream; charset=UTF-8");
                IOUtils.write(bytes, response.getOutputStream());

            } catch (Exception e) {
                log.error("导出文件异常", e);
            }
        }
    }

    /**
     * 导出缺失保单
     *
     * @param input
     * @return
     */
    @Override
    public List<ExportDeletionPolicyOut> exportDeletionPolicy(ExportDeletionPolicyVo input) {
        List<SettlementReconcileInfoEntity> reconcileInfoList = new ArrayList<>();
        if (StrUtil.isNotBlank(input.getReconcileCode())) {
            reconcileInfoList = settlementReconcileInfoService.lambdaQuery()
                    .eq(SettlementReconcileInfoEntity::getReconcileType, input.getReconcileType())
                    .eq(SettlementReconcileInfoEntity::getReconcileCode, input.getReconcileCode())
                    .eq(SettlementReconcileInfoEntity::getReconcileStatus,
                            ReconcileStatusEnum.RECONCILE_ING.getStatusCode()).list();
        } else {
            //获取对账单中的保单数据
            reconcileInfoList = settlementReconcileInfoService.lambdaQuery()
                    .eq(SettlementReconcileInfoEntity::getReconcileType, input.getReconcileType())
                    .eq(SettlementReconcileInfoEntity::getReconcileStatus,
                            ReconcileStatusEnum.RECONCILE_ING.getStatusCode()).list();
        }
        if (CollUtil.isEmpty(reconcileInfoList)) {
            return Collections.emptyList();
        }
        Map<String, SettlementReconcileInfoEntity> reconcileInfoMap = reconcileInfoList.stream()
                .collect(Collectors.toMap(SettlementReconcileInfoEntity::getReconcileCode, v -> v));

        List<SettlementReconcilePolicyFileEntity> reconcilePolicyFileList =
                settlementReconcilePolicyFileService.lambdaQuery()
                        .in(SettlementReconcilePolicyFileEntity::getReconcileCode, new ArrayList<>(reconcileInfoMap.keySet()))
                        .eq(SettlementReconcilePolicyFileEntity::getReconcileSubjectCode,
                                ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getCode())
                        .eq(SettlementReconcilePolicyFileEntity::getFileType, 0).list();

        if (reconcilePolicyFileList.isEmpty()) {
            return Collections.emptyList();
        }
        // 获取批单数据
        List<String> batchCodeList =
                reconcilePolicyFileList.stream().map(SettlementReconcilePolicyFileEntity::getBatchCode)
                        .filter(StrUtil::isNotBlank).collect(Collectors.toList());
        // 获取新单数据
        List<String> policyNoList = reconcilePolicyFileList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode()))
                .map(SettlementReconcilePolicyFileEntity::getPolicyNo).collect(Collectors.toList());

        List<String> jobList = settlementEventJobService.lambdaQuery()
                .in(CollUtil.isNotEmpty(batchCodeList), SettlementEventJobEntity::getEndorsementNo, batchCodeList)
                .or(o -> o.in(SettlementEventJobEntity::getEventBusinessCode, policyNoList)
                        .isNull(SettlementEventJobEntity::getEndorsementNo)).list().stream().map(m -> {
                    if (StrUtil.isNotBlank(m.getEndorsementNo())) {
                        return m.getEventBusinessCode() + "_" + m.getEndorsementNo();
                    } else {
                        return m.getEventBusinessCode();
                    }
                }).collect(Collectors.toList());

        // 获取JOB数据
        return reconcilePolicyFileList.stream().filter(f -> {
            String key = f.getPolicyNo();
            if (StrUtil.isNotBlank(f.getBatchCode())) {
                key += "_" + f.getBatchCode();
            }
            return !jobList.contains(key);
        }).map(m -> {
            ExportDeletionPolicyOut result = BeanUtil.copyProperties(m, ExportDeletionPolicyOut.class);
            SettlementReconcileInfoEntity settlementReconcileInfo = reconcileInfoMap.get(m.getReconcileCode());
            if (settlementReconcileInfo != null) {
                result.setSettlementCompanyName(settlementReconcileInfo.getCompanyName());
                result.setExternalSignatoryName(settlementReconcileInfo.getExternalSignatoryName());
                result.setHeadName(settlementReconcileInfo.getHeadName());
            }
            result.setEndorsementNo(m.getBatchCode());
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 获取溯源信息 处理规则 1. 确认单中有对账单号，则直接返回
     *
     * @param billCode
     * @return
     */
    @Override
    public TraceSourceOut findTraceSource(String billCode) {
        StopWatch stopWatch = new StopWatch(billCode);
        // 设置开始
        stopWatch.start("基本信息");
        TraceSourceOut result = new TraceSourceOut();
        result.setBillCode(billCode);
        // 获取确认单信息
        SettlementReconcileConfirmEntity settlementReconcileConfirm = Optional.ofNullable(
                settlementReconcileConfirmService.lambdaQuery().eq(SettlementReconcileConfirmEntity::getBillCode, billCode)
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在")));
        // 获取结算单信息
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                        settlementReconcileInfoService.lambdaQuery()
                                .eq(SettlementReconcileInfoEntity::getReconcileCode, settlementReconcileConfirm.getReconcileCode())
                                .one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取结算单信息不存在")));

        // 基础数据+保司对账单
        String policyNo = settlementReconcileConfirm.getPolicyNo();
        result.setDiffType(settlementReconcileConfirm.getDiffType());
        result.setBillCode(billCode);
        result.setPolicyNo(policyNo);
        result.setReconcileType(settlementReconcileInfo.getReconcileType());
        result.setReconcileCode(settlementReconcileInfo.getReconcileCode());
        result.setReconcileStatus(settlementReconcileInfo.getReconcileStatus());
        result.setCompanyName(settlementReconcileInfo.getCompanyName());
        result.setMarkStatus(settlementReconcileConfirm.getMarkStatus());
        result.setProductCode(settlementReconcileConfirm.getProductCode());
        result.setProductName(settlementReconcileConfirm.getProductName());
        result.setReconcileMonth(settlementReconcileInfo.getReconcileMonth());
        stopWatch.stop();
        stopWatch.start("主险信息");
        // 处理主险信息,其实没啥用
        if (StrUtil.isNotBlank(settlementReconcileConfirm.getProductCode())) {
            InsuranceProductProductMapOut reconcileInsuranceProduct =
                    protocolInsuranceProductProductService.findReconcileInsuranceProduct(
                            settlementReconcileConfirm.getProductCode(), settlementReconcileInfo.getReconcileType());
            if (reconcileInsuranceProduct != null) {
                result.setInsuranceProductCode(reconcileInsuranceProduct.getInsuranceProductCode());
                result.setInsuranceProductName(reconcileInsuranceProduct.getInsuranceProductName());
            }
        }
        stopWatch.stop();
        stopWatch.start("科目信息");
        // 对账单科目信息
        List<SettlementReconcileSubjectEntity> settlementReconcileSubjectList =
                settlementReconcileSubjectService.lambdaQuery()
                        .in(SettlementReconcileSubjectEntity::getReconcileCode, settlementReconcileConfirm.getReconcileCode())
                        .list();
        if (CollUtil.isEmpty(settlementReconcileSubjectList)) {
            return result;
        }
        stopWatch.stop();
        stopWatch.start("险种/产品信息");
        // 结算险种信息
        List<SubjectProductListOut> subjectProductList =
                settlementReconcileCompanySubjectProductService.findSubjectProductListByReconcileCode(
                        settlementReconcileConfirm.getReconcileCode());
        if (CollUtil.isEmpty(subjectProductList)) {
            return result;
        }
        result.setSettlementReconcileSubjectList(settlementReconcileSubjectList);

        if (CollUtil.isNotEmpty(subjectProductList)) {
            List<ReconcileInsuranceProductOut> insuranceProductList = new ArrayList<>();
            subjectProductList.forEach(action -> {
                if (CollUtil.isNotEmpty(action.getProductList())) {
                    action.getProductList().forEach(product -> {
                        ReconcileInsuranceProductOut reactionProduct =
                                BeanUtil.copyProperties(product, ReconcileInsuranceProductOut.class);
                        BeanUtil.copyProperties(action, reactionProduct);
                        insuranceProductList.add(reactionProduct);
                    });
                }
            });
            result.setInsuranceProductList(insuranceProductList);
        }
        stopWatch.stop();
        stopWatch.start("保司对账单明细");
        // 保司对账单明细
        List<SettlementReconcileCompanyEntity> reconcileCompanyList = settlementReconcileCompanyService.lambdaQuery()
                .eq(SettlementReconcileCompanyEntity::getBillCode, result.getBillCode()).list();
        result.setReconcileCompanyList(reconcileCompanyList);
        stopWatch.stop();
        stopWatch.start("结算明细");
        // 结算明细
        List<ReconcilePolicyInfoOut> policyInfoList = this.getSettlementPolicyInfoList(result);
        result.setSettlementPolicyInfoList(policyInfoList);
        stopWatch.stop();
        stopWatch.start("获取结算Job数据");
        // 获取结算Job数据
        List<ReconcileJobOut> reconcileJob = this.getReconcileJob(result);
        result.setReconcileJobList(reconcileJob);
        stopWatch.stop();
        stopWatch.start("差异详情");
        //获取差异详情进行复制
        SettlementReconcileDiff settlementReconcileDiff = this.querySettlementReconcileDiffInfo(billCode);
        stopWatch.stop();
        BeanUtil.copyProperties(settlementReconcileDiff, result);
        log.info(stopWatch.prettyPrint());
        return result;
    }

    private List<ReconcileJobOut> getReconcileJob(TraceSourceOut result) {
        List<ReconcileJobOut> reconcileJobList = companyReconcileToReconcileJob(result);

        return reconcileJobList;
    }

    /**
     * 将保司对账单明细转成job
     *
     * @param result
     * @return
     */
    private List<ReconcileJobOut> companyReconcileToReconcileJob(TraceSourceOut result) {
        List<SettlementReconcileCompanyEntity> reconcileCompanyList = result.getReconcileCompanyList();
        if (CollUtil.isEmpty(reconcileCompanyList)) {
            return Collections.emptyList();
        }
        List<String> endorsementNos =
                reconcileCompanyList.stream().map(SettlementReconcileCompanyEntity::getEndorsementNo)
                        .filter(StrUtil::isNotBlank).collect(Collectors.toList());

        // 获取异常的job数据
        List<SettlementEventJobEntity> settlementEventJobList = settlementEventJobService.lambdaQuery()
                .likeRight(SettlementEventJobEntity::getEventBusinessCode, result.getPolicyNo())
                .ne(result.getReconcileType() == 0, SettlementEventJobEntity::getIncomeEventStatus, 1)
                .ne(result.getReconcileType() == 1, SettlementEventJobEntity::getContractIncomeEventStatus, 1)
                .in(!endorsementNos.isEmpty() && endorsementNos.size() == reconcileCompanyList.size(),
                        SettlementEventJobEntity::getEndorsementNo, endorsementNos).list();

        List<ReconcileJobOut> resultList = new ArrayList<>();
        settlementEventJobList.forEach(m -> {
            ReconcileJobOut reconcileJob = new ReconcileJobOut();
            reconcileJob.setPolicyNo(m.getEventBusinessCode());
            if (StrUtil.isNotBlank(m.getEndorsementNo())) {
                reconcileJob.setEndorsementNo(m.getEndorsementNo());
            }
            reconcileJob.setPushEventCode(m.getPushEventCode());
            reconcileJob.setEventDesc(m.getEventDesc());
            if (result.getReconcileType() == 0) {
                reconcileJob.setEventStatus(m.getIncomeEventStatus());
                if (JSONUtil.isJson(m.getIncomeEventMessage())) {
                    reconcileJob.setEventMessage(JSONUtil.parseObj(m.getIncomeEventMessage()).getStr("message"));
                } else {
                    reconcileJob.setEventMessage(m.getIncomeEventMessage());
                }
                reconcileJob.setEventFinishTime(m.getIncomeFinishTime());
            } else if (result.getReconcileType() == 1) {
                reconcileJob.setEventStatus(m.getContractIncomeEventStatus());
                if (JSONUtil.isJson(m.getContractIncomeEventMessage())) {
                    reconcileJob.setEventMessage(
                            JSONUtil.parseObj(m.getContractIncomeEventMessage()).getStr("message"));
                } else {
                    reconcileJob.setEventMessage(m.getContractIncomeEventMessage());
                }
                reconcileJob.setEventFinishTime(m.getContractIncomeFinishTime());
            }
            resultList.add(reconcileJob);
        });
        List<SettlementEventJobEntity> settlementJobList = new ArrayList<>();
        // 在处理一下明细不存在的数据
        if (endorsementNos.size() != reconcileCompanyList.size()) {
            // 获取批单号为空的数据
            settlementJobList.addAll(settlementEventJobService.lambdaQuery()
                    .likeRight(SettlementEventJobEntity::getEventBusinessCode, result.getPolicyNo())
                    .apply("LENGTH(IFNULL(endorsement_no,'')) = 0").list());
        }
        if (!endorsementNos.isEmpty()) {
            settlementJobList.addAll(settlementEventJobService.lambdaQuery()
                    .likeRight(SettlementEventJobEntity::getEventBusinessCode, result.getPolicyNo())
                    .in(SettlementEventJobEntity::getEndorsementNo, endorsementNos).list());
        }
        List<String> endorsementNoList =
                settlementJobList.stream().map(SettlementEventJobEntity::getEndorsementNo).collect(Collectors.toList());
        reconcileCompanyList.stream().filter(f -> {
            if (StrUtil.isBlank(f.getEndorsementNo())) {
                return settlementJobList.stream().noneMatch(fi -> StrUtil.isBlank(fi.getEndorsementNo()));
            } else {
                return !endorsementNoList.contains(f.getEndorsementNo());
            }
        }).forEach(action -> {
            ReconcileJobOut reconcileJob = new ReconcileJobOut();
            reconcileJob.setPolicyNo(action.getPolicyNo());
            reconcileJob.setEndorsementNo(action.getEndorsementNo());
            reconcileJob.setEventMessage("结算中心数据不存在");
            reconcileJob.setEventStatus(0);
            reconcileJob.setEventDesc("数据不存在");
            reconcileJob.setEventFinishTime(new Date());
            resultList.add(reconcileJob);
        });

        return resultList;
    }


    private List<ReconcileJobOut> companyReconcileToReconcileJob(List<SettlementReconcileCompanyEntity> reconcileCompanyList, Integer reconcileType, String policyNo) {
        if (CollUtil.isEmpty(reconcileCompanyList)) {
            return Collections.emptyList();
        }
        List<String> endorsementNos =
                reconcileCompanyList.stream().map(SettlementReconcileCompanyEntity::getEndorsementNo)
                        .filter(StrUtil::isNotBlank).collect(Collectors.toList());

        // 获取异常的job数据
        List<SettlementEventJobEntity> settlementEventJobList = settlementEventJobService.lambdaQuery()
                .likeRight(SettlementEventJobEntity::getEventBusinessCode, policyNo)
                .ne(reconcileType == 0, SettlementEventJobEntity::getIncomeEventStatus, 1)
                .ne(reconcileType == 1, SettlementEventJobEntity::getContractIncomeEventStatus, 1)
                .in(!endorsementNos.isEmpty() && endorsementNos.size() == reconcileCompanyList.size(),
                        SettlementEventJobEntity::getEndorsementNo, endorsementNos).list();

        List<ReconcileJobOut> resultList = new ArrayList<>();
        settlementEventJobList.forEach(m -> {
            ReconcileJobOut reconcileJob = new ReconcileJobOut();
            reconcileJob.setPolicyNo(m.getEventBusinessCode());
            if (StrUtil.isNotBlank(m.getEndorsementNo())) {
                reconcileJob.setEndorsementNo(m.getEndorsementNo());
            }
            reconcileJob.setPushEventCode(m.getPushEventCode());
            reconcileJob.setEventDesc(m.getEventDesc());
            if (reconcileType == 0) {
                reconcileJob.setEventStatus(m.getIncomeEventStatus());
                if (JSONUtil.isJson(m.getIncomeEventMessage())) {
                    reconcileJob.setEventMessage(JSONUtil.parseObj(m.getIncomeEventMessage()).getStr("message"));
                } else {
                    reconcileJob.setEventMessage(m.getIncomeEventMessage());
                }
                reconcileJob.setEventFinishTime(m.getIncomeFinishTime());
            } else if (reconcileType == 1) {
                reconcileJob.setEventStatus(m.getContractIncomeEventStatus());
                if (JSONUtil.isJson(m.getContractIncomeEventMessage())) {
                    reconcileJob.setEventMessage(
                            JSONUtil.parseObj(m.getContractIncomeEventMessage()).getStr("message"));
                } else {
                    reconcileJob.setEventMessage(m.getContractIncomeEventMessage());
                }
                reconcileJob.setEventFinishTime(m.getContractIncomeFinishTime());
            }
            resultList.add(reconcileJob);
        });
        List<SettlementEventJobEntity> settlementJobList = new ArrayList<>();
        // 在处理一下明细不存在的数据
        if (endorsementNos.size() != reconcileCompanyList.size()) {
            // 获取批单号为空的数据
            settlementJobList.addAll(settlementEventJobService.lambdaQuery()
                    .likeRight(SettlementEventJobEntity::getEventBusinessCode, policyNo)
                    .apply("LENGTH(IFNULL(endorsement_no,'')) = 0").list());
        }
        if (!endorsementNos.isEmpty()) {
            settlementJobList.addAll(settlementEventJobService.lambdaQuery()
                    .likeRight(SettlementEventJobEntity::getEventBusinessCode, policyNo)
                    .in(SettlementEventJobEntity::getEndorsementNo, endorsementNos).list());
        }
        List<String> endorsementNoList =
                settlementJobList.stream().map(SettlementEventJobEntity::getEndorsementNo).collect(Collectors.toList());
        reconcileCompanyList.stream().filter(f -> {
            if (StrUtil.isBlank(f.getEndorsementNo())) {
                return settlementJobList.stream().noneMatch(fi -> StrUtil.isBlank(fi.getEndorsementNo()));
            } else {
                return !endorsementNoList.contains(f.getEndorsementNo());
            }
        }).forEach(action -> {
            ReconcileJobOut reconcileJob = new ReconcileJobOut();
            reconcileJob.setPolicyNo(action.getPolicyNo());
            reconcileJob.setEndorsementNo(action.getEndorsementNo());
            reconcileJob.setEventMessage("结算中心数据不存在");
            reconcileJob.setEventStatus(0);
            reconcileJob.setEventDesc("数据不存在");
            reconcileJob.setEventFinishTime(new Date());
            resultList.add(reconcileJob);
        });

        return resultList;
    }


    /**
     * 获取结算明细 不要无需对账的数据
     *
     * @param result 对账编码
     * @return
     */
    private List<ReconcilePolicyInfoOut> getSettlementPolicyInfoList(TraceSourceOut result) {
        StopWatch stopWatch = new StopWatch("获取结算明细");
        stopWatch.start("我司获取到的交易明细信息");
        String postponedMonth = DateUtil.parse(result.getReconcileMonth(), "yyyy年MM月").toString("yyyy-MM");
        //获取险种信息
        List<String> productCodes =
                result.getInsuranceProductList().stream().map(ReconcileInsuranceProductOut::getProductCode).distinct()
                        .collect(Collectors.toList());

        //获取结算科目配置信息
        List<String> reconcileSubjectCodes = result.getSettlementReconcileSubjectList().stream()
                .map(SettlementReconcileSubjectEntity::getReconcileSubjectCode).distinct().collect(Collectors.toList());
        // 服务费
        reconcileSubjectCodes.add(ReconcileSubjectOnlineEnum.SERVICE_FEE.getCode());

        // 我司获取到的交易明细信息
        List<String> settlementCodeList = settlementReconcilePolicyService.lambdaQuery()
                .eq(SettlementReconcilePolicyEntity::getBillCode, result.getBillCode()).list().stream()
                .map(SettlementReconcilePolicyEntity::getSettlementCode).collect(Collectors.toList());
        stopWatch.stop();
        stopWatch.start("根据保单号获取未结算的明细数据");
        //根据保单号获取未结算的明细数据
        List<SettlementPolicyInfoEntity> settlementPolicyList =
                settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                        .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus, CollUtil.newArrayList(1, 3))
                        .eq(SettlementPolicyInfoEntity::getReconcileType, result.getReconcileType())
                        .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode()).and(
                                a -> a.eq(SettlementPolicyInfoEntity::getPolicyNo, result.getPolicyNo()).or()
                                        .eq(SettlementPolicyInfoEntity::getThirdPolicyNo, result.getPolicyNo())).and(
                                a -> a.le(SettlementPolicyInfoEntity::getPostponedMonth, postponedMonth).or()
                                        .isNull(SettlementPolicyInfoEntity::getPostponedMonth)).list();
        stopWatch.stop();
        stopWatch.start("过滤明细数据");
        // 结算对账单明细(不包含冲正数据)
        // 处理明细数据
        List<ReconcilePolicyInfoOut> policyInfoList = settlementPolicyList.stream().map(m -> {
            ReconcilePolicyInfoOut policyInfo = BeanUtil.copyProperties(m, ReconcilePolicyInfoOut.class);
            if (settlementCodeList.contains(m.getSettlementCode())) {
                policyInfo.setIsHave(true);
                policyInfo.setNoHaveMsg("匹配成功");
                return policyInfo;
            }
            //险种不匹配
            if (!productCodes.contains(m.getProductCode())) {
                policyInfo.setIsHave(false);
                policyInfo.setNoHaveMsg("险种不匹配");
                return policyInfo;
            }
            // 科目不匹配
            if (!reconcileSubjectCodes.contains(m.getSettlementSubjectCode())) {
                policyInfo.setIsHave(false);
                policyInfo.setNoHaveMsg("科目不匹配");
                return policyInfo;
            }
            // 时间不匹配
            policyInfo.setIsHave(false);
            policyInfo.setNoHaveMsg("时间不匹配");
            return policyInfo;
        }).collect(Collectors.toList());
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return policyInfoList;
    }

    /**
     * 刷新任务
     *
     * @param input
     */
    @Override
    public void refreshSettlementEventJob(RefreshJobVo input) {
        // 获取确认单信息
        SettlementReconcileConfirmEntity settlementReconcileConfirm = Optional.ofNullable(
                        settlementReconcileConfirmService.lambdaQuery()
                                .eq(SettlementReconcileConfirmEntity::getBillCode, input.getBillCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在")));

        // 获取结算单信息
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, settlementReconcileConfirm.getReconcileCode())
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算清单信息不存在")));

        if (settlementReconcileInfo.getReconcileType() == 0) {
            // 协议
            RefreshSettlementEventJobVo eventJob = new RefreshSettlementEventJobVo();
            eventJob.setPushEventCodeList(input.getPushEventCodeList());
            eventJob.setIncomeEventStatus(0);
            eventJob.setIncomeEventMessage("管理后台触发重新执行任务");
            settlementReconcileBaseService.refreshSettlementEventJob(eventJob);
        } else if (settlementReconcileInfo.getReconcileType() == 1) {
            // 合约
            RefreshSettlementEventJobVo eventJob = new RefreshSettlementEventJobVo();
            eventJob.setPushEventCodeList(input.getPushEventCodeList());
            eventJob.setContractIncomeEventStatus(0);
            eventJob.setContractIncomeEventMessage("管理后台触发重新执行任务");
            settlementReconcileBaseService.refreshSettlementEventJob(eventJob);
        }

    }

    /**
     * 刷新明细
     *
     * @param input
     */
    @Override
    public void handleSettlementPolicyInfo(RefreshPolicyVo input) {
        SysUserEntity user = ShiroUtils.getUserEntity();
        // 获取确认单信息
        SettlementReconcileConfirmEntity settlementReconcileConfirm = Optional.ofNullable(
                        settlementReconcileConfirmService.lambdaQuery()
                                .eq(SettlementReconcileConfirmEntity::getBillCode, input.getBillCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在")));

        // 获取结算单信息
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, settlementReconcileConfirm.getReconcileCode())
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算清单信息不存在")));
        HandleSettlementPolicyInfoVo handleSettlementPolicyInfo = new HandleSettlementPolicyInfoVo();
        handleSettlementPolicyInfo.setIds(input.getIds());
        handleSettlementPolicyInfo.setEventSourceCodeList(input.getEventSourceCodeList());
        handleSettlementPolicyInfo.setReconcileExecuteDesc(input.getReconcileExecuteDesc());
        handleSettlementPolicyInfo.setReconcileExecuteStatus(input.getReconcileExecuteStatus());
        handleSettlementPolicyInfo.setUpdateUser(user.getUsername());
        handleSettlementPolicyInfo.setReconcileType(settlementReconcileInfo.getReconcileType());
        settlementReconcileBaseService.handleSettlementPolicyInfo(handleSettlementPolicyInfo);
    }

    /**
     * 批量刷新结算单保费费率
     *
     * @param reconcileCode 结算单编码
     */
    @Override
    public String batchRefreshRate(String reconcileCode) {
        // 判断如果是已完成对账的就不需要刷新
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                        settlementReconcileInfoService.lambdaQuery()
                                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算清单信息不存在")));
        if (!settlementReconcileInfo.getReconcileStatus().equals(ReconcileStatusEnum.RECONCILE_ING.getStatusCode())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("结算状态不是对账中,不允许操作"));
        }
        // 刷新费率信息
        return settlementReconcileBaseService.batchRefreshRate(reconcileCode);
    }

    /**
     * 设置标记状态
     *
     * @param billCode 编码
     */
    @Override
    public void updateMarkStatus(String billCode) {
        // 获取确认单信息
        SettlementReconcileConfirmEntity settlementReconcileConfirm = Optional.ofNullable(
                settlementReconcileConfirmService.lambdaQuery().eq(SettlementReconcileConfirmEntity::getBillCode, billCode)
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在")));
        if (settlementReconcileConfirm.getMarkStatus() == 0) {
            settlementReconcileConfirm.setMarkStatus(1);
        } else {
            settlementReconcileConfirm.setMarkStatus(0);
        }

        settlementReconcileConfirmService.updateById(settlementReconcileConfirm);
    }

    /**
     * 创建对账单
     *
     * @param vo
     */
    @Override
    public void createReconcile(CreateReconcileVo vo) {
        // 去重以后 数据数量大于1的时候提示错误,如果是1 则提取那个值
        List<SettlementReconcileCompanySubjectEntity> settlementReconcileCompanySubjectList =
                settlementReconcileCompanySubjectService.lambdaQuery()
                        .eq(SettlementReconcileCompanySubjectEntity::getMergeCode, vo.getMergeCode()).list();
        if (CollUtil.isEmpty(settlementReconcileCompanySubjectList)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("合并错误,请检查"));
        }
        long count = settlementReconcileCompanySubjectList.stream()
                .map(SettlementReconcileCompanySubjectEntity::getStatementDate).distinct().count();
        if (count > 1) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("合并数据账单是不在同一天,请检查"));
        }
        //生成对账单
        SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject =
                CollUtil.getFirst(settlementReconcileCompanySubjectList);
        com.mpolicy.settlement.core.common.reconcile.CreateReconcileVo createReconcileVo =
                new com.mpolicy.settlement.core.common.reconcile.CreateReconcileVo();
        createReconcileVo.setReconcileMonth(vo.getReconcileMonth());
        createReconcileVo.setMergeCode(vo.getMergeCode());
        createReconcileVo.setReconcileCompanyCode(settlementReconcileCompanySubject.getReconcileCompanyCode());
        settlementReconcileBaseService.forceCreateReconcile(createReconcileVo);

    }

    /**
     * 设置延期下个月
     *
     * @param input
     */
    @Override
    public void nextPostponedMonth(NextPostponedMonthVo input) {
        // 获取确认单信息
        SettlementReconcileConfirmEntity settlementReconcileConfirm = Optional.ofNullable(
                        settlementReconcileConfirmService.lambdaQuery()
                                .eq(SettlementReconcileConfirmEntity::getBillCode, input.getBillCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在")));

        // 获取结算单信息
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, settlementReconcileConfirm.getReconcileCode())
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算清单信息不存在")));
        //
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyInfoService.lambdaQuery()
                .in(SettlementPolicyInfoEntity::getEventSourceCode, input.getEventSourceCodeList())
                .eq(SettlementPolicyInfoEntity::getReconcileType, settlementReconcileInfo.getReconcileType()).list();
        if (!settlementPolicyInfoList.isEmpty()) {
            settlementPolicyInfoList.forEach(action -> {
                action.setPostponedMonth(input.getPostponedMonth());
            });
            // 更新数据
            settlementPolicyInfoService.updateBatchById(settlementPolicyInfoList);
        }

    }

    /**
     * 获取导出的差异明细数据
     *
     * @param settlementReconcileInfo        结算信息
     * @param settlementReconcileConfirmList 确认单记录
     * @return
     */
    @Override
    public List<SettlementReconcileDiffExcel> exportSettlementReconcileDiffList(
            SettlementReconcileInfoEntity settlementReconcileInfo,
            List<SettlementReconcileConfirmEntity> settlementReconcileConfirmList) {
        if (settlementReconcileConfirmList.isEmpty()) {
            return Collections.emptyList();
        }
        List<SettlementReconcileDiffExcel> resultList = new ArrayList<>();
        List<String> billCodeList =
                settlementReconcileConfirmList.stream().map(SettlementReconcileConfirmEntity::getBillCode)
                        .collect(Collectors.toList());
        // 按照
        Map<String, List<SettlementReconcileDiffExcel>> reconcilePolicyMap =
                settlementReconcilePolicyService.findSettlementReconcileDiffList(billCodeList).stream()
                        .collect(Collectors.groupingBy(SettlementReconcileDiffExcel::getBillCode));
        Map<String, List<SettlementReconcileDiffExcel>> reconcileCompanyMap =
                settlementReconcileCompanyService.lambdaQuery()
                        .in(SettlementReconcileCompanyEntity::getBillCode, billCodeList).list().stream().map(m -> {
                            SettlementReconcileDiffExcel excel = BeanUtil.copyProperties(m, SettlementReconcileDiffExcel.class);
                            excel.setPremium(m.getRealityPremium());
                            excel.setSettlementRate(m.getSettlementRate());
                            excel.setSettlementAmount(m.getCompanyAmount());
                            excel.setSettlementRate(m.getSettlementRate());
                            return excel;
                        }).collect(Collectors.groupingBy(SettlementReconcileDiffExcel::getBillCode));

        settlementReconcileConfirmList.forEach(action -> {
            if (reconcilePolicyMap.containsKey(action.getBillCode())) {
                List<SettlementReconcileDiffExcel> settlementReconcileDiffList =
                        reconcilePolicyMap.get(action.getBillCode());
                settlementReconcileDiffList.forEach(diff -> {
                    diff.setSettlementCompanyName(settlementReconcileInfo.getCompanyName());
                    diff.setReconcileCode(settlementReconcileInfo.getReconcileCode());
                    diff.setReconcileMonth(settlementReconcileInfo.getReconcileMonth());
                    ReconcileDiffTypeEnum reconcileDiffType =
                            ReconcileDiffTypeEnum.getReconcileDiffType(action.getDiffType());
                    diff.setDiffName(reconcileDiffType != null ? reconcileDiffType.getName() : "未知");
                    diff.setDataType("我司对账单");
                    resultList.add(diff);
                });
            }
            if (reconcileCompanyMap.containsKey(action.getBillCode())) {
                List<SettlementReconcileDiffExcel> settlementReconcileDiffList =
                        reconcileCompanyMap.get(action.getBillCode());
                settlementReconcileDiffList.forEach(diff -> {
                    diff.setReconcileCode(settlementReconcileInfo.getReconcileCode());
                    diff.setReconcileMonth(settlementReconcileInfo.getReconcileMonth());
                    diff.setSettlementCompanyName(settlementReconcileInfo.getCompanyName());
                    ReconcileDiffTypeEnum reconcileDiffType =
                            ReconcileDiffTypeEnum.getReconcileDiffType(action.getDiffType());
                    diff.setDiffName(reconcileDiffType != null ? reconcileDiffType.getName() : "未知");
                    diff.setDataType("保司对账单");
                    resultList.add(diff);
                });
            }
        });
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelayReconcile(ReconcileBatchDelayInput delayInput) {

        // 获取确认单信息
        List<SettlementReconcileConfirmEntity> settlementReconcileConfirmList = Optional.ofNullable(
                        settlementReconcileConfirmService.lambdaQuery()
                                .in(SettlementReconcileConfirmEntity::getBillCode, delayInput.getBillCodeList()).list())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在")));

        // 获取结算单信息
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, delayInput.getReconcileCode())
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算清单信息不存在")));

        String postponedMonth = DateUtil.parse(delayInput.getMonth(), "yyyy年MM月").toString("yyyy-MM");

        List<String> policyList = settlementReconcileConfirmList.stream().map(SettlementReconcileConfirmEntity::getPolicyNo).collect(Collectors.toList());
        //根据保单号获取未结算的明细数据
        List<SettlementPolicyInfoEntity> settlementPolicyList =
                settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getReconcileStatus, 0)
                        .in(SettlementPolicyInfoEntity::getReconcileExecuteStatus, CollUtil.newArrayList(1, 3))
                        .eq(SettlementPolicyInfoEntity::getReconcileType, settlementReconcileInfo.getReconcileType())
                        .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode()).and(
                                a -> a.in(SettlementPolicyInfoEntity::getPolicyNo, policyList).or()
                                        .in(SettlementPolicyInfoEntity::getThirdPolicyNo, policyList)).and(
                                a -> a.le(SettlementPolicyInfoEntity::getPostponedMonth, postponedMonth).or()
                                        .isNull(SettlementPolicyInfoEntity::getPostponedMonth)).list();


        if (CollectionUtil.isEmpty(settlementPolicyList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未获取到延期对账数据"));
        }

        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyInfoService.lambdaQuery()
                .in(SettlementPolicyInfoEntity::getEventSourceCode, settlementPolicyList.stream().map(SettlementPolicyInfoEntity::getEventSourceCode).collect(Collectors.toList()))
                .eq(SettlementPolicyInfoEntity::getReconcileType, settlementReconcileInfo.getReconcileType()).list();
        if (!settlementPolicyInfoList.isEmpty()) {
            settlementPolicyInfoList.forEach(action -> {
                action.setPostponedMonth(postponedMonth);
            });
            // 更新数据
            settlementPolicyInfoService.updateBatchById(settlementPolicyInfoList);
        }

    }

    @Override
    public void batchNeedlessReconcile(BatchNeedlessReconcileInput needlessReconcileInput) {

        StringBuilder errorMsg = new StringBuilder();
        for (String x : needlessReconcileInput.getBillCodeList()) {
            try {
                log.info("刷新对账单-{}", x);
                TraceSourceOut traceSourceOut = findTraceSource(x);
                if (Objects.isNull(traceSourceOut)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("确认单信息不存在"));
                }
                if (CollectionUtil.isNotEmpty(traceSourceOut.getSettlementPolicyInfoList())) {
                    RefreshPolicyVo policyVo = new RefreshPolicyVo();
                    policyVo.setBillCode(x);
                    List<Integer> needlessPolicyList = traceSourceOut.getSettlementPolicyInfoList()
                            .stream()
                            .filter(ReconcilePolicyInfoOut::getIsHave)
                            .map(ReconcilePolicyInfoOut::getId)
                            .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(needlessPolicyList)) {
                        continue;
                    }
                    policyVo.setIds(
                            needlessPolicyList
                    );
                    policyVo.setReconcileExecuteDesc(needlessReconcileInput.getReason());
                    policyVo.setReconcileExecuteStatus(2);
                    handleSettlementPolicyInfo(policyVo);
                }
            } catch (Exception e) {
                log.warn("刷新对账单失败-{}", x, e);
                errorMsg.append("对账单-").append(x).append("设置批量无需对账失败,异常原因：").append(e.getMessage()).append(";\n");
            }
        }

        if (StrUtil.isNotBlank(errorMsg)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(errorMsg.toString()));
        }

    }

    @Override
    public void batchRefreshFailEvent(List<String> billCodeList) {

        StringBuilder errorMsg = new StringBuilder();
        if (CollectionUtil.isEmpty(billCodeList)) {
            return;
        }

        for (String billCode : billCodeList) {

            try {
                TraceSourceOut traceSourceOut = findTraceSource(billCode);

                if (Objects.isNull(traceSourceOut) || CollectionUtil.isEmpty(traceSourceOut.getReconcileJobList())) {
                    continue;
                }
                // 获取结算单信息
                SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                        settlementReconcileInfoService.lambdaQuery()
                                .eq(SettlementReconcileInfoEntity::getReconcileCode, traceSourceOut.getReconcileCode())
                                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算清单信息不存在")));


                if (settlementReconcileInfo.getReconcileType() == 0) {
                    // 协议
                    RefreshSettlementEventJobVo eventJob = new RefreshSettlementEventJobVo();
                    eventJob.setPushEventCodeList(traceSourceOut.getReconcileJobList().stream().map(ReconcileJobOut::getPushEventCode).collect(Collectors.toList()));
                    eventJob.setIncomeEventStatus(0);
                    eventJob.setIncomeEventMessage("管理后台触发重新执行任务");
                    settlementReconcileBaseService.refreshSettlementEventJob(eventJob);
                } else if (settlementReconcileInfo.getReconcileType() == 1) {
                    // 合约
                    RefreshSettlementEventJobVo eventJob = new RefreshSettlementEventJobVo();
                    eventJob.setPushEventCodeList(traceSourceOut.getReconcileJobList().stream().map(ReconcileJobOut::getPushEventCode).collect(Collectors.toList()));
                    eventJob.setContractIncomeEventStatus(0);
                    eventJob.setContractIncomeEventMessage("管理后台触发重新执行任务");
                    settlementReconcileBaseService.refreshSettlementEventJob(eventJob);
                }
            } catch (GlobalException e) {
                log.warn("批量刷新对账单状态异常-{}", e.getMsg());
                errorMsg.append(StrUtil.format("对账单{}刷新任务失败;\n", billCode));
            }

            if (StrUtil.isNotBlank(errorMsg)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(errorMsg.toString()));
            }
        }

    }

    /**
     * 设置数据挂起
     *
     * @param input 请求参数
     */
    @Override
    public void hangUp(ReconcileHangUpVo input) {
        log.info("挂起={}", JSONUtil.toJsonStr(input));
        // 分片处理精度差异
        int id = 0;
        int limit = 5000;
        while (true) {
            List<SettlementReconcileConfirmEntity> reconcileConfirmList =
                    settlementReconcileConfirmService.lambdaQuery()
                            .gt(SettlementReconcileConfirmEntity::getId, id)
                            .in(CollUtil.isNotEmpty(input.getBillCodes()), SettlementReconcileConfirmEntity::getBillCode, input.getBillCodes())
                            .eq(SettlementReconcileConfirmEntity::getReconcileCode, input.getReconcileCode())
                            .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0)
                            .eq(SettlementReconcileConfirmEntity::getBillEnable, 1)
                            .eq(SettlementReconcileConfirmEntity::getDiffType, ReconcileDiffTypeEnum.COMPANY_MISSED.getCode())
                            .orderByAsc(SettlementReconcileConfirmEntity::getId)
                            .last("LIMIT " + limit)
                            .list();
            if (reconcileConfirmList.isEmpty()) {
                log.info("需处理ID>{}的仅保费的对账数据为空，无需处理", id);
                break;
            }
            log.info("开始处理ID>{}的仅保费的对账数据{}条", id, reconcileConfirmList.size());
            // 获取最后一条数据的ID
            id = CollUtil.getLast(reconcileConfirmList).getId();


            List<String> billCodes = reconcileConfirmList.stream().map(SettlementReconcileConfirmEntity::getBillCode)
                    .collect(Collectors.toList());
            List<String> settlementCodes = settlementReconcilePolicyService.lambdaQuery()
                    .in(SettlementReconcilePolicyEntity::getBillCode, billCodes).list()
                    .stream().map(SettlementReconcilePolicyEntity::getSettlementCode)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(settlementCodes)) {
                // 设置状态为挂起
                settlementPolicyInfoService.lambdaUpdate()
                        .set(SettlementPolicyInfoEntity::getHangReconcileCode, input.getReconcileCode())
                        .set(SettlementPolicyInfoEntity::getHangStatus, 1)
                        .in(SettlementPolicyInfoEntity::getSettlementCode, settlementCodes)
                        .update();
            }

        }
    }

    /**
     * 取消数据挂起
     */
    @Override
    public void unHangUp(ReconcileHangUpVo input) {
        settlementPolicyInfoService.lambdaUpdate()
                .eq(SettlementPolicyInfoEntity::getHangReconcileCode, input.getReconcileCode())
                .set(SettlementPolicyInfoEntity::getHangStatus, 0)
                .set(SettlementPolicyInfoEntity::getHangReconcileCode, null)
                .update();
    }

    /**
     * 处理仅报送保费数据
     *
     * @param input 请求参数
     */
    @Override
    public void handleOnlySubmitPremium(ReconcileHangUpVo input) {
        // 获取差异类型为仅报送保费的数据,将数据处理更新为已处理
        String key = StrUtil.format("RECONCILE_ONLY_SUBMIT_PREMIUM:{}", input.getReconcileCode());
        boolean lock = RedissLockUtil.tryLock(key, 3600, -1);
        if (!lock) {
            return;
        }
        log.info("开始处理对账单={}批量仅报送保费处理 start", input.getReconcileCode());
        try {
            String username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUsername();
            int id = 0;
            int limit = 5000;
            while (true) {
                // 分片处理精度差异
                List<SettlementReconcileConfirmEntity> reconcileConfirmList =
                        settlementReconcileConfirmService.lambdaQuery().gt(SettlementReconcileConfirmEntity::getId, id)
                                .in(CollUtil.isNotEmpty(input.getBillCodes()), SettlementReconcileConfirmEntity::getBillCode, input.getBillCodes())
                                .eq(SettlementReconcileConfirmEntity::getReconcileCode, input.getReconcileCode())
                                .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0)
                                .eq(SettlementReconcileConfirmEntity::getBillEnable, 1)
                                .eq(SettlementReconcileConfirmEntity::getDiffType, ReconcileDiffTypeEnum.ONLY_SUBMIT_PREMIUM.getCode())
                                .orderByAsc(SettlementReconcileConfirmEntity::getId)
                                .last("LIMIT " + limit)
                                .list();
                if (reconcileConfirmList.isEmpty()) {
                    log.info("需处理ID>{}的仅保费的对账数据为空，无需处理", id);
                    break;
                }
                log.info("开始处理ID>{}的仅保费的对账数据{}条", id, reconcileConfirmList.size());

                reconcileConfirmList.forEach(x -> {
                    x.setSettlementAmount(x.getXiaowhaleAmount());
                    x.setSettlementPremium(x.getPremium());
                    x.setSettlementRate(x.getXiaowhaleSettlementRate());
                    x.setReversalAmount(x.getXiaowhaleAmount().negate());
                    x.setDiffStatus(1);
                    x.setDiffOpeUserName(username);
                    x.setOpeType(0);
                    x.setDiffWhy("仅报送保费处理");
                });
                // 4 批量受理
                settlementReconcileConfirmService.updateBatchById(reconcileConfirmList, limit);
                // 获取最后一条数据的ID
                id = CollUtil.getLast(reconcileConfirmList).getId();
                //休息500毫秒再继续 稍微减轻一下数据库的压力
                ThreadUtils.sleep(500);
            }
            log.info("开始更新差额数据......");

            // 5 处理对账单差额信息
            List<SettlementReconcileConfirmEntity> confirmList = settlementReconcileConfirmService.lambdaQuery()
                    .eq(SettlementReconcileConfirmEntity::getReconcileCode, input.getReconcileCode())
                    .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1).list();

            BigDecimal diffAmount = confirmList.stream().map(SettlementReconcileConfirmEntity::getDiffAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal reversalAmount = confirmList.stream().map(SettlementReconcileConfirmEntity::getReversalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("对账单={},差异金额={},冲正金额={}", input.getReconcileCode(), diffAmount, reversalAmount);
            settlementReconcileInfoService.lambdaUpdate()
                    .eq(SettlementReconcileInfoEntity::getReconcileCode, input.getReconcileCode())
                    .set(SettlementReconcileInfoEntity::getDiffAmount, diffAmount)
                    .set(SettlementReconcileInfoEntity::getReversalAmount, reversalAmount).update();
            log.info("差额数据更新完成.....");
        } catch (GlobalException e) {
            log.warn("对账单={}对账失败,失败原因={}", input.getReconcileCode(), e.getMsg());
        } catch (Exception e) {
            log.warn("处理结算={}数据仅报送保费差异异常", input.getReconcileCode(), e);
        } finally {
            RedissLockUtil.unlock(key);
            log.info("处理对账单={}开始处理批量仅报送保费处理 end", input.getReconcileCode());
        }
    }


    /**
     * 设置对账单为待对账
     *
     * @param input 请求参数
     */
    @Override
    @Transactional
    public void setToBeReconciled(ReconcileHangUpVo input) {
        // 1.清空保司上传的对账单文件
        // 判断对账单状态为未完成对账状态
        SettlementReconcileInfoEntity settlementReconcileInfo =
                settlementReconcileInfoService.getOne(
                        new LambdaQueryWrapper<SettlementReconcileInfoEntity>().eq(
                                SettlementReconcileInfoEntity::getReconcileCode, input.getReconcileCode()));
        if (settlementReconcileInfo.getReconcileStatus() == 3) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单已经完成对账，无法进行重置"));
        }
        // 执行重置
        settlementReconcilePolicyService.remove(
                Wrappers.<SettlementReconcilePolicyEntity>lambdaQuery()
                        .eq(SettlementReconcilePolicyEntity::getReconcileCode, input.getReconcileCode()));
        settlementReconcileCompanyService.remove(
                Wrappers.<SettlementReconcileCompanyEntity>lambdaQuery()
                        .eq(SettlementReconcileCompanyEntity::getReconcileCode, input.getReconcileCode()));
        settlementReconcileConfirmService.remove(
                Wrappers.<SettlementReconcileConfirmEntity>lambdaQuery()
                        .eq(SettlementReconcileConfirmEntity::getReconcileCode, input.getReconcileCode()));
        settlementReconcileDiffBacklogService.remove(
                Wrappers.<SettlementReconcileDiffBacklogEntity>lambdaQuery()
                        .eq(SettlementReconcileDiffBacklogEntity::getReconcileCode, input.getReconcileCode()));
        // 重置为待对账
        settlementReconcileInfo.setCompanyAmount(BigDecimal.ZERO);
        settlementReconcileInfo.setXiaowhaleAmount(BigDecimal.ZERO);
        settlementReconcileInfo.setDiffAmount(BigDecimal.ZERO);
        settlementReconcileInfo.setReversalAmount(BigDecimal.ZERO);
        settlementReconcileInfo.setReconcileStatus(ReconcileStatusEnum.TO_BE_RECONCILE.getStatusCode());
        settlementReconcileInfo.setInvoiceStatus(SettlementInvoiceStatusEnum.CANNOT_BE_INVOICED.getCode());
        settlementReconcileInfoService.updateById(settlementReconcileInfo);
        log.info("重置对账单完成，重置对账单单号={}", input.getReconcileCode());
    }
}
