package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileSummaryInfoService;
import com.mpolicy.manage.modules.settlement.vo.summary.SettlementReconcileSummaryInfo;
import com.mpolicy.manage.modules.settlement.vo.summary.SettlementReconcileSummaryVo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 结算汇总单管理
 *
 * <AUTHOR>
 * @since 2023-05-24 15:59
 */
@RestController
@RequestMapping("/settlement/reconcile/summary")
@Api(tags = "结算汇总单管理")
@Slf4j
public class ReconcileSummaryController extends ReconcileBaseController {


    @Autowired
    private SettlementReconcileSummaryInfoService settlementReconcileSummaryInfoService;

    @ApiOperation(value = "分页查询结算汇总单信息列表", notes = "分页查询结算汇总单信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "reconcileMonth", dataType = "String", value = "结算月份"),
            @ApiImplicitParam(paramType = "query", name = "innerSignatoryCode", dataType = "String", value = "内部签署方编码"),
            @ApiImplicitParam(paramType = "query", name = "externalSignatoryCode", dataType = "String", value = "外部签署方编码"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions("settlement:reconcile:summary:list")
    public Result<PageUtils<SettlementReconcileSummaryInfo>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("获取结算汇总单信息列表，查询条件={}", params);
        PageUtils<SettlementReconcileSummaryInfo> page = settlementReconcileSummaryInfoService.querySettlementSummaryInfoServiceList(params);
        return Result.success(page);
    }
    /**
     * 生成汇总单
     *
     * <AUTHOR>
     * @date 2023/5/26 16:20
     * @param settlementReconcileSummaryVo:
     * @return : com.mpolicy.common.result.Result<java.lang.String>
     */
    @ApiOperation(value = "生成汇总单", notes = "生成汇总单")
    @PostMapping("/save")
    @RequiresPermissions("settlement:reconcile:summary:save")
    public Result<String> save(@RequestBody @ApiParam(name = "settlementReconcileSummaryVo", value = "影像件收集提交参数", required = true) SettlementReconcileSummaryVo settlementReconcileSummaryVo) {

        return Result.success();
    }
    /**
     * 删除汇总单
     *
     * @param summaryCode:
     * @return : com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/5/26 16:21
     */
    @ApiOperation(value = "删除汇总单", notes = "删除汇总单")
    @GetMapping("/delete/{summaryCode}")
    @RequiresPermissions("settlement:reconcile:summary:delete")
    public Result<String> delete(@PathVariable @ApiParam(name = "summaryCode", value = "差异受理编号") String summaryCode) {
        settlementReconcileSummaryInfoService.deleteSettlementSummaryInfo(summaryCode);
        return Result.success();
    }
}
