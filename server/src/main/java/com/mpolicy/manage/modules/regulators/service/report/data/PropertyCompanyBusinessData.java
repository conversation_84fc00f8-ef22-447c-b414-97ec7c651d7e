package com.mpolicy.manage.modules.regulators.service.report.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 代理产险公司业务表报告数据
 *
 * <AUTHOR>
 * @date 2022-01-21 13:26
 */
@Data
@ApiModel(value = "代理产险公司业务表报告数据")
public class PropertyCompanyBusinessData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目", example = "寿险小计")
    private String projectName;

    @ApiModelProperty(value = "行次", example = "1")
    private String lineNumber;

    @ApiModelProperty(value = "父节点行次", example = "1")
    private String parentLineNumber;

    @ApiModelProperty(value = "保费金额-累计", example = "1")
    private String policyPrem;

    @ApiModelProperty(value = "应付保费-累计", example = "1")
    private String payablePrem;

    @ApiModelProperty(value = "代理佣金累计-累计", example = "1")
    private String proxyCommission;



    @ApiModelProperty(value = "自营保费金额", example = "1")
    private String inPolicyPrem;

    @ApiModelProperty(value = "自营代理佣金", example = "1")
    private String inProxyCommission;

    @ApiModelProperty(value = "第三方保费金额", example = "1")
    private String escrowPolicyPrem;

    @ApiModelProperty(value = "第三方代理佣金", example = "1")
    private String escrowProxyCommission;
}
