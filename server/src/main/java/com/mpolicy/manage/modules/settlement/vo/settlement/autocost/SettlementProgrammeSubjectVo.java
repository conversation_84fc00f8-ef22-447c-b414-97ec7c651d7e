package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 10:46 上午
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementProgrammeSubjectVo implements Serializable {

    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 方案名称
     */
    private String programmeName;
    /**
     * 科目编号
     */
    private String subjectCode;
    /**
     * 科目名称
     */
    private String subjectName;
    /**
     * 明细类型：policy 保单维度，product 险种维度，policy_product保单险种维度,custom自定义（不同的类型调用的接口不一样）
     */
    private String itemType;

    /**
     * 科目顺序
     */
    private Integer orderNumber;
}
