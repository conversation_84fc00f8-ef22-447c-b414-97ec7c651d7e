package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementPremChangeLogDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementPremChangeLogEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementPremChangeLogService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("settlementPremChangeLogService")
public class SettlementPremChangeLogServiceImpl extends ServiceImpl<SettlementPremChangeLogDao, SettlementPremChangeLogEntity> implements SettlementPremChangeLogService {


    /**
     * 保存费率变更记录
     *
     * @param pushEventCode 推送唯一标识
     * @param changeType    类型
     * @param businessCode  事件编码
     * @param dataJson      数据体
     */
    @Override
    public void saveSettlementPremChangeLog(String pushEventCode, Integer changeType, String businessCode, String dataJson) {
        SettlementPremChangeLogEntity settlementPremChangeLog = new SettlementPremChangeLogEntity();
        settlementPremChangeLog.setPushEventCode(pushEventCode);
        settlementPremChangeLog.setChangeType(changeType);
        settlementPremChangeLog.setBusinessCode(businessCode);
        settlementPremChangeLog.setDataJson(dataJson);
        save(settlementPremChangeLog);
    }
}
