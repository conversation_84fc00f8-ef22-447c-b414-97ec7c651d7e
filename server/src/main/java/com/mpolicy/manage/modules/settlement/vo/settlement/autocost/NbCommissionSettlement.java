package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/2/4 14:47
 * @Version 1.0
 */
@Data
public class NbCommissionSettlement {

    /**
     * 结佣月份
     */
    @ApiModelProperty(value = "结佣月份")
    private String costSettlementCycle;

    /**
     * 结佣机构编码  当全部汇总是为空
     */
    @ApiModelProperty(value = "结佣机构编码")
    private String settlementInstitution;

    /**
     * 结佣机构编码  当全部汇总是为空
     */
    @ApiModelProperty(value = "结佣机构编码名称")
    private String settlementInstitutionName;

    @ApiModelProperty(value = "金额")
    private BigDecimal grantAmount;

    @ApiModelProperty(value = "汇总金额")
    private BigDecimal summaryAmount;

    @ApiModelProperty(value = "结算状态")
    private String settlementStatus;

    @ApiModelProperty(value = "结算人")
    private String operator;

    @ApiModelProperty(value = "确认结算时间")
    private LocalDateTime confirmTime;

    class SettlementInstitution {

    }

}
