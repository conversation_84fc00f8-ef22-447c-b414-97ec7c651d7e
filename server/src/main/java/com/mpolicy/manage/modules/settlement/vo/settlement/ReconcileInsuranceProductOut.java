package com.mpolicy.manage.modules.settlement.vo.settlement;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ReconcileInsuranceProductOut implements Serializable {

    /**
     * 协议保司产品编码
     */
    private String insuranceProductCode;
    /**
     * 协议保司产品名称
     */
    private String insuranceProductName ;
    /**
     * 小鲸险种编码
     */
    private String productCode;
    /**
     * 小鲸险种名称
     */
    private String productName;
    /**
     * 投保计划
     */
    private String productPlan;

    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    /**
     * 保司简称
     */
    private String companyShortName;

    private Integer reconcileType;
    /**
     * 校验续投年期 0:否 1:是
     */
    private Integer isCheckRenewalPeriod;
}
