package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReconcileCompanySubjectListOut implements Serializable {
    private static final long serialVersionUID = -3461000202165521060L;

    private Integer id;
    /**
     * 科目编码
     */
    private String subjectRuleCode;
    /**
     * 产品所属分公司名称
     */
    private String companyName;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
    /**
     * 结算保司对账编码
     */
    private String reconcileCompanyCode;
    /**
     * 结算科目编码
     */
    private String reconcileSubjectCode;
    /**
     * 结算科目中文
     */
    private String reconcileSubjectName;
    /**
     * 账单日
     */
    private Integer statementDate;
    /**
     * 账单日月末最后一天0:不是 1:是
     */
    private Integer statementDateMonthEnd;
    /**
     * 适用范围0:通用 1:指定险种
     */
    private Integer subjectScope;
    /**
     * 适用范围描述
     */
    private String subjectScopeDesc;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 合并编码
     */
    private String mergeCode;
    /**
     * 状态0:禁用 1:启用
     */
    private Integer subjectRuleStatus;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createUser;
}
