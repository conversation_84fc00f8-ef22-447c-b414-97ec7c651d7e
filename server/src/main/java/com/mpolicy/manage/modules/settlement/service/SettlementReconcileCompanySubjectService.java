package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectEntity;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanySubjectInfo;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanySubjectListOut;
import com.mpolicy.manage.modules.settlement.vo.SaveReconcileCompanySubject;
import com.mpolicy.manage.modules.settlement.vo.UpdateReconcileCompanySubject;

import java.util.List;

/**
 * 结算保司配置对应规则科目表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:45:57
 */
public interface SettlementReconcileCompanySubjectService extends IService<SettlementReconcileCompanySubjectEntity> {

    /**
     * 获取结算保司配置对应规则科目表列表
     *
     * @param reconcileCompanyCode 保司结算编码
     * @param mergeCode            合并编码
     * @return
     */
    List<ReconcileCompanySubjectListOut> findReconcileCompanySubjectList(String reconcileCompanyCode, Integer reconcileType, String mergeCode);

    /**
     * 新增结算保司科目信息
     *
     * @param params
     */
    void saveReconcileCompanySubject(SaveReconcileCompanySubject params);

    /**
     * 修改结算保司科目信息
     *
     * @param params
     */
    void updateReconcileCompanySubject(UpdateReconcileCompanySubject params);

    /**
     * 合并结算保司科目信息
     *
     * @param idList
     */
    void mergeSubject(List<Integer> idList);

    /**
     * 拆分结算保司科目信息
     *
     * @param idList
     */
    void splitSubject(List<Integer> idList);

    /**
     * 获取指定保司产品列表
     *
     * @param reconcileCompanyCode 结算保司配置编码
     * @return
     */
    List<SelectOut> findSelectInsuranceProductList(String reconcileCompanyCode, Integer reconcileType);

    /**
     * 获取详情信息
     *
     * @param id
     * @return
     */
    ReconcileCompanySubjectInfo findReconcileCompanySubjectInfoById(Integer id);

    /**
     * 删除结算保司科目信息
     *
     * @param id
     */
    void deleteReconcileCompanySubject(Integer id);
}

