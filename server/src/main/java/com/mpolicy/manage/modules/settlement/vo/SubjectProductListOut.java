package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductProductEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SubjectProductListOut implements Serializable {
    private static final long serialVersionUID = 7508400834185191290L;
    /**
     * 协议险种编码
     */
    private String insuranceProductCode;
    /**
     * 协议险种名称
     */
    private String insuranceProductName;
    /**
     * 险种集合
     */
    private List<EpProtocolInsuranceProductProductEntity> productList;
}
