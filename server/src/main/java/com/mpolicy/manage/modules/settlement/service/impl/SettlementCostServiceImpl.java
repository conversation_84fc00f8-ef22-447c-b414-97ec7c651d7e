package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.common.SettlementAutoCostBaseService;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostAutoInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoRecordEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeRecordEntity;
import com.mpolicy.manage.modules.settlement.enums.ConfirmStatusEnum;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.SettlementCostAutoInfoService;
import com.mpolicy.manage.modules.settlement.service.SettlementCostProgrammeRecordService;
import com.mpolicy.manage.modules.settlement.service.SettlementCostService;
import com.mpolicy.manage.modules.settlement.vo.confirm.SettlementReconcileConfirmInfo;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.*;
import com.mpolicy.settlement.core.common.autocost.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 10:39 上午
 * @Version 1.0
 */
@Service("settlementCostService")
@Slf4j
public class SettlementCostServiceImpl implements SettlementCostService {

    @Autowired
    private SettlementCostAutoInfoService settlementCostAutoInfoService;

    @Autowired
    private SettlementCostAutoInfoDao settlementCostAutoInfoDao;
    @Autowired
    private SettlementCostProgrammeRecordService settlementCostProgrammeRecordService;

    @Autowired
    private SettlementAutoCostBaseService settlementAutoCostBaseService;


    public Map<String, String> queryAllSummaryDynamicSubject(SettlementCostAutoInput input) {
        List<SettlementDynamicSubjectVO> dynamicSubjectVOList = settlementCostAutoInfoDao.queryAllSummaryDynamicSubject(input);
        if (CollectionUtils.isEmpty(dynamicSubjectVOList)) {
            return Collections.emptyMap();
        }

        return dynamicSubjectVOList.stream().collect(
                Collectors.toMap(SettlementDynamicSubjectVO::getSubjectCode, SettlementDynamicSubjectVO::getSubjectName, (t1, t2) -> t2)
        );
    }

    public Map<String, String> queryAllSummarySubject(SettlementCostAutoInput input) {
        List<SettlementDynamicSubjectVO> dynamicSubjectVOList = settlementCostAutoInfoDao.queryAllSummarySubject(input);
        if (CollectionUtils.isEmpty(dynamicSubjectVOList)) {
            return Collections.emptyMap();
        }

        return dynamicSubjectVOList.stream().collect(
                Collectors.toMap(SettlementDynamicSubjectVO::getSubjectCode, SettlementDynamicSubjectVO::getSubjectName, (t1, t2) -> t2)
        );
    }


    @Override
    public PageUtils<SettlementCostAutoInfoVo> pageSettlementCostAutoInfo(SettlementCostAutoInput input) {
        //按照结算机构展示，查询所有的动态科目
        List<SettlementDynamicSubjectVO> dynamicSubjectVOList = queryDynamic(input);

        IPage<SettlementCostAutoInfoEntity> pageList = settlementCostAutoInfoService.queryPage(input);

        PageUtils<SettlementCostAutoInfoVo> result = pageEntityToVo(pageList);
        List<SettlementCostAutoInfoVo> pageResultList = result.getList();
        setSettlementInfoDynamicList(pageResultList, dynamicSubjectVOList);

        return result;

    }

    private void setSettlementInfoDynamicList(List<SettlementCostAutoInfoVo> pageResultList, List<SettlementDynamicSubjectVO> dynamicSubjectVOList) {
        Map<PageSettlementInstitutionKeyVO, SettlementCostAutoInfoVo> institutionKeyMap = pageResultList.stream().collect(Collectors.toMap(
                PageSettlementInstitutionKeyVO::from,
                Function.identity()
        ));

        Set<String> monthList = pageResultList.stream().map(SettlementCostAutoInfoVo::getSettlementMonth).collect(Collectors.toSet());
        Set<String> sendObjectCodeList = pageResultList.stream().map(SettlementCostAutoInfoVo::getSendObjectCode).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(monthList) && CollectionUtils.isNotEmpty(sendObjectCodeList)) {
            List<SettlementCostAutoRecordEntity> autoRecordEntityList = settlementCostAutoInfoDao.queryInstitutionSub(
                    monthList
                    , sendObjectCodeList
            );

            Map<PageSettlementInstitutionKeyVO, List<SettlementDynamicSubjectVO>> institutionKeyVOMap = autoRecordEntityList.stream().collect(
                    Collectors.groupingBy(
                            PageSettlementInstitutionKeyVO::fromAutoRecord
                            , Collectors.mapping(SettlementDynamicSubjectVO::fromAutoRecord, Collectors.toList())
                    )
            );


            institutionKeyMap.forEach((key, value) -> value.setDynamicSubjectList(
                    dynamicSubjectVOList.stream().map(SettlementDynamicSubjectVO::copy).peek(
                            x -> {
                                SettlementDynamicSubjectVO existedDynamicSubjectVO = Optional.ofNullable(institutionKeyVOMap.get(key))
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .filter(y -> Objects.equals(y.getSubjectCode(), x.getSubjectCode()))
                                        .findFirst()
                                        .orElse(null);
                                if (Objects.nonNull(existedDynamicSubjectVO)) {
                                    x.setAmount(existedDynamicSubjectVO.getAmount());
                                }
                            }
                    ).collect(Collectors.toList())
            ));
        }

    }


    @Override
    public PageUtils<SettlementCostAutoInfoVo> pageSettlementCostAutoInfoBySummary(SettlementCostAutoInput input) {
        //按照结算机构展示，查询所有的动态科目
        List<SettlementDynamicSubjectVO> dynamicSubjectVOList = queryDynamic(input);
        IPage<SettlementCostAutoInfoEntity> pageList = settlementCostAutoInfoService.pageSettlementCostAutoInfoBySummary(input);
        PageUtils<SettlementCostAutoInfoVo> result = pageEntityToVo(pageList);

        List<SettlementCostAutoInfoVo> pageResultList = result.getList();
        handleSummaryDynamicSubject(pageResultList, dynamicSubjectVOList);


        return result;
    }

    @Override
    public List<SettlementDetailAllSubjectVO> querySettlementDynamicSubjectVOS(SettlementCostAutoInput input) {

        List<SettlementDetailAllSubjectVO> dynamicSubjectVOList = settlementCostAutoInfoDao.listSettlementDetailAllSubjectVO(input);
        return Stream.concat(
                Arrays.stream(CostSubjectEnum.values())
                        .filter(y -> !Objects.equals(y, CostSubjectEnum.DYNAMIC_SUBJECT))
                        .map(
                        x -> {
                            SettlementDetailAllSubjectVO settlementDynamicSubjectVO = new SettlementDetailAllSubjectVO();
                            settlementDynamicSubjectVO.setParentSubjectCode(x.getCode());
                            settlementDynamicSubjectVO.setSubjectCode(x.getCode());
                            settlementDynamicSubjectVO.setSubjectName(x.getName());
                            return settlementDynamicSubjectVO;
                        }
                )
                , Optional.ofNullable(dynamicSubjectVOList).orElse(Collections.emptyList()).stream()
        ).collect(Collectors.toList());
    }

    public List<SettlementDynamicSubjectVO> queryDynamic(SettlementCostAutoInput input) {
        return queryAllSummaryDynamicSubject(input).entrySet().stream().map(
                x -> {
                    SettlementDynamicSubjectVO settlementDynamicSubjectVO = new SettlementDynamicSubjectVO();
                    settlementDynamicSubjectVO.setSubjectCode(x.getKey());
                    settlementDynamicSubjectVO.setSubjectName(x.getValue());
                    return settlementDynamicSubjectVO;
                }
        ).collect(Collectors.toList());
    }

    private void handleSummaryDynamicSubject(List<SettlementCostAutoInfoVo> pageResultList, List<SettlementDynamicSubjectVO> dynamicSubjectVOList) {
        Map<PageSettlementSummaryKeyVO, SettlementCostAutoInfoVo> institutionKeyMap = pageResultList.stream()
                .collect(
                        Collectors.toMap(
                                PageSettlementSummaryKeyVO::from,
                                Function.identity(),
                                (t1, t2) -> t1
                        )
                );

        List<SettlementCostAutoRecordEntity> autoRecordEntityList = Collections.emptyList();
        Set<String> settlementMonthList = pageResultList.stream().map(SettlementCostAutoInfoVo::getSettlementMonth).collect(Collectors.toSet());
        Set<String> sendObjectList = pageResultList.stream().map(SettlementCostAutoInfoVo::getSendObjectCode).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(settlementMonthList) && CollectionUtils.isNotEmpty(sendObjectList)) {
            autoRecordEntityList = settlementCostAutoInfoDao.queryInstitutionSub(settlementMonthList, sendObjectList);
        }


        if (CollectionUtils.isNotEmpty(autoRecordEntityList)) {
            Map<PageSettlementSummaryKeyVO, List<SettlementDynamicSubjectVO>> institutionKeyVOMap = autoRecordEntityList.stream().collect(
                    Collectors.groupingBy(
                            PageSettlementSummaryKeyVO::fromAutoRecord
                            , Collectors.mapping(SettlementDynamicSubjectVO::fromAutoRecord, Collectors.toList())
                    )
            );
            institutionKeyMap.forEach((key, value) -> value.setDynamicSubjectList(
                    dynamicSubjectVOList.stream().map(SettlementDynamicSubjectVO::copy).peek(
                            x -> {
                                List<SettlementDynamicSubjectVO> existedDynamicSubjectVO = Optional.ofNullable(institutionKeyVOMap.get(key))
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .filter(y -> Objects.equals(y.getSubjectCode(), x.getSubjectCode()))
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(existedDynamicSubjectVO)) {
                                    x.setAmount(existedDynamicSubjectVO.stream().map(SettlementDynamicSubjectVO::getAmount)
                                                        .reduce(BigDecimal::add).orElse(null)
                                    );
                                }
                            }
                    ).collect(Collectors.toList())
            ));
        }
    }

    private final Integer EXPORT_BATCH_SIZE = 1000;

    @Override
    public void pageSettlementCostAutoInfoBySummaryExport(SettlementCostAutoInput input, HttpServletResponse response) {

        //按照结算机构展示，查询所有的动态科目
        List<SettlementDynamicSubjectVO> dynamicSubjectVOList = queryDynamic(input);
        IPage<SettlementCostAutoInfoEntity> pageList = settlementCostAutoInfoService.pageSettlementCostAutoInfoBySummary(input);
        int total = Integer.parseInt(String.valueOf(pageList.getTotal()));

        int currentSize = 0;
        ExcelWriter writer = ExcelUtil.getWriter(true);

        while (currentSize < total) {

            boolean isHeadKey = currentSize == 0;

            List<SettlementCostAutoInfoEntity> costAutoInfoEntityList = settlementCostAutoInfoService.listSettlementCostAutoInfoBySummary(input, currentSize, EXPORT_BATCH_SIZE);
            currentSize = Math.min(currentSize + EXPORT_BATCH_SIZE, total);

            if (CollectionUtil.isNotEmpty(costAutoInfoEntityList)) {
                List<SettlementCostAutoInfoVo> costAutoInfoVoList = costAutoInfoEntityList.stream().map(
                        x -> {
                            SettlementCostAutoInfoVo vo = new SettlementCostAutoInfoVo();
                            BeanUtil.copyProperties(x, vo);
                            return vo;
                        }
                ).collect(Collectors.toList());
                handleSummaryDynamicSubject(costAutoInfoVoList, dynamicSubjectVOList);
                List<List<String>> dataList = export(costAutoInfoVoList, Sets.newHashSet("settlementInstitutionName"), isHeadKey);
                utilExport(dataList, response, !(currentSize < total), URLUtil.encode("结算汇总" + System.currentTimeMillis() + ".xlsx"), isHeadKey, writer);
            }

        }


    }


    public static void utilExport(
            List<List<String>> dataList
            , HttpServletResponse response
            , boolean closeable
            , String fileName
            , boolean isHeadKey
            , ExcelWriter writer
    ) {

        OutputStream out = null;
        try{
            out = response.getOutputStream();
        } catch(IOException e) {
            log.info("获取返回流失败-{}", e.toString());
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("导出失败"));
        }

        writer.write(dataList, isHeadKey);
        if (closeable) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader(
                    "Content-Disposition",
                    StrUtil.format("attachment;filename={}", fileName, StandardCharsets.UTF_8)
            );
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        }

    }

    @Override
    public void pageSettlementCostAutoInfoExport(SettlementCostAutoInput input, HttpServletResponse response) {
        //按照结算机构展示，查询所有的动态科目
        List<SettlementDynamicSubjectVO> dynamicSubjectVOList = queryDynamic(input);

        IPage<SettlementCostAutoInfoEntity> pageList = settlementCostAutoInfoService.queryPage(input);
        int total = Integer.parseInt(String.valueOf(pageList.getTotal()));

        int currentSize = 0;
        ExcelWriter writer = ExcelUtil.getWriter(true);

        while (currentSize < total) {

            boolean isHeadKey = currentSize == 0;

            List<SettlementCostAutoInfoEntity> costAutoInfoEntityList = settlementCostAutoInfoService.listExportPage(input, currentSize, EXPORT_BATCH_SIZE);
            currentSize = Math.min(currentSize + EXPORT_BATCH_SIZE, total);

            if (CollectionUtil.isNotEmpty(costAutoInfoEntityList)) {
                List<SettlementCostAutoInfoVo> costAutoInfoVoList = costAutoInfoEntityList.stream().map(
                        x -> {
                            SettlementCostAutoInfoVo vo = new SettlementCostAutoInfoVo();
                            BeanUtil.copyProperties(x, vo);
                            return vo;
                        }
                ).collect(Collectors.toList());
                handleSummaryDynamicSubject(costAutoInfoVoList, dynamicSubjectVOList);
                List<List<String>> dataList = export(costAutoInfoVoList, Collections.emptySet(), isHeadKey);
                utilExport(dataList, response, !(currentSize < total), URLUtil.encode("结算机构" + System.currentTimeMillis() + ".xlsx"), isHeadKey, writer);
            }

        }

    }

    private PageUtils<SettlementCostAutoInfoVo> pageEntityToVo(IPage<SettlementCostAutoInfoEntity> pageList) {
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<SettlementCostAutoInfoVo> list = pageList.getRecords().stream().map(a -> {
                SettlementCostAutoInfoVo bean = new SettlementCostAutoInfoVo();
                BeanUtils.copyProperties(a, bean);

                return bean;
            }).collect(Collectors.toList());
            return new PageUtils<>(list, (int) pageList.getTotal(), (int) pageList.getPages(), (int) pageList.getCurrent());
        }
        return new PageUtils<>(Collections.emptyList(), (int) pageList.getTotal(), (int) pageList.getPages(), (int) pageList.getCurrent());
    }


    static Set<String> lashMonthSet = Sets.newHashSet("shortPromotion", "pcoAllowance", "supervisorPerformance", "pcoPerformance", "directorIncentive");
    static Set<String> monthBeforeLastMonthSet = Sets.newHashSet("longPromotion");
    static Set<String> monthBeforeBeforeLastMonthSet = Sets.newHashSet("longReissuePromotion", "pcoReissuePerformance", "supervisorReissuePerformance");

    public static List<List<String>> export(List<SettlementCostAutoInfoVo> exportList, Set<String> excludeSet, boolean isHeadKey) {

        if (CollectionUtils.isEmpty(exportList)) {
            return Collections.emptyList();
        }

        DateTime date = DateUtil.parse(exportList.get(0).getSettlementMonth(), "yyyyMM");

        DateTime lastMonth = DateUtil.offsetMonth(date, -1);
        DateTime monthBeforeLastMonth = DateUtil.offsetMonth(lastMonth, -1);
        DateTime monthBeforeBeforeLastMonth = DateUtil.offsetMonth(monthBeforeLastMonth, -1);

        List<Field> fieldList = Arrays.stream(ReflectUtil.getFields(SettlementCostAutoInfoVo.class))
                .filter(
                        x -> Arrays.stream(x.getAnnotations())
                                .anyMatch(y -> y.annotationType().isAssignableFrom(ExcelProperty.class))
                )
                .filter(x -> !excludeSet.contains(x.getName()))
                .collect(Collectors.toList());
        List<List<String>> export = Lists.newArrayList();
        List<String> list = (List<String>) CollUtil.addAll(
                fieldList.stream().map(
                                f -> {

                                    if (lashMonthSet.contains(f.getName())) {
                                        return DateUtil.month(lastMonth) + 1 + "月" + f.getAnnotation(ExcelProperty.class).value()[0];
                                    }
                                    if (monthBeforeLastMonthSet.contains(f.getName())) {
                                        return DateUtil.month(monthBeforeLastMonth) + 1 + "月" + f.getAnnotation(ExcelProperty.class).value()[0];
                                    }

                                    if (monthBeforeBeforeLastMonthSet.contains(f.getName())) {
                                        return DateUtil.month(monthBeforeBeforeLastMonth) + 1 + "月" + f.getAnnotation(ExcelProperty.class).value()[0];
                                    }

                                    return f.getAnnotation(ExcelProperty.class).value()[0];
                                }

                        )
                        .collect(Collectors.toList()),
                Optional.ofNullable(exportList.get(0).getDynamicSubjectList())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(SettlementDynamicSubjectVO::getSubjectName)
                        .collect(Collectors.toList())
        );

        export = exportList.stream().map(
                x ->
                        (List<String>) CollUtil.addAll(
                                fieldList.stream().map(
                                                f -> ReflectUtil.getFieldValue(x, f)).map(
                                                v -> {
                                                    if (Objects.isNull(v)) {
                                                        return "";
                                                    } else if (v.getClass().isAssignableFrom(BigDecimal.class)) {
                                                        return ((BigDecimal) v).toPlainString();
                                                    } else {
                                                        return String.valueOf(v);
                                                    }
                                                }
                                        )
                                        .collect(Collectors.toList()),
                                Optional.ofNullable(x.getDynamicSubjectList())
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .map(SettlementDynamicSubjectVO::getAmount)
                                        .map(y -> Objects.isNull(y) ? null : y.toPlainString())
                                        .collect(Collectors.toList())
                        )

        ).collect(Collectors.toList());
        if (isHeadKey) {
            export.add(0, list);

        }

        return export;
    }

    @Override
    public void confirmSettlement(String programmeCode, String settlementCycle, String confirmUser) {
        settlementAutoCostBaseService.confirmProgrammeSettlement(programmeCode,settlementCycle,confirmUser,true);
    }

    public Boolean validateConfirmed(String programmeCode, String settlementCycle){
        SettlementCostProgrammeRecordEntity recordEntity = settlementCostProgrammeRecordService.lambdaQuery().eq(SettlementCostProgrammeRecordEntity::getCostSettlementCycle,settlementCycle)
                .eq(StringUtils.isNotBlank(programmeCode),SettlementCostProgrammeRecordEntity::getProgrammeCode,programmeCode)
                .one();
        return recordEntity!= null&& Objects.equals(recordEntity.getConfirmStatus(), ConfirmStatusEnum.CONFIRMED.getCode())?Boolean.TRUE:Boolean.FALSE;
    }

    @Override
    public PageUtils<SettlementConfirmPageVO> pageSettlementConfirmPage(SettlementConfirmInput input) {
        IPage<SettlementCostProgrammeRecordEntity> pageList = settlementCostProgrammeRecordService.page(
                new Page<>(input.getPage(), input.getLimit())
                , new LambdaQueryWrapper<>(new SettlementCostProgrammeRecordEntity())
                        .eq(StrUtil.isNotBlank(input.getProgrammeRecordStatus()), SettlementCostProgrammeRecordEntity::getConfirmStatus, input.getProgrammeRecordStatus())
                        .eq(StrUtil.isNotBlank(input.getCostSettlementCycle()), SettlementCostProgrammeRecordEntity::getCostSettlementCycle, input.getCostSettlementCycle())
                        .orderBy(true, false, SettlementCostProgrammeRecordEntity::getCostSettlementCycle)
        );

        if (CollectionUtil.isNotEmpty(pageList.getRecords())) {

            List<String> cycleList = pageList.getRecords().stream().map(SettlementCostProgrammeRecordEntity::getCostSettlementCycle).collect(Collectors.toList());

            List<SettlementConfirmInstitutionPageVO> institutionPageVOList = settlementCostAutoInfoDao.listConfirmInstitutionPage(cycleList);
            Map<String, List<SettlementConfirmInstitutionPageVO>> cycleMap = Optional.ofNullable(institutionPageVOList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.groupingBy(SettlementConfirmInstitutionPageVO::getCostSettlementCycle));

            List<SettlementConfirmPageVO> list = pageList.getRecords().stream().map(
                    x -> {
                        SettlementConfirmPageVO item = new SettlementConfirmPageVO();
                        item.setCostSettlementCycle(x.getCostSettlementCycle());
                        item.setConfirmUser(x.getConfirmUser());
                        item.setList(cycleMap.get(x.getCostSettlementCycle()));
                        item.setConfirmStatus(x.getConfirmStatus());
                        item.setConfirmTime(x.getConfirmTime());
                        return item;
                    }
            ).collect(Collectors.toList());

            return new PageUtils<>(list, (int) pageList.getTotal(), (int) pageList.getPages(), (int) pageList.getCurrent());

        }

        return new PageUtils<>(Collections.emptyList(), (int) pageList.getTotal(), (int) pageList.getPages(), (int) pageList.getCurrent());

    }

    /**
     * 装载结算文件导入申请
     *
     * @param apply   装载结算文件导入申请信息
     * @return 申请编码
     * <AUTHOR>
     * @since 2024/9/29
     */
    public String uploadHistoryLongCostApply(CostUploadApplyInput apply) {

        return settlementAutoCostBaseService.uploadHistoryLongCostApply(apply,true);

    }

    /**
     * 装载结算文件导入数据信息
     *
     * @param applyCode 申请编码
     * @param userName  操作员
     * @return 操作结果
     * <AUTHOR>
     * @since 2024/9/29
     */
    public String loadCost(String applyCode, String userName) {
        return settlementAutoCostBaseService.loadCost(applyCode,userName,true);
    }

    public List<SettlementDynamicSubjectDefinition> listSettlementDynamicSubject(String subjectName) {
        return settlementAutoCostBaseService.listSettlementDynamicSubject(subjectName,true);
    }
    public String saveSettlementDynamicSubjectDefinition(SettlementDynamicSubjectDefinition definition) {
        return settlementAutoCostBaseService.saveSettlementDynamicSubjectDefinition(definition,true);
    }

    public PageUtils<SettlementDirectorNotMatchRetDto> pageNotMatchLog(SettlementDirectorNotMatchLogQuery query){
        return settlementAutoCostBaseService.pageNotMatchLog(query,true);
    }

    public void exportSettlementPcoInfo(HttpServletResponse response,String settlementCycle)throws IOException {
        org.springframework.util.StopWatch stopWatch = new StopWatch();
        // 设置开始
        log.info("结算生服对接人导出生成 .....开始");
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode(settlementCycle+"生服对接人列表.xlsx"));
            com.alibaba.excel.ExcelWriter writer = new com.alibaba.excel.ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, SettlementPcoInfoExportVo.class);
            sheet.setSheetName("sheet1");

            List<SettlementPcoInfoExpertDto> sourceData = settlementAutoCostBaseService.listSettlementPcoInfo(settlementCycle,true);
            List<SettlementPcoInfoExportVo> expertDtos = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(sourceData)) {
                for(int i=0;i<sourceData.size();i++){
                    SettlementPcoInfoExpertDto o = sourceData.get(i);
                    SettlementPcoInfoExportVo vo = new SettlementPcoInfoExportVo();
                    BeanUtils.copyProperties(o,vo);
                    vo.setNum(i+1);
                    expertDtos.add(vo);
                }

            } else {
                log.info("结算生服对接人导出生成，退出构建，执行导出");
            }
            writer.write(expertDtos, sheet);
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("结算生服对接人导出生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


}
