package com.mpolicy.manage.modules.settlement.vo.settlement;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "结算明细差异清单", description = "结算明细差异清单")
public class SettlementReconcileDiffExcel  extends BaseRowModel implements Serializable {


    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    @ExcelProperty(value = "对账唯一单号")
    private String reconcileCode;

    /**
     * 对账单名称
     */
    @ApiModelProperty(value = "结算保司", example = "众安2023020203")
    @ExcelProperty(value = "结算保司")
    private String settlementCompanyName;

    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023年06期")
    @ExcelProperty(value = "对账月度")
    private String reconcileMonth;

    /**
     * 汇总对账纪录编号
     */
    @ApiModelProperty(value = "对账员明细纪录编号", example = "RC2023052302020200")
    @ExcelProperty(value = "对账员明细纪录编号")
    private String billCode;

    /**
     * 差异类型名称
     */
    @ApiModelProperty(value = "差异类型名称", example = "保费不一致")
    @ExcelProperty(value = "差异类型名称")
    private String diffName;

    /**
     * 投保时间
     */
    @ExcelProperty(value = "投保时间")
    private Date applicantTime;
    /**
     * 交单时间
     */
    @ExcelProperty(value = "交单时间")
    private Date orderTime;
    /**
     * 承保时间
     */
    @ExcelProperty(value = "承保时间")
    private Date approvedTime;
    /**
     * 生效时间
     */
    @ExcelProperty(value = "生效时间")
    private Date enforceTime;

    @ExcelProperty(value = "投保人姓名")
    private String applicantName;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "泰康保险有限公司")
    @ExcelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 差异保单编号
     */
    @ApiModelProperty(value = "差异保单编号", example = "TK2020202")
    @ExcelProperty(value = "差异保单编号")
    private String policyNo;

    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号", example = "TK2020202")
    @ExcelProperty(value = "批单号")
    private String endorsementNo;

    /**
     * 保全生效时间
     */
    @ApiModelProperty(value = "保全生效时间", example = "2023-05-23 11:11:11")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "保全生效时间")
    private Date preservationEffectTime;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码", example = "TK2020202")
    private String reconcileSubjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "对账科目名称", example = "首期年佣金")
    @ExcelProperty(value = "对账科目名称")
    private String reconcileSubjectName;

    /**
     * 协议产品编码
     */
    @ApiModelProperty(value = "协议产品编码", example = "XT2020")
    @ExcelProperty(value = "协议产品编码")
    private String protocolProductCode;

    /**
     * 协议产品名称
     */
    @ApiModelProperty(value = "协议产品名称", example = "泰康百万医疗重疾2020款")
    @ExcelProperty(value = "协议产品名称")
    private String protocolProductName;

    @ExcelProperty(value = "计划名称")
    private String planName;

    /**
     * 小鲸手续费金额
     */
    @ApiModelProperty(value = "小鲸保费金额", example = "1222.12")
    @ExcelProperty(value = "保费金额")
    private BigDecimal premium;

    @ApiModelProperty(value = "小鲸手续费比例", example = "1222.12")
    @ExcelProperty(value = "结算比例")
    private String settlementRate;

    @ApiModelProperty(value = "小鲸手续费金额", example = "1222.12")
    @ExcelProperty(value = "结算金额")
    private BigDecimal settlementAmount;
    /**
     * 结算比例
     */
    @ExcelProperty(value = "结算方式")
    private String settlementRateMethod;
    /**
     * 结算金额
     */
    @ExcelProperty(value = "税率")
    private String settlementRateTax;

    @ExcelProperty(value = "数据类型")
    private String dataType;

    @ExcelProperty(value = "明细标识")
    private String settlementCode;
}
