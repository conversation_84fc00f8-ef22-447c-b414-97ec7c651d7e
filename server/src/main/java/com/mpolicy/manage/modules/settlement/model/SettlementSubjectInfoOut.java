package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementSubjectInfoOut implements Serializable {
    private static final long serialVersionUID = -9100981929368765501L;

    @ApiModelProperty(value = "科目ID")
    private Integer id;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    @ApiModelProperty(value = "科目名称")
    private String subjectCode;

    /**
     * 承保结束时间
     */
    private Integer approvedEndDay;
    /**
     * 回执结束时间
     */
    private Integer receiptEndDay;
    /**
     * 回访结束时间
     */
    private Integer revisitEndDay;
    /**
     * 承保结束时间
     */
    private String approvedEndDayType;
    /**
     * 回执结束时间
     */
    private String receiptEndDayType;
    /**
     * 回访结束时间
     */
    private String revisitEndDayType;

    private long revision;
}
