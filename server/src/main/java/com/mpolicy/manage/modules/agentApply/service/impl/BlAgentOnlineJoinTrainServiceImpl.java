package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinTrainDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinTrainEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineJoinTrainService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("blAgentOnlineJoinTrainService")
public class BlAgentOnlineJoinTrainServiceImpl extends ServiceImpl<BlAgentOnlineJoinTrainDao, BlAgentOnlineJoinTrainEntity> implements BlAgentOnlineJoinTrainService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<BlAgentOnlineJoinTrainEntity> page = this.page(
                new Query<BlAgentOnlineJoinTrainEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
