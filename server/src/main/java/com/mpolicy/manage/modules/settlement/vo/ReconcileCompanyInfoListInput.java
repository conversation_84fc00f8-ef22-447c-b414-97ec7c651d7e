package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ReconcileCompanyInfoListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 6681695665557777715L;

    private String companyCode;

    private String reconcileCompanyName;

    private String reconcileCompanyCode;
    private String subjectReconcileCompanyCode;
    /**
     * 结算科目编码
     */
    private String subjectRuleCode;

    private String innerSignatoryCode;

    private String externalSignatoryCode;

    private String settlementCompanyCode;
}
