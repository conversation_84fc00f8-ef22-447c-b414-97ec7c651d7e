package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApplyEntity;

/**
 * 科目范围(动态科目)数据申请记录
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
public interface SettlementCostSubjectDataDynamicApplyService extends IService<SettlementCostSubjectDataDynamicApplyEntity> {

}

