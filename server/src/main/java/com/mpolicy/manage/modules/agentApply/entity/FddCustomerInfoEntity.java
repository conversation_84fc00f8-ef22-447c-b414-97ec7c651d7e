package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 法大大客户签约信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-27 11:20:06
 */
@TableName("fdd_customer_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FddCustomerInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 所属模块(1-代理人线上入职 2-投保告知书)
     */
    private Integer model;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 客户实名手机号
     */
    private String mobile;
    /**
     * 客户编号
     */
    private String loanCustId;
    /**
     * 客户身份证
     */
    private String idNo;
    /**
     * 客户实名状态(0-未认证 1-认证中 2-未授权 3-授权中 4-已授权 5-非本人实名)
     */
    private Integer status;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
