package com.mpolicy.manage.modules.settlement.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.SettlementMethodEnum;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.policy.common.ep.policy.OptUserInfoVo;
import com.mpolicy.policy.common.ep.policy.common.PolicyFileImportApplyVo;
import generator.domain.SettlementPolicyProductFeePrem;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【settlement_policy_product_fee_prem】的数据库操作Service
 * @createDate 2025-02-27 14:16:04
 */
public interface SettlementPolicyProductFeePremService extends IService<SettlementPolicyProductFeePrem> {


    PageUtils<SettlementPolicyProductTaxPremListOut> pageList(SettlementPolicyProductTaxPremListInput input);

    void updatePolicyTaxPremList(List<SettlementPolicyProductPremEntity> list, SysUserEntity userEntity);
}
