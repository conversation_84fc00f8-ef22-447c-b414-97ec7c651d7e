package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agentApply.dao.FddCustomerInfoDao;
import com.mpolicy.manage.modules.agentApply.entity.FddCustomerInfoEntity;
import com.mpolicy.manage.modules.agentApply.service.FddCustomerInfoService;
import org.springframework.stereotype.Service;


@Service("fddCustomerInfoService")
public class FddCustomerInfoServiceImpl extends ServiceImpl<FddCustomerInfoDao, FddCustomerInfoEntity> implements FddCustomerInfoService {

}
