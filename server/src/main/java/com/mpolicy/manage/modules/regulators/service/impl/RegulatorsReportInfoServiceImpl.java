package com.mpolicy.manage.modules.regulators.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.regulators.common.RegulatorsConstant;
import com.mpolicy.manage.modules.regulators.dao.RegulatorsReportInfoDao;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportStatusEnum;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportInfoService;

import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportList;
import com.mpolicy.web.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service("regulatorsReportInfoService")
public class RegulatorsReportInfoServiceImpl extends ServiceImpl<RegulatorsReportInfoDao, RegulatorsReportInfoEntity> implements RegulatorsReportInfoService {

    @Override
    public PageUtils<RegulatorsReportList> queryRegulatorsReportList(Map<String, Object> params) {

        int year = 0;
        int month = 0;
        //  报告月度 2022-01
        String reportMonth = (String) params.get("reportMonth");
        if (StringUtils.isNotBlank(reportMonth)) {
            year = Integer.parseInt(reportMonth.split("-")[0]);
            month = Integer.parseInt(reportMonth.split("-")[1]);
        }
        //  报送状态0未报送 1报送
        Integer reportStatus = RequestUtils.objectValueToInteger(params, "reportStatus");
        //  最后上传时间
        String uploadDate = (String) params.get("uploadDate");

        // 分页查询
        IPage<RegulatorsReportInfoEntity> page = this.page(
                new Query<RegulatorsReportInfoEntity>().getPage(params),
                new LambdaQueryWrapper<RegulatorsReportInfoEntity>()
                        .eq(reportStatus != null, RegulatorsReportInfoEntity::getReportStatus, reportStatus)
                        .eq(year > 0, RegulatorsReportInfoEntity::getRegulatorsYear, year)
                        .eq(month > 0, RegulatorsReportInfoEntity::getRegulatorsMonth, month)
                        .apply(StringUtils.isNotBlank(uploadDate), "date_format(update_time,'%Y-%m-%d') = {0}", uploadDate)
                        .orderByDesc(RegulatorsReportInfoEntity::getId)
        );


        // 构建返回的vo list
        List<RegulatorsReportList> regulatorsReportList = new ArrayList<>();

        page.getRecords().forEach(x -> {
            RegulatorsReportList bean = new RegulatorsReportList();
            BeanUtils.copyProperties(x, bean);
            // 是否完整上传
            bean.setReportStatusCode(x.getReportStatus());
            bean.setReportStatus(RegulatorsReportStatusEnum.deCode(x.getReportStatus()).getName());
            bean.setUploadCompleteStatus(x.getUploadCompleteStatus() == 1 ? "是" : "否");
            bean.setUpdateTime(DateUtil.formatDate(x.getUpdateTime()));
            regulatorsReportList.add(bean);
        });

        // 重新构建分页返回对象
        return new PageUtils(regulatorsReportList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }
}
