package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单发票关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */
@TableName("settlement_reconcile_invoice_map")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInvoiceMapEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 发票编码
     */
    private String invoiceCode;
    /**
     * 对账单编码
     */
    private String reconcileCode;

    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;
    /**
     * 开票中金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 可开票金额
     */
    private BigDecimal invoicableAmount;

    /**
     * 申请开票金额
     */
    private BigDecimal applyAmount;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
