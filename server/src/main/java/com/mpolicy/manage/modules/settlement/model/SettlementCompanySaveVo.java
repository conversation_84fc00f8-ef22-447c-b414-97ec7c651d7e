package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SettlementCompanySaveVo implements Serializable {
    private static final long serialVersionUID = 6632649858695906320L;

    @ApiModelProperty(value = "生效状态")
    private Integer effectiveStatus;

    @ApiModelProperty(value = "账单日")
    private Integer billDay;

    @ApiModelProperty(value = "是否为最末最后一天")
    private Integer isMonthEnd;

    @ApiModelProperty(value = "保司编码")
    private String companyCode;

    @ApiModelProperty(value = "外部签署方类型")
    private String externalSignatoryType;

    @ApiModelProperty(value = "内部签署方分支")
    private String innerSignatoryBranch;

    @ApiModelProperty(value = "外部签署方名称")
    private String externalSignatoryName;

    @ApiModelProperty(value = "结算科目集合")
    private List<Integer> subjectIdList;

}
