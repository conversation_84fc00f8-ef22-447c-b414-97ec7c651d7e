package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementPremChangeLogEntity;

/**
 * <AUTHOR>
 */
public interface SettlementPremChangeLogService extends IService<SettlementPremChangeLogEntity> {


    /**
     * 保存变更记录
     *
     * @param pushEventCode 推送唯一标识
     * @param premType      类型
     * @param businessCode  事件编码
     * @param data          数据体
     */
    void saveSettlementPremChangeLog(String pushEventCode, Integer premType, String businessCode, String data);
}
