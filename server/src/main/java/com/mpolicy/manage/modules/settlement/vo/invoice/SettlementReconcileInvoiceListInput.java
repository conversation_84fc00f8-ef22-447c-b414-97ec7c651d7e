package com.mpolicy.manage.modules.settlement.vo.invoice;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */

@Data
public class SettlementReconcileInvoiceListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开票类型 0:协议 1:合约
     */
    private Integer reconcileType;
    /**
     * 发票编码
     */
    private String invoiceCode;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 对账单号
     */
    private String reconcileCode;
    /**
     * 申请状态
     */
    private Integer applyStatus;

    /**
     * 开票方式 1:提前开票 0:正常开票
     */
    private Integer invoiceMode;

    /**
     * 是否发送保司 0:没有发 1:已发送
     */
    private Integer pushCompanyStatus;

    /**
     * 快递状态 0:无需 1:未发 2:已发
     */
    private Integer expressStatus;
    /**
     * 邮件发送状态0:无需发送 1:未发送 2:已发送
     */
    private Integer sendMailboxStatus;

    /**
     * 红冲状态 0未红 1:已红冲 2:红冲处理中 3:红冲失败
     */
    @ApiModelProperty("红冲状态 0未红 1:已红冲 2:红冲处理中 3:红冲失败")
    private Integer redFlushStatus;

    /**
     * 开票组织编码
     */
    private String invoicingOrgCode;
    /**
     * 申请开始时间
     */
    private String applyBeginTime;
    /**
     * 申请结束时间
     */
    private String applyEndTime;

}
