package com.mpolicy.manage.modules.settlement.model;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementCompanyVo extends BasePage implements Serializable {
    private static final long serialVersionUID = -595678367140847440L;

    @ApiModelProperty(value = "保司编码")
    private String companyCode;
}
