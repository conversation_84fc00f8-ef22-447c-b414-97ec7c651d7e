package com.mpolicy.manage.modules.settlement.vo.settlement;

import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileSubjectEntity;
import com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TraceSourceOut extends SettlementReconcileDiff implements Serializable {

    /**
     * 结算编码
     */
    private String reconcileCode;
    /**
     * 结算状态
     */
    private Integer reconcileStatus;
    /**
     * 结算保司
     */
    private String companyName;
    /**
     * 编码
     */
    private String billCode;
    private Integer reconcileType;

    /**
     * 步骤序号
     */
    private Integer stepIndex;

    /**
     * 差异类型
     */
    private String diffType;

    /**
     * 保单号5
     */
    private String policyNo;
    private String productCode;
    private String productName;
    /**
     * 协议保司产品编码
     */
    private String insuranceProductCode;
    /**
     * 协议保司产品名称
     */
    private String insuranceProductName ;
    /**
     * 标记状态0:未标记 1:已标记
     */
    private Integer markStatus;
    /**
     * 保司对账单
     */
    private List<SettlementReconcileCompanyEntity> reconcileCompanyList;
    /**
     * 结算任务信息
     */
    private List<ReconcileJobOut> reconcileJobList;
    /**
     * 对账单科目信息
     */
    private List<SettlementReconcileSubjectEntity> settlementReconcileSubjectList;
    /**
     * 协议产品信息
     */
    private List<ReconcileInsuranceProductOut> insuranceProductList;

    /**
     * 清单明细
     */
    private List<ReconcilePolicyInfoOut> settlementPolicyInfoList;

}
