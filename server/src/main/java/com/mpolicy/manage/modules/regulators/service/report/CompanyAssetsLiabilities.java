package com.mpolicy.manage.modules.regulators.service.report;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.regulators.common.RegulatorsKeys;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.report.data.CompanyAssetsLiabilitiesData;
import com.mpolicy.manage.modules.regulators.service.report.listener.CompanyAssetsLiabilitiesListener;
import com.mpolicy.manage.modules.regulators.utils.RegulatorsExcelUtil;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportData;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 资产负债表
 *
 * <AUTHOR>
 * @date 2022-01-21 14:46
 */
@Slf4j
@Service
public class CompanyAssetsLiabilities extends AbsRegulatorsReport<RegulatorsReportData> {


    @Override
    public RegulatorsReportTypeEnum getRegulatorsReportType() {
        return RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES;
    }


    @Override
    public boolean checkRegulatorsReportFile(String fileCode, Boolean insertDataRedis) {
        // 检测fileCode
        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("责任定义文件不存在")));


        return checkRegulatorsReportFile(document, insertDataRedis);
    }

    @Override
    public boolean checkRegulatorsReportFile(SysDocumentEntity document, Boolean insertDataRedis) {

        // 1 生成到本地
        String localFilePath = tempPath.concat(document.getFileName());
        storageService.downloadFileToLocal(document.getFilePath(), localFilePath);

        // 2 解析xls
        List<CompanyAssetsLiabilitiesData> reportData;
        try {
            reportData = RegulatorsExcelUtil.readExcelByList(localFilePath, 1, 2, new CompanyAssetsLiabilitiesListener());
        }catch (Exception e){
            log.warn("资产负债表模板解析异常：",e);
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("资产负债表模板错误，请核查"));
        }

        // 3 todo 校验数据

        // 4 写入redis
        if (insertDataRedis) {
            JSONObject data = new JSONObject();
            data.put("data", JSON.toJSON(reportData));
            data.put("reportType", RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getCode());
            data.put("reportTypeName", RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getName());
            redisService.set(RegulatorsKeys.REGULATORS_REPORT, StrUtil.format("{}-{}", RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getCode(), document.getFileCode()), data.toJSONString());
        }
        return true;
    }

    /**
     * <p>
     * 资产负债表
     * 1 根据机构编码 + 报备文件类型 + 年度 + 月度，判断是否有纪录，有的话也先删除 : 这块代码也可以考虑集成到父类中
     * 2 暂时就只写入机构报备db
     * </p>
     */
    @Override
    void generatorReport(RegulatorsReportInfoEntity reportInfo, RegulatorsReportData regulatorsReportData, SysDocumentEntity document) {

        // 根据报文中的机构报备编号获取是否存在纪录
        RegulatorsReportOrgEntity bean = Optional.ofNullable(
                regulatorsReportOrgService.lambdaQuery()
                        .eq(RegulatorsReportOrgEntity::getOrgRegulatorsNo, regulatorsReportData.getOrgRegulatorsNo())
                        .one()
        ).orElse(new RegulatorsReportOrgEntity());

        BeanUtils.copyProperties(regulatorsReportData, bean);
        // 赋值机构编码
        bean.setOrgRegulatorsNo(regulatorsReportData.getOrgRegulatorsNo());
        // 文件path
        bean.setReportFilePath(document.getFilePath());
        // 文件名称
        bean.setReportFileName(document.getFileName());
        // 报告文件类型
        bean.setReportType(regulatorsReportData.getReportType().getCode());
        bean.setReportName(regulatorsReportData.getReportType().getName());
        bean.setReportStatus(0);
        // 报告内容
        String reportData = redisService.get(RegulatorsKeys.REGULATORS_REPORT, StrUtil.format("{}-{}", RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getCode(), document.getFileCode()), String.class);
        if(StringUtils.isBlank(reportData)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取缓存报告内容缺失"));
        }
        bean.setReportData(reportData);
        bean.setReportTitle(StrUtil.format("{}-{}", reportInfo.getRegulatorsName(), RegulatorsReportTypeEnum.COMPANY_ASSETS_LIABILITIES.getName()));
        regulatorsReportOrgService.saveOrUpdate(bean);
        log.info("机构报备[资产负债表] 操作完成");
    }
}