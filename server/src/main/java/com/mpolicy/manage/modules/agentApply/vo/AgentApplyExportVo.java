package com.mpolicy.manage.modules.agentApply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.*;

/**
 * @Description 
 * @return 代理人申请信息导出
 * @Date 2022/11/28 18:32
 * <AUTHOR>
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentApplyExportVo extends BaseRowModel {
    private static final long serialVersionUID = 1L;


    @ExcelProperty(value = "被增员人姓名")
    private String agentName;

    @ExcelProperty(value = "被增员人身份证号码")
    private String idCard;

    @ExcelProperty(value = "被增员人类型")
    private String position;

    @ExcelProperty(value = "增员人名称")
    private String recruitName;

    @ExcelProperty(value = "增员人编码")
    private String businessCode;

    private Integer status;
    @ExcelProperty(value = "状态")
    private String statusDesc;
}
