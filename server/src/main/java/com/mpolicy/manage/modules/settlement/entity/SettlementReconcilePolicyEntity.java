package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司结算对账单关联数据
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_policy")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcilePolicyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 保单明细对账业务编号
	 */
	private String policyBillCode;
	/**
	 * 对账保单业务汇总编号
	 */
	private String billCode;
	/**
	 * 对账唯一单号
	 */
	private String reconcileCode;
	/**
	 * 对账单名称
	 */
	private String reconcileName;
	/**
	 * 对账月度
	 */
	private String reconcileMonth;

	/**
     * 单据明细唯一编号
     */
	private String settlementCode;
	/**
	 * 保单明细生成方式1系统自动、2小鲸对账单
	 */
	private Integer reconcileGenerateType;
	/**
	 * 保险公司编码
	 */
	private String companyCode;
	/**
	 * 保险公司名称
	 */
	private String companyName;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 保单产品类型;个团车财
	 */
	private String policyProductType;
	/**
	 * 记账业务事件编码;新单、续期、保全、费率调整、其他
	 */
	private String settlementEventCode;
	/**
	 * 记账业务事件名称;新契约、犹豫期退保
	 */
	private String settlementEventDesc;
	/**
	 * 科目编码
	 */
	private String reconcileSubjectCode;
	/**
	 * 科目名称
	 */
	private String reconcileSubjectName;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 协议产品编码
	 */
	private String protocolProductCode;
	/**
	 * 协议产品名称
	 */
	private String protocolProductName;
	/**
	 * 计划编码
	 */
	private String planCode;
	/**
	 * 费率编码
	 */
	private String premCode;
	/**
	 * 计划名称
	 */
	private String planName;
	/**
	 * 长短险标记 0短险1长险
	 */
	private Integer longShortFlag;
	/**
	 * 险种大类
	 */
	private String productGroup;

	/**
	 * 二级分类编码
	 */
	private String level2Code;
	/**
	 * 三级分类编码
	 */
	private String level3Code;

	/**
	 * 批单号
	 */
	private String endorsementNo;

	/**
	 * 保全生效时间
	 */
	private Date preservationEffectTime;
	/**
	 * 被保人年龄
	 */
	@ApiModelProperty(value = "被保人保单生效时年龄")
	private Integer insuredPolicyAge;
	/**
	 * 是否主险
	 */
	private Integer mainInsurance;
	/**
	 * 续期年期
	 */
	private Integer renewalYear;
	/**
	 * 续期期次
	 */
	private Integer renewalPeriod;
	/**
	 * 保额
	 */
	private BigDecimal coverage;
	/**
	 * 保额单位;保额单位，0：元，1：份，2：元/天
	 */
	private Integer coverageUnit;
	/**
	 * 保额单位名称
	 */
	private String coverageUnitName;
	/**
	 * 保障期间类型
	 */
	private String insuredPeriodType;
	/**
	 * 保障时长
	 */
	private Integer insuredPeriod;
	/**
	 * 缴费方式
	 */
	private String periodType;
	/**
	 * 缴费期间类型
	 */
	private String paymentPeriodType;
	/**
	 * 缴费时长
	 */
	private Integer paymentPeriod;
	/**
	 * 保费
	 */
	private BigDecimal premium;
	/**
	 * 险种总保费
	 */
	private BigDecimal productPremiumTotal;
	/**
	 * 结算费率
	 */
	private String settlementRate;
	/**
	 * 结算金额
	 */
	private BigDecimal settlementAmount;
	/**
	 * 对账状态
	 */
	private Integer reconcileStatus;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
