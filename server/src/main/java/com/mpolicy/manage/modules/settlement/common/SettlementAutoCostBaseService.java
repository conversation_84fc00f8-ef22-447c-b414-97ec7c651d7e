package com.mpolicy.manage.modules.settlement.common;

import cn.hutool.core.date.StopWatch;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.CostUploadApplyInput;
import com.mpolicy.settlement.core.client.SettlementAutoCostClient;
import com.mpolicy.settlement.core.common.autocost.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 结算系统服务
 *
 * <AUTHOR>
 * @since 2023-05-25 00:33
 */
@Component
@Slf4j
public class SettlementAutoCostBaseService {

    @Autowired
    private SettlementAutoCostClient settlementAutoCostClient;

    /**
     * 动态科目申请
     *
     * @param apply   动态科目文件申请信息
     * @param throwEx 出现异常是否抛出异常
     * @return 动态科目申请编码
     * <AUTHOR>
     * @since 2024/1/29
     */
    public String uploadSubjectDataDynamicApply(SubjectDataDynamicApply apply, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementAutoCostClient.uploadSubjectDataDynamicApply(apply);
        if (!result.isSuccess()) {
            log.warn("动态科目申请操作失败，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("动态科目申请操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 装载动态科目数据信息
     *
     * @param applyCode 动态科目申请编码
     * @param userName  操作员
     * @param throwEx   出现异常是否抛出异常
     * @return 操作结果
     * <AUTHOR>
     * @since 2024/1/29
     */
    @Async
    public String loadDynamicSubject(String applyCode, String userName, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementAutoCostClient.loadDynamicSubject(applyCode, userName);
        if (!result.isSuccess()) {
            log.warn("装载动态科目数据信息，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("装载动态科目数据信息...耗时：{}", millis);
        return result.getData();
    }

    public String confirmProgrammeSettlement(String programmeCode,String settlementCycle, String confirmUser, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementAutoCostClient.confirmCostProgramme(programmeCode, settlementCycle,confirmUser);
        if (!result.isSuccess()) {
            log.warn("确认结算，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("确认结算...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 装载结算文件导入申请
     *
     * @param apply   装载结算文件导入申请信息
     * @param throwEx 出现异常是否抛出异常
     * @return 申请编码
     * <AUTHOR>
     * @since 2024/9/29
     */
    public String uploadHistoryLongCostApply(CostUploadApplyInput apply, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        SettlementHistoryCostUploadApply upload = new SettlementHistoryCostUploadApply();
        BeanUtils.copyProperties(apply,upload);
        Result<String> result = settlementAutoCostClient.uploadHistoryLongCostApply(upload);
        if (!result.isSuccess()) {
            log.warn("结算文件导入申请操作失败，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("结算文件导入申请申请操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 装载结算文件导入数据信息
     *
     * @param applyCode 申请编码
     * @param userName  操作员
     * @param throwEx   出现异常是否抛出异常
     * @return 操作结果
     * <AUTHOR>
     * @since 2024/9/29
     */
    public String loadCost(String applyCode, String userName, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementAutoCostClient.loadCost(applyCode, userName);
        if (!result.isSuccess()) {
            log.warn("装载结算文件导入数据信息，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("装载结算文件导入数据信息...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 根据动态科目名称模糊查询科目定义信息
     * @param subjectName
     * @param throwEx
     * @return
     */
    public List<SettlementDynamicSubjectDefinition> listSettlementDynamicSubject(String subjectName, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<List<SettlementDynamicSubjectDefinition>> result = settlementAutoCostClient.listSettlementDynamicSubject(subjectName);
        if (!result.isSuccess()) {
            log.warn("根据动态科目名称模糊查询科目定义信息，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return Collections.emptyList();
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("根据动态科目名称模糊查询科目定义信息...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 保存动态科目定义信息
     * @param definition
     * @param throwEx
     * @return
     */
    public String saveSettlementDynamicSubjectDefinition(SettlementDynamicSubjectDefinition definition, boolean throwEx) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementAutoCostClient.saveSettlementDynamicSubjectDefinition(definition);
        if (!result.isSuccess()) {
            log.warn("保存动态科目定义信息，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("保存动态科目定义信息...耗时：{}", millis);
        return result.getData();
    }

    public PageUtils<SettlementCostImportRecord> pageCostImportRecord(SettlementCostImportRecordQuery query, boolean throwEx){
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<PageUtils<SettlementCostImportRecord>> result = settlementAutoCostClient.pageSettlementCostImportRecord(query);
        if (!result.isSuccess()) {
            log.warn("文件导入记录查询，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("文件导入记录查询...耗时：{}", millis);
        return result.getData();
    }

    public PageUtils<SettlementCostImportOperationRecord> pageCostImportOperationRecord(SettlementCostImportOperationRecordQuery query, boolean throwEx){
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<PageUtils<SettlementCostImportOperationRecord>> result = settlementAutoCostClient.pageSettlementCostImportOperationRecord(query);
        if (!result.isSuccess()) {
            log.warn("文件导入操作记录查询，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("文件导入操作记录查询...耗时：{}", millis);
        return result.getData();
    }

    public String deleteImportRecord(SettlementCostImportRecordDelete recordDelete, boolean throwEx){
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementAutoCostClient.deleteImportRecord(recordDelete);
        if (!result.isSuccess()) {
            log.warn("结算文件记录删除，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("结算文件记录删除...耗时：{}", millis);
        return result.getData();
    }

    public List<SettlementCostImportErrorData> listImportErrorDataByApplyCode(String applyCode, boolean throwEx){
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<List<SettlementCostImportErrorData>> result = settlementAutoCostClient.listImportErrorDataByApplyCode(applyCode);
        if (!result.isSuccess()) {
            log.warn("根据申请编码查询导入错误记录，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("结算文件记录删除...耗时：{}", millis);
        return result.getData();
    }

    public PageUtils<SettlementDirectorNotMatchRetDto> pageNotMatchLog(SettlementDirectorNotMatchLogQuery query, boolean throwEx){
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<PageUtils<SettlementDirectorNotMatchRetDto>> result = settlementAutoCostClient.pageNotMatchLog(query);
        if (!result.isSuccess()) {
            log.warn("主任寻人规则不匹配日志记录，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("主任寻人规则不匹配日志记录...耗时：{}", millis);
        return result.getData();
    }

    public List<SettlementPcoInfoExpertDto> listSettlementPcoInfo(String settlementCycle, boolean throwEx){
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<List<SettlementPcoInfoExpertDto>> result = settlementAutoCostClient.listSettlementPcoInfo(settlementCycle);
        if (!result.isSuccess()) {
            log.warn("结算生服对接人列表，信息={} ", result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("结算生服对接人列表...耗时：{}", millis);
        return result.getData();
    }
}