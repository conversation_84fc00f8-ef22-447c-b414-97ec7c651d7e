package com.mpolicy.manage.modules.settlement.vo.invoice;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 对账单发票信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */


@Data
public class SettlementReconcileInvoiceUpdateInput extends SettlementReconcileInvoiceSaveInput implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 对账单类型
	 */
	@NotBlank(message = "发票编码不能为空")
	private String invoiceCode;

}
