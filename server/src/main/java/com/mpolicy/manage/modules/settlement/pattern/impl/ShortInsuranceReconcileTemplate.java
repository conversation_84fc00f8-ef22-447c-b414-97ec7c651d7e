package com.mpolicy.manage.modules.settlement.pattern.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyProductEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementSubjectEntity;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateHandler;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanyProductService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInfoService;
import com.mpolicy.manage.modules.settlement.service.SettlementSubjectService;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 短险解析模版
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ShortInsuranceReconcileTemplate extends ReconcileTemplateHandler {

    private final SettlementReconcileInfoService settlementReconcileInfoService;
    private final SettlementReconcileCompanyProductService settlementReconcileCompanyProductService;
    private final SettlementSubjectService settlementSubjectService;
    private final List<String> TEMPLATE_TITLE = CollUtil.newArrayList("保单号", "批单号", "保司对账单产品名称", "新单/续期", "续期期次", "应税/免税", "实收保费");

    /**
     * 读取文件:
     * 注意读取文件的模版一定要正确 标题可以追加 不可修改
     * 横版:     保单号	批单号	险种名称	保单状态	新单/续期	续期期次	应税/免税	实收保费	费用类型	手续费比率	手续费	费用类型	手续费比率	手续费	费用类型	手续费比率	手续费	费用类型	手续费比率	手续费	费用类型	手续费比率	手续费
     * 竖版/财险: 保单号	批单号	险种名称	保单状态	新单/续期	续期期次	应税/免税	实收保费	费用类型	手续费比率	手续费
     *
     * @param inputStream   文件
     * @param reconcileCode 对账单编码
     * @return
     */
    @Override
    public List<ReconcileRuleFileTemplate> readFile(InputStream inputStream, String reconcileCode) {
        //  判断对账单是否存在
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode)
                        .one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单不存在")));
        List<SettlementSubjectEntity> settlementSubjectList = settlementSubjectService.list();
        Map<String, String> settlementSubjectMapName = settlementSubjectList.stream().collect(Collectors.toMap(SettlementSubjectEntity::getSubjectName, SettlementSubjectEntity::getSubjectCode));
        List<ReconcileRuleFileTemplate> resultList = new ArrayList<>();
        try {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> read = reader.read();
            if (CollUtil.isEmpty(read)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件内容为空"));
            }

            //第一行数据不做处理 标题
            List<String> titleList = (List<String>) (List) read.get(0);
            if (titleList.size() < TEMPLATE_TITLE.size()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件模版异常"));
            }
            //判断模版前面是不是正确的,如果不是正确的提示模版错误
            for (int i = 0; i < TEMPLATE_TITLE.size(); i++) {
                if (!titleList.get(i).equals(TEMPLATE_TITLE.get(i))) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("模版第[{}]列列名称错误,应该是[{}]", i + 1, TEMPLATE_TITLE.get(i))));
                }
            }

            //获取第二列数据集合
            List<String> companyProductNameList = read.stream()
                    .filter(f -> !StrUtil.isBlankIfStr(f.get(2)) && !"保司对账单产品名称".equals(f.get(2).toString()))
                    .map(m -> m.get(2).toString())
                    .distinct()
                    .collect(Collectors.toList());
            if (companyProductNameList.isEmpty()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("没有读取保司对账单产品名称"));
            }
            Map<String, SettlementReconcileCompanyProductEntity> settlementReconcileCompanyProductMap = settlementReconcileCompanyProductService.lambdaQuery()
                    .in(SettlementReconcileCompanyProductEntity::getCompanyCode, StrUtil.split(settlementReconcileInfo.getProductCompanyCode(), ','))
                    .in(SettlementReconcileCompanyProductEntity::getCompanyProductName, companyProductNameList)
                    .list().stream()
                    .collect(Collectors.toMap(SettlementReconcileCompanyProductEntity::getCompanyProductName, v -> v,
                            (v1, v2) -> {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保司对账单产品[{}]存在多个对应关系,请检查", v1.getCompanyProductName())));
                            }));
            List<String> unCompanyNameList = CollUtil.subtractToList(companyProductNameList, new ArrayList<>(settlementReconcileCompanyProductMap.keySet()));
            if (CollUtil.isNotEmpty(unCompanyNameList)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保司对账单产品{}没有配置关联关系", JSONUtil.toJsonStr(unCompanyNameList))));
            }
            // 开始读取文件问文件
            for (int i = 1; i < read.size(); i++) {
                //这是这一行的数据
                List<Object> rows = read.get(i);
                //保单号 为空的时候他是非法数据,直接丢弃
                String policyNo = Objects.toString(rows.get(0), "");
                if (StrUtil.isBlank(policyNo)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文件第{}行没有没有保单号", (i + 1))));
                }
                //批次号,这个不做校验
                String batchCode = Objects.toString(rows.get(1), "");
                // 保司对账单产品名称
                String companyProductName = Objects.toString(rows.get(2), "");
                if (StrUtil.isBlank(companyProductName)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文件第{}行没有保司对账单产品名称", (i + 1))));
                }
                if (!settlementReconcileCompanyProductMap.containsKey(companyProductName)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文件第{}行保司对账单产品名称没有配置关联关系", (i + 1))));
                }
                // 新单/续期
                String insuranceType = Objects.toString(rows.get(3), "新单");
                //续期期次 新单空这个值1  续期从2开始
                Integer renewalPeriod = Integer.parseInt(Objects.toString(rows.get(4), "1"));
                //应税/免税
                String dutyType = "应税".equals(Objects.toString(rows.get(5), "免税")) ? "1" : "0 ";
                //实收保费
                String realityPremium = Objects.toString(rows.get(6), "0").replace("%", "").trim();
                //
                for (int j = 0; j < titleList.size(); j++) {
                    // 标题为费用类型,且费用类型有值 往后取2个值 手续费比率	手续费
                    if ("费用类型".equals(titleList.get(j)) && !StrUtil.isBlankIfStr(rows.get(j))) {
                        if (rows.size() < j) {
                            break;
                        }
                        String subjectName = Objects.toString(rows.get(j), "");
                        if (settlementSubjectMapName.containsKey(subjectName)) {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                    StrUtil.format("文件第{}行科目名称{}系统未配置,请联系开发人员进行配置后再上传",
                                            (i + 1))));
                        }
                        SettlementReconcileCompanyProductEntity settlementReconcileCompanyProduct = settlementReconcileCompanyProductMap.get(companyProductName);
                        //保单号	批单号	险种名称	保单状态	新单/续期	续期期次	应税/免税	实收保费	费用类型	手续费比率	手续费
                        ReconcileRuleFileTemplate template = new ReconcileRuleFileTemplate();
                        //保单号
                        template.setPolicyNo(policyNo);
                        //批次号
                        template.setBatchCode(batchCode);
                        template.setInsuranceProductCode(settlementReconcileCompanyProduct.getInsuranceProductCode());
                        template.setInsuranceProductName(settlementReconcileCompanyProduct.getInsuranceProductName());
                        template.setProductName(settlementReconcileCompanyProduct.getProductName());
                        template.setProductCode(settlementReconcileCompanyProduct.getProductCode());
                        //新单/续期
                        template.setInsuranceType(insuranceType);
                        //续期期次
                        template.setRenewalPeriod(renewalPeriod);
                        //应税/免税 0:免税 1:应税
                        template.setDutyType(dutyType);
                        //实收保费
                        template.setRealityPremium(new BigDecimal(realityPremium));
                        //科目编码和描述信息
                        template.setReconcileSubjectCode(settlementSubjectMapName.get(subjectName));
                        template.setReconcileSubjectName(subjectName);
                        template.setSettlementRate(new BigDecimal(Objects.toString(rows.get(j + 1), "0").replace("%", "").trim()));
                        template.setCompanyAmount(new BigDecimal(Objects.toString(rows.get(j + 2), "0").replace("%", "").trim()));
                        resultList.add(template);
                    }
                }
            }
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            log.warn("读取文件异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件异常"));
        }
        if (resultList.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取为文件内容为空"));
        }
        log.info("解析文件完成={}", JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    @Override
    public ReconcileTemplateEnum getReconcileTemplate() {
        return null;
    }

    @Override
    public void setReconcileTemplate(ReconcileTemplateEnum reconcileTemplate) {

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // ReconcileTemplateFactory.register(ReconcileTemplateEnum.SHORT_INSURANCE, this);
        //  ReconcileTemplateFactory.register(ReconcileTemplateEnum.XIAOWHALE_SHORT_INSURANCE, this);
    }
}
