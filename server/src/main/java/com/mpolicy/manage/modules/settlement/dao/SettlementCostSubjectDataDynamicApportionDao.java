package com.mpolicy.manage.modules.settlement.dao;

import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApportionEntity;
import com.mpolicy.service.common.mapper.ImsBaseMapper;

/**
 * 科目范围(动态科目)分摊明细
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
public interface SettlementCostSubjectDataDynamicApportionDao extends ImsBaseMapper<SettlementCostSubjectDataDynamicApportionEntity> {
	
}
