package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.dao.SettlementPolicyReviseDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyReviseEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyReviseService;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseListOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseOut;
import org.springframework.stereotype.Service;

@Service("settlementPolicyReviseService")
public class SettlementPolicyReviseServiceImpl extends ServiceImpl<SettlementPolicyReviseDao, SettlementPolicyReviseEntity> implements SettlementPolicyReviseService {
    /**
     * 查询分页列表
     * @param input 过滤条件
     * @return 分页结果
     */
    @Override
    public PageUtils<SettlementPolicyReviseListOut> findSettlementPolicyReviseList(SettlementPolicyReviseInput input) {
        IPage<SettlementPolicyReviseListOut> page = baseMapper.findSettlementPolicyReviseList(new Page<>(input.getPage(), input.getLimit()), input);
        return new PageUtils<>(page);
    }

    /**
     * 根据id获取数据详情
     * @param id 数据id
     * @return 数据详情
     */
    @Override
    public SettlementPolicyReviseOut findSettlementPolicyReviseById(Integer id) {
        SettlementPolicyReviseEntity info = getById(id);
        //封装数据返回结果
        SettlementPolicyReviseOut result = new SettlementPolicyReviseOut();
        result.setAfterData(JSONUtil.toBean(info.getAfterData(), SettlementPolicyInfoEntity.class));
        result.setBeforeData(JSONUtil.toBean(info.getBeforeData(), SettlementPolicyInfoEntity.class));
        return result;
    }
}
