package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectProductService;
import com.mpolicy.manage.modules.settlement.vo.SubjectProductListOut;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;


/**
 * 关联险种
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:45:57
 */
@Api(tags = "关联险种")
@RestController
@RequestMapping("settlement/reconcile/company/subject/product")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SettlementReconcileCompanySubjectProductController {

    private final SettlementReconcileCompanySubjectProductService settlementReconcileCompanySubjectProductService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取关联险种列表", notes = "分页获取关联险种列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    public Result<PageUtils> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils page = settlementReconcileCompanySubjectProductService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 根据结算编码获取结算险种列表
     *
     * @param subjectRuleCode 科目编码
     * @return
     */
    @GetMapping("findSubjectProductListBySubjectRuleCode/{subjectRuleCode}")
    public Result<List<SubjectProductListOut>> findSubjectProductListBySubjectRuleCode(@PathVariable(required = false)
                                                                                       @NotBlank(message = "结算编码不能为空") String subjectRuleCode) {
        List<SubjectProductListOut> list = settlementReconcileCompanySubjectProductService.findSubjectProductListBySubjectRuleCode(subjectRuleCode);
        return Result.success(list);
    }

    /**
     * 根据合并编码获取结算险种列表
     *
     * @param reconcileCode 对账单编码
     * @return
     */
    @GetMapping("findSubjectProductListByReconcileCode/{reconcileCode}")
    public Result<List<SubjectProductListOut>> findSubjectProductListByReconcileCode(@PathVariable(required = false)
                                                                                     @NotBlank(message = "对账单编码不能为空") String reconcileCode) {
        List<SubjectProductListOut> list = settlementReconcileCompanySubjectProductService.findSubjectProductListByReconcileCode(reconcileCode);
        return Result.success(list);
    }
}
