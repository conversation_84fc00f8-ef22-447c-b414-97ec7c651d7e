package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyProductEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;

/**
 * 结算保司配置对应规则科目表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 14:29:16
 */
public interface SettlementReconcileCompanyProductService extends IService<SettlementReconcileCompanyProductEntity> {


    /**
     * 分页列表
     * @param input
     * @return
     */
    PageUtils<SettlementReconcileCompanyProductListOut> findSettlementReconcileCompanyProductList(SettlementReconcileCompanyProductListInput input);

    /**
     * 详情
     * @param id
     * @return
     */
    SettlementReconcileCompanyProductInfoOut findSettlementReconcileCompanyProductInfo(Integer id);

    /**
     * 新增
     * @param input
     */
    void saveSettlementReconcileCompanyProduct(SettlementReconcileCompanyProductSaveInput input);

    /**
     * 修改
     * @param input
     */
    void updateSettlementReconcileCompanyProduct(SettlementReconcileCompanyProductUpdateInput input);
}

