package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInvoiceEnclosureDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceEnclosureEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceEnclosureService;
import org.springframework.stereotype.Service;

@Service("settlementReconcileInvoiceEnclosureService")
public class SettlementReconcileInvoiceEnclosureServiceImpl extends ServiceImpl<SettlementReconcileInvoiceEnclosureDao, SettlementReconcileInvoiceEnclosureEntity> implements SettlementReconcileInvoiceEnclosureService {


}
