package com.mpolicy.manage.modules.settlement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class UploadPolicyProductPremInput implements Serializable {
    private static final long serialVersionUID = -6969263215888642832L;



    @ApiModelProperty(name = "fileSystem", required = true, value = "文件所属模块", example = "customer")
    @NotBlank(message = "文件所属模块不能为空")
    private String fileSystem;

    @ApiModelProperty(name = "modelCode", value = "文件用途", example = "portrait")
    @NotBlank(message = "文件用途不能为空")
    private String modelCode;

    @ApiModelProperty(name = "reconcileType", value = "结算类型", example = "portrait")
    @NotNull(message = "结算类型不能为空")
    private Integer reconcileType;

    @ApiModelProperty(name = "protocolCode", required = true, value = "上传的文件")
    @NotNull(message = "上传的文件不能为空")
    private MultipartFile file;
}
