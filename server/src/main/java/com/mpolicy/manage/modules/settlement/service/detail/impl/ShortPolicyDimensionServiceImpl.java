package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsPolicyDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryShort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/31 11:19
 * @Version 1.0
 */

@Service
public class ShortPolicyDimensionServiceImpl extends AbsPolicyDimensionService<List<DetailSummaryShort>> {

    @Override
    public List<DetailSummaryShort> querySummary(PageSettlementDetailParams p) {
        DetailSummaryShort result = new DetailSummaryShort();
        result.setMonth(calLastMonth(p));

        BigDecimal cost = costAutoInfoDao.shortSummary(p);
        result.setCostAmount(cost);

        return Lists.newArrayList(result);

    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.SHORT_PROMOTION);
    }

}
