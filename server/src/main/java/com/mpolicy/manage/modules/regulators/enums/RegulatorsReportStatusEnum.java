package com.mpolicy.manage.modules.regulators.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 监管报备状态枚举COMPANY_ASSETS_LIABILITIES
 *
 * <AUTHOR>
 * @version 2022/01/20
 */
public enum RegulatorsReportStatusEnum {

    /**
     * 监管报备状态枚举
     */
    PROCESS(0, "未报送"),
    FINISH(1, "已报送");

    @Getter
    private final Integer code;

    @Getter
    private final String name;

    RegulatorsReportStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * <p>
     * 根据报备内容编码获取报备内容枚举
     * </p>
     *
     * @param code 报备内容编码
     * <AUTHOR>
     * @since 2022/01/20
     */
    public static RegulatorsReportStatusEnum deCode(Integer code) {
        return Arrays.stream(RegulatorsReportStatusEnum.values()).filter(x -> x.code.equals(code)).findFirst().orElse(null);
    }
}
