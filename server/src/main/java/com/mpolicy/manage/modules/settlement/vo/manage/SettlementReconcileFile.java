package com.mpolicy.manage.modules.settlement.vo.manage;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 对账单文件列表
 *
 * <AUTHOR>
 * @since 2023-05-30 14:53
 */
@Data
@ApiModel(value = "对账单文件信息", description = "对账单文件信息")
public class SettlementReconcileFile implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    private String reconcileCode;

    /**
     * 对账文件编码
     */
    @ApiModelProperty(value = "对账文件编码", example = "OSS2023052302020200")
    private String reconcileFileCode;

    /**
     * 对账文件类型;3+1文件编码
     */
    @ApiModelProperty(value = "对账文件编码", example = "1")
    private String reconcileFileType;

    /**
     * 对账文件类型;3+1文件编码
     */
    @ApiModelProperty(value = "对账文件名称", example = "人身险-横向")
    private String reconcileFileName;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", example = "文件名称.xls")
    private String fileName;

    /**
     * 文件下载地址
     */
    @ApiModelProperty(value = "文件名称", example = "https://oss-xjxhserver.xiaowhale.com/traceSerial/Recall/INS20220813084647916642/20220813/0a1981142f654d66ba2508d335e9e127/%E6%94%AF%E4%BB%98%E7%BB%93%E6%9E%9C-%E6%9F%A5%E7%9C%8B%E6%89%BF%E4%BF%9D%E7%BB%93%E6%9E%9C-20220813801218.jpg")
    private String fileUrl;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "张三")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2023-05-23 11:11:11")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}