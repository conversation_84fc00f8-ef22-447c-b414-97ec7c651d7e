package com.mpolicy.manage.modules.settlement.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReconcileCompanyInfoListOut implements Serializable {
    private static final long serialVersionUID = 9051029899236596744L;
    @TableId
    private Integer id;
    /**
     * 结算保司编码
     */
    private String reconcileCompanyCode;
    /**
     * 结算保司名称
     */
    private String reconcileCompanyName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    private String externalSignatoryTypeDesc;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 结算保司
     */
    private String settlementCompanyCode;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
    /**
     * 产品所属分公司编码
     */
    private String companyCode;
    /**
     * 产品所属分公司名称
     */
    private String companyName;
    /**
     * 规则科目数量
     */
    private Integer subjectRuleCount;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
