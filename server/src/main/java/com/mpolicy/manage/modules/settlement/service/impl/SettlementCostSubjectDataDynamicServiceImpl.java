package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.helper.DocumentHelper;
import com.mpolicy.manage.modules.settlement.common.SettlementAutoCostBaseService;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApplyEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicApplyService;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicService;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyInfo;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyInput;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyQuery;
import com.mpolicy.manage.modules.settlement.vo.dynamic.SubjectDataDynamicApplyRecord;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.settlement.core.common.autocost.SubjectDataDynamicApply;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 科目范围(动态科目)数据服务接口实现
 *
 * <AUTHOR>
 * @date 2024-01-29 20:50
 */
@Slf4j
@Service
public class SettlementCostSubjectDataDynamicServiceImpl implements SettlementCostSubjectDataDynamicService {

    @Autowired
    private SettlementAutoCostBaseService settlementAutoCostBaseService;

    @Autowired
    private SettlementCostSubjectDataDynamicApplyService settlementCostSubjectDataDynamicApplyService;

    /**
     * 构建动态科目数据
     *
     * @param apply        申请基础信息
     * @param documentInfo 文档信息
     * @param userName     操作员
     * @return 构建动态科目数据
     * <AUTHOR>
     * @since 2024/1/29
     */
    private SubjectDataDynamicApply builderDynamicApply(SubjectDataDynamicApplyInput apply, SysDocumentEntity documentInfo, String userName) {
        // 构建动态科目数据input
        SubjectDataDynamicApply bean = new SubjectDataDynamicApply();
        bean.setCostSettlementCycle(apply.getCostSettlementCycle());
        bean.setFileCode(apply.getFileCode());
        bean.setFileType(apply.getFileType());
        bean.setFileName(documentInfo.getFileName());
        bean.setFilePath(documentInfo.getFilePath());
        bean.setDomainPath(documentInfo.getDomainPath());
        bean.setUserName(userName);
        bean.setOpType(apply.getOpType());
        return bean;
    }

    public PageUtils<SubjectDataDynamicApplyRecord> pageSubjectDataDynamicApply(SubjectDataDynamicApplyQuery query){

        // 分页查询
        IPage<SettlementCostSubjectDataDynamicApplyEntity> page = settlementCostSubjectDataDynamicApplyService.page(
                new Page<>(query.getPage(),query.getLimit()),
                new LambdaQueryWrapper<SettlementCostSubjectDataDynamicApplyEntity>()
                        .eq(StringUtils.isNotBlank(query.getCostSettlementCycle()),SettlementCostSubjectDataDynamicApplyEntity::getCostSettlementCycle, query.getCostSettlementCycle())
                        .eq(StringUtils.isNotBlank(query.getFileType()),SettlementCostSubjectDataDynamicApplyEntity::getFileType,query.getFileType())
                        .ge(query.getStartTime()!=null,SettlementCostSubjectDataDynamicApplyEntity::getCreateTime,query.getStartTime())
                        .le(query.getEndTime()!=null,SettlementCostSubjectDataDynamicApplyEntity::getCreateTime,query.getEndTime())
                        .orderByDesc(SettlementCostSubjectDataDynamicApplyEntity::getId)
        );
        // 构建返回的vo list
        List<SubjectDataDynamicApplyRecord> result = new ArrayList<>();
        page.getRecords().forEach(x -> {
            SubjectDataDynamicApplyRecord bean = new SubjectDataDynamicApplyRecord();
            BeanUtils.copyProperties(x, bean);
            bean.setOpUser(x.getCreateUser());
            bean.setOpTime(x.getCreateTime());
            result.add(bean);
        });
        // 重新构建分页返回对象
        return new PageUtils(result, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }
    @Override
    public PageUtils<SubjectDataDynamicApplyInfo> dynamicApplyList(Map<String, Object> params) {
        // 结算周期 + 申请状态
        String costSettlementCycle = RequestUtils.objectValueToString(params, "costSettlementCycle");
        if(StringUtils.isBlank(costSettlementCycle)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算周期参数缺失"));
        }
        Integer applyStatus = RequestUtils.objectValueToInteger(params, "applyStatus");
        // 分页查询
        IPage<SettlementCostSubjectDataDynamicApplyEntity> page = settlementCostSubjectDataDynamicApplyService.page(
                new Query<SettlementCostSubjectDataDynamicApplyEntity>().getPage(params),
                new LambdaQueryWrapper<SettlementCostSubjectDataDynamicApplyEntity>()
                        .eq(SettlementCostSubjectDataDynamicApplyEntity::getCostSettlementCycle, costSettlementCycle)
                        .eq(applyStatus!=null, SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, applyStatus)
                        .orderByDesc(SettlementCostSubjectDataDynamicApplyEntity::getId)
        );
        // 构建返回的vo list
        List<SubjectDataDynamicApplyInfo> result = new ArrayList<>();

        page.getRecords().forEach(x -> {
            SubjectDataDynamicApplyInfo bean = new SubjectDataDynamicApplyInfo();
            BeanUtils.copyProperties(x, bean);
            result.add(bean);
        });
        // 重新构建分页返回对象
        return new PageUtils(result, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public SubjectDataDynamicApplyInfo querySubjectDataDynamicApply(String applyCode) {
        final SettlementCostSubjectDataDynamicApplyEntity applyEntity = Optional.ofNullable(settlementCostSubjectDataDynamicApplyService.lambdaQuery()
                .eq(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, applyCode)
                .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("{} 申请纪录信息不存在", applyCode))));

        SubjectDataDynamicApplyInfo result = new SubjectDataDynamicApplyInfo();
        BeanUtils.copyProperties(applyEntity, result);
        return result;
    }

    @Override
    public String subjectDataDynamicApply(SubjectDataDynamicApplyInput apply, String userName) {
        // 1 获取上传文件信息
        final SysDocumentEntity documentInfo = DocumentHelper.getDocumentInfo(apply.getFileCode());

        // 2 构建动态科目数据input
        final SubjectDataDynamicApply subjectDataDynamicApply = builderDynamicApply(apply, documentInfo, userName);

        final String result = settlementAutoCostBaseService.uploadSubjectDataDynamicApply(subjectDataDynamicApply, true);
        log.info("动态科目数据上传结果：{}", result);
        return result;
    }

    @Override
    public void loadDynamicSubject(String applyCode, String userName) {
        // 更新为处理中
        settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, 3)
                .eq(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode,applyCode)
                .update();
        // 执行动态科目数据加载
        String result = settlementAutoCostBaseService.loadDynamicSubject(applyCode, userName, true);
        log.info("动态科目数据加载结果：{}", result);
    }






}
