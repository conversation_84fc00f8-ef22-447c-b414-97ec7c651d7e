package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsProductDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryPcoPerformance;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/2/4 00:24
 * @Version 1.0
 */
@Service
public class PcoPerformanceServiceImpl extends AbsProductDimensionService<List<DetailSummaryPcoPerformance>> {
    @Override
    public List<DetailSummaryPcoPerformance> querySummary(PageSettlementDetailParams p) {
        DetailSummaryPcoPerformance summaryPcoPerformance = costAutoInfoDao.pcoPerformanceSummary(p);
        if (Objects.nonNull(summaryPcoPerformance)) {
            summaryPcoPerformance.setMonth(calLastMonth(p));
        }
        return Lists.newArrayList(summaryPcoPerformance);
    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.PCO_PERFORMANCE);
    }
}
