package com.mpolicy.manage.modules.commission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicInfoEntity;
import com.mpolicy.manage.modules.commission.vo.*;

import java.util.List;

/**
 * 基础佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */
public interface CommissionBasicInfoService extends IService<CommissionBasicInfoEntity> {

    /**
     * 获取基础佣金配置列表
     * @param input
     * @return
     */
    PageUtils<CommissionBasicInfoListOut> findCommissionBasicInfoList(CommissionBasicInfoListInput input);

    /**
     * 获取基础佣金配置详情
     * @param id
     * @return
     */
        CommissionBasicInfoInfoOut findCommissionBasicInfoById(Integer id);

    /**
     * 新增基础佣金配置数据
     * @param input
     * @return
     */
    void saveCommissionBasicInfo(CommissionBasicInfoSaveInput input);

    /**
     * 修改基础佣金配置数据
     * @param input
     * @return
     */
    void updateCommissionBasicInfoById(CommissionBasicInfoUpdateInput input);

    /**
     * 删除
     * @param asList
     */
    void deleteByIds(List<Integer> asList);
}

