package com.mpolicy.manage.modules.agentApply.vo;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentApplyPageListVO
 * Description: 代理人入职申请分页请求信息
 * date: 2022/11/28 15:47
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyPageListVO extends BasePage implements Serializable {
    @ApiModelProperty(value = "代理人工号")
    private String agentCode;
    /**
     * 增员人编码
     */
    @ApiModelProperty(value = "增员人编码")
    private String recruitCode;

    private List<String> orgCodeList;

    /**
     * 互联网告知书签署状态(0:未签署 2:已签署)
     */
    private Integer marketingSignStatus;
}
