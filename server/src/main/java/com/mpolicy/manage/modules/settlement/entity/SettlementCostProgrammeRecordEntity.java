package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支出结算方案记录表
 * 
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
@TableName("settlement_cost_programme_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostProgrammeRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 方案编码
	 */
	private String programmeCode;
	/**
	 * 科目数据完成状态;0待完成-1完成
	 */
	private Integer subjectDataStatus;
	/**
	 * 科目数据完成描述
	 */
	private String subjectDataDesc;
	/**
	 * 科目结算完成状态;0待完成-1完成
	 */
	private Integer subjectCalculateStatus;
	/**
	 * 科目结算完成描述
	 */
	private String subjectCalculateDesc;
	/**
	 * 方案记录状态;0待完成-1完成
	 */
	private Integer programmeRecordStatus;
	/**
	 * 方案完成时间
	 */
	private Date programmeFinishTime;
	/**
	 * 结算方案总金额
	 */
	private BigDecimal costTotalCash;
	/**
	 * 周期结算方案描述
	 */
	private String costProgrammeDesc;
	/**
	 * 确认结算状态;0未确认1确认中2已确认
	 */
	private String confirmStatus;
	/**
	 *确认结算操作员
	 */
	private String confirmUser;
	/**
     * 确认结算完成时间
	 */
	private Date confirmTime;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
