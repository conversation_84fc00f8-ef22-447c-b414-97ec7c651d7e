package com.mpolicy.manage.modules.regulators.service.report.listener;

import cn.hutool.core.annotation.Alias;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.modules.regulators.service.report.data.CompanyAssetsLiabilitiesData;
import com.mpolicy.manage.modules.regulators.service.report.data.LifeCompanyBusinessData;
import com.mpolicy.manage.modules.regulators.service.report.data.PropertyCompanyBusinessData;
import com.mpolicy.manage.modules.settlement.vo.PolicyProductFeePremExcel;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/14 17:11
 * @Version 1.0
 */
@Slf4j
public class TaxFeeObjectEventListener extends ReportObjectEventListener<Object, PolicyProductFeePremExcel> {

    List<PolicyProductFeePremExcel> datas = new ArrayList<>();

    @Override
    public List<PolicyProductFeePremExcel> getReadData() {
        return datas;
    }

    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.debug("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);
        JSONArray lineData = JSON.parseArray(xlsLineData);

        if (context.getCurrentRowNum() == 0) {
            return;
        }
        // 解析赋值对象
        PolicyProductFeePremExcel bean = new PolicyProductFeePremExcel();
        bean.setPolicyNo(!lineData.isEmpty() ? lineData.getString(0):null);
        bean.setBatchCode(lineData.size()>1 ? lineData.getString(1): null);
        bean.setInsuranceProductCode(lineData.size()>2 ? lineData.getString(2):null);
        bean.setProductCode(lineData.size()>3?lineData.getString(3):null);
        bean.setPremium(lineData.size()>4?lineData.getString(4):null);
        bean.setTaxAfterPremium(lineData.size()>5?lineData.getString(5):null);
        bean.setTaxRate(lineData.size()>6?lineData.getString(6):null);
        datas.add(bean);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
