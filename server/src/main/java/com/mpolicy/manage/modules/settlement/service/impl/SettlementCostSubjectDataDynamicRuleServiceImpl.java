package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostSubjectDataDynamicRuleDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicRuleEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 科目范围(动态科目)规则
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
@Slf4j
@Service("settlementCostSubjectDataDynamicRuleService")
public class SettlementCostSubjectDataDynamicRuleServiceImpl extends ServiceImpl<SettlementCostSubjectDataDynamicRuleDao, SettlementCostSubjectDataDynamicRuleEntity> implements SettlementCostSubjectDataDynamicRuleService {


}
