package com.mpolicy.manage.modules.agentApply.vo;

import com.mpolicy.web.common.validator.group.AddGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ClassName: AgentApplyFinishInterview
 * Description: 代理人补充信息
 * date: 2022/11/30 12:30
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyReplenishVO implements Serializable {

    @ApiModelProperty(value = "代理人编码",required = true)
    @NotBlank(message = "代理人编码不能为空")
    private String agentCode;

    /**
     * 执业证编码
     */
    @ApiModelProperty(value = "执业证编码", required = true)
    @NotBlank(message = "执业证编码不能为空",groups = {AddGroup.class})
    private String certificateNum;

    @ApiModelProperty(value = "业务编码", required = true)
    @NotBlank(message = "业务编码不能为空")
    private String businessCode;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull(message = "开始日期不能为空",groups = {AddGroup.class})
    private Date startDate;
    /**
     * 是否长期有效 1:长期;0:非长期
     */
    @ApiModelProperty(value = "是否长期有效1:长期;0:非长期", required = true)
    private Integer longTerm;
    /**
     * 截至日期
     */
    @ApiModelProperty(value = "截至日期")
    private Date endDate;

    /**
     * 初始职位
     */
    @ApiModelProperty(value = "初始职位-AGENT_POSITION", required = true)
    @NotBlank(message = "初始职位不能为空",groups = {AddGroup.class})
    private String position;

    /**
     * 初始档次
     */
    @ApiModelProperty(value = "初始档次", required = true,example = "获取列表类似AGENT_POSITION:0,传递值为AGENT_POSITION:0:DEGREE:1")
    private String positionDegree;

    @ApiModelProperty(value = "附件信息", required = true)
    @Valid
    private List<AgentApplyAttachmentVo> fileList;

    /**
     * 是否为暂存
     */
    @ApiModelProperty(value = "是否暂存 1是 0否", required = true,example = "是否暂存 1是 0否")
    @NotNull(message = "是否为暂存方式不能为空")
    private Integer isStaging;
}
