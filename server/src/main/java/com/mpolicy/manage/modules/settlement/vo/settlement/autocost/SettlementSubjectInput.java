package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 3:25 下午
 * @Version 1.0
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettlementSubjectInput implements Serializable {
    /**
     * 发放对象编码
     */
    private String sendObjectCode;
    /**
     * 发放对象机构编码
     */
    private String objectOrgCode;
    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 科目编码
     */
    private String subjectCode;
    /**
     * 周期
     */
    private String costSettlementCycle;

}
