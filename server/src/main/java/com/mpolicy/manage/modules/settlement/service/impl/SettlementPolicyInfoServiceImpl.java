package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelDistributionInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelDistributionInfoService;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.settlement.common.SettlementReconcileBaseService;
import com.mpolicy.manage.modules.settlement.dao.SettlementPolicyInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyReviseEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyInfoService;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyReviseService;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyInfoOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyInfoReviseVo;
import com.mpolicy.manage.modules.settlement.vo.policy.SettlementReconcilePolicyInfo;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.policy.common.enums.ProdTypeEnum;
import com.mpolicy.settlement.core.common.reconcile.HandleSettlementPolicyInfoVo;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算保单明细记录表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
@Slf4j
@Service("settlementPolicyInfoService")
public class SettlementPolicyInfoServiceImpl extends ServiceImpl<SettlementPolicyInfoDao, SettlementPolicyInfoEntity>
        implements SettlementPolicyInfoService {

    @Autowired
    private SettlementPolicyReviseService settlementPolicyReviseService;

    @Autowired
    private SettlementReconcileBaseService settlementReconcileBaseService;
    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;
    @Autowired
    private InsuranceCompanyService insuranceCompanyService;
    @Autowired
    private ChannelDistributionInfoService channelDistributionInfoService;

    @Override
    public PageUtils<SettlementReconcilePolicyInfo> querySettlementPolicyInfoServiceList(Map<String, Object> params) {
        String startTimeSuffix = " 00:00:00";
        String endTimeSuffix = " 23:59:59";
        // 条件参数获取
        //记账时间
        String settlementTimeStart = RequestUtils.objectValueToString(params, "settlementTimeStart");
        String settlementTimeEnd = RequestUtils.objectValueToString(params, "settlementTimeEnd");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //保险公司
        String companyCode = RequestUtils.objectValueToString(params, "companyCode");
        //产品名称
        String commodityCode = RequestUtils.objectValueToString(params, "commodityCode");
        //承保时间
        String approvedTimeStart = RequestUtils.objectValueToString(params, "approvedTimeStart");
        String approvedTimeEnd = RequestUtils.objectValueToString(params, "approvedTimeEnd");
        //被保人姓名/证件号/手机号
        String insuredNameMobileCard = RequestUtils.objectValueToString(params, "insuredNameMobileCard");
        //投保人姓名/证件号/手机号
        String applicantNameMobileCard = RequestUtils.objectValueToString(params, "applicantNameMobileCard");
        //产品类型
        String commodityType = RequestUtils.objectValueToString(params, "commodityType");
        //生效时间
        String enforceTimeStart = RequestUtils.objectValueToString(params, "enforceTimeStart");
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");
        // 交单时间
        String orderTimeStart = RequestUtils.objectValueToString(params, "orderTimeStart");
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //保单状态
        String policyStatus = RequestUtils.objectValueToString(params, "policyStatus");
        //单据编号
        String settlementCode = RequestUtils.objectValueToString(params, "settlementCode");
        //订单号
        String serialNumber = RequestUtils.objectValueToString(params, "serialNumber");
        //结算月份
        String settlementMonth = RequestUtils.objectValueToString(params, "settlementMonth");
        //销售渠道编码
        String channelCode = RequestUtils.objectValueToString(params, "channelCode");
        //渠道推荐人编码名称
        String referrerCodeName = RequestUtils.objectValueToString(params, "referrerCodeName");
        //代理人编码
        String mainAgentCode = RequestUtils.objectValueToString(params, "mainAgentCode");
        //对账结果/状态：0未对账1对账中2已完成对账
        String reconcileStatus = RequestUtils.objectValueToString(params, "reconcileStatus");
        //协议编码
        String protocolCode = RequestUtils.objectValueToString(params, "protocolCode");
        String endorsementNo = RequestUtils.objectValueToString(params, "endorsementNo");
        String reconcileType = RequestUtils.objectValueToString(params, "reconcileType");
        //冲正状态
        Integer rectificationMark = RequestUtils.objectValueToInteger(params, "rectificationMark");

        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();

        if (Objects.nonNull(channelBranchCodeList) && channelBranchCodeList.isEmpty()) {
            return null;
        }
        // 分页查询基础数据
        IPage<SettlementPolicyInfoEntity> pageResult =
                this.page(
                        new Query<SettlementPolicyInfoEntity>().getPage(params),
                        new LambdaQueryWrapper<SettlementPolicyInfoEntity>().and(
                                        StringUtils.isNotBlank(policyNo),
                                        a -> a.eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo).or()
                                                .eq(SettlementPolicyInfoEntity::getThirdPolicyNo, policyNo)
                                )
                                .eq(StringUtils.isNotBlank(endorsementNo), SettlementPolicyInfoEntity::getEndorsementNo,
                                    endorsementNo
                                )
                                .eq(StringUtils.isNotBlank(reconcileType), SettlementPolicyInfoEntity::getReconcileType,
                                    reconcileType
                                )
                                .eq(rectificationMark != null, SettlementPolicyInfoEntity::getRectificationMark, rectificationMark)
                                .eq(StringUtils.isNotBlank(serialNumber), SettlementPolicyInfoEntity::getSerialNumber, serialNumber)
                                .eq(StringUtils.isNotBlank(companyCode), SettlementPolicyInfoEntity::getCompanyCode, companyCode)
                                .eq(StringUtils.isNotBlank(commodityCode), SettlementPolicyInfoEntity::getCommodityCode,
                                    commodityCode
                                ).and(
                                        StringUtils.isNotBlank(insuredNameMobileCard),
                                        Wrapper -> Wrapper.eq(SettlementPolicyInfoEntity::getInsuredName, insuredNameMobileCard).or()
                                                .eq(SettlementPolicyInfoEntity::getInsuredMobile, insuredNameMobileCard).or()
                                                .eq(SettlementPolicyInfoEntity::getInsuredIdCard, insuredNameMobileCard)
                                )
                                .and(
                                        StringUtils.isNotBlank(applicantNameMobileCard),
                                        Wrapper -> Wrapper.eq(SettlementPolicyInfoEntity::getApplicantName, applicantNameMobileCard)
                                                .or().eq(SettlementPolicyInfoEntity::getApplicantMobile, applicantNameMobileCard).or()
                                                .eq(SettlementPolicyInfoEntity::getApplicantIdCard, applicantNameMobileCard)
                                )
                                .eq(StringUtils.isNotBlank(commodityType), SettlementPolicyInfoEntity::getCommodityType,
                                    commodityType
                                )
                                .eq(StringUtils.isNotBlank(policyStatus), SettlementPolicyInfoEntity::getPolicyStatus, policyStatus)
                                .eq(StringUtils.isNotBlank(settlementCode), SettlementPolicyInfoEntity::getSettlementCode,
                                    settlementCode
                                )
                                .eq(StringUtils.isNotBlank(settlementMonth), SettlementPolicyInfoEntity::getSettlementMonth,
                                    settlementMonth
                                )
                                .eq(StringUtils.isNotBlank(channelCode), SettlementPolicyInfoEntity::getChannelCode, channelCode)
                                .and(
                                        StringUtils.isNotBlank(referrerCodeName),
                                        Wrapper -> Wrapper.eq(SettlementPolicyInfoEntity::getReferrerCode, referrerCodeName).or()
                                                .eq(SettlementPolicyInfoEntity::getReferrerName, referrerCodeName)
                                )
                                .eq(StringUtils.isNotBlank(mainAgentCode), SettlementPolicyInfoEntity::getMainAgentCode,
                                    mainAgentCode
                                )
                                .eq(StringUtils.isNotBlank(reconcileStatus), SettlementPolicyInfoEntity::getReconcileStatus, reconcileStatus)
                                .in(CollectionUtil.isNotEmpty(channelBranchCodeList), SettlementPolicyInfoEntity::getChannelCode, channelBranchCodeList)
                                .apply(StringUtils.isNotBlank(settlementTimeStart), "settlement_time >= {0}",
                                       settlementTimeStart + startTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(settlementTimeEnd), "settlement_time <= {0}",
                                       settlementTimeEnd + endTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(approvedTimeStart), "approved_time >= {0}",
                                       approvedTimeStart + startTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(approvedTimeEnd), "approved_time <= {0}",
                                       approvedTimeEnd + endTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(orderTimeStart), "order_time >= {0}",
                                       orderTimeStart + startTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(orderTimeEnd), "order_time <= {0}",
                                       orderTimeEnd + endTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(enforceTimeStart), "enforce_time >= {0}",
                                       enforceTimeStart + startTimeSuffix
                                )
                                .apply(StringUtils.isNotBlank(enforceTimeEnd), "enforce_time <= {0}",
                                       enforceTimeEnd + endTimeSuffix
                                ).orderByDesc(SettlementPolicyInfoEntity::getId)
                );
        if (CollUtil.isNotEmpty(pageResult.getRecords())) {
            // 险种编码
            List<String> productCodes = pageResult.getRecords().stream().map(SettlementPolicyInfoEntity::getProductCode).collect(Collectors.toList());
            Map<String, String> productCodeMap = insuranceProductInfoService.lambdaQuery()
                    .in(InsuranceProductInfoEntity::getProductCode, productCodes)
                    .list().stream()
                    .filter(f -> StrUtil.isNotBlank(f.getProductChannel()))
                    .collect(Collectors.toMap(InsuranceProductInfoEntity::getProductCode, InsuranceProductInfoEntity::getProductChannel));
            Map<String, String> productChannelMap = new HashMap<>();
            if (!productCodeMap.isEmpty()) {
                productChannelMap.putAll(insuranceCompanyService.lambdaQuery()
                                                 .in(InsuranceCompanyEntity::getCompanyCode, productCodeMap.values()).list()
                                                 .stream().collect(Collectors.toMap(InsuranceCompanyEntity::getCompanyCode, InsuranceCompanyEntity::getCompanyName)));
                productChannelMap.putAll(channelDistributionInfoService.lambdaQuery()
                                                 .in(ChannelDistributionInfoEntity::getChannelCode, productCodeMap.values()).list()
                                                 .stream().collect(Collectors.toMap(ChannelDistributionInfoEntity::getChannelCode, ChannelDistributionInfoEntity::getChannelName)));
            }
            // 构建vo
            // todo 枚举字典处理
            List<SettlementReconcilePolicyInfo> resultData = pageResult.getRecords().stream().map(x -> {
                SettlementReconcilePolicyInfo bean = new SettlementReconcilePolicyInfo();
                BeanUtils.copyProperties(x, bean);
                //处理结算状态 如果是无需对账也设置成 对账已完成
                if (x.getReconcileExecuteStatus() == 2) {
                    bean.setReconcileStatusDesc(ReconcileStatusEnum.RECONCILE_FINISH.getStatusDesc());
                } else {
                    bean.setReconcileStatusDesc(ReconcileStatusEnum.decode(x.getReconcileStatus()).getStatusDesc());
                }
                ProdTypeEnum prodTypeEnum = ProdTypeEnum.getProdTypeEnum(bean.getProductType());
                if (prodTypeEnum != null) {
                    bean.setProductTypeDesc(prodTypeEnum.getProdType());
                }
                if (productCodeMap.containsKey(x.getProductCode())) {
                    bean.setProductChannel(productCodeMap.get(x.getProductCode()));
                    if (productChannelMap.containsKey(productCodeMap.get(x.getProductCode()))) {
                        String productChannelName = productChannelMap.get(productCodeMap.get(x.getProductCode()));
                        bean.setProductChannelName(productChannelName);
                    }
                }
                return bean;
            }).collect(Collectors.toList());

            // 重新构建分页返回对象
            return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(),
                                 (int) pageResult.getCurrent()
            );
        }
        return new PageUtils<>(new ArrayList<>(), 0, 1, 1);
    }

    /**
     * 获取保单详情
     *
     * @param settlementCode
     * @return
     */
    @Override
    public SettlementPolicyInfoOut findSettlementPolicyInfo(String settlementCode) {
        SettlementPolicyInfoEntity settlementPolicyInfo =
                Optional.ofNullable(lambdaQuery().eq(SettlementPolicyInfoEntity::getSettlementCode, settlementCode).one())
                        .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单详情不存在")));
        return BeanUtil.copyProperties(settlementPolicyInfo, SettlementPolicyInfoOut.class);
    }

    /**
     * 修正数据
     *
     * @param vo
     */
    @Override
    public void reviseSettlementPolicyInfo(SettlementPolicyInfoReviseVo vo) {
        SettlementPolicyInfoEntity settlementPolicyInfo = Optional.ofNullable(
                        lambdaQuery().eq(SettlementPolicyInfoEntity::getSettlementCode, vo.getSettlementCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单详情不存在")));
        String beforeData = JSONUtil.toJsonStr(settlementPolicyInfo);
        BeanUtil.copyProperties(vo, settlementPolicyInfo);
        String afterData = JSONUtil.toJsonStr(settlementPolicyInfo);
        //更新信息
        updateById(settlementPolicyInfo);
        SettlementPolicyReviseEntity settlementPolicyRevise = new SettlementPolicyReviseEntity();
        settlementPolicyRevise.setSettlementCode(vo.getSettlementCode());
        settlementPolicyRevise.setBeforeData(beforeData);
        settlementPolicyRevise.setAfterData(afterData);
        settlementPolicyReviseService.save(settlementPolicyRevise);
    }

    /***
     * 刷新费率信息
     * @param settlementCode
     */
    @Override
    public void refreshCommission(String settlementCode) {
        SysUserEntity user = ShiroUtils.getUserEntity();
        SettlementPolicyInfoEntity settlementPolicyInfo =
                Optional.ofNullable(lambdaQuery().eq(SettlementPolicyInfoEntity::getSettlementCode, settlementCode).one())
                        .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算单明细不存在")));
        // 重新计算费率信息
        HandleSettlementPolicyInfoVo handleSettlementPolicyInfoVo = new HandleSettlementPolicyInfoVo();
        handleSettlementPolicyInfoVo.setIds(CollUtil.newArrayList(settlementPolicyInfo.getId()));
        handleSettlementPolicyInfoVo.setReconcileType(settlementPolicyInfo.getReconcileType());
        handleSettlementPolicyInfoVo.setReconcileExecuteDesc("刷新费率信息");
        handleSettlementPolicyInfoVo.setReconcileExecuteStatus(0);
        handleSettlementPolicyInfoVo.setUpdateUser(user.getUsername());
        settlementReconcileBaseService.handleSettlementPolicyInfo(handleSettlementPolicyInfoVo);
    }
}
