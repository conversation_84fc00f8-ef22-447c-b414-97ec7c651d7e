package com.mpolicy.manage.modules.settlement.vo.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
@Data
public class UpdateExpressVo implements Serializable {
    private static final long serialVersionUID = 7768949162226090703L;


    @NotBlank(message = "发票编码不能为空")
    @ApiModelProperty(value = "发票编码")
    private String invoiceCode;

    @NotBlank(message = "快递名称不能为空")
    private String expressName;

    @NotBlank(message = "快递运单号不能为空")
    private String expressWaybillNumber;
}
