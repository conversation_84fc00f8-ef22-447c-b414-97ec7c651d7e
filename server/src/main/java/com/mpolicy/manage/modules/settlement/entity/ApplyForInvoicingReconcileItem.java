package com.mpolicy.manage.modules.settlement.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ApplyForInvoicingReconcileItem implements Serializable {
    private static final long serialVersionUID = -292012297095440330L;

    /**
     * 对账单编码
     */
    private String reconcileCode;

    /**
     * 申请开票金额
     */
    private BigDecimal applyAmount;
}
