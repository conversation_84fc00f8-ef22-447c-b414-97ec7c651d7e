package com.mpolicy.manage.modules.settlement.vo.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算合约配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-28 14:21:38
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileContractInfoListOut implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 结算保司编码
     */
    private String reconcileCompanyCode;
    /**
     * 结算保司名称
     */
    private String reconcileCompanyName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    private String externalSignatoryTypeDesc;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 内部签署方类型
     */
    private String innerSignatoryType;
    private String innerSignatoryTypeDesc;

    private Integer subjectRuleCount;
    /**
     * 结算企业
     */
    private String settlementCompanyName;
    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
