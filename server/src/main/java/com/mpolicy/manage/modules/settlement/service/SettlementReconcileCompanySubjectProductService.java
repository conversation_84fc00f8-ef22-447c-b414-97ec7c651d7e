package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectProductEntity;
import com.mpolicy.manage.modules.settlement.vo.SubjectProductListOut;

import java.util.List;
import java.util.Map;

/**
 * 关联险种
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:45:57
 */
public interface SettlementReconcileCompanySubjectProductService extends IService<SettlementReconcileCompanySubjectProductEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);

    /**
     * 根据结算编码获取结算险种列表
     * @param subjectRuleCode 结算科目编码
     * @return
     */
    List<SubjectProductListOut> findSubjectProductListBySubjectRuleCode(String subjectRuleCode);

    /**
     * 根据结算编码获取结算险种列表
     * @param reconcileCode 合并编码
     * @return
     */
    List<SubjectProductListOut> findSubjectProductListByReconcileCode(String reconcileCode);
}

