package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentApplyPageListOut
 * Description: 代理人入职申请分页返回
 * date: 2022/11/28 14:26
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyExamPageListOut implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * 题目编码
     */
    @ApiModelProperty(value = "题目编码")
    private String code;
    /**
     * 所属部分(1:一 2:二 3:三)
     */
    @ApiModelProperty(value = "所属部分(1:一 2:二 3:三)")
    private Integer part;
    @ApiModelProperty(value = "所属部分")
    private String partName;
    /**
     * 题型(1:单选题 2:多选题 3:判断题)
     */
    @ApiModelProperty(value = "题型(1:单选题 2:多选题 3:判断题)")
    private Integer type;
    @ApiModelProperty(value = "题型")
    private String typeName;
    /**
     * 分值
     */
    @ApiModelProperty(value = "分值")
    private String score;
    /**
     * 题目
     */
    @ApiModelProperty(value = "题目")
    private String title;

    @ApiModelProperty("题目答案")
    private List<AgentApplyAnswerOut> answer;
}
