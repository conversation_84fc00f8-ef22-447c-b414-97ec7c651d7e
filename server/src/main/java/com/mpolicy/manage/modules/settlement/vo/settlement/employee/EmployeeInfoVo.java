package com.mpolicy.manage.modules.settlement.vo.settlement.employee;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/30 2:07 上午
 * @Version 1.0
 */
@Data
@ApiModel(value = "员工信息", description = "员工信息")
public class EmployeeInfoVo implements Serializable {
    @ApiModelProperty(
            name = "员工ID"
    )
    private Long employeeId;
    @ApiModelProperty(
            name = "员工工号"
    )
    private String employeeCode;
    @ApiModelProperty(
            name = "员工姓名"
    )
    private String employeeName;
    @ApiModelProperty(
            name = "岗位"
    )
    private String jobPositionName;
    @ApiModelProperty(
            name = "岗位ID"
    )
    private String jobPositionId;
    @ApiModelProperty(
            name = "岗位编码"
    )
    private String jobPositionCode;
    @ApiModelProperty(
            name = "任职部门"
    )
    private String branchName;
    @ApiModelProperty(
            name = "任职部门ID"
    )
    private Long branchId;
    @ApiModelProperty(
            name = "任职部门编码，分支员工为分支编码"
    )
    private String branchCode;
}
