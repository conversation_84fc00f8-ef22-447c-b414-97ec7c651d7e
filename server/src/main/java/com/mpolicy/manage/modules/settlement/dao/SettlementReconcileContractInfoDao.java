package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileContractInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileContractInfoListInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileContractInfoListOut;
import org.apache.ibatis.annotations.Param;

/**
 * 结算合约配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-28 14:21:38
 */
public interface SettlementReconcileContractInfoDao extends BaseMapper<SettlementReconcileContractInfoEntity> {


    /**
     * 获取结算合约配置表列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<SettlementReconcileContractInfoListOut> findSettlementReconcileContractInfoList(@Param("page") Page<SettlementReconcileContractInfoListOut> page, @Param("input") SettlementReconcileContractInfoListInput input);

}
