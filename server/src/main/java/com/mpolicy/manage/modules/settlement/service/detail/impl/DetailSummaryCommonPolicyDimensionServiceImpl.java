package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsPolicyDimensionService;
import com.mpolicy.manage.modules.settlement.service.detail.AbsProductDimensionService;
import com.mpolicy.manage.modules.settlement.service.detail.BaseDynamicShowType;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryCommon;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/4 17:37
 * @Version 1.0
 */
@Service
public class DetailSummaryCommonPolicyDimensionServiceImpl extends AbsPolicyDimensionService<List<DetailSummaryCommon>> implements BaseDynamicShowType {
    @Override
    public List<DetailSummaryCommon> querySummary(PageSettlementDetailParams p) {
        BigDecimal summaryCommon = costAutoInfoDao.shortSummary(p);
        DetailSummaryCommon r = new DetailSummaryCommon();
        r.setCostAmount(summaryCommon);
        r.setMonth(calLastMonth(p));
        return Lists.newArrayList(r);
    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(
                CostSubjectEnum.VEHICLE_VESSEL_TAX
                , CostSubjectEnum.ADD_COMM
                , CostSubjectEnum.LONG_NOT_RENEWAL_REBATE_COMM
                , CostSubjectEnum.DYNAMIC_SUBJECT
        );
    }

    @Override
    public String dynamicShowType() {
        return "policy";
    }
}
