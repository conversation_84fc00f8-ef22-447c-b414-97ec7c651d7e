package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 关联险种
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:45:57
 */
@TableName("settlement_reconcile_company_subject_product")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileCompanySubjectProductEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 结算编码
	 */
	private String subjectRuleCode;
	/**
	 * 协议险种编码
	 */
	private String insuranceProductCode;
	/**
	 * 协议险种名称
	 */
	private String insuranceProductName;
	/**
	 * 逻辑删除 0:存在;-1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
