package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ClassName: AgentApplyFinishInterview
 * Description: 代理人申请完成培训
 * date: 2022/11/30 12:30
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyFinishTrainVO implements Serializable {

    @ApiModelProperty(value = "代理人编码",required = true)
    @NotBlank(message = "代理人编码不能为空")
    private String agentCode;

    @ApiModelProperty(value = "培训日期集合",required = true)
    @NotEmpty(message = "培训日期不能为空")
    @Size(min = 3,message = "培训日期不能少于3个")
    private List<Date> dateList;
}
