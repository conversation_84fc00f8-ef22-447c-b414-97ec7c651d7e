package com.mpolicy.manage.modules.settlement.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算交互事件受理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-05 14:34:54
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementEventJobInfoOut implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件描述
     */
    private String eventDesc;
    /**
     * 业务编码
     */
    private String eventBusinessCode;
    /**
     * push事件唯一凭证编码
     */
    private String pushEventCode;
    /**
     * 保单中心合同
     */
    private String contractCode;
    /**
     * 事件来源;保单中心、协议管理、车险佣金
     */
    private String eventSource;
    /**
     * 业务请求事件报文
     */
    private String eventRequest;
    /**
     * 业务请求响应报文
     */
    private String eventResponse;
    /**
     * 请求响应消息
     */
    private String eventMessage;
    /**
     *
     */
    private Integer eventCostMillis;
    /**
     * 事件处理完成时间
     */
    private Date eventFinishTime;
    /**
     * 事件状态;0代处理1处理中2处理完成
     */
    private Integer eventStatus;
    /**
     * 签约类型：1小鲸，2非小鲸
     */
    private Integer businessSignType;
    /**
     * 是否混合签约0否1是
     */
    private Integer mixSign;
    /**
     * 收入处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer incomeEventStatus;
    /**
     * 小鲸收入事件响应信息
     */
    private String incomeEventMessage;
    /**
     * 收入事件处理完成事件
     */
    private Date incomeFinishTime;
    /**
     * 支出处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer costEventStatus;
    /**
     * 支出事件响应信息
     */
    private String costEventMessage;
    /**
     * 支出事件处理完成事件
     */
    private Date costFinishTime;
    /**
     * 混合单收入处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer contractIncomeEventStatus;
    /**
     * 混合收入事件响应信息
     */
    private String contractIncomeEventMessage;
    /**
     * 混合收入事件处理完成事件
     */
    private Date contractIncomeFinishTime;

}
