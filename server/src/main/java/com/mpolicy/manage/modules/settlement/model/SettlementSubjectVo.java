package com.mpolicy.manage.modules.settlement.model;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementSubjectVo extends BasePage implements Serializable {
    private static final long serialVersionUID = 7308659484179021191L;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;
}
