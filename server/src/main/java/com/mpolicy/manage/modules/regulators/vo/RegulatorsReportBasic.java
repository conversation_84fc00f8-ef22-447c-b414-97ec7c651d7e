package com.mpolicy.manage.modules.regulators.vo;

/**
 * 监管报备报备内容基类
 *
 * <AUTHOR>
 * @date 2022-01-20 15:07
 */
public interface RegulatorsReportBasic {

    /**
     * <p>
     * 公司月度报备唯一编号
     * </p>
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    String reportRegulatorsNo();

    /**
     * <p>
     * 获取机构报备唯一编码
     * </p>
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    String getOrgRegulatorsNo();

    /**
     * <p>
     * 获取操作机构编码
     * </p>
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    String getReportOrgCode();

    /**
     * <p>
     * 获取操作报备的文件编码
     * </p>
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    String getReportFileCode();

    /**
     * <p>
     * 报备年度
     * </p>
     *
     * @return java.lang.Integer
     * <AUTHOR>
     * @since 2022/1/20
     */
    Integer reportRegulatorsYear();

    /**
     * <p>
     * 报备月度
     * </p>
     *
     * @return java.lang.Integer
     * <AUTHOR>
     * @since 2022/1/20
     */
    Integer reportRegulatorsMonth();

    /**
     * <p>
     * 获取报备操作的用户名称
     * </p>
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    String getReportUploadUserName();

    /**
     * <p>
     * 设置报备对象的机构报备唯一编号
     * </p>
     *
     * @param orgRegulatorsNo 报备机构编号
     * @return void
     * <AUTHOR>
     * @since 2022/1/20
     */
    void setOrgRegulatorsNo(String orgRegulatorsNo);

    /**
     * <p>
     * 设置报备年度
     * </p>
     *
     * @param year year
     * @return void
     * <AUTHOR>
     * @since 2022/1/21
     */
    void setRegulatorsYear(Integer year);

    /**
     * <p>
     * 设置报备月度
     * </p>
     *
     * @param month 月
     * @return void
     * <AUTHOR>
     * @since 2022/1/21
     */
    void setRegulatorsMonth(Integer month);
}
