package com.mpolicy.manage.modules.settlement.service.detail;

/**
 * <AUTHOR>
 * @Date 2024/1/31 01:11
 * @Version 1.0
 */

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;

import java.util.List;

/**
 *
 * @param <S> 汇总
 * @param <L> 明细列表
 * @param <P> 参数
 */
public interface SettlementDetailService<S, L, P> {

    /**
     * 明细分页列表
     * @param p 参数
     * @return
     */
    IPage<L> queryDetail(P p);

    /**
     * 汇总数据
     * @param p 参数
     * @return
     */
    S querySummary(P p);

    /**
     * 科目类型
     * @return {@link CostSubjectEnum}
     */
    List<CostSubjectEnum> subjectType();

}
