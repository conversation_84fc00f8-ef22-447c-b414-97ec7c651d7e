package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SaveReconcileCompanyInfo implements Serializable {
    private static final long serialVersionUID = 3377268931661788137L;

    @NotEmpty(message = "产品所属分公司不能为空")
    private List<String> companyCodeArr;

    private String reconcileCompanyName;

    @NotBlank(message = "结算保司不能为空")
    private String settlementCompanyCode;

    @NotBlank(message = "外部签署方不能为空")
    private String externalSignatoryCode;

    private List<String> externalSignatoryCodeArr;


    @NotBlank(message = "外部签署方类型不能为空")
    private String externalSignatoryType;


    @NotBlank(message = "内部签署方不能为空")
    private String innerSignatoryCode;

    private List<String> innerSignatoryCodeArr;
}
