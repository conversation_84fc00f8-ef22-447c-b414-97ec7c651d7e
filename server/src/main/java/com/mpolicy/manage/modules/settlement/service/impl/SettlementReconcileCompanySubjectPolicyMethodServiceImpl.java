package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanySubjectPolicyMethodDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectPolicyMethodEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectPolicyMethodService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("settlementReconcileCompanySubjectPolicyMethodService")
public class SettlementReconcileCompanySubjectPolicyMethodServiceImpl extends ServiceImpl<SettlementReconcileCompanySubjectPolicyMethodDao, SettlementReconcileCompanySubjectPolicyMethodEntity> implements SettlementReconcileCompanySubjectPolicyMethodService {
}
