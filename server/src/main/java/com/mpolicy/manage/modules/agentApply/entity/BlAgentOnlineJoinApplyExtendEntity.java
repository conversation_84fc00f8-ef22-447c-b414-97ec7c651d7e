package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人线上入职扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
@TableName("bl_agent_online_join_apply_extend")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineJoinApplyExtendEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    private String agentCode;
    /**
     * 执业证编码
     */
    private String certificateNum;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 是否长期有效 1:长期;0:非长期
     */
    private Integer longTerm;
    /**
     * 截至日期
     */
    private Date endDate;
    /**
     * 银行所在地区代码
     */
    private String bandLocationCode;
    /**
     * 开户银行
     */
    private String bandName;
    /**
     * 开户支行名称
     */
    private String bandBranchName;
    /**
     * 银行卡号
     */
    private String bandCardNum;
    /**
     * 联行号
     */
    private String bankNumber;
    /**
     * 个人介绍
     */
    private String profile;
    /**
     * 获取的荣誉
     */
    private String honor;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;
    
}
