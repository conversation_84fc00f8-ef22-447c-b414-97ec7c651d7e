package com.mpolicy.manage.modules.settlement.vo.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
@Data
public class SystemRollbackVo implements Serializable {
    private static final long serialVersionUID = 8894830286088562962L;


    @NotBlank(message = "发票编码不能为空")
    @ApiModelProperty(value = "发票编码")
    private String invoiceCode;

    /**
     *
     */
    @NotBlank(message = "系统失败原因不能为空")
    @ApiModelProperty(value = "系统失败原因")
    private String invoicingFallbackReason;

}
