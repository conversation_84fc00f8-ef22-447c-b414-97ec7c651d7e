package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SettlementPolicyInfoReviseVo implements Serializable {

    @NotBlank(message = "结算编码不能位空")
    private String settlementCode;

    /**
     * 投保时间
     */
    private Date applicantTime;
    /**
     * 交单时间
     */
    private Date orderTime;
    /**
     * 承保时间
     */
    private Date approvedTime;
    /**
     * 生效时间
     */
    private Date enforceTime;
    /**
     * 应缴时间
     */
    private Date payableTime;
    /**
     * 实缴时间
     */
    private Date realityTime;
    /**
     * 回访时间
     */
    private Date revisitTime;
    /**
     * 回执时间
     */
    private Date receiptTime;
    /**
     * 保全生效时间
     */
    private Date preservationEffectTime;
    /**
     * 批改时间
     */
    private Date endorsementTime;
    /**
     * 结算月度
     */
    private String postponedMonth;
}
