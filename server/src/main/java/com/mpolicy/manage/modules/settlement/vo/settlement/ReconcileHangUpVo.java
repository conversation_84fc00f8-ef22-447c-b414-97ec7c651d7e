package com.mpolicy.manage.modules.settlement.vo.settlement;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class ReconcileHangUpVo implements Serializable {
    private static final long serialVersionUID = -6702268518037913410L;
    @NotBlank(message = "对账单编码不能为空")
    private String reconcileCode;

    private  List<String> billCodes;
}
