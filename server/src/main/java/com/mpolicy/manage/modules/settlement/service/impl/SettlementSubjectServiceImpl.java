package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementSubjectDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementSubjectEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementSubjectService;
import org.springframework.stereotype.Service;

@Service("settlementSubjectService")
public class SettlementSubjectServiceImpl extends ServiceImpl<SettlementSubjectDao, SettlementSubjectEntity> implements SettlementSubjectService {
}
