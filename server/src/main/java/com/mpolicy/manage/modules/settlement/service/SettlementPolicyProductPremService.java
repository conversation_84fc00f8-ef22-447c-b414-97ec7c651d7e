package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.manage.modules.settlement.vo.*;

/**
 * 结算保单险种费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-22 15:08:37
 */
public interface SettlementPolicyProductPremService extends IService<SettlementPolicyProductPremEntity> {


    /***
     * 上传一单一议保单费率
     * @param input
     */
    void uploadPremiumFile(UploadPolicyProductPremInput input);

    /**
     * 获取分页列表数据
     * @param input
     * @return
     */
    PageUtils<SettlementPolicyProductPremListOut> findSettlementPolicyProductPremList(SettlementPolicyProductPremListInput input);

    void savePolicyProductPrem(SavePolicyProductPrem params);

    void updatePolicyProductPrem(UpdatePolicyProductPrem params);

    SettlementPolicyProductPrem info(Integer id);

    void deletePolicyProductPrem(Integer id);
}

