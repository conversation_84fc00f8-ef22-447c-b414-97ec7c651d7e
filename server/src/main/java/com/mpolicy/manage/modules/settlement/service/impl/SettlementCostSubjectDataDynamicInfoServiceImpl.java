package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostSubjectDataDynamicInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicInfoEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostSubjectDataDynamicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 科目范围(动态科目)数据
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
@Slf4j
@Service
public class SettlementCostSubjectDataDynamicInfoServiceImpl extends ServiceImpl<SettlementCostSubjectDataDynamicInfoDao, SettlementCostSubjectDataDynamicInfoEntity> implements SettlementCostSubjectDataDynamicInfoService {


}
