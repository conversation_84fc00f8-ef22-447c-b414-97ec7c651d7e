package com.mpolicy.manage.modules.insurance.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyBranchEntity;
import com.mpolicy.manage.modules.insurance.service.IInsuranceCompanyBranchService;
import com.mpolicy.manage.modules.insurance.vo.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;


/**
 * 保险公司分支(分公司)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-21 14:49:28
 */
@Api(tags = "保险公司分支(分公司)")
@Validated
@RestController
@RequestMapping("insurance/company/branch/")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InsuranceCompanyBranchController {

    private final IInsuranceCompanyBranchService insuranceCompanyBranchService;

    @GetMapping("list")
    @RequiresPermissions("product:insurancecompanybranch:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<InsuranceCompanyBranchListOut>> list(InsuranceCompanyBranchListInput input) {
        PageUtils<InsuranceCompanyBranchListOut> page = insuranceCompanyBranchService.findInsuranceCompanyBranchList(input);
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @RequiresPermissions("product:insurancecompanybranch:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<InsuranceCompanyBranchInfoOut> info(@PathVariable(value = "id", required = false)
                                                      @NotNull(message = "操作的数据id不能为空")
                                                      @ApiParam(value = "详情ID") Integer id) {
        InsuranceCompanyBranchInfoOut insuranceCompanyBranch = insuranceCompanyBranchService.findInsuranceCompanyBranchById(id);
        return Result.success(insuranceCompanyBranch);
    }

    @SysLog("保存保险公司分支(分公司)数据")
    @PostMapping("save")
    @RequiresPermissions("product:insurancecompanybranch:save")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid InsuranceCompanyBranchSaveInput input) {
        insuranceCompanyBranchService.saveInsuranceCompanyBranch(input);
        return Result.success();
    }

    @SysLog("修改保险公司分支(分公司)数据")
    @PostMapping("update")
    @RequiresPermissions("product:insurancecompanybranch:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid InsuranceCompanyBranchUpdateInput input) {
        insuranceCompanyBranchService.updateInsuranceCompanyBranchById(input);
        return Result.success();
    }

    @SysLog("删除保险公司分支(分公司)信息")
    @PostMapping("delete")
    @RequiresPermissions("product:insurancecompanybranch:delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false)
                         @NotEmpty(message = "删除的数据ids不能为空")
                         @ApiParam(value = "批量删除的ID") Integer[] ids) {
        insuranceCompanyBranchService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }

    @SysLog("修改分支状态")
    @PostMapping("updateBranchStatus")
    @RequiresPermissions("product:insurancecompanybranch:update")
    @ApiOperation(value = "修改分支状态", notes = "修改分支状态", httpMethod = "POST")
    public Result updateBranchStatus(@RequestBody(required = false) @Valid UpdateBranchStatusVo vo) {
        insuranceCompanyBranchService.lambdaUpdate()
                .eq(InsuranceCompanyBranchEntity::getId, vo.getId())
                .set(InsuranceCompanyBranchEntity::getBranchStatus, vo.getBranchStatus())
                .update();
        return Result.success();
    }

    /**
     * 获取分支下拉列表
     * @param companyCode 保司编码
     * @return 分支列表
     */
    @GetMapping("findSelectList")
    @ApiOperation(value = "获取分支下拉列表", notes = "获取分支下拉列表", httpMethod = "GET")
    public Result<List<SelectOut>> findSelectList(@RequestParam(value = "companyCode", required = false) String companyCode) {
        List<SelectOut> selectList = insuranceCompanyBranchService.findSelectList(companyCode);
        return Result.success(selectList);
    }

}
