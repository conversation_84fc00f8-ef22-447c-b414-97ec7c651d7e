package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司结算对账单汇总申请单
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@TableName("settlement_reconcile_summary_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileSummaryInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 结算汇总编号
	 */
	private String summaryCode;
	/**
	 * 对账月度
	 */
	private String reconcileMonth;
	/**
	 * 关联对账单编码集合
	 */
	private String reconcileCodeList;
	/**
	 * 内部签署方编码
	 */
	private String innerSignatoryCode;
	/**
	 * 内部签署方名称
	 */
	private String innerSignatoryName;
	/**
	 * 外部签署方类型字典
	 */
	private String externalSignatoryType;
	/**
	 * 外部签署方编码
	 */
	private String externalSignatoryCode;
	/**
	 * 外部签署方名称
	 */
	private String externalSignatoryName;
	/**
	 * 保司数量
	 */
	private Integer summaryCompanyCount;
	/**
	 * 科目名称集合;科目名称逗号分割
	 */
	private String summarySubjectNames;
	/**
	 * 总保费
	 */
	private BigDecimal totalAmount;
	/**
	 * 代理人保费
	 */
	private BigDecimal proxyAmount;
	/**
	 * 线上对账保费
	 */
	private BigDecimal onlineAmount;
	/**
	 * 线下对账保费
	 */
	private BigDecimal offlineAmount;
	/**
	 * 汇总图片
	 */
	private String summaryPicUrl;
	/**
	 * 汇总xls
	 */
	private String summaryXlsUrl;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
