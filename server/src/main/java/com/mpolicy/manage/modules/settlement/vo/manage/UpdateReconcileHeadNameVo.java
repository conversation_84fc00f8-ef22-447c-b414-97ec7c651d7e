package com.mpolicy.manage.modules.settlement.vo.manage;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UpdateReconcileHeadNameVo implements Serializable {
    private static final long serialVersionUID = -8832939494190878772L;

    @NotBlank(message = "对账单编码不能为空")
    private String reconcileCode;


    @NotBlank(message = "责任人不能为空")
    private String headName;
}
