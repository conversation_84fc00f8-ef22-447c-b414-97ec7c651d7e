package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.*;

/**
 * 结算保司配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:32:26
 */
public interface SettlementReconcileCompanyInfoService extends IService<SettlementReconcileCompanyInfoEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<ReconcileCompanyInfoListOut> queryPage(ReconcileCompanyInfoListInput paramMap);

    /**
     * 获取详情
     *
     * @param id 主键id
     * @return
     */
    SettlementReconcileCompanyInfo info(Integer id);

    /**
     * 新增结算保司信息
     *
     * @param params
     */
    void saveReconcileCompanyInfo(SaveReconcileCompanyInfo params);


    /**
     * 修改协议保司配置
     *
     * @param params
     */
    void updateReconcileCompanyInfo(UpdateReconcileCompanyInfo params);

    /**
     * 删除协议保司配置
     * @param id
     */
    void deleteReconcileCompanyInfo(Integer id);
}

