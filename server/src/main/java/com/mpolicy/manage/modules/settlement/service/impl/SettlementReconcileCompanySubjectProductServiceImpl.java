package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductEntity;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductProductEntity;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductProductService;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductService;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanySubjectProductDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectProductEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileSubjectEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectProductService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileSubjectService;
import com.mpolicy.manage.modules.settlement.vo.SubjectProductListOut;
import com.mpolicy.web.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service("settlementReconcileCompanySubjectProductService")
public class SettlementReconcileCompanySubjectProductServiceImpl extends ServiceImpl<SettlementReconcileCompanySubjectProductDao, SettlementReconcileCompanySubjectProductEntity> implements SettlementReconcileCompanySubjectProductService {

    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;

    @Autowired
    private SettlementReconcileSubjectService settlementReconcileSubjectService;
    @Autowired
    private IEpProtocolInsuranceProductProductService protocolInsuranceProductProductService;

    @Autowired
    private IEpProtocolInsuranceProductService protocolInsuranceProductService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<SettlementReconcileCompanySubjectProductEntity> page = this.page(
                new Query<SettlementReconcileCompanySubjectProductEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }


    /**
     * 根据结算编码获取结算险种列表
     *
     * @param subjectRuleCode 结算科目编码
     * @return
     */
    @Override
    public List<SubjectProductListOut> findSubjectProductListBySubjectRuleCode(String subjectRuleCode) {
        SettlementReconcileCompanySubjectEntity reconcileCompanySubject = Optional.ofNullable(settlementReconcileCompanySubjectService.lambdaQuery()
                .eq(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCode)
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算科目配置不存在")));
        List<SubjectProductListOut> resultList;
        if (StatusEnum.NORMAL.getCode().equals(reconcileCompanySubject.getSubjectScope())) {
            //获取科目指定险种
            resultList = lambdaQuery().eq(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode, subjectRuleCode)
                    .list().stream()
                    .map(m -> BeanUtil.copyProperties(m, SubjectProductListOut.class))
                    .collect(Collectors.toList());
        } else {
            // 选择了通用但是需要排除的险种
            List<String> insuranceProductCodeList = lambdaQuery().eq(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode, subjectRuleCode)
                    .list().stream().map(SettlementReconcileCompanySubjectProductEntity::getInsuranceProductCode)
                    .collect(Collectors.toList());
            resultList = settlementReconcileCompanySubjectService.findSelectInsuranceProductList(reconcileCompanySubject.getReconcileCompanyCode(), reconcileCompanySubject.getReconcileType())
                    .stream()
                    .filter(f -> !insuranceProductCodeList.contains(f.getValue()))
                    .map(m -> {
                        SubjectProductListOut result = new SubjectProductListOut();
                        result.setInsuranceProductCode(m.getValue());
                        result.setInsuranceProductName(m.getLabel());
                        return result;
                    })
                    .collect(Collectors.toList());
        }
        if (!resultList.isEmpty()) {
            List<String> insuranceProductCodes = resultList.stream().map(SubjectProductListOut::getInsuranceProductCode)
                    .distinct().collect(Collectors.toList());

            List<Integer> insuranceProductIds = protocolInsuranceProductService.lambdaQuery()
                .eq(EpProtocolInsuranceProductEntity::getReconcileType, reconcileCompanySubject.getReconcileType())
                .in(EpProtocolInsuranceProductEntity::getInsuranceProductCode, insuranceProductCodes).list().stream()
                .map(EpProtocolInsuranceProductEntity::getId).collect(Collectors.toList());
            if (insuranceProductIds.isEmpty()){
                return Collections.emptyList();
            }
            Map<String, List<EpProtocolInsuranceProductProductEntity>> protocolInsuranceProductProductMap = protocolInsuranceProductProductService.lambdaQuery()
                    .in(EpProtocolInsuranceProductProductEntity::getInsuranceProductId, insuranceProductIds)
                    .list().stream().collect(Collectors.groupingBy(EpProtocolInsuranceProductProductEntity::getInsuranceProductCode));
            resultList.forEach(action -> {
                if (protocolInsuranceProductProductMap.containsKey(action.getInsuranceProductCode())) {
                    action.setProductList(protocolInsuranceProductProductMap.get(action.getInsuranceProductCode()));
                }
            });

        }


        return resultList;
    }


    /**
     * 根据合并编码获取结算险种列表
     *
     * @param reconcileCode 对账单编码
     * @return
     */
    @Override
    public List<SubjectProductListOut> findSubjectProductListByReconcileCode(String reconcileCode) {
        // 获取对账单科目信息
        List<String> subjectRuleCodeList = settlementReconcileSubjectService.lambdaQuery()
                .eq(SettlementReconcileSubjectEntity::getReconcileCode, reconcileCode)
                .list().stream().map(SettlementReconcileSubjectEntity::getSubjectRuleCode)
                .collect(Collectors.toList());
        if (subjectRuleCodeList.isEmpty()){
            return Collections.emptyList();
        }
        List<SubjectProductListOut> resultList = new ArrayList<>();
        List<SettlementReconcileCompanySubjectEntity> companySubjectList = settlementReconcileCompanySubjectService.lambdaQuery()
                .in(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCodeList)
                .list();
        companySubjectList.forEach(action -> {
            List<SubjectProductListOut> subjectProductList = this.findSubjectProductListBySubjectRuleCode(action.getSubjectRuleCode());
            if (CollUtil.isNotEmpty(subjectProductList)) {
                resultList.addAll(subjectProductList);
            }
        });
        //险种信息去重
        return resultList.stream().distinct().collect(Collectors.toList());
    }
}
