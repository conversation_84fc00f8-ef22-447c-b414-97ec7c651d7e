package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementPolicyProductPremListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = -6464638173745006558L;


    @ApiModelProperty(name = "reconcileType", value = "结算类型", example = "portrait")
    @NotNull(message = "结算类型不能为空")
    private Integer reconcileType;

    @ApiModelProperty(name = "batchCode", value = "批单号", example = "portrait")
    private String batchCode;

    @ApiModelProperty(name = "policyNo", value = "保单号", example = "portrait")
    private String policyNo;

}
