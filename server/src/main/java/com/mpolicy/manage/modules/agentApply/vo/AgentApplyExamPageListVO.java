package com.mpolicy.manage.modules.agentApply.vo;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: AgentApplyPageListVO
 * Description: 代理人入职申请分页请求信息
 * date: 2022/11/28 15:47
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyExamPageListVO extends BasePage implements Serializable {

    @ApiModelProperty(value = "题目编码")
    private String code;
}
