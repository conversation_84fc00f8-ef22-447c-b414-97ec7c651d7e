package com.mpolicy.manage.modules.commission.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CommissionBasicPolicyPremExcel implements Serializable {
    private static final long serialVersionUID = 1368916258497736958L;

    /**
     * 保单号
     */
    @Alias("保单号")
    private String policyNo;
    /**
     * 批单号
     */
    @Alias("批单号")
    private String batchCode;

    @Alias("缴费期次")
    private Integer period;

    @Alias("保单年期")
    private Integer year;
    /**
     * 小鲸险种编码
     */
    @Alias("险种编码")
    private String productCode;

    @Alias("险种名称")
    private String productName;
    /**
     * 保费
     */
    @Alias("保费（元）")
    private BigDecimal premium;
    /**
     * 车船税（元）
     */
    @Alias("车船税（元）")
    private BigDecimal vehicleVesselTax;
    /**
     * 车船税费率
     */
    @Alias("车船税费率")
    private BigDecimal vehicleVesselTaxRate;
    /**
     * 基础佣金费率
     */
    @Alias("基础佣金费率")
    private BigDecimal commissionRate;
    @Alias("结算机构")
    private String settlementCompanyName;
}
