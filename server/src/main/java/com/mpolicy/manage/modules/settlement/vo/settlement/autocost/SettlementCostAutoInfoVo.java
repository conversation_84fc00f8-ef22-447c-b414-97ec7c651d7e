package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description 农保员工佣金结算汇总/按结算机构汇总的数据
 * @date 2023/12/18 8:47 下午
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostAutoInfoVo implements Serializable {
    /**
     * 结佣月份
     */
    @ApiModelProperty(value = "结佣月份")
    @ExcelProperty(value = "结佣月份")
    private String settlementMonth;

    /**
     * 结佣机构编码  当全部汇总是为空
     */
    @ApiModelProperty(value = "结佣机构编码")
    private String settlementInstitution;

    /**
     * 结佣机构名称
     */
    @ApiModelProperty(value = "结佣机构名称")
    @ExcelProperty(value = "结算机构")
    private String settlementInstitutionName;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String regionCode;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @ExcelProperty(value = "区域")
    private String regionName;

    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码")
    private String objectOrgCode;

    /**
     * 分支
     */
    @ApiModelProperty(value = "分支")
    @ExcelProperty(value = "分支")
    private String objectOrgName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号")
    private String sendObjectCode;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名")
    private String sendObjectName;

    /**
     * 短险推广费(实际月份：上月)
     */
    @ApiModelProperty(value = "短险推广费(实际月份：上月)")
    @ExcelProperty(value = "短险推广费")
    private BigDecimal shortPromotion;

    /**
     * 长险推广费暂发
     */
    @ApiModelProperty(value = "长险推广费暂发(实际月份：上上月)")
    @ExcelProperty(value = "长险推广费")
    private BigDecimal longPromotion;

    /**
     * 长险推广费补发 LONG_REISSUE_PROMOTION
     */
    @ApiModelProperty(value = "长险推广费补发(实际月份：上上上月)")
    @ExcelProperty(value = "长险推广费补发")
    private BigDecimal longReissuePromotion;

    /**
     * 加佣
     */
    @ApiModelProperty(value = "加佣")
    @ExcelProperty(value = "加佣奖励")
    private BigDecimal addComm;

    /**
     * 督导绩效暂发
     */
    @ApiModelProperty(value = "督导绩效暂发(上月)")
    @ExcelProperty(value = "督导绩效暂发")
    private BigDecimal supervisorPerformance;

    /**
     * 督导绩效补发
     */
    @ApiModelProperty(value = "督导绩效补发(实际月份：上上上月)")
    @ExcelProperty(value = "督导绩效补发")
    private BigDecimal supervisorReissuePerformance;

    /**
     * 津贴
     */
    @ApiModelProperty(value = "津贴")
    @ExcelProperty(value = "PCO津贴")
    private BigDecimal pcoAllowance;

    /**
     * pco绩效暂发
     */
    @ApiModelProperty(value = "pco绩效暂发(上月)")
    @ExcelProperty(value = "PCO绩效暂发")
    private BigDecimal pcoPerformance;

    /**
     * pco绩效补发
     */
    @ApiModelProperty(value = "pco绩效补发")
    @ExcelProperty(value = "PCO绩效补发")
    private BigDecimal pcoReissuePerformance;

    /**
     * 主任激励奖
     */
    @ApiModelProperty(value = "主任激励奖")
    @ExcelProperty(value = "主任激励奖")
    private BigDecimal directorIncentive;

    /**
     * 农机险绩效
     */
    @ApiModelProperty(value = "农机险绩效")
    @ExcelProperty(value = "农机险绩效")
    private BigDecimal agriculturalMachineryPerformance;
    /**
     * 车险后台绩效
     */
    @ApiModelProperty(value = "车险后台绩效")
    @ExcelProperty(value = "车险后台绩效")
    private BigDecimal vehiclePerformance;

    /**
     * 车险车船税推广费
     */
    @ApiModelProperty(value = "车险车船税推广费")
    @ExcelProperty(value = "车险车船税推广费")
    private BigDecimal vehicleVesselTax;
    /**
     * 长险未续回扣
     */
    @ApiModelProperty(value = "长险未续回扣")
    @ExcelProperty(value = "长期险未续回扣")
    private BigDecimal longNotRenewalRebateComm;

    /**
     * 长险复效补发
     */
    @ApiModelProperty(value = "长险复效补发")
    @ExcelProperty(value = "长险复效补发")
    private BigDecimal longRestatementReissueComm;

    /**
     * 整村推进
     */
    @ApiModelProperty(value = "整村推进")
    @ExcelProperty(value = "整村推进奖励")
    private BigDecimal ruralProxy;

    /**
     * 代发区域营销费
     */
    @ApiModelProperty(value = "代发区域营销费")
    @ExcelProperty(value = "代发区域营销费")
    private BigDecimal issuingRegionalMarketingFee;

    @ApiModelProperty(value = "自定义科目")
    List<SettlementDynamicSubjectVO> dynamicSubjectList;

    @ApiModelProperty(value = "个人合计")
    @ExcelProperty(value = "个人合计")
    private BigDecimal personalTotal;

    /**
     * 个人合计
     */

    public BigDecimal getPersonalTotal() {
        BigDecimal total = BigDecimal.ZERO;
        if (shortPromotion != null) {
            total = total.add(shortPromotion);
        }
        if (longPromotion != null) {
            total = total.add(longPromotion);
        }
        if (longReissuePromotion != null) {
            total = total.add(longReissuePromotion);
        }
        if (addComm != null) {
            total = total.add(addComm);
        }
        if (supervisorPerformance != null) {
            total = total.add(supervisorPerformance);
        }
        if (supervisorReissuePerformance != null) {
            total = total.add(supervisorReissuePerformance);
        }
        if (pcoAllowance != null) {
            total = total.add(pcoAllowance);
        }
        if (pcoPerformance != null) {
            total = total.add(pcoPerformance);
        }
        if (pcoReissuePerformance != null) {
            total = total.add(pcoReissuePerformance);
        }
        if (directorIncentive != null) {
            total = total.add(directorIncentive);
        }
        if (agriculturalMachineryPerformance != null) {
            total = total.add(agriculturalMachineryPerformance);
        }
        if (vehiclePerformance != null) {
            total = total.add(vehiclePerformance);
        }
        if (vehicleVesselTax != null) {
            total = total.add(vehicleVesselTax);
        }
        if (longNotRenewalRebateComm != null) {
            total = total.add(longNotRenewalRebateComm);
        }
        if (longRestatementReissueComm != null) {
            total = total.add(longRestatementReissueComm);
        }
        if (ruralProxy != null) {
            total = total.add(ruralProxy);
        }
        if (issuingRegionalMarketingFee != null) {
            total = total.add(issuingRegionalMarketingFee);
        }
        if (CollectionUtils.isNotEmpty(dynamicSubjectList)) {
            total = total.add(
                    dynamicSubjectList.stream().map(SettlementDynamicSubjectVO::getAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO)
            );
        }

        return total;

    }

    /**
     * 结算确认状态
     */
    @ApiModelProperty(value = "结算确认状态 0未确认(未结算)，1已确认")
    @ExcelProperty(value = "结算确认状态")
    private Integer confirmStatus;

    /**
     * 确认结算时间
     */
    @ApiModelProperty(value = "确认结算时间")
    @ExcelProperty(value = "确认结算时间")
    private Date confirmTime;


    public static void main(String[] args) {

        List<SettlementCostAutoInfoVo> employees = IntStream.range(0, 1).mapToObj(
                x -> {
                    SettlementCostAutoInfoVo costVO = new SettlementCostAutoInfoVo();
                    costVO.setSettlementMonth("2023-08");
                    costVO.setSendObjectCode("ZHNX34439");
                    costVO.setSendObjectName("吴锡");
                    costVO.setDynamicSubjectList(
                            IntStream.range(0, 1).mapToObj(
                                    y -> {
                                        SettlementDynamicSubjectVO vo = new SettlementDynamicSubjectVO();
                                        vo.setSubjectCode("334534e");
                                        vo.setSubjectName("ererr");
                                        vo.setAmount(BigDecimal.TEN);
                                        return vo;
                                    }
                            ).collect(Collectors.toList())
                    );

                    return costVO;
                }

        ).collect(Collectors.toList());




    }
}



