package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保司结算对账单关联保司对账单数据
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileCompanyService")
public class SettlementReconcileCompanyServiceImpl extends ServiceImpl<SettlementReconcileCompanyDao, SettlementReconcileCompanyEntity> implements SettlementReconcileCompanyService {

}
