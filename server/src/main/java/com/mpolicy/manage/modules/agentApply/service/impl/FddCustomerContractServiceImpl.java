package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agentApply.dao.FddCustomerContractDao;
import com.mpolicy.manage.modules.agentApply.entity.FddCustomerContractEntity;
import com.mpolicy.manage.modules.agentApply.service.FddCustomerContractService;
import org.springframework.stereotype.Service;


@Service("fddCustomerContractService")
public class FddCustomerContractServiceImpl extends ServiceImpl<FddCustomerContractDao, FddCustomerContractEntity> implements FddCustomerContractService {

}
