package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.protocol.entity.EpProtocolInsuranceProductEntity;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductService;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyProductDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyProductEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanyProductService;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


/**
 * <AUTHOR>
 */
@Service("settlementReconcileCompanyProductService")
public class SettlementReconcileCompanyProductServiceImpl extends ServiceImpl<SettlementReconcileCompanyProductDao, SettlementReconcileCompanyProductEntity> implements SettlementReconcileCompanyProductService {
    @Autowired
    private IEpProtocolInsuranceProductService protocolInsuranceProductService;

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;
    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;

    /**
     * 分页数据
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<SettlementReconcileCompanyProductListOut> findSettlementReconcileCompanyProductList(SettlementReconcileCompanyProductListInput input) {
        IPage<SettlementReconcileCompanyProductListOut> page = baseMapper.findSettlementReconcileCompanyProductList(new Page<SettlementReconcileCompanyProductListOut>(input.getPage(), input.getLimit()), input);
        return new PageUtils(page);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public SettlementReconcileCompanyProductInfoOut findSettlementReconcileCompanyProductInfo(Integer id) {
        SettlementReconcileCompanyProductEntity settlementReconcileCompanyProduct = baseMapper.selectById(id);
        SettlementReconcileCompanyProductInfoOut result = BeanUtil.copyProperties(settlementReconcileCompanyProduct, SettlementReconcileCompanyProductInfoOut.class);
        return result;
    }


    /**
     * 新增
     *
     * @param input
     */
    @Override
    public void saveSettlementReconcileCompanyProduct(SettlementReconcileCompanyProductSaveInput input) {
        //判断这个保司下的产品是否存在
        SettlementReconcileCompanyProductEntity settlementReconcileCompanyProduct = lambdaQuery().eq(SettlementReconcileCompanyProductEntity::getCompanyCode, input.getCompanyCode())
                .eq(SettlementReconcileCompanyProductEntity::getCompanyProductName, input.getCompanyProductName())
                .one();
        if (settlementReconcileCompanyProduct != null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("当前保司存在这个产品名称了,请勿重复添加"));
        }
        //数据拷贝
        SettlementReconcileCompanyProductEntity reconcileCompanyProduct = BeanUtil.copyProperties(input, SettlementReconcileCompanyProductEntity.class);

        InsuranceCompanyEntity insuranceCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                        .eq(InsuranceCompanyEntity::getCompanyCode, input.getCompanyCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("机构信息不存在")));
        reconcileCompanyProduct.setCompanyName(insuranceCompany.getCompanyName());

        EpProtocolInsuranceProductEntity protocolInsuranceProduct = Optional.ofNullable(protocolInsuranceProductService.lambdaQuery()
                        .eq(EpProtocolInsuranceProductEntity::getInsuranceProductCode, input.getInsuranceProductCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("协议产品信息不存在")));
        reconcileCompanyProduct.setInsuranceProductName(protocolInsuranceProduct.getInsuranceProductName());

        InsuranceProductInfoEntity insuranceProduct = Optional.ofNullable(insuranceProductInfoService.lambdaQuery()
                        .eq(InsuranceProductInfoEntity::getProductCode, input.getProductCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("小鲸险种信息不存在")));
        reconcileCompanyProduct.setProductName(insuranceProduct.getProductName());


        // 添加数据
        save(reconcileCompanyProduct);
    }

    /**
     * 修改
     *
     * @param input
     */
    @Override
    public void updateSettlementReconcileCompanyProduct(SettlementReconcileCompanyProductUpdateInput input) {
        SettlementReconcileCompanyProductEntity info = Optional.ofNullable(baseMapper.selectById(input.getId()))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("数据不存在")));
        if (!info.getCompanyCode().equals(info.getCompanyCode()) || !info.getCompanyProductName().equals(input.getCompanyProductName())) {
            SettlementReconcileCompanyProductEntity settlementReconcileCompanyProduct = lambdaQuery()
                    .eq(SettlementReconcileCompanyProductEntity::getCompanyCode, input.getCompanyCode())
                    .eq(SettlementReconcileCompanyProductEntity::getCompanyProductName, input.getCompanyProductName())
                    .one();
            if (settlementReconcileCompanyProduct != null) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("当前保司存在这个产品名称了,请勿重复添加"));
            }
        }
        //数据拷贝
        SettlementReconcileCompanyProductEntity reconcileCompanyProduct = BeanUtil.copyProperties(input, SettlementReconcileCompanyProductEntity.class);
        InsuranceCompanyEntity insuranceCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                        .eq(InsuranceCompanyEntity::getCompanyCode, input.getCompanyCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("机构信息不存在")));
        reconcileCompanyProduct.setCompanyName(insuranceCompany.getCompanyName());

        EpProtocolInsuranceProductEntity protocolInsuranceProduct = Optional.ofNullable(protocolInsuranceProductService.lambdaQuery()
                        .eq(EpProtocolInsuranceProductEntity::getInsuranceProductCode, input.getInsuranceProductCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("协议产品信息不存在")));
        reconcileCompanyProduct.setInsuranceProductName(protocolInsuranceProduct.getInsuranceProductName());

        InsuranceProductInfoEntity insuranceProduct = Optional.ofNullable(insuranceProductInfoService.lambdaQuery()
                        .eq(InsuranceProductInfoEntity::getProductCode, input.getProductCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("小鲸险种信息不存在")));
        reconcileCompanyProduct.setProductName(insuranceProduct.getProductName());

        // 更新数据
        updateById(reconcileCompanyProduct);
    }
}
