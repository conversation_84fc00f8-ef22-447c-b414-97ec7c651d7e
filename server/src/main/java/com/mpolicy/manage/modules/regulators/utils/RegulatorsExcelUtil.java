package com.mpolicy.manage.modules.regulators.utils;

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.excel.metadata.Sheet;
import com.mpolicy.common.utils.excel.ExcelListener;
import com.mpolicy.manage.modules.regulators.service.report.listener.ReportObjectEventListener;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 监管报备 报备报告xls读取扩展
 *
 * <AUTHOR>
 * @date 2022-01-21 16:00
 */
public class RegulatorsExcelUtil {

    /**
     * <p>
     * 读取 Excel(指定 sheet 和 headLineNum) [适用数据量大，自定义Listener进行分批控制]
     * </p>
     *
     * @param filePath      读取的xls文件路径
     * @param clazz         泛型类型
     * @param excelListener 自定义监听器
     */
    public static <T, R> List<R> readExcelByList(String filePath, int sheetNo, int headLineNum, ReportObjectEventListener<T, R> excelListener) {
        ExcelReader reader = getReader(filePath, excelListener);
        if (reader == null) {
            return null;
        }
        reader.read(new Sheet(sheetNo, headLineNum));
        return excelListener.getReadData();
    }

    /**
     * 返回 ExcelReader
     *
     * @param filePath      需要解析的 Excel 文件
     * @param excelListener new ExcelListener()
     */
    private static ExcelReader getReader(String filePath,
                                         AnalysisEventListener excelListener) {
        InputStream inputStream;
        try {
            inputStream = new BufferedInputStream(new FileInputStream(filePath));
            return new ExcelReader(inputStream, null, excelListener, false);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <p>
     * 泛型注解读取 Excel(多个 sheet) [适用数据量不大，直接读取整个结果到List<T>]
     * </p>
     *
     * @param filePath 文件
     * @param clazz    clazz
     * @param trim 是否trim
     * @return Excel 数据 list
     */
    public static <T extends BaseRowModel> List<T> readExcel(String filePath, Class<T> clazz, boolean trim) {
        ExcelListener<T> excelListener = new ExcelListener<>();
        ExcelReader reader = getReader(filePath, excelListener);
        if (reader == null) {
            return null;
        }
        for (Sheet sheet : reader.getSheets()) {
            if (clazz != null) {
                sheet.setClazz(clazz);
            }
            sheet.setHeadLineMun(1);
            reader.read(sheet);
        }
        return excelListener.getDatas();
    }

    /**
     * 返回 ExcelReader
     *
     * @param filePath      需要解析的 Excel 文件
     * @param excelListener new ExcelListener()
     * @param trim 是否trim
     */
    private static ExcelReader getReader(String filePath,
                                         AnalysisEventListener excelListener, boolean trim) {
        InputStream inputStream;
        try {
            inputStream = new BufferedInputStream(new FileInputStream(filePath));
            return new ExcelReader(inputStream, null, excelListener, false);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
