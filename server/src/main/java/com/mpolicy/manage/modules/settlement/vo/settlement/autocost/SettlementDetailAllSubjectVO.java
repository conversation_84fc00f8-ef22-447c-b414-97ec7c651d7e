package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/2/22 15:07
 * @Version 1.0
 */
@Data
public class SettlementDetailAllSubjectVO {

    @ApiModelProperty(value = "父级科目")
    private String parentSubjectCode;

    @ApiModelProperty(value = "科目编码")
    private String subjectCode;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    @ApiModelProperty(value = "数据类型")
    private String costDataType;

}
