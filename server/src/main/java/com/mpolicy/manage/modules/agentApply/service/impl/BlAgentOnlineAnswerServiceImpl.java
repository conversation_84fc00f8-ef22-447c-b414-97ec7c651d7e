package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineAnswerDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineAnswerEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineAnswerService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("blAgentOnlineAnswerService")
public class BlAgentOnlineAnswerServiceImpl extends ServiceImpl<BlAgentOnlineAnswerDao, BlAgentOnlineAnswerEntity> implements BlAgentOnlineAnswerService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<BlAgentOnlineAnswerEntity> page = this.page(
                new Query<BlAgentOnlineAnswerEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
