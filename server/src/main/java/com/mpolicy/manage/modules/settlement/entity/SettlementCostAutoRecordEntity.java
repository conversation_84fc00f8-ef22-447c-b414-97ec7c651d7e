package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/1/26 11:28
 * @Version 1.0
 */

@Data
public class SettlementCostAutoRecordEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 自动结算支出编码
     */
    private String autoCostCode;
    /**
     * 计算周期
     */
    private String costSettlementCycle;
    /**
     * 计算时间
     */
    private Date costSettlementTime;
    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 方案名称
     */
    private String programmeName;
    /**
     * 科目编码
     */
    private String subjectCode;
    /**
     * 科目名称
     */
    private String subjectName;
    /**
     * 发放对象类型
     */
    private String sendObjectType;
    /**
     * 发放对象编码
     */
    private String sendObjectCode;
    /**
     * 发放对象名称
     */
    private String sendObjectName;
    /**
     * 发放对象机构编码
     */
    private String objectOrgCode;
    /**
     * 发放对象机构名称
     */
    private String objectOrgName;
    /**
     * 单据编号
     */
    private String documentCode;
    /**
     * 结算机构编码
     */
    private String settlementInstitution;
    /**
     * 结算机构名称
     */
    private String settlementInstitutionName;
    /**
     * 记账日期
     */
    private Date settlementDate;
    /**
     * 明细类型;policy 保单维度，product 险种维度，policy_product保单险种维度
     */
    private String costDataType;
    /**
     * 保费
     */
    private BigDecimal premium;
    /**
     * 费用类型：推广费，津贴、激励，整村推荐推广费，代发区域营销费
     */
    private String amountType;
    /**
     * 费用
     */
    private BigDecimal amount;
    /**
     * 提成比例
     */
    private BigDecimal commissionRate;
    /**
     * 自动结算佣金
     */
    private BigDecimal commissionAmount;
    /**
     * 发放比例 0-100
     */
    private BigDecimal grantRate;
    /**
     * 发放金额
     */
    private BigDecimal grantAmount;
    /**
     * 续期率
     */
    private BigDecimal renewalRate;
    /**
     * 继续率月份[yyyy-MM]
     */
    private String bizMonth;
    /**
     * 增长系数
     */
    private BigDecimal growthFactor;
    /**
     * pco等级
     */
    private String pcoLevel;
    /**
     * 注册用户数
     */
    private Integer registeredUsers;
    /**
     * 业务数据类型
     */
    private String businessDataType;
    /**
     * 源单据编号
     */
    private String sourceDocumentCode;
    /**
     * 源自动结算支出编码
     */
    private String sourceAutoCostCode;
    /**
     * 发放科目编码
     */
    private String sendSubjectCode;
    /**
     * 发放科目名称
     */
    private String sendSubjectName;
    /**
     * 是否动态科目
     */
    private Integer dynamicFlag;
    /**
     * 是否删除;0有效-1删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;


}
