package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算交互事件受理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-05 14:34:54
 */

@Data
public class SettlementEventJobListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 业务编码
     */
    private String eventBusinessCode;
    /**
     * push事件唯一凭证编码
     */
        private String pushEventCode;
    /**
     * 保单中心合同
     */
    private String contractCode;
    /**
     * 事件来源;保单中心、协议管理、车险佣金
     */
    private String eventSource;
    /**
     * 签约类型：1小鲸，2非小鲸
     */
    private Integer businessSignType;
    /**
     * 事件状态;0代处理1处理中2处理完成
     */
    private Integer eventStatus;
    /**
     * 收入处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer incomeEventStatus;
    /**
     * 支出处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer costEventStatus;
    /**
     * 混合单收入处理状态：0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer contractIncomeEventStatus;

}
