package com.mpolicy.manage.modules.endorsement.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.endorsement.dao.EndorsementOrderInfoDao;
import com.mpolicy.manage.modules.endorsement.entity.EndorsementOrderInfoEntity;
import com.mpolicy.manage.modules.endorsement.service.EndorsementOrderInfoService;
import com.mpolicy.manage.modules.endorsement.vo.EndorsementOrderInfo;
import com.mpolicy.manage.modules.endorsement.vo.EndorsementRecallOut;
import com.mpolicy.manage.modules.insure.enums.EndorsementTypeEnum;
import com.mpolicy.manage.modules.insure.enums.InsureOrderStatusEnum;
import com.mpolicy.manage.modules.order.service.TraceSerialRecallDataService;
import com.mpolicy.manage.modules.sys.util.DicOperatingUtils;
import com.mpolicy.order.client.InsureOrderClient;
import com.mpolicy.order.common.endorsement.PolicyEndorsementOrderData;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Service("endorsementOrderInfoService")
@Slf4j
public class EndorsementOrderInfoServiceImpl extends ServiceImpl<EndorsementOrderInfoDao, EndorsementOrderInfoEntity> implements EndorsementOrderInfoService {

    @Autowired
    private InsureOrderClient insureOrderClient;

    @Autowired
    private TraceSerialRecallDataService traceSerialRecallDataService;

    @Override
    public PageUtils<EndorsementOrderInfo> queryPage(Map<String, Object> params) {

        String referrerName = RequestUtils.objectValueToString(params, "referrerName");
        String holderName = RequestUtils.objectValueToString(params, "holderName");
        String companyCode = RequestUtils.objectValueToString(params, "companyCode");
        String startDate = RequestUtils.objectValueToString(params, "startDate");
        String endDate = RequestUtils.objectValueToString(params, "endDate");
        String endorsementCode = RequestUtils.objectValueToString(params, "endorsementCode");
        String commodityCode = RequestUtils.objectValueToString(params, "commodityCode");
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        String policyCode = RequestUtils.objectValueToString(params, "policyCode");
        String endorsementType = RequestUtils.objectValueToString(params, "endorsementType");
        Integer endorsementStatus = RequestUtils.objectValueToInteger(params, "endorsementStatus");

        IPage<EndorsementOrderInfoEntity> page = this.page(
                new Query<EndorsementOrderInfoEntity>().getPage(params),
                new LambdaQueryWrapper<EndorsementOrderInfoEntity>()
                        .ne(EndorsementOrderInfoEntity::getEndorsementStatus, 11)
                        .eq(StringUtils.isNotBlank(endorsementType), EndorsementOrderInfoEntity::getEndorsementType, endorsementType)
                        .eq(endorsementStatus != null, EndorsementOrderInfoEntity::getEndorsementStatus, endorsementStatus)
                        .eq(StringUtils.isNotBlank(policyCode), EndorsementOrderInfoEntity::getPolicyCode, policyCode)
                        .eq(StringUtils.isNotBlank(policyNo), EndorsementOrderInfoEntity::getSourcePolicyNo, policyNo)
                        .eq(StringUtils.isNotBlank(commodityCode), EndorsementOrderInfoEntity::getCommodityCode, commodityCode)
                        .eq(StringUtils.isNotBlank(endorsementCode), EndorsementOrderInfoEntity::getEndorsementCode, endorsementCode)
                        .eq(StringUtils.isNotBlank(companyCode), EndorsementOrderInfoEntity::getCompanyCode, companyCode)
                        .like(StringUtils.isNotBlank(holderName), EndorsementOrderInfoEntity::getHolderName, holderName)
                        .like(StringUtils.isNotBlank(referrerName), EndorsementOrderInfoEntity::getReferrerName, referrerName)
                        .apply(StringUtils.isNotBlank(startDate), "date_format(create_time,'%Y-%m-%d') >= {0}", startDate)
                        .apply(StringUtils.isNotBlank(endDate), "date_format(create_time,'%Y-%m-%d') <= {0}", endDate)
                        .orderByDesc(EndorsementOrderInfoEntity::getUpdateTime)
        );
        JSONArray cacheDicList = JSON.parseArray(ConstantCacheHelper.getValue("sysDictionaryMapper_dict", "[]"));
        if (CollectionUtils.isEmpty(cacheDicList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("投保字典配置缓存缺失"));
        }
        // 构建返回的vo list
        List<EndorsementOrderInfo> result = new ArrayList<>();
        page.getRecords().forEach(x -> {
            EndorsementOrderInfo bean = new EndorsementOrderInfo();
            BeanUtils.copyProperties(x, bean);
            // 支付时间
            bean.setPayTime(DateUtil.formatDate(x.getPayTime()));
            //保单号
            bean.setPolicyNo(x.getSourcePolicyNo());
            // 创建时间
            bean.setCreateTime(DateUtil.formatDate(x.getCreateTime()));
            //生效日期（增员）
            bean.setAddEffectiveDate(DateUtil.formatDate(x.getAddEffectiveDate()));
            //生效日期（减员）
            bean.setReduceEffectiveDate(DateUtil.formatDate(x.getReduceEffectiveDate()));
            // 保全类型
            bean.setEndorsementTypeDesc(EndorsementTypeEnum.decode(x.getEndorsementType()).getName());
            // 投保状态描述
            bean.setEndorsementStatusDesc(InsureOrderStatusEnum.decode(x.getEndorsementStatus()).getName());
            // 证件类型
            result.add(bean);
        });
        // 重新构建分页返回对象
        return new PageUtils(result, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public PolicyEndorsementOrderData queryEndorsementDetail(String endorsementCode) {
        // 调用投保中心服务的feign 获取保全订单详情
        Result<PolicyEndorsementOrderData> endorsementDetail = insureOrderClient.endorsementDetail(endorsementCode, false, false);
        log.info("获取投保订单详情，请求保全唯一编号=[{}],响应结果=[{}]", endorsementCode, JSON.toJSONString(endorsementDetail));
        if (!endorsementDetail.isSuccess()) {
            throw new GlobalException(endorsementDetail);
        }
        return endorsementDetail.getData();
    }

    @Override
    public EndorsementRecallOut queryEndorsementRecallData(String endorsementCode) {
        // 1-1 【获取】投保订单数据不存在
        EndorsementOrderInfoEntity orderInfo = Optional.ofNullable(
                lambdaQuery()
                        .eq(EndorsementOrderInfoEntity::getEndorsementCode, endorsementCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保全订单数据不存在")));
        EndorsementRecallOut result = new EndorsementRecallOut();
        BeanUtils.copyProperties(orderInfo, result);
        // 保全类型
        result.setEndorsementTypeDesc(EndorsementTypeEnum.decode(orderInfo.getEndorsementType()).getName());
        // 投保状态描述
        result.setEndorsementStatusDesc(InsureOrderStatusEnum.decode(orderInfo.getEndorsementStatus()).getName());
        // 创建时间
        result.setCreateTime(DateUtil.formatDate(orderInfo.getCreateTime()));
        result.setRecallList(traceSerialRecallDataService.getPolicyTraceSerialRecallData(endorsementCode));
        return result;
    }
}
