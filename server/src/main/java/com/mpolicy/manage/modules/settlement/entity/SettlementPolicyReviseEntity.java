package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "settlement_policy_revise")
public class SettlementPolicyReviseEntity implements Serializable {

    private static final long serialVersionUID = -8674812886478149580L;
    @TableId
    private Integer id; // 主键id

    private String settlementCode; // 结算编码

    private String beforeData; // 修改前数据

    private String afterData; // 修改后数据
    @TableField(fill = FieldFill.INSERT)
    private String createUser; // 创建人
    @TableField(fill = FieldFill.INSERT)
    private Date createTime; // 创建时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser; // 更新人
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime; // 更新时间
}