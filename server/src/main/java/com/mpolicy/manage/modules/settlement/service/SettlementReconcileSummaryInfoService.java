package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileSummaryInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.summary.SettlementReconcileSummaryInfo;

import java.util.Map;

/**
 * 保司结算对账单汇总申请单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
public interface SettlementReconcileSummaryInfoService extends IService<SettlementReconcileSummaryInfoEntity> {

    /**
     * 分页查询结算汇总单信息列表
     *
     * @param params:
     * @return : com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.confirm.SettlementReconcileConfirmInfo>
     * <AUTHOR>
     * @date 2023/5/26 14:07
     */
    PageUtils<SettlementReconcileSummaryInfo> querySettlementSummaryInfoServiceList(Map<String, Object> params);
    /**
     * 删除汇总单
     *
     * <AUTHOR>
     * @date 2023/6/2 10:26
     * @param summaryCode:
     * @return : void
     */
    void deleteSettlementSummaryInfo(String summaryCode);
}

