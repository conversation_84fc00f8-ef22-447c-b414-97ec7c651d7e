package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileContractInfoService;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 结算合约配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-28 14:21:38
 */
@RestController
@RequestMapping("settlement/reconcile/contract/info")
@Api(tags = "结算合约配置表")
public class SettlementReconcileContractInfoController {

    @Autowired
    private SettlementReconcileContractInfoService settlementReconcileContractInfoService;


    @PostMapping("list")
    @RequiresPermissions("settlement:reconcile-contract-info:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<SettlementReconcileContractInfoListOut>> list(@RequestBody @Valid SettlementReconcileContractInfoListInput input) {
        PageUtils<SettlementReconcileContractInfoListOut> page = settlementReconcileContractInfoService.findSettlementReconcileContractInfoList(input);
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @RequiresPermissions("settlement:reconcile-contract-info:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<SettlementReconcileContractInfoInfoOut> info(@PathVariable(value = "id", required = false)
                                                               @NotNull(message = "操作的数据id不能为空")
                                                               @ApiParam(value = "详情ID") Integer id) {
        SettlementReconcileContractInfoInfoOut settlementReconcileContractInfo = settlementReconcileContractInfoService.findSettlementReconcileContractInfoById(id);
        return Result.success(settlementReconcileContractInfo);
    }

    @SysLog("保存结算合约配置表数据")
    @PostMapping("save")
    @RequiresPermissions("settlement:reconcile-contract-info:save")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid SettlementReconcileContractInfoSaveInput input) {
        settlementReconcileContractInfoService.saveSettlementReconcileContractInfo(input);
        return Result.success();
    }

    @SysLog("修改结算合约配置表数据")
    @PostMapping("update")
    @RequiresPermissions("settlement:reconcile-contract-info:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid SettlementReconcileContractInfoUpdateInput input) {
        settlementReconcileContractInfoService.updateSettlementReconcileContractInfoById(input);
        return Result.success();
    }

    @SysLog("删除结算合约配置表信息")
    @PostMapping("delete")
    @RequiresPermissions("settlement:reconcile-contract-info:delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false)
                         @NotEmpty(message = "删除的数据ids不能为空")
                         @ApiParam(value = "批量删除的ID") Integer[] ids) {
        settlementReconcileContractInfoService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }
}
