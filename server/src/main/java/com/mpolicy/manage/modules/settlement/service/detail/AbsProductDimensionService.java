package com.mpolicy.manage.modules.settlement.service.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostAutoInfoDao;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailPolicyDimensionVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailProductDimensionVO;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2024/2/2 17:33
 * @Version 1.0
 */

public abstract class AbsProductDimensionService<S> extends AbsCommonParamsSettlementDetail<S, SettlementDetailProductDimensionVO>{

    @Autowired
    protected SettlementCostAutoInfoDao costAutoInfoDao;

    @Override
    public IPage<SettlementDetailProductDimensionVO> queryDetail(PageSettlementDetailParams p) {
        return costAutoInfoDao.pageProductDimension(new Page<>(p.getPage(), p.getLimit()), p);
    }

}
