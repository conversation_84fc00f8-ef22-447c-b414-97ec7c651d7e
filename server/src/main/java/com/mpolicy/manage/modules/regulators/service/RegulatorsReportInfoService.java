package com.mpolicy.manage.modules.regulators.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportList;

import java.util.Map;

/**
 * 监管报备月度报备主表
 *
 * <AUTHOR>
 * @date 2022-01-20 14:34:19
 */
public interface RegulatorsReportInfoService extends IService<RegulatorsReportInfoEntity> {

    /**
     * <p>
     * 协议管理列表分页查询
     * </p>
     *
     * @param paramMap 查询条件
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.regulators.vo.RegulatorsReportList>
     * <AUTHOR>
     * @since 2022/1/20
     */
    PageUtils<RegulatorsReportList> queryRegulatorsReportList(Map<String, Object> paramMap);
}

