package com.mpolicy.manage.modules.settlement.vo.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ReconcileInvoiceTrackListOut implements Serializable {
    private static final long serialVersionUID = -2238106408496485912L;

    /**
     * 操作人
     */
    private String operateUser;
    /**
     * 操作描述
     */
    private String operateDesc;
    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
