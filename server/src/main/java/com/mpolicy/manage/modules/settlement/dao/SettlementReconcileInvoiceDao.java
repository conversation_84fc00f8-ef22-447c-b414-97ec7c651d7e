package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceEntity;
import com.mpolicy.manage.modules.settlement.vo.invoice.SettlementReconcileInvoiceListInput;
import com.mpolicy.manage.modules.settlement.vo.invoice.SettlementReconcileInvoiceListOut;
import org.apache.ibatis.annotations.Param;

/**
 * 对账单发票信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */
public interface SettlementReconcileInvoiceDao extends BaseMapper<SettlementReconcileInvoiceEntity> {


    /**
     * 获取对账单发票信息列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<SettlementReconcileInvoiceListOut> findSettlementReconcileInvoiceList(@Param("page") Page<SettlementReconcileInvoiceListOut> page, @Param("input") SettlementReconcileInvoiceListInput input);

}
