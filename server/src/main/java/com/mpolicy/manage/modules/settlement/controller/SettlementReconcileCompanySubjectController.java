package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectService;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanySubjectInfo;
import com.mpolicy.manage.modules.settlement.vo.ReconcileCompanySubjectListOut;
import com.mpolicy.manage.modules.settlement.vo.SaveReconcileCompanySubject;
import com.mpolicy.manage.modules.settlement.vo.UpdateReconcileCompanySubject;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;


/**
 * 结算保司配置对应规则科目表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-22 20:45:57
 */
@Api(tags = "结算保司配置对应规则科目表")
@Validated
@RestController
@RequestMapping("settlement/reconcile/company/subject")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SettlementReconcileCompanySubjectController {

    private final SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取结算保司配置对应规则科目表列表", notes = "获取结算保司配置对应规则科目表列表")
    @GetMapping("/list/{reconcileCompanyCode}")
    public Result<List<ReconcileCompanySubjectListOut>> findReconcileCompanySubjectList(@PathVariable(required = false)
                                                                                        @NotBlank(message = "结算保司编码不能为空") String reconcileCompanyCode,
                                                                                        @RequestParam(value = "reconcileType", required = false)
                                                                                        @NotNull(message = "结算类型不能为空") Integer reconcileType,
                                                                                        @RequestParam(value = "mergeCode", required = false)
                                                                                                    String mergeCode) {
        List<ReconcileCompanySubjectListOut> list = settlementReconcileCompanySubjectService.findReconcileCompanySubjectList(reconcileCompanyCode, reconcileType,mergeCode);
        return Result.success(list);
    }

    /**
     * 信息
     */
    @GetMapping("/info/{id}")
    public Result<ReconcileCompanySubjectInfo> findReconcileCompanySubjectInfo(@PathVariable("id") Integer id) {
        ReconcileCompanySubjectInfo settlementReconcileCompanySubject = settlementReconcileCompanySubjectService.findReconcileCompanySubjectInfoById(id);
        return Result.success(settlementReconcileCompanySubject);
    }


    /**
     * 新增结算保司科目信息
     *
     * @param params
     * @return
     */
    @PostMapping("save")
    @SysLog(value = "新增结算保司科目信息")
    public Result save(@RequestBody @Valid SaveReconcileCompanySubject params) {
        settlementReconcileCompanySubjectService.saveReconcileCompanySubject(params);
        return Result.success();
    }

    /**
     * 修改结算保司科目信息
     *
     * @param params
     * @return
     */
    @PostMapping("update")
    @SysLog(value = "修改结算保司科目信息")
    public Result update(@RequestBody @Valid UpdateReconcileCompanySubject params) {
        settlementReconcileCompanySubjectService.updateReconcileCompanySubject(params);
        return Result.success();
    }

    @PostMapping("delete/{id}")
    @SysLog(value = "删除结算保司科目信息")
    public Result delete(@PathVariable(value = "id", required = false) @NotNull(message = "ID不能为空") Integer id) {
        settlementReconcileCompanySubjectService.deleteReconcileCompanySubject(id);
        return Result.success();
    }

    @PostMapping("mergeSubject")
    @SysLog(value = "合并结算保司科目信息")
    public Result mergeSubject(@RequestBody Integer[] ids) {
        settlementReconcileCompanySubjectService.mergeSubject(Arrays.asList(ids));
        return Result.success();
    }

    @PostMapping("splitSubject")
    @SysLog(value = "拆分结算保司科目信息")
    public Result splitSubject(@RequestBody Integer[] ids) {
        settlementReconcileCompanySubjectService.splitSubject(Arrays.asList(ids));
        return Result.success();
    }

    /**
     * 获取指定保司产品列表
     *
     * @param reconcileCompanyCode 结算保司编码
     * @return
     */
    @GetMapping("findSelectInsuranceProductList/{reconcileCompanyCode}")
    public Result<List<SelectOut>> findSelectInsuranceProductList(@PathVariable(required = false)
                                                                  @NotBlank(message = "结算保司编码不能为空") String reconcileCompanyCode,
                                                                  @RequestParam(required = false)@NotNull(message = "结算类型不能为空") Integer reconcileType) {
        List<SelectOut> list = settlementReconcileCompanySubjectService.findSelectInsuranceProductList(reconcileCompanyCode,reconcileType);
        return Result.success(list);
    }
}
