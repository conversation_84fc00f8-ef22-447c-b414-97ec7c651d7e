package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceTrackService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开票轨迹
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */
@RestController
@RequestMapping("settlement//reconcile/invoice/track")
@Api(tags = "开票轨迹")
public class SettlementReconcileInvoiceTrackController {

    @Autowired
    private SettlementReconcileInvoiceTrackService settlementReconcileInvoiceTrackService;

}
