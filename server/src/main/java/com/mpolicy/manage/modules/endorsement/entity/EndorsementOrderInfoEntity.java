package com.mpolicy.manage.modules.endorsement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保全订单信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:10:55
 */
@TableName("endorsement_order_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EndorsementOrderInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 保全编号
	 */
	private String endorsementCode;
	/**
	 * 原投保单号
	 */
	private String sourcePolicyNo;
	/**
	 * 原投订单号
	 */
	private String sourceInsureOrderCode;
	/**
	 * 组合类型，个险，财险，团险
	 */
	private String portfolioGroup;
	/**
	 * 保全类型，增减员
	 */
	private String endorsementType;
	/**
	 * 保全模式 批改
	 */
	private String insureMode;
	/**
	 * 所属用户编码
	 */
	private String userNo;
	/**
	 * 所属用户名称
	 */
	private String userName;
	/**
	 * 用户微信openId
	 */
	private String openId;
	/**
	 * 业务平台预投保号
	 */
	private String insAdvancePolicyCode;
	/**
	 * 保险业务平台核保结果信息
	 */
	private String insUnderwriteMsg;
	/**
	 * 承保时间
	 */
	private Date underwriteTime;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 承保保单号
	 */
	private String policyCode;
	/**
	 * 保司原电子保单地址
	 */
	private String companyPolicyUrl;
	/**
	 * 电子保单地址 电子保单地址
	 */
	private String policyUrl;
	/**
	 * 支付模式编码
	 */
	private String payType;
	/**
	 * 投保支付号
	 */
	private String payNo;
	/**
	 * 投保支付时间
	 */
	private Date payTime;
	/**
	 * 保司编码
	 */
	private String companyCode;
	/**
	 * 保司名称
	 */
	private String companyName;
	/**
	 * 保司logo
	 */
	private String companyLogo;
	/**
	 * 保单类型
	 */
	private String policyType;
	/**
	 * 组合编码
	 */
	private String portfolioCode;
	/**
	 * 组合名称
	 */
	private String portfolioName;
	/**
	 * 商品编码
	 */
	private String commodityCode;
	/**
	 * 商品名称
	 */
	private String commodityName;
	/**
	 * 商品销售模式
	 */
	private String commoditySellMode;
	/**
	 * 保费试算支付保费
	 */
	private BigDecimal premium;
	/**
	 * 实际支付金额
	 */
	private BigDecimal payPremium;
	/**
	 * 生效日期
	 */
	private Date effectiveDate;
	/**
	 * 终止时间
	 */
	private String invalidDate;
	/**
	 * 实际增员生效日期
	 */
	private Date addEffectiveDate;
	/**
	 * 实际减员生效日期
	 */
	private Date reduceEffectiveDate;
	/**
	 * 管理机构编码
	 */
	private String orgCode;
	/**
	 * 管理机构名称
	 */
	private String orgName;
	/**
	 * 投保销售渠道编码
	 */
	private String channelCode;
	/**
	 * 投保销售渠道名称
	 */
	private String channelName;
	/**
	 * 投保用户所属渠道编码
	 */
	private String userChannelCode;
	/**
	 * 用户渠道名称
	 */
	private String userChannelName;
	/**
	 * 投保代理人类型
	 */
	private String agentType;
	/**
	 * 代理人编码
	 */
	private String agentCode;
	/**
	 * 代理人名称
	 */
	private String agentName;
	/**
	 * 推荐人编码
	 */
	private String referrerCode;
	/**
	 * 推荐人名称
	 */
	private String referrerName;
	/**
	 * 推荐人证件号码
	 */
	private String referrerIdNo;
	/**
	 * 农保工号
	 */
	private String manageWno;
	/**
	 * 客户子渠道信息
	 */
	private String childChannelCode;
	/**
	 * 客户来源快照
	 */
	private String customerSource;
	/**
	 * 渠道权限
	 */
	private String branchCode;
	/**
	 * 渠道机构权限标识
	 */
	private String channelBranchCode;
	/**
	 * 操作终端
	 */
	private String terminalType;
	/**
	 * 自定义参数
	 */
	private String customParam;
	/**
	 * 保全操作ip
	 */
	private String endorsementIp;
	/**
	 * 保全/核保时间
	 */
	private Date endorsementTime;
	/**
	 * 保全完成用时秒
	 */
	private Integer endorsementTimeSecond;
	/**
	 * 保全激活状态 0未激活1已激活
	 */
	private Integer insureActivateStatus;
	/**
	 * 保全状态
	 */
	private Integer endorsementStatus;
	/**
	 * 投保人姓名
	 */
	private String holderName;
	/**
	 * 投保人证件号码
	 */
	private String holderIdNo;
	/**
	 * 投保人手机号
	 */
	private String holderMobile;
	/**
	 * 投保人证件类型
	 */
	private String holderCertiCode;
	/**
	 * 统一用户id
	 */
	private String oneId;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private long revision;
}
