package com.mpolicy.manage.modules.insurance.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.annotation.SysDbLog;
import com.mpolicy.manage.modules.insurance.service.InsuranceGroupPlanInfoService;
import com.mpolicy.manage.modules.insurance.vo.GroupPlanBean;
import com.mpolicy.manage.modules.insurance.vo.GroupPlanExportVo;
import com.mpolicy.manage.modules.insurance.vo.GroupPlanProductInfo;
import com.mpolicy.manage.modules.insurance.vo.GroupPlanVo;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/10/31 10:26
 */
@RestController
@RequestMapping("insurance/group/plan")
@Api(tags = "团险计划")
@Slf4j
public class ProductGroupPlanController {

    @Resource
    private InsuranceGroupPlanInfoService insuranceGroupPlanInfoService;

    @ApiOperation(value = "获取团险计划列表", notes = "获取团险计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "planCode", dataType = "String", value = "计划编码"),
            @ApiImplicitParam(paramType = "query", name = "planName", dataType = "String", value = "计划名称"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码"),
            @ApiImplicitParam(paramType = "query", name = "productName", dataType = "String", value = "险种名称"),
            @ApiImplicitParam(paramType = "query", name = "startCreateTime", dataType = "String", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", name = "endCreateTime", dataType = "String", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"product:groupPlan:list"})
    public Result<PageUtils<GroupPlanBean>> list(@RequestParam Map<String, Object> params) {
        PageUtils<GroupPlanBean> page = insuranceGroupPlanInfoService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 获取有险种的保司
     */
    @ApiOperation(value = "获取有险种的保司", notes = "获取有险种的保司")
    @GetMapping("/getCompanyList")
    public Result<List<JSONObject>> getCompanyList() {
        List<JSONObject> list = insuranceGroupPlanInfoService.getCompanyList();
        return Result.success(list);
    }

    /**
     * 获取保司下的险种
     */
    @ApiOperation(value = "获取保司下的险种", notes = "获取保司下的险种")
    @PostMapping("/getProductList")
    public Result<List<JSONObject>> getProductList(@RequestParam(value = "companyCode") @ApiParam(name = "companyCode", required = true, value = "保司编码") String companyCode) {
        List<JSONObject> list = insuranceGroupPlanInfoService.getProductList(companyCode);
        return Result.success(list);
    }

    /**
     * 添加——修改团险计划列表
     */
    @ApiOperation(value = "添加——修改团险计划列表", notes = "添加——修改团险计划列表")
    @PostMapping("/save_update")
    @RequiresPermissions(value = {"product:groupPlan:save","product:groupPlan:update"},logical = Logical.OR)
    @SysDbLog("添加——修改团险计划列表")
    public Result saveUpdate(@RequestBody @ApiParam(name = "groupPlanVo", value = "保司升级信息", required = true) GroupPlanVo groupPlanVo) {
        ValidatorUtils.validateEntity(groupPlanVo);
        insuranceGroupPlanInfoService.saveUpdate(groupPlanVo);
        return Result.success();
    }

    /**
     * 导出团险计划
     */
    @GetMapping("/export")
    @ApiOperation(value = "导出团险计划", notes = "导出团险计划")
    @RequiresPermissions(value = {"product:groupPlan:export"})
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        params.put("limit", "-1");
        PageUtils<GroupPlanBean> page = insuranceGroupPlanInfoService.queryPage(params);
        AtomicInteger i = new AtomicInteger(1);
        ArrayList<GroupPlanExportVo> list = Lists.newArrayList();
        page.getList().forEach(x -> {
            GroupPlanExportVo vo = new GroupPlanExportVo();
            BeanUtils.copyProperties(x, vo);
            vo.setId(i.getAndIncrement());
            list.add(vo);
        });
        ExcelUtil.writeExcel(response, list, URLUtil.encode("团险计划", CharsetUtil.CHARSET_UTF_8), "sheet1", new GroupPlanExportVo());
        return Result.success();
    }

    /**
     * 团险计划详情
     */
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"product:groupPlan:list"})
    @ApiOperation(value = "团险计划详情", notes = "团险计划详情")
    public Result<GroupPlanVo> info(@ApiParam(name = "id", value = "编码", required = true) @PathVariable("id") Integer id) {
        return Result.success(insuranceGroupPlanInfoService.getInfo(id));
    }
    /**
     * 获取计划
     */
    @GetMapping("/company/{companyCode}")
    @ApiOperation(value = "获取计划", notes = "获取计划")
    public Result<JSONArray> getPlanList(@ApiParam(name = "companyCode", value = "保司编码", required = true) @PathVariable("companyCode") String companyCode) {
        return Result.success(insuranceGroupPlanInfoService.getPlanList(companyCode));
    }
    /**
     * 获取计划险种
     */
    @GetMapping("/planProduct/{planCode}")
    @ApiOperation(value = "获取计划险种", notes = "获取计划险种")
    public Result<List<GroupPlanProductInfo>> getPlanProduct(@ApiParam(name = "planCode", value = "计划编码", required = true) @PathVariable("planCode") String planCode) {
        return Result.success(insuranceGroupPlanInfoService.getPlanProduct(planCode));
    }
    /**
     * 团险计划数据清洗
     */
    @GetMapping("/update/db")
    public Result updateDb() {
        insuranceGroupPlanInfoService.updateDb();
        return Result.success();
    }

}
