package com.mpolicy.manage.modules.settlement.vo.confirm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 手续费结算确认单列表信息
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "手续费结算确认单列表信息", description = "手续费结算确认单列表信息")
public class SettlementReconcileConfirmInfo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023-05")
    @ExcelProperty(value = "对账月度")
    private String reconcileMonth;
    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "太平洋")
    @ExcelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称", example = "首期年佣金")
    @ExcelProperty(value = "科目名称")
    private String reconcileSubjectName;
    /**
     * 对账保单业务汇总明细编号
     */
    @ApiModelProperty(value = "单据编号", example = "12345tfdser")
    @ExcelProperty(value = "单据编号")
    private String billCode;
    /**
     * 险种名称
     */
    @ApiModelProperty(value = "产品名称", example = "意外险")
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "AS23456723456")
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 续期期次
     */
    @ApiModelProperty(value = "投保期数", example = "1")
    @ExcelProperty(value = "投保期数")
    private Integer renewalPeriod;
    /**
     * 缴费时长
     */
    @ApiModelProperty(value = "缴费期限", example = "5")
    @ExcelProperty(value = "缴费期限")
    private Integer paymentPeriod;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费", example = "529.00")
    @ExcelProperty(value = "保费")
    private BigDecimal premium;
}
