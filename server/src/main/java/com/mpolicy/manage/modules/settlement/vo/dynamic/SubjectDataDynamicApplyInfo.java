package com.mpolicy.manage.modules.settlement.vo.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 动态科目文件申请信息
 *
 * <AUTHOR>
 * @date 2023-12-18 20:48
 */
@Data
@ApiModel(value = "动态科目文件申请信息集合", description = "动态科目文件申请信息集合")
public class SubjectDataDynamicApplyInfo implements Serializable {

    private static final long serialVersionUID = 1;

    private String fileType;

    /**
     * 所属周期
     */
    @ApiModelProperty(value = "所属周期", example = "202312")
    private String costSettlementCycle;


    /**
     * 申请编码
     */
    @ApiModelProperty(value = "申请编码", example = "AP20231218204322ClEHFv")
    private String applyCode;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", example = "BC20231218204322ClEHFv")
    private String batchCode;

    /**
     * 文件编码
     */
    @ApiModelProperty(value = "文件编码", example = "oss20231218204322ClEHFv")
    private String fileCode;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", example = "小鲸向海.xls")
    private String fileName;

    /**
     * 对象存储地址
     */
    @ApiModelProperty(value = "对象存储地址", example = "protocol/product/20240129/1de2374c12b14233a304d3042e9c08e2/2024-1众安在线&小鲸-代理协议2024续签 (1) (1).xlsx")
    private String filePath;

    /**
     * 文件地址域名
     */
    @ApiModelProperty(value = "文件地址下载地址", example = "https://oss-xjxhserver.xiaowhale.com/protocol/product/20240129/1de2374c12b14233a304d3042e9c08e2/2024-1众安在线&小鲸-代理协议2024续签 (1) (1).xlsx")
    private String domainPath;

    /**
     * 动态科目数量
     */
    @ApiModelProperty(value = "动态科目数量", example = "1")
    private Integer dynamicSize;

    /**
     * 动态科目总金额
     */
    @ApiModelProperty(value = "动态科目总金额", example = "100.25")
    private BigDecimal dynamicTotalCash;

    /**
     * 申请状态;申请状态0待处理-1失败1成功
     */
    @ApiModelProperty(value = "申请状态;申请状态 0待处理/-1失败/-2作废/1成功/3处理中", example = "1")
    private Integer applyStatus;

    /**
     * 申请备注信息
     */
    @ApiModelProperty(value = "申请信息", example = "申请的信息")
    private String applyMessage;

    /**
     * 异常记录数
     */
    @ApiModelProperty(value = "异常记录数", example = "异常记录数")
    private Integer errorNum;
}
