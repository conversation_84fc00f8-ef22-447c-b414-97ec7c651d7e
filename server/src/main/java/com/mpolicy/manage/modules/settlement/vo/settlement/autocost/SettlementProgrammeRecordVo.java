package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 10:46 上午
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementProgrammeRecordVo implements Serializable {

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 所属周期
     */
    private String costSettlementCycle;
    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 科目数据完成状态;0待完成-1完成
     */
    private Integer subjectDataStatus;
    /**
     * 科目数据完成描述
     */
    private String subjectDataDesc;
    /**
     * 科目结算完成状态;0待完成-1完成
     */
    private Integer subjectCalculateStatus;
    /**
     * 科目结算完成描述
     */
    private String subjectCalculateDesc;
    /**
     * 方案记录状态;0待完成-1完成
     */
    private Integer programmeRecordStatus;
    /**
     * 方案完成时间
     */
    private Date programmeFinishTime;
    /**
     * 结算方案总金额
     */
    private BigDecimal costTotalCash;
    /**
     * 周期结算方案描述
     */
    private String costProgrammeDesc;
    /**
     * 确认结算状态;0未确认1确认中2已确认
     */
    private String confirmStatus;
    /**
     *确认结算操作员
     */
    private String confirmUser;
    /**
     * 确认结算完成时间
     */
    private Date confirmTime;
}
