package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostProgrammeRecordEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeRecordVo;

public interface SettlementCostProgrammeRecordService extends IService<SettlementCostProgrammeRecordEntity> {
    /**
     * 根据方案编码和结算周期查询方案记录
     * @param programmeCode
     * @return
     */
    SettlementProgrammeRecordVo getProgrammeRecord(String programmeCode, String costSettlementCycle);

}
