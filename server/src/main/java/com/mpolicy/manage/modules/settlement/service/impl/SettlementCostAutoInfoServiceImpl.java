package com.mpolicy.manage.modules.settlement.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostAutoInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementCostAutoInfoService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementCostAutoInfoVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementCostAutoInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDynamicSubjectVO;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 2:41 下午
 * @Version 1.0
 */
@Service("settlementCostAutoInfoService")
@Slf4j
public class SettlementCostAutoInfoServiceImpl extends ServiceImpl<SettlementCostAutoInfoDao, SettlementCostAutoInfoEntity> implements SettlementCostAutoInfoService {


    @Override
    public IPage<SettlementCostAutoInfoEntity> queryPage(SettlementCostAutoInput input) {

        IPage<SettlementCostAutoInfoEntity> page = this.page(
                new Page<>(input.getPage(), input.getLimit()),
                new QueryWrapper<SettlementCostAutoInfoEntity>().lambda()
                        //.in(CollectionUtils.isNotEmpty(input.getObjectOrgCode()),)
                        .eq(StringUtils.isNotBlank(input.getObjectOrgCode()), SettlementCostAutoInfoEntity::getObjectOrgCode, input.getObjectOrgCode())
                        .eq(StringUtils.isNotBlank(input.getSendObjectCode()), SettlementCostAutoInfoEntity::getSendObjectCode, input.getSendObjectCode())
                        .eq(StringUtil.isNotBlank(input.getSettlementInstitution()), SettlementCostAutoInfoEntity::getSettlementInstitution, input.getSettlementInstitution())
                        .eq(SettlementCostAutoInfoEntity::getSettlementMonth, input.getSettlementMonth())
                        .eq(input.getSettlementStatus() != null, SettlementCostAutoInfoEntity::getConfirmStatus, input.getSettlementStatus())

        );

        return page;
    }

    @Override
    public List<SettlementCostAutoInfoEntity> listExportPage(SettlementCostAutoInput input, Integer start, Integer batchSize) {

        return baseMapper.selectList(
                new QueryWrapper<SettlementCostAutoInfoEntity>().lambda()
                        //.in(CollectionUtils.isNotEmpty(input.getObjectOrgCode()),)
                        .eq(StringUtils.isNotBlank(input.getObjectOrgCode()), SettlementCostAutoInfoEntity::getObjectOrgCode, input.getObjectOrgCode())
                        .eq(StringUtils.isNotBlank(input.getSendObjectCode()), SettlementCostAutoInfoEntity::getSendObjectCode, input.getSendObjectCode())
                        .eq(StringUtil.isNotBlank(input.getSettlementInstitution()), SettlementCostAutoInfoEntity::getSettlementInstitution, input.getSettlementInstitution())
                        .eq(StringUtil.isNotBlank(input.getSettlementMonth()), SettlementCostAutoInfoEntity::getSettlementMonth, input.getSettlementMonth())
                        .eq(input.getSettlementStatus() != null, SettlementCostAutoInfoEntity::getConfirmStatus, input.getSettlementStatus())
                        .last(StrUtil.format("limit {}, {}", start, batchSize))
        );

    }


    @Override
    public IPage<SettlementCostAutoInfoEntity> pageSettlementCostAutoInfoBySummary(SettlementCostAutoInput input) {
        return baseMapper.pageSettlementCostAutoInfoBySummary(new Page<SettlementCostAutoInfoEntity>(input.getPage(), input.getLimit()), input);

    }

    @Override
    public List<SettlementCostAutoInfoEntity> listSettlementCostAutoInfoBySummary(SettlementCostAutoInput input, Integer start,
                                                                                  Integer batchSize) {
        return baseMapper.pageSettlementCostAutoInfoBySummaryExport(input, start, batchSize);

    }


}
