package com.mpolicy.manage.modules.settlement.service.detail;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostAutoInfoDao;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailPolicyDimensionVo;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 前端要求汇总的数据都转数组
 *
 * <AUTHOR>
 * @Date 2024/1/31 01:18
 * @Version 1.0
 */
public abstract class AbsPolicyDimensionService<S> extends AbsCommonParamsSettlementDetail<S, SettlementDetailPolicyDimensionVo> {

    @Autowired
    protected SettlementCostAutoInfoDao costAutoInfoDao;

    @Override
    public IPage<SettlementDetailPolicyDimensionVo> queryDetail(PageSettlementDetailParams p) {
        return costAutoInfoDao.pagePolicyDimension(new Page<>(p.getPage(), p.getLimit()), p);
    }



}
