package com.mpolicy.manage.modules.commission.controller;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListOut;
import com.mpolicy.manage.modules.settlement.vo.UploadPolicyProductPremInput;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 基础佣金费率-一单一议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */
@RestController
@RequestMapping("commission/basic/policy/prem")
@Api(tags = "基础佣金费率-一单一议")
public class CommissionBasicPolicyPremController {

    @Autowired
    private CommissionBasicPolicyPremService commissionBasicPolicyPremService;


    @GetMapping("list")
    @RequiresPermissions("commission:basic-policy-prem:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<CommissionBasicPolicyPremListOut>> list(CommissionBasicPolicyPremListInput input){
        PageUtils<CommissionBasicPolicyPremListOut> page = commissionBasicPolicyPremService.findCommissionBasicPolicyPremList(input);
        return Result.success(page);
    }


    @PostMapping("delete/{id}")
    @SysLog(value = "删除费率")
    public Result delete(@PathVariable("id") Integer id) {
        commissionBasicPolicyPremService.deletePolicyProductPrem(id);
        return Result.success();
    }



    @ApiOperation(value = "上传一单一议保单费率", notes = "上传一单一议保单费率")
    @PostMapping("uploadPremiumFile")
    public Result uploadPremiumFile(UploadPolicyProductPremInput input) {
        FileModelEnum fileModelEnum = FileModelEnum.decode(input.getFileSystem());
        if (fileModelEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件操作分类参数错误"));
        }
        MultipartFile file = input.getFile();
        // 验证文件格式
        if (!fileModelEnum.checkFileType(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("上传文件格式不支持"));
        }
        // 上传文件
        commissionBasicPolicyPremService.uploadPremiumFile(input);
        return Result.success();
    }
}
