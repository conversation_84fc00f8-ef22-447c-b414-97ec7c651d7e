package com.mpolicy.manage.modules.settlement.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PolicyProductPremExcel implements Serializable {

    private static final long serialVersionUID = -5309130288285739435L;

    /**
     * 保单号
     */
    @Alias("保单号")
    private String policyNo;

    @Alias("批单号")
    private String batchCode;

    @Alias("缴费期次")
    private Integer period;

    @Alias("保单年期")
    private Integer year;

    /**
     * 小鲸险种编码
     */
    @Alias("险种编码")
    private String productCode;

    @Alias("险种名称")
    private String productName;

    @Alias("保司协议险种编码")
    private String insuranceProductCode;

    /**
     * 保费
     */
    @Alias("税前保费（元）")
    private BigDecimal premium;

    @Alias("税后保费（元）")
    private BigDecimal taxAfterPremium;

    /**
     * 结算方式 按全保费(含税)结算 按净保费(税后)结算
     */
    @Alias("结算方式")
    private String settlementMethod;

    /**
     * 税率
     */
    @Alias("税率")
    private String taxRate;

    /**
     * 费率
     */
    @Alias("费率")
    private BigDecimal yearRate;

    private String premCode;

}
