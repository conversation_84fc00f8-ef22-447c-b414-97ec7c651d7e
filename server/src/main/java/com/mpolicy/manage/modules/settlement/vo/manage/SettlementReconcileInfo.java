package com.mpolicy.manage.modules.settlement.vo.manage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司对账单管理列表信息
 *
 * <AUTHOR>
 * @since 2023-05-25 01:02
 */
@Data
@ApiModel(value = "保司对账单管理列表信息", description = "保司对账单管理列表信息")
public class SettlementReconcileInfo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    @ExcelProperty(value = "对账唯一单号")
    private String reconcileCode;

    private Integer reconcileType;



    @ApiModelProperty(value = "结算编码", example = "RC2023052302020200")
    @ExcelProperty(value = "结算编码")
    private String reconcileCompanyCode;

    /**
     * 对账单名称
     */
    @ApiModelProperty(value = "对账单名称", example = "众安2023020203")
    @ExcelProperty(value = "对账单名称")
    private String reconcileName;

    @ApiModelProperty(value = "结算科目备注", example = "众安2023020203")
    @ExcelProperty(value = "结算科目备注")
    private String subjectRemark;

    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023年06期")
    @ExcelProperty(value = "对账月度")
    private String reconcileMonth;

    /**
     * 生成账单日
     */
    @ApiModelProperty(value = "生成账单日", example = "每月22日")
    @ExcelProperty(value = "生成账单日")
    private String createReconcileDay;

    /**
     * 保险公司编码
     */
    @ApiModelProperty(value = "保险公司编码", example = "TK20202020")
    private String companyCode;

    @ApiModelProperty(value = "科目编码集合", example = "TK20202020")
    private String subjectRuleCodes;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "泰康保险公司")
    @ExcelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 内部签署方编码
     */
    @ApiModelProperty(value = "内部签署方编码", example = "TK20202020")
    private String innerSignatoryCode;

    /**
     * 内部签署方名称
     */
    @ApiModelProperty(value = "内部签署方名称", example = "北京小鲸向海保险代理公司")
    @ExcelProperty(value = "内部签署方名称")
    private String innerSignatoryName;

    /**
     * 外部签署方类型字典
     */
    @ApiModelProperty(value = "外部签署方类型字典", example = "代理公司")
    @ExcelProperty(value = "外部签署方类型")
    private String externalSignatoryType;

    /**
     * 外部签署方编码
     */
    @ApiModelProperty(value = "外部签署方编码", example = "TK20202020")
    private String externalSignatoryCode;

    /**
     * 外部签署方名称
     */
    @ApiModelProperty(value = "外部签署方名称", example = "泰康保险公司北京总公司")
    @ExcelProperty(value = "外部签署方名称")
    private String externalSignatoryName;

    /**
     * 保司对账单文件数量
     */
    @ApiModelProperty(value = "保司对账单文件数量", example = "1")
    @ExcelProperty(value = "保司对账单文件数量")
    private Integer companyBillCount = 0;

    /**
     * 对账保单数量
     */
    @ApiModelProperty(value = "对账保单数量", example = "122")
    @ExcelProperty(value = "对账保单数量")
    private Integer policyCount = 0;

    /**
     * 小鲸手续费金额
     */
    @ApiModelProperty(value = "小鲸手续费金额", example = "1222.12")
    @ExcelProperty(value = "小鲸手续费金额")
    private BigDecimal xiaowhaleAmount = BigDecimal.ZERO;

    /**
     * 保司手续费金额
     */
    @ApiModelProperty(value = "保司手续费金额", example = "1222.12")
    @ExcelProperty(value = "保司手续费金额")
    private BigDecimal companyAmount = BigDecimal.ZERO;

    /**
     * 平账金额
     */
    @ApiModelProperty(value = "平账金额", example = "12.12")
    @ExcelProperty(value = "平账金额")
    private BigDecimal reversalAmount = BigDecimal.ZERO;

    /**
     * 差异金额
     */
    @ApiModelProperty(value = "差异金额", example = "12.12")
    @ExcelProperty(value = "差异金额")
    private BigDecimal diffAmount = BigDecimal.ZERO;
    /**
     * 已开票金额
     */
    @ApiModelProperty(value = "已开票金额", example = "12.12")
    @ExcelProperty(value = "已开票金额")
    private BigDecimal invoicedAmount;
    /**
     * 开票中金额
     */
    @ApiModelProperty(value = "开票冻结金额", example = "12.12")
    @ExcelProperty(value = "开票冻结金额")
    private BigDecimal invoiceAmount;
    /**
     * 可开票金额
     */
    @ApiModelProperty(value = "可开票金额", example = "12.12")
    @ExcelProperty(value = "可开票金额")
    private BigDecimal invoicableAmount;

    @ApiModelProperty(value = "开票状态", example = "1")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "开票状态描述", example = "12.12")
    @ExcelProperty(value = "开票状态描述")
    private String invoiceStatusDesc;

    /**
     * 对账单下载地址
     */
    @ApiModelProperty(value = "对账单下载地址", example = "https://cfpamf.yuque.com/xg4y3f/voxwu3/pkd63wbov9p3w8nd")
    private String reconcileXlsUrl;

    /**
     * 对账单保单信息下载地址
     */
    @ApiModelProperty(value = "对账单保单信息下载地址", example = "https://cfpamf.yuque.com/xg4y3f/voxwu3/pkd63wbov9p3w8nd")
    private String reconcilePolicyUrl;

    /**
     * 对账状态;0待对账1账单生成中2对账中3对账已完成
     */
    @ApiModelProperty(value = "对账状态 0:待对账、1：账单生成中、2：对账中、3：对账已完成、4：对账单生成失败", example = "2")
    @ExcelProperty(value = "对账状态0")
    private Integer reconcileStatus;

    /**
     * 对账操作员
     */
    @ApiModelProperty(value = "对账操作员", example = "张无忌")
    @ExcelProperty(value = "对账操作员")
    private String reconcileUser;

    @ApiModelProperty(value = "责任人", example = "责任人")
    @ExcelProperty(value = "责任人")
    private String headName;

    /**
     * 对账完成时间
     */
    @ApiModelProperty(value = "对账完成时间", example = "2023-05-23 11:11:11")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "对账完成时间")
    private Date reconcileTime;

    /**
     * 对账完成时间
     */
    @ApiModelProperty(value = "更新时间", example = "2023-05-23 11:11:11")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


}