package com.mpolicy.manage.modules.regulators.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 监管报备内容枚举
 *
 * <AUTHOR>
 * @version 2022/01/20
 */
public enum RegulatorsReportTypeEnum {

    /**
     * 监管报备内容枚举
     */
    PROPERTY_COMPANY_BUSINESS("CORE_SYSTEM:REGULATORS:REPORT_TYPE:PROPERTY_COMPANY_BUSINESS", "代理产险公司业务表"),
    LIFE_COMPANY_BUSINESS("CORE_SYSTEM:REGULATORS:REPORT_TYPE:LIFE_COMPANY_BUSINESS", "代理人身险公司业务表"),
    COMPANY_BASIC_INFO("CORE_SYSTEM:REGULATORS:REPORT_TYPE:COMPANY_BASIC_INFO", "基本情况表"),
    COMPANY_INCOME("CORE_SYSTEM:REGULATORS:REPORT_TYPE:COMPANY_INCOME", "利润表"),
    COMPANY_ASSETS_LIABILITIES("CORE_SYSTEM:REGULATORS:REPORT_TYPE:COMPANY_ASSETS_LIABILITIES", "资产负债表");

    @Getter
    private final String code;

    @Getter
    private final String name;

    RegulatorsReportTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * <p>
     * 根据报备内容编码获取报备内容枚举
     * </p>
     *
     * @param code 报备内容编码
     * <AUTHOR>
     * @since 2022/01/20
     */
    public static RegulatorsReportTypeEnum deCode(String code) {
        return Arrays.stream(RegulatorsReportTypeEnum.values()).filter(x -> x.code.equals(code)).findFirst().orElse(null);
    }
}
