package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductPremService;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;


/**
 * 结算保单险种费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-22 15:08:37
 */
@RestController
@RequestMapping("settlement/policy/product/prem")
@Api(tags = "结算保单险种费率")
public class SettlementPolicyProductPremController {

    @Autowired
    private SettlementPolicyProductPremService settlementPolicyProductPremService;

    @GetMapping("list")
    @RequiresPermissions(value = {"settlement:policy:all"})
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<SettlementPolicyProductPremListOut>> list(SettlementPolicyProductPremListInput input){
        PageUtils<SettlementPolicyProductPremListOut> page = settlementPolicyProductPremService.findSettlementPolicyProductPremList(input);
        return Result.success(page);
    }

    /**
     * 新增结算保单险种费率
     *
     * @param params
     * @return
     */
    @PostMapping("save")
    @RequiresPermissions(value = {"settlement:policy:all"})
    @SysLog(value = "新增结算保单险种费率")
    public Result save(@RequestBody @Valid SavePolicyProductPrem params) {
        settlementPolicyProductPremService.savePolicyProductPrem(params);
        return Result.success();
    }

    /**
     * 修改协议保司配置
     *
     * @param params
     * @return
     */
    @PostMapping("update")
    @RequiresPermissions(value = {"settlement:policy:all"})
    @SysLog(value = "修改协议保司配置")
    public Result update(@RequestBody @Valid UpdatePolicyProductPrem params) {
        settlementPolicyProductPremService.updatePolicyProductPrem(params);
        return Result.success();
    }

    /**
     * 信息
     */
    @GetMapping("info/{id}")
    @RequiresPermissions(value = {"settlement:policy:all"})
    public Result<SettlementPolicyProductPrem> info(@PathVariable("id") Integer id) {
        SettlementPolicyProductPrem info = settlementPolicyProductPremService.info(id);
        return Result.success(info);
    }

    /**
     * 删除
     */
    @PostMapping("delete/{id}")
    @RequiresPermissions(value = {"settlement:policy:all"})
    @SysLog(value = "删除结算保单险种费率")
    public Result delete(@PathVariable("id") Integer id) {
        settlementPolicyProductPremService.deletePolicyProductPrem(id);
        return Result.success();
    }


    @ApiOperation(value = "上传一单一议保单费率", notes = "上传一单一议保单费率")
    @PostMapping("uploadPremiumFile")
    @RequiresPermissions(value = {"settlement:policy:all"})
    public Result uploadPremiumFile(UploadPolicyProductPremInput input) {
        FileModelEnum fileModelEnum = FileModelEnum.decode(input.getFileSystem());
        if (fileModelEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件操作分类参数错误"));
        }
        MultipartFile file = input.getFile();
        // 验证文件格式
        if (!fileModelEnum.checkFileType(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("上传文件格式不支持"));
        }
        // 上传文件
        settlementPolicyProductPremService.uploadPremiumFile(input);
        return Result.success();
    }


}
