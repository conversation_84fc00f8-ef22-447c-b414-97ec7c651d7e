package com.mpolicy.manage.modules.insurance.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoDisclosuresAttachmentService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoDisclosuresMapService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoDisclosuresService;
import com.mpolicy.product.common.product.ProductInfoDisclosuresInput;
import com.mpolicy.product.common.product.ProductInfoDisclosuresOut;
import com.mpolicy.product.common.product.ProductInfoDisclosuresVO;
import com.mpolicy.web.common.annotation.PassToken;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


/**
 * Created by 陈杰生
 * 产品披露信息
 * <p>
 * 2023/12/11 10:30
 */
@Validated
@RestController
@RequestMapping("insurance/productDisclosures")
@Api(tags = "产品披露信息")
@Slf4j
public class ProductInfoDisclosuresController {

    @Autowired
    private InsuranceProductInfoDisclosuresService disclosuresService;

    @Autowired
    private InsuranceProductInfoDisclosuresMapService disclosuresMapService;

    @Autowired
    private InsuranceProductInfoDisclosuresAttachmentService disclosuresAttachmentService;

    @ApiOperation(value = "保存产品披露信息", notes = "保存产品披露信息")
    @PostMapping("/save")
    @RequiresPermissions(value = {"product:disclosures:all"})
    @Transactional(rollbackFor = Exception.class)
    public Result<String> save(@RequestBody @ApiParam(name = "productInfoDisclosuresVO", value = "保存产品披露信息") ProductInfoDisclosuresVO productInfoDisclosuresVO) {
        // 1. 校验VO类
        ValidatorUtils.validateEntity(productInfoDisclosuresVO);
        // 2. save 产品披露信息
        disclosuresService.saveDisclosuresInfo(productInfoDisclosuresVO);
        // 3. save 产品披露备案信息
        disclosuresMapService.saveOrUpdateDisclosuresMapInfo(productInfoDisclosuresVO);
        // 4. save 产品披露附件信息
        disclosuresAttachmentService.saveOrUpdateDisclosureAttachmentInfo(productInfoDisclosuresVO);
        // 5. 无报错，响应成功
        return Result.success();
    }

    @ApiOperation(value = "删除产品披露信息", notes = "删除产品披露信息")
    @GetMapping("/delete/{commodityCode}")
    @RequiresPermissions(value = {"product:disclosures:all"})
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(@PathVariable("commodityCode") String commodityCode) {
        // 1. delete 产品披露信息
        disclosuresService.deleteDisclosuresInfo(commodityCode);
        // 2. delete 产品披露备案信息
        disclosuresMapService.deleteDisclosuresMapInfo(commodityCode);
        // 3. delete 产品披露附件信息
        disclosuresAttachmentService.deleteDisclosuresAttachmentInfo(commodityCode);
        // 3. 无报错，响应成功
        return Result.success();
    }

    @ApiOperation(value = "修改产品披露信息", notes = "修改产品披露信息")
    @PostMapping("/update")
    @RequiresPermissions(value = {"product:disclosures:all"})
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody @ApiParam(name = "productInfoDisclosuresVO", value = "修改产品披露信息") ProductInfoDisclosuresVO productInfoDisclosuresVO) {
        // 1. 校验VO类
        ValidatorUtils.validateEntity(productInfoDisclosuresVO);
        // 2. update 产品披露信息
        disclosuresService.updateDisclosuresInfo(productInfoDisclosuresVO);
        // 3. update 产品披露备案信息
        disclosuresMapService.saveOrUpdateDisclosuresMapInfo(productInfoDisclosuresVO);
        // 4. update 产品披露附件信息
        disclosuresAttachmentService.saveOrUpdateDisclosureAttachmentInfo(productInfoDisclosuresVO);
        // 4. 无报错，响应成功
        return Result.success();
    }

    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    @GetMapping("/list")
    @RequiresPermissions(value = {"product:disclosures:all"})
    @PassToken
    public Result<PageUtils<ProductInfoDisclosuresOut>> list(ProductInfoDisclosuresInput productInfoDisclosuresInput) {
        // 1. 获取分页数据
        PageUtils<ProductInfoDisclosuresOut> page = disclosuresService.queryPage(productInfoDisclosuresInput);
        // 2. 返回数据
        return Result.success(page);
    }

    @ApiOperation(value = "获取产品披露信息", notes = "获取产品披露信息")
    @GetMapping("/query/{commodityCode}")
    @RequiresPermissions(value = {"product:disclosures:all"})
    @PassToken
    public Result<ProductInfoDisclosuresVO> query(@PathVariable("commodityCode") String commodityCode) {
        return Result.success(disclosuresService.queryProductInfoDisclosuresDetail(commodityCode));
    }

    @ApiOperation(value = "导出产品披露信息", notes = "导出产品披露信息")
    @PostMapping("export")
    @RequiresPermissions(value = {"product:disclosures:all"})
    public void export(@RequestBody ProductInfoDisclosuresVO productInfoDisclosuresVO, HttpServletResponse response) {
        disclosuresService.exportProductInfoDisclosuresDetail(productInfoDisclosuresVO, response);
    }


}
