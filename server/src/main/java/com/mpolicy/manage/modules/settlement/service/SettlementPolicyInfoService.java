package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyInfoOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyInfoReviseVo;
import com.mpolicy.manage.modules.settlement.vo.policy.SettlementReconcilePolicyInfo;

import java.util.Map;

/**
 * 结算保单明细记录表
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
public interface SettlementPolicyInfoService extends IService<SettlementPolicyInfoEntity> {

    /**
     * 分页查询保单信息列表
     *
     * @param params:
     * @return : com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.policy.SettlementReconcilePolicyInfo>
     * <AUTHOR>
     * @date 2023/5/26 11:54
     */
    PageUtils<SettlementReconcilePolicyInfo> querySettlementPolicyInfoServiceList(Map<String, Object> params);

    /**
     * 获取保单详情
     * @param settlementCode
     * @return
     */
    SettlementPolicyInfoOut findSettlementPolicyInfo(String settlementCode);

    /**
     * 修正数据
     * @param vo
     */
    void reviseSettlementPolicyInfo(SettlementPolicyInfoReviseVo vo);

    /**
     * 刷新费率信息
     * @param settlementCode
     */
    void refreshCommission(String settlementCode);
}

