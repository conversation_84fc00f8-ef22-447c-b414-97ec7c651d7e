package com.mpolicy.manage.modules.settlement.vo;

import com.mpolicy.manage.modules.sys.fileManage.dto.ExcelImportErrorTipsVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/28 14:53
 * @Version 1.0
 */
@Data
public class ImportPolicyProductFeePremExcelResult {

    /**
     * 校验错误数
     */
    @ApiModelProperty(" 校验错误数")
    private Integer errorCount=0;

    /**
     * 校验成功数量
     */
    @ApiModelProperty(" 校验成功数量")
    private Integer successCount=0;


    /**
     * 总数
     */
    @ApiModelProperty(" 总数")
    private Integer total=0;

    /**
     * 错误清单
     */
    @ApiModelProperty(" 错误清单")
    private String errorUrl;


    /**
     * 错误信息
     */
    @ApiModelProperty(" 错误信息")
    private String msg;

    @ApiModelProperty("错误信息集合")
    private List<ExcelImportErrorTipsVo> errorTipsVoList;
}
