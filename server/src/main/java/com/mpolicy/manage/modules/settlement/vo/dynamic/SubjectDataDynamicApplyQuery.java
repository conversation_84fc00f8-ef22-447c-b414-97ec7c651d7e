package com.mpolicy.manage.modules.settlement.vo.dynamic;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算文件导入记录查询
 *
 * <AUTHOR>
 * @date 2023-12-18 20:48
 */
@Data
@ApiModel(value = "结算文件导入记录查询", description = "结算文件导入记录查询")
public class SubjectDataDynamicApplyQuery extends BasePage implements Serializable {

    private static final long serialVersionUID = 1;
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 所属周期
     */
    @ApiModelProperty(value = "所属周期", example = "202312")
    private String costSettlementCycle;


    /**
     * 申请编码
     */
    @ApiModelProperty(value = "申请编码", example = "AP20231218204322ClEHFv")
    private String applyCode;

    @ApiModelProperty(value = "导入开始时间")
    private Date startTime;
    @ApiModelProperty(value = "导入开始时间")
    private Date endTime;
}
