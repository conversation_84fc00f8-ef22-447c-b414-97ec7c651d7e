package com.mpolicy.manage.modules.settlement.service.impl;


import com.mpolicy.manage.modules.settlement.service.SettlementCostProgrammeSubjectService;
import com.mpolicy.manage.modules.settlement.service.SettlementProgrammeService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeSubjectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 2:41 下午
 * @Version 1.0
 */
@Service("settlementProgrammeService")
@Slf4j
public class SettlementProgrammeServiceImpl implements SettlementProgrammeService {

    @Autowired
    private SettlementCostProgrammeSubjectService settlementCostProgrammeSubjectService;

    @Override
    public List<SettlementProgrammeSubjectVo> listProgrammeSubject(String programmeCode){
        return settlementCostProgrammeSubjectService.listProgrammeSubject(programmeCode);
    }

}
