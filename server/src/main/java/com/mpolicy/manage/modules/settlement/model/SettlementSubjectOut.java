package com.mpolicy.manage.modules.settlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SettlementSubjectOut implements Serializable {
    private static final long serialVersionUID = 2666829683539431049L;

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    @ApiModelProperty(value = "承保结束时间")
    private String approvedEnd;

    @ApiModelProperty(value = "回执结束时间")
    private String receiptEnd;

    @ApiModelProperty(value = "回执结束时间")
    private String revisitEnd;

}
