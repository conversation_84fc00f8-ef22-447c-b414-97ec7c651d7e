package com.mpolicy.manage.modules.settlement.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SettlementReconcileCompanyInfo implements Serializable {
    private static final long serialVersionUID = -695333451368536972L;
    @TableId
    private Integer id;
    /**
     * 结算保司编码
     */
    private String reconcileCompanyCode;
    private String reconcileCompanyName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private List<List<String>> externalSignatoryCodeArr;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;

    private List<String> innerSignatoryCodeArr;
    /**
     * 产品所属分公司
     */
    private List<String> companyCodeArr;
    /**
     * 结算保司
     */
    private String settlementCompanyCode;
    /**
     * 结算保类型
     */
    private String settlementCompanyType;
    private  List<String>  settlementCompanyCodeArr;
    /**
     * 规则科目数量
     */
    private Integer subjectRuleCount;

}
