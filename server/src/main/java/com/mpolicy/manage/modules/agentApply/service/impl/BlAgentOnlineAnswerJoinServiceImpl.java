package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineAnswerJoinDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineAnswerJoinEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineAnswerJoinService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("blAgentOnlineAnswerJoinService")
public class BlAgentOnlineAnswerJoinServiceImpl extends ServiceImpl<BlAgentOnlineAnswerJoinDao, BlAgentOnlineAnswerJoinEntity> implements BlAgentOnlineAnswerJoinService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<BlAgentOnlineAnswerJoinEntity> page = this.page(
                new Query<BlAgentOnlineAnswerJoinEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
