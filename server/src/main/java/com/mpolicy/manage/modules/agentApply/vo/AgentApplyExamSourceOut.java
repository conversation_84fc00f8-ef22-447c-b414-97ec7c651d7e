package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * ClassName: AgentApplyAttachmentOut
 * Description:
 * date: 2022/11/30 11:20
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyExamSourceOut implements Serializable {
    private static final long serialVersionUID = -4051013835901841705L;

    @ApiModelProperty(value = "合格分数", required = true)
    @NotBlank(message = "合格分数不能为空")
    @Pattern(regexp = "^[0-9]*$", message = "合格分数只能为数字")
    private String score;
}
