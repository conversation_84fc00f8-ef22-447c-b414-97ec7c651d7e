package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoRecordEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @Date 2024/1/26 10:31
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PageSettlementSummaryKeyVO {
    @ApiModelProperty(value = "结佣月份")
    private String settlementMonth;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String regionCode;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String regionName;

    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码")
    private String objectOrgCode;

    /**
     * 分支
     */
    @ApiModelProperty(value = "分支")
    private String objectOrgName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String sendObjectCode;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String sendObjectName;

    public static PageSettlementSummaryKeyVO from(SettlementCostAutoInfoVo autoInfoEntity) {

        PageSettlementSummaryKeyVO settlementSummaryKeyVO = new PageSettlementSummaryKeyVO();
        settlementSummaryKeyVO.setSettlementMonth(autoInfoEntity.getSettlementMonth());
        settlementSummaryKeyVO.setRegionCode(autoInfoEntity.getRegionCode());
        settlementSummaryKeyVO.setRegionName(autoInfoEntity.getRegionName());
        settlementSummaryKeyVO.setObjectOrgCode(autoInfoEntity.getObjectOrgCode());
        settlementSummaryKeyVO.setObjectOrgName(autoInfoEntity.getObjectOrgName());
        settlementSummaryKeyVO.setSendObjectCode(autoInfoEntity.getSendObjectCode());
        settlementSummaryKeyVO.setSendObjectName(autoInfoEntity.getSendObjectName());
        return settlementSummaryKeyVO;

    }

    public static PageSettlementSummaryKeyVO fromAutoRecord(SettlementCostAutoRecordEntity autoInfoEntity) {

        PageSettlementSummaryKeyVO settlementSummaryKeyVO = new PageSettlementSummaryKeyVO();
        settlementSummaryKeyVO.setSettlementMonth(autoInfoEntity.getCostSettlementCycle());
//        settlementSummaryKeyVO.setRegionCode(autoInfoEntity.getRegionCode());
//        settlementSummaryKeyVO.setRegionName(autoInfoEntity.getRegionName());
        settlementSummaryKeyVO.setObjectOrgCode(autoInfoEntity.getObjectOrgCode());
        settlementSummaryKeyVO.setObjectOrgName(autoInfoEntity.getObjectOrgName());
        settlementSummaryKeyVO.setSendObjectCode(autoInfoEntity.getSendObjectCode());
        settlementSummaryKeyVO.setSendObjectName(autoInfoEntity.getSendObjectName());
        return settlementSummaryKeyVO;

    }


}
