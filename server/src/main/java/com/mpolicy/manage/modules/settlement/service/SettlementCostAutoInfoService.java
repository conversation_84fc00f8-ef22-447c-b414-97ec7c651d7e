package com.mpolicy.manage.modules.settlement.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementCostAutoInput;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 2:41 下午
 * @Version 1.0
 */

public interface SettlementCostAutoInfoService {
    /**
     * 分页查询
     *
     * @param input
     * @return
     */
    IPage<SettlementCostAutoInfoEntity> queryPage(SettlementCostAutoInput input);

    List<SettlementCostAutoInfoEntity> listExportPage(SettlementCostAutoInput input, Integer start, Integer batchSize);

    /**
     * 分页获取每个人每个机构下的汇总数据
     *
     * @param input
     * @return
     */
    IPage<SettlementCostAutoInfoEntity> pageSettlementCostAutoInfoBySummary(SettlementCostAutoInput input);

    List<SettlementCostAutoInfoEntity> listSettlementCostAutoInfoBySummary(SettlementCostAutoInput input, Integer start,
                                                                           Integer batchSize);
}
