package com.mpolicy.manage.modules.settlement.service;

import com.mpolicy.manage.modules.settlement.vo.settlement.employee.EmployeeInfoInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.EmployeeInfoVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.OrganizationCache;

import java.util.List;

public interface SettlementEmployeeService {
    /**
     * 获取员工所有区域信息
     * @return
     */
    List<OrganizationCache> listAllRegion();

    /**
     * 查询员工信息
     * @param input
     * @return
     */
    List<EmployeeInfoVo> listEmployeeInfo(EmployeeInfoInput input);
}
