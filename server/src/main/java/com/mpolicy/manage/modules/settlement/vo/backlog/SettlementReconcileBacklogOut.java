package com.mpolicy.manage.modules.settlement.vo.backlog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 保司结算对账单差异详情
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "保司结算对账单差异详情", description = "保司结算对账单差异详情")
public class SettlementReconcileBacklogOut implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 差异受理编号
     */
    @ApiModelProperty(value = "差异受理编号", example = "CY2023052302020200")
    private String backlogCode;
    /**
     * 差异类型名称
     */
    @ApiModelProperty(value = "差异类型名称", example = "我司漏单")
    private String diffName;
    /**
     * 差异保单编号
     */
    @ApiModelProperty(value = "差异保单编号", example = "ASDF23456789")
    private String policyNo;

}
