package com.mpolicy.manage.modules.regulators.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsMonthReportEntity;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportInput;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportOut;
import org.apache.ibatis.annotations.Param;

public interface RegulatorsMonthReportDao extends BaseMapper<RegulatorsMonthReportEntity> {

    /**
     * 获取数据列表
     * @param page 分页参数
     * @param input 查询条件
     * @return 返回结果
     */
    IPage<RegulatorsMonthReportOut> queryPage(@Param("page") Page<RegulatorsMonthReportOut> page, @Param("input") RegulatorsMonthReportInput input);
}
