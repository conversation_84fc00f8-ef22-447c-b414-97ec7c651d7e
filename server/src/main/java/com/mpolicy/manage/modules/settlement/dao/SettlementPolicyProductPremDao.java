package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductPremListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductPremListOut;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 结算保单险种费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-22 15:08:37
 */
public interface SettlementPolicyProductPremDao extends ImsBaseMapper<SettlementPolicyProductPremEntity> {


    /**
     * 分页获取数据
     *
     * @param page
     * @param input
     * @return
     */
    IPage<SettlementPolicyProductPremListOut> findSettlementPolicyProductPremList(@Param("page") Page<SettlementPolicyProductPremListOut> page,@Param("input") SettlementPolicyProductPremListInput input);
}
