package com.mpolicy.manage.modules.regulators.common;

import com.mpolicy.common.redis.key.BasePrefix;

/**
 * 机构报备-redis
 *
 * <AUTHOR>
 * @version 2022/01/23
 */
public class RegulatorsKeys extends BasePrefix {

    private RegulatorsKeys(String prefix) {
        super(prefix);
    }

    private RegulatorsKeys(int expireSeconds, String prefix) {
        super(expireSeconds, prefix);
    }

    /**
     * <p>
     * 机构报备报告内容
     * </p>
     */
    public static RegulatorsKeys REGULATORS_REPORT = new RegulatorsKeys(60 * 5, "REGULATORS_REPORT");
}