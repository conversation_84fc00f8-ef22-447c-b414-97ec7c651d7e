package com.mpolicy.manage.modules.regulators.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 监管报备报告处理data
 *
 * <AUTHOR>
 * @date 2022-01-20 15:23
 */
@Data
@ApiModel(value = "监管报备月度列表")
public class RegulatorsReportList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报备月度编码
     */
    @ApiModelProperty(value = "报备月度编码",example = "BB10000000")
    private String regulatorsNo;

    /**
     * 报备月度名称
     */
    @ApiModelProperty(value = "报备月度名称",example = "保险专业中介机构月度报表（2022年X月）")
    private String regulatorsName;

    /**
     * 报备机构分支数量
     */
    @ApiModelProperty(value = "报备机构分支数量",example = "1")
    private Integer regulatorsOrgCount;

    /**
     * 报备上传状态 0否1是
     */
    @ApiModelProperty(value = "是否完整上传",example = "是")
    private String uploadCompleteStatus;

    /**
     * 报备状态 0未报送1已报送
     */
    @ApiModelProperty(value = "报送状态编码",example = "1")
    private Integer reportStatusCode;

    /**
     * 报备状态 0未报送1已报送
     */
    @ApiModelProperty(value = "报送状态描述",example = "已报送")
    private String reportStatus;

    /**
     * 最后编辑时间
     */
    @ApiModelProperty(value = "最后编辑时间", example = "2019-01-01")
    private String updateTime;
}
