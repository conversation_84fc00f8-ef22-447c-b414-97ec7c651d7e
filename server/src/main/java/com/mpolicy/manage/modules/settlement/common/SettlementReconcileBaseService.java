package com.mpolicy.manage.modules.settlement.common;

import cn.hutool.core.date.StopWatch;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.client.SettlementReconcileClient;
import com.mpolicy.settlement.core.common.reconcile.*;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 结算系统服务
 *
 * <AUTHOR>
 * @since 2023-05-25 00:33
 */
@Component
@Slf4j
public class SettlementReconcileBaseService {

    @Autowired
    private SettlementReconcileClient settlementReconcileClient;

    /**
     * 上传保司对账单文件
     *
     * @param reconcileCode         对账单唯一编号
     * @param reconcileTemplateCode 对账单模版编码
     * @param fileCode              文件编码
     * @param userName              操作人
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:45
     */
    public String uploadReconcileFile(String reconcileCode, String reconcileTemplateCode, String fileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.uploadReconcileFile(reconcileCode, reconcileTemplateCode, fileCode, userName);
        if (!result.isSuccess()) {
            log.warn("结算上传对账单操作信息失败，结算对账单号={} 操作文件类型={} fileCode={} userName={} msg={}", reconcileCode, reconcileTemplateCode, fileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("结算上传对账单操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 重新上传保司对账单文件
     *
     * @param reconcileCode         对账单唯一编号
     * @param reconcileTemplateCode 对账单模版编码
     * @param sourceFileCode        原文件编码
     * @param fileCode              文件编码
     * @param userName              操作人
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:46
     */
    public String retryUploadReconcileFile(String reconcileCode, String reconcileTemplateCode, String sourceFileCode, String fileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.retryUploadReconcileFile(reconcileCode, reconcileTemplateCode, sourceFileCode, fileCode, userName);
        if (!result.isSuccess()) {
            log.warn("结算重新上传对账单操作信息失败，结算对账单号={} 操作文件类型={} sourceFileCode={} fileCode={} userName={} msg={}", reconcileCode, reconcileTemplateCode, sourceFileCode, fileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("结算重新上传对账单操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 删除对账关联对账文件
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作人
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:46
     */
    public String removeReconcileFile(String reconcileCode, String fileCode, String userName) {
        Result<String> result = settlementReconcileClient.removeReconcileFile(reconcileCode, fileCode, userName);
        if (!result.isSuccess()) {
            log.warn("结算删除对账关联对账文件操作信息失败，结算对账单号={} fileCode={} userName={} msg={}", reconcileCode, fileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        log.info("结算删除对账关联对账文件操作成功...");
        return result.getData();
    }

    /**
     * 开始对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:46
     */
    public String startReconcile(String reconcileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.startReconcile(reconcileCode, userName);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【开始对账】失败，结算对账单号={}  userName={} msg={}", reconcileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("操作结算中心【开始对账】操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 批量处理对账单精度信息
     *
     * @param input 批量处理对账单精度信息对象
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 17:48
     */
    public String reconcileAmountAccuracy(ReconcileAmountAccuracyInput input) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.reconcileAmountAccuracy(input);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【批量精度处理】失败，请求报文={} msg={}", JSON.toJSONString(input), result.getMsg());
            throw new GlobalException(result);
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("操作结算中心【批量精度处理】操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 重新对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:47
     */
    public String retryStartReconcile(String reconcileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.retryStartReconcile(reconcileCode, userName);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【重新对账】失败，结算对账单号={}  userName={} msg={}", reconcileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("操作结算中心【重新对账】操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 完成对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:47
     */
    public String finishReconcile(String reconcileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.finishReconcile(reconcileCode, userName);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【完成对账】失败，结算对账单号={}  userName={} msg={}", reconcileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("操作结算中心【完成对账】操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 刷新对账单保单数据
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:47
     */
    public String reconcileRefreshPolicy(String reconcileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.reconcileRefreshPolicy(reconcileCode, userName);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【刷新对账单保单数据】失败，结算对账单号={}  userName={} msg={}", reconcileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("操作结算中心【刷新对账单保单数据】操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 关闭对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:47
     */
    public String closeReconcile(String reconcileCode, String userName) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        Result<String> result = settlementReconcileClient.closeReconcile(reconcileCode, userName);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【关闭对账】失败，结算对账单号={}  userName={} msg={}", reconcileCode, userName, result.getMsg());
            throw new GlobalException(result);
        }
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("操作结算中心【关闭对账】操作成功...耗时：{}", millis);
        return result.getData();
    }

    /**
     * 差异处理申请
     *
     * @param diffBacklogInput 差异处理申请对象
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/5/25 00:47
     */
    public String diffBacklog(DiffBacklogInput diffBacklogInput) {
        Result<String> result = settlementReconcileClient.diffBacklog(diffBacklogInput);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【差异处理】失败，请求={}  msg={}", JSON.toJSONString(diffBacklogInput), result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 刷新任务
     */
    public void refreshSettlementEventJob(RefreshSettlementEventJobVo input) {
        Result<String> result = settlementReconcileClient.refreshSettlementEventJob(input);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【刷新JOB任务】失败，请求={}  msg={}", JSON.toJSONString(input), result.getMsg());
            throw new GlobalException(result);
        }
    }

    /**
     * 刷新明细
     */
    public void handleSettlementPolicyInfo(HandleSettlementPolicyInfoVo input) {
        Result<String> result = settlementReconcileClient.handleSettlementPolicyInfo(input);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【刷新保单信息】失败，请求={}  msg={}", JSON.toJSONString(input), result.getMsg());
            throw new GlobalException(result);
        }
    }

    /**
     * 批量刷新结算单保费费率
     *
     * @param reconcileCode 结算编码
     */
    public String batchRefreshRate(String reconcileCode) {
        Result<String> result = settlementReconcileClient.batchRefreshRate(reconcileCode);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【刷新保单信息】失败，请求={}  msg={}", reconcileCode, result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 创建对账单
     */
    public String forceCreateReconcile(CreateReconcileVo createReconcileVo) {
        Result<String> result = settlementReconcileClient.forceCreateReconcile(createReconcileVo);
        if (!result.isSuccess()) {
            log.warn("操作结算中心【刷新保单信息】创建对账单，请求={}  msg={}", JSONUtil.toJsonStr(createReconcileVo), result.getMsg());
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 处理结算任务
     *
     * @param codeList 事件编码集合
     */
    public void handleSettlementEventJob(ArrayList<String> codeList) {
        Result<String> result = settlementReconcileClient.handleSettlementEventJob(codeList);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
    }


    /**
     * 冲正处理
     *
     * @param vo 冲正处理
     */
    public void rectificationOne(RectificationOneVo vo) {
        Result<String> result = settlementReconcileClient.rectificationOne(vo);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
    }

    /**
     * 校验保单是否已完成对账
     * @param policyNo 保单号
     * @return True:已完成对账；False:未完成对账
     */
    public Boolean isCompletedReconcileRecord(String policyNo){
        log.info("开始校验保单是否完成结算:{}",policyNo);
        Result<Boolean> result = settlementReconcileClient.isCompletedReconcileRecord(policyNo);
        log.info("结算查询完成:{}",result);
        if(!result.isSuccess()){
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 校验保单是否已完成对账
     * @param policyNoList 保单号
     * @return True:已完成对账；False:未完成对账
     */
    public List<BatchIsCompletedReconcileRecord> batchIsCompletedReconcileRecord(List<String> policyNoList){
        log.info("开始校验保单是否完成结算:{}",policyNoList);
        Result<List<BatchIsCompletedReconcileRecord>> result = settlementReconcileClient.batchIsCompletedReconcileRecord(policyNoList);
        log.info("结算查询完成:{}",result.getMsg());
        if(!result.isSuccess()){
            throw new GlobalException(result);
        }
        return result.getData();
    }

    /**
     * 校验批单是否已完成对账
     * @param preservationCode 保全编号
     * @return True:已完成对账；False:未完成对账
     */
    public Boolean isCompletedReconcileRecord4Correct(String preservationCode){
        log.info("开始校验批单是否完成结算:{}",preservationCode);

        Result<List<BatchIsCompletedReconcileRecordPreservation>> result = settlementReconcileClient.batchIsCompletedReconcileRecordPreservation(Collections.singletonList(preservationCode));
        log.info("结算查询完成:{}",result);
        if(!result.isSuccess()){
            throw new GlobalException(result);
        }
        List<BatchIsCompletedReconcileRecordPreservation> data = result.getData();
        if(CollectionUtils.isEmpty(data)){
            return Boolean.FALSE;
        }
        return data.get(0).getIsCompleted();
    }
}