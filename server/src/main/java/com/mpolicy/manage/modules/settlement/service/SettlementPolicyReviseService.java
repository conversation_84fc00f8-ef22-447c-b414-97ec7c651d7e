package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyReviseEntity;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseListOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseOut;

public interface SettlementPolicyReviseService extends IService<SettlementPolicyReviseEntity> {
    /**
     * 分页数据
     * @param input 查询条件
     * @return 数据结果
     */
    PageUtils<SettlementPolicyReviseListOut> findSettlementPolicyReviseList(SettlementPolicyReviseInput input);

    /**
     * 根据id查询数据
     * @param id 数据id
     * @return 数据结果
     */
    SettlementPolicyReviseOut findSettlementPolicyReviseById(Integer id);
}
