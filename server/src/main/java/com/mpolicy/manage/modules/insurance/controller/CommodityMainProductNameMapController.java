package com.mpolicy.manage.modules.insurance.controller;

import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.insurance.entity.CommodityMainProductNameMapEntity;
import com.mpolicy.manage.modules.insurance.service.CommodityMainProductNameMapService;
import com.mpolicy.manage.modules.insurance.vo.CommodityMainProductNameMapAllType;
import com.mpolicy.manage.modules.insurance.vo.CommodityMainProductNameMapExportExcel;
import com.mpolicy.manage.modules.insurance.vo.CommodityMainProductNameMapQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 商品险种分类映射
 *
 *
 * @create 2024/11/1
 * @since 1.0.0
 */
@Slf4j
@Validated
@RestController
@Api(tags = "商品险种分类映射")
@RequestMapping("/insurance/commodity/main/product/name/map")
public class CommodityMainProductNameMapController {

    @Resource
    private CommodityMainProductNameMapService commodityMainProductNameMapService;


    /**
     *
     *
     *
     *
     * 获取产品映射的所有的字典
     *
     *
     * @return 保险产品列表
     */
    @ApiOperation(value = "所有字典码值", notes = "所有字典码值")
    @GetMapping("/dictionary")
    public Result<CommodityMainProductNameMapAllType> dictionary() {
        return Result.success(CommodityMainProductNameMapAllType.getInstance());
    }

    /**
     *
     * 保存
     *
     * @return
     */
    @ApiOperation(value = "新增产品小鲸险种分类映射", notes = "新增产品小鲸险种分类映射")
    @PostMapping("/save")
    @RequiresPermissions(value = {"policy:risk:commodity:all"})
    public Result<Object> save(@RequestBody CommodityMainProductNameMapEntity commodityMainProductNameMapEntity) {
        commodityMainProductNameMapService.save(commodityMainProductNameMapEntity);
        return Result.success();
    }

    /**
     * 更新保险产品
     * @param commodityMainProductNameMapEntity 保险产品
     */
    @ApiOperation(value = "修改产品小鲸险种分类映射", notes = "修改产品小鲸险种分类映射")
    @PostMapping("/update")
    @RequiresPermissions(value = {"policy:risk:commodity:all"})
    public Result<Object> update(@RequestBody CommodityMainProductNameMapEntity commodityMainProductNameMapEntity) {
        commodityMainProductNameMapService.updateById(commodityMainProductNameMapEntity);
        return Result.success();
    }

    /**
     *
     *
     *
     * 分页查询条件
     *
     *
     * @param
     *
     *
     * commodityMainProductNameMapQueryVO
     *
     *
     * 查询条件
     */
    @ApiOperation(value = "查询分页产品小鲸险种分类映射", notes = "查询分页产品小鲸险种分类映射")
    @GetMapping("/page")
    @RequiresPermissions(value = {"policy:risk:commodity:all"})
    public Result<IPage<CommodityMainProductNameMapEntity>> page( CommodityMainProductNameMapQueryVO commodityMainProductNameMapQueryVO) {
        IPage<CommodityMainProductNameMapEntity> page = commodityMainProductNameMapService.queryPageByQueryVO(commodityMainProductNameMapQueryVO);
        return Result.success(page);
    }

    /**
     *
     *
     * 批量导出险种分类映射
     *
     *
     *
     * @param response
     *
     * 相应
     *
     * @return
     */
    @GetMapping("/export")
    @RequiresPermissions(value = {"policy:risk:commodity:all"})
    @ApiOperation(value = "批量导出分页产品小鲸险种分类映射", notes = "批量导出分页产品小鲸险种分类映射",produces = "application/octet-stream")
    public Result export(HttpServletResponse response, CommodityMainProductNameMapQueryVO commodityMainProductNameMapQueryVO) {
        List<CommodityMainProductNameMapExportExcel> commodityMainProductNameMapExportExcels = commodityMainProductNameMapService.getExportByQueryVO(commodityMainProductNameMapQueryVO);
        ExcelUtil.writeExcel(response, commodityMainProductNameMapExportExcels, URLUtil.encode("险种分类映射", StandardCharsets.UTF_8), "sheet1", new CommodityMainProductNameMapExportExcel());
        return Result.success();
    }

}
