package com.mpolicy.manage.modules.regulators.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 监管报备月度报备主表
 * 
 * <AUTHOR>
 * @date 2022-01-20 14:34:19
 */
@TableName("ep_regulators_report_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegulatorsReportInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 报备月度编码
	 */
	private String regulatorsNo;
	/**
	 * 报备月度名称
	 */
	private String regulatorsName;
	/**
	 * 报备机构分支数量
	 */
	private Integer regulatorsOrgCount;
	/**
	 * 报备年度
	 */
	private Integer regulatorsYear;
	/**
	 * 报备月度
	 */
	private Integer regulatorsMonth;
	/**
	 * 报备上传状态 0否1是
	 */
	private Integer uploadCompleteStatus;
	/**
	 * 报备状态 0未报送1已报送
	 */
	private Integer reportStatus;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
