package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentApplyUpdateInfoVO
 * Description: 代理人修改信息请求
 * date: 2022/11/30 12:06
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyUpdateInfoVO implements Serializable {
    private static final long serialVersionUID = -5488206945138126181L;

    @ApiModelProperty(value = "代理人编码",required = true)
    @NotBlank(message = "代理人编码不能为空")
    private String agentCode;

    @ApiModelProperty("附件列表")
    @Valid
    private List<AgentApplyAttachmentVo> accessoryList;

    @ApiModelProperty(value = "代理人基本信息", required = true)
    @NotNull(message = "代理人基本信息不能为空")
    @Valid
    private AgentApplyBasicInfoOut agentUserInfo;

    @ApiModelProperty(value = "代理人扩展信息", required = true)
    @NotNull(message = "代理人扩展信息不能为空")
    @Valid
    private AgentApplyExtendInfoOut agentExtend;

    @ApiModelProperty(value = "代理人工作室信息", required = true)
    @Valid
    private AgentOnlineStudioIpVo agentOnlineStudioIpVo;
}
