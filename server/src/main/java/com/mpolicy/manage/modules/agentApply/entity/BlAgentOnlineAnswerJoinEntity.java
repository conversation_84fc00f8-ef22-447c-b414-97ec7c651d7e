package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 代理人入职答题表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-07 13:40:42
 */
@TableName("bl_agent_online_answer_join")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineAnswerJoinEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    private String agentCode;
    /**
     * 所属题目code
     */
    private String qCode;
    /**
     * 是否通过(0:未通过 1:通过)
     */
    private Integer isPass;
    /**
     * 答题结果
     */
    private String answer;
    /**
     * 得分
     */
    private String score;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
}
