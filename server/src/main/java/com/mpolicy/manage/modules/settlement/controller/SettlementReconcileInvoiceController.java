package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceService;
import com.mpolicy.manage.modules.settlement.service.impl.SettlementReconcileInvoiceServiceImpl;
import com.mpolicy.manage.modules.settlement.vo.invoice.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.simpleframework.xml.core.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */
@RestController
@RequestMapping("settlement/reconcile/invoice")
@Api(tags = "对账单发票信息")
public class SettlementReconcileInvoiceController {

    @Autowired
    private SettlementReconcileInvoiceService settlementReconcileInvoiceService;


    @GetMapping("list")
    @RequiresPermissions("settlement:invoice:all")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<SettlementReconcileInvoiceListOut>> list(SettlementReconcileInvoiceListInput input) {
        PageUtils<SettlementReconcileInvoiceListOut> page = settlementReconcileInvoiceService.findSettlementReconcileInvoiceList(input);
        return Result.success(page);
    }


    @GetMapping("info/{invoiceCode}")
    @RequiresPermissions("settlement:invoice:all")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<SettlementReconcileInvoiceInfoOut> info(@PathVariable(value = "invoiceCode", required = false) @NotNull(message = "发票编码不能为空") @ApiParam(value = "发票编码") String invoiceCode) {
        SettlementReconcileInvoiceInfoOut settlementReconcileInvoice = settlementReconcileInvoiceService.findSettlementReconcileInvoiceByInvoiceCode(invoiceCode);
        return Result.success(settlementReconcileInvoice);
    }

    @SysLog("保存对账单发票信息数据")
    @PostMapping("save")
    @RequiresPermissions("settlement:invoice:all")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid SettlementReconcileInvoiceSaveInput input) {
        settlementReconcileInvoiceService.saveSettlementReconcileInvoice(input);
        return Result.success();
    }

    @SysLog("修改对账单发票信息数据")
    @PostMapping("update")
    @RequiresPermissions("settlement:invoice:all")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid SettlementReconcileInvoiceUpdateInput input) {
        settlementReconcileInvoiceService.updateSettlementReconcileInvoiceById(input);
        return Result.success();
    }

    @SysLog("开票财务审核")
    @PostMapping("financeExamine")
    @ApiOperation(value = "开票财务审核", notes = "开票财务审核", httpMethod = "POST")
    public Result financeExamine(@RequestBody(required = false) @Valid FinanceExamineVo input) {
        settlementReconcileInvoiceService.financeExamine(input);
        return Result.success();
    }

    @SysLog("系统回退")
    @PostMapping("systemRollback")
    @ApiOperation(value = "系统回退", notes = "系统回退", httpMethod = "POST")
    public Result systemRollback(@RequestBody(required = false) @Valid SystemRollbackVo input) {
        settlementReconcileInvoiceService.systemRollback(input);
        return Result.success();
    }

    @SysLog("发送至保司")
    @PostMapping("sendToCompany")
    @ApiOperation(value = "发送至保司", notes = "发送至保司", httpMethod = "POST")
    public Result sendToCompany(@RequestBody(required = false) @Valid SendToCompanyVo input) {
        settlementReconcileInvoiceService.sendToCompany(input);
        return Result.success();
    }

    @SysLog("更新快递信息")
    @PostMapping("updateExpress")
    @ApiOperation(value = "更新快递信息", notes = "更新快递信息", httpMethod = "POST")
    public Result updateExpress(@RequestBody(required = false) @Valid UpdateExpressVo input) {
        settlementReconcileInvoiceService.updateExpress(input);
        return Result.success();
    }

    @SysLog("查询轨迹信息")
    @GetMapping("findReconcileInvoiceTrackList")
    @ApiOperation(value = "查询轨迹信息", notes = "查询轨迹信息", httpMethod = "GET")
    public Result<List<ReconcileInvoiceTrackListOut>> findReconcileInvoiceTrackList(@RequestParam(required = false, value = "invoiceCode")
                                                                                    @Validate @NotBlank(message = "申请编码不能为空") String invoiceCode) {
        List<ReconcileInvoiceTrackListOut> result = settlementReconcileInvoiceService.findReconcileInvoiceTrackList(invoiceCode);
        return Result.success(result);
    }

    @SysLog("删除对账单发票信息信息")
    @PostMapping("delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false) @NotEmpty(message = "删除的数据ids不能为空") @ApiParam(value = "批量删除的ID") Integer[] ids) {
        settlementReconcileInvoiceService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }

    @GetMapping("findReconcileInvoiceSettlementReconcileList")
    @ApiOperation(value = "根据发票编码获取发票关联对账单信息", notes = "根据发票编码获取发票关联对账单信息", httpMethod = "GET")
    public Result<List<ReconcileInvoiceSettlementReconcileOut>> findReconcileInvoiceSettlementReconcileList(@RequestParam(value = "invoiceCode", required = false)
                                                              @NotBlank(message = "发票编码不能为空") String invoiceCode) {
      List<ReconcileInvoiceSettlementReconcileOut> resultList =  settlementReconcileInvoiceService.findReconcileInvoiceSettlementReconcileList(invoiceCode);
        return Result.success(resultList);
    }

    @PostMapping("retry/{invoiceCode}")
    @ApiOperation(value = "发票重试", notes = "发票重试", httpMethod = "POST")
    public Result<Void> retry(@PathVariable(value = "invoiceCode", required = false) @NotNull(message = "发票申请编码不能为空") @ApiParam(value = "发票申请编码不能为空") String invoiceApplyCode) {

        settlementReconcileInvoiceService.invoiceToFttmRetry(invoiceApplyCode);
        return Result.success();

    }
    @Autowired
    private SettlementReconcileInvoiceServiceImpl reconcileInvoiceService;

    @PostMapping("aaa/{invoiceCode}")
    @ApiOperation(value = "test", notes = "test", httpMethod = "POST")
    public Result<Void> aaa(@PathVariable(value = "invoiceCode", required = false) @NotNull(message = "发票申请编码不能为空") @ApiParam(value = "发票申请编码不能为空") String invoiceApplyCode, int templateType) {

        reconcileInvoiceService.sendDingTalkMessage(templateType, reconcileInvoiceService.lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, invoiceApplyCode).one());
        return Result.success();

    }
}
