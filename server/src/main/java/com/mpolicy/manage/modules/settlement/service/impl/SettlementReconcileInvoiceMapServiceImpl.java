package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInvoiceMapDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceMapEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceMapService;
import org.springframework.stereotype.Service;

@Service("settlementReconcileInvoiceMapService")
public class SettlementReconcileInvoiceMapServiceImpl extends ServiceImpl<SettlementReconcileInvoiceMapDao, SettlementReconcileInvoiceMapEntity> implements SettlementReconcileInvoiceMapService {


}
