package com.mpolicy.manage.modules.regulators.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportData;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportList;

import java.util.Map;

/**
 * 监管报备机构信息表
 *
 * <AUTHOR>
 * @date 2022-01-20 14:34:19
 */
public interface RegulatorsReportOrgService extends IService<RegulatorsReportOrgEntity> {

    /**
     * <p>
     * 月度机构报备列表
     * </p>
     *
     * @param paramMap 查询条件
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportList>
     * <AUTHOR>
     * @since 2022/1/20
     */
    PageUtils<OrgRegulatorsReportList> queryOrgRegulatorsReportList(String regulatorsNo, Map<String, Object> paramMap);

    /**
     * <p>
     * 获取机构报备报告内容
     * </p>
     *
     * @param orgRegulatorsNo 机构报备报告唯一编号
     * @return com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportData
     * <AUTHOR>
     * @since 2022/1/21
     */
    OrgRegulatorsReportData queryOrgRegulatorsReport(String orgRegulatorsNo);
}

