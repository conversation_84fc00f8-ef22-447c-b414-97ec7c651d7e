package com.mpolicy.manage.modules.settlement.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReconcileCompanySubjectInfo implements Serializable {


    private static final long serialVersionUID = 4033286345663966122L;
    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 结算保司对账编码
     */
    private String reconcileCompanyCode;
    /**
     * 结算科目编码
     */
    private String reconcileSubjectCode;
    /**
     * 账单日
     */
    private Integer statementDate;
    /**
     * 是否是首年
     */
    private Integer isFirstYear;
    /**
     * 账单日月末最后一天0:不是 1:是
     */
    private Integer statementDateMonthEnd;
    /**
     * 适用范围0:通用 1:指定险种
     */
    private Integer subjectScope;
    /**
     * 对账方式 0:实时 1:过宽
     */
    private Integer reconciliationMethod;
    /**
     * 过宽日期截止类型
     */
    private String wideDateEndType;
    /**
     * 过宽日期截止每月几日
     */
    private Integer wideDateEndDay;
    /**
     * 实收日期截止类型
     */
    private String paymentReceivedDateEndType;
    /**
     * 实收日期截止每月几日
     */
    private Integer paymentReceivedDateEndDay;
    /**
     * 续期生效日期截止类型
     */
    private String renewalEffectiveTimeEndType;
    /**
     * 续期生效日期截止每月几日
     */
    private Integer renewalEffectiveTimeEndDay;

    /**
     * 宽限期
     */
    private Integer gracePeriodDay;
    /**
     * 投保时间截止类型
     */
    private String applicantTimeEndType;
    /**
     * 投保时间截止每月几日
     */
    private Integer applicantTimeEndDay;
    /**
     * 承保时间截止类型
     */
    private String underwriteTimeEndType;
    /**
     * 承保时间截止每月几日
     */
    private Integer underwriteTimeEndDay;
    /**
     * 缴费期截止类型
     */
    private String paymentPeriodEndType;
    /**
     * 缴费期截止每月几日
     */
    private Integer paymentPeriodEndDay;
    /**
     * 生效时间截止类型
     */
    private String effectiveTimeEndType;
    /**
     * 生效时间截止每月几日
     */
    private Integer effectiveTimeEndDay;
    /**
     * 回执时间截止类型
     */
    private String receiptTimeEndType;
    /**
     * 回执时间截止每月几日
     */
    private Integer receiptTimeEndDay;
    /**
     * 回访时间截止类型
     */
    private String callbackTimeEndType;
    /**
     * 回访时间截止每月几日
     */
    private Integer callbackTimeEndDay;

    /**
     * 到期前续投承保时间截止类型
     */
    private String beforeInsureTimeEndType;
    /**
     * 到期前续投承保时间截止每月几日
     */
    private Integer beforeInsureTimeEndDay;
    /**
     * 到期前续投承保时间截止类型
     */
    private String beforeUnderwriteTimeEndType;
    /**
     * 到期前续投承保时间截止每月几日
     */
    private Integer beforeUnderwriteTimeEndDay;
    /**
     * 到期前续投生效时间截止类型
     */
    private String beforeEffectiveTimeEndType;
    /**
     * 到期前续投生效时间截止每月几日
     */
    private Integer beforeEffectiveTimeEndDay;

    /**
     * 到期后续投承保时间截止类型
     */
    private String afterInsureTimeEndType;
    /**
     * 到期后续投承保时间截止每月几日
     */
    private Integer afterInsureTimeEndDay;
    /**
     * 到期后续投承保时间截止类型
     */
    private String afterUnderwriteTimeEndType;
    /**
     * 到期后续投承保时间截止每月几日
     */
    private Integer afterUnderwriteTimeEndDay;
    /**
     * 到期后续投生效时间截止类型
     */
    private String afterEffectiveTimeEndType;
    /**
     * 到期后续投生效时间截止每月几日
     */
    private Integer afterEffectiveTimeEndDay;

    /**
     * 保全生效时间截止类型
     */
    private String preservationEffectiveTimeEndType;
    /**
     * 保全生效时间截止每月几日
     */
    private Integer preservationEffectiveTimeEndDay;
    /**
     * 批改时间截止类型
     */
    private String correctTimeEndType;
    /**
     * 批改时间截止每月几日
     */
    private Integer correctTimeEndDay;

    /**
     * 状态0:禁用 1:启用
     */
    private Integer subjectRuleStatus;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 指定协议产品编码
     */
    private List<String> insuranceProductCodeList;

    private List<Integer> policyMethodList;
}
