package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算保单明细记录表
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
@TableName("settlement_policy_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementPolicyInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 单据明细唯一编号
	 */
	private String settlementCode;
	/**
	 * 结算类型0:小鲸 1:非小鲸
	 */
	private Integer reconcileType;
	/**
	 * 原单据明细编号
	 */
	private String sourceSettlementCode;
	/**
	 * 调整业务类型;新业务数据正确数据/新业务数据冲正数据/原始业务数据
	 */
	private String sourceSettlementType;
	/**
	 * 调整业务说明;同步保单保全信息（修改保费）
	 */
	private String sourceSettlementDesc;
	/**
	 * 记账日期
	 */
	private Date settlementDate;
	/**
	 * 记账时间
	 */
	private Date settlementTime;
	/**
	 * 记账生成类型;1 事件、2系统冲正、3 线下对账单确认
	 */
	private Integer settlementGenerateType;
	/**
	 * 记账事件编码;新单、续期、保全、费率调整、其他
	 */
	private String settlementEventCode;
	/**
	 * 记账事件摘要说明;新契约、犹豫期退保
	 */
	private String settlementEventDesc;
	/**
	 * 记账单据说明
	 */
	private String settlementDesc;
	/**
	 * 科目编码
	 */
	private String settlementSubjectCode;
	/**
	 * 科目名称
	 */
	private String settlementSubjectName;

	/**
	 * 业务类型 1:城市业务、2:农村业务
	 */
	private Integer businessType;

	/**
	 * 保单合同号
	 */
	private String contractCode;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 第三方保单号
	 */
	private String thirdPolicyNo;
	/**
	 * 保单产品类型;个团车财
	 */
	private String policyProductType;
	/**
	 * 续投状态-1无、0到期前、1、到期后
	 */
	private Integer gracePeriodStatus;
	/**
	 * 保单状态
	 */
	private String policyStatus;
	/**
	 * 是否犹豫期退保;-1无0否1是
	 */
	private Integer hesitateSurrender;
	/**
	 * 保单来源
	 */
	private String policySource;

	/**
	 * 三方编码
	 */
	private String serialNumber;

	/**
	 * 保险公司编码
	 */
	private String companyCode;
	/**
	 * 保险公司名称
	 */
	private String companyName;
	/**
	 * 产品编码
	 */
	private String commodityCode;
	/**
	 * 产品名称
	 */
	private String commodityName;
	/**
	 * 产品类型
	 */
	private String commodityType;
	/**
	 * 销售方式;0:网销 1:线下
	 */
	private Integer salesType;
	/**
	 * 销售平台
	 */
	private String salesPlatform;
	/**
	 * 出单客户编号
	 */
	private String customerCode;
	/**
	 * 投保时间
	 */
	private Date applicantTime;
	/**
	 * 交单时间
	 */
	private Date orderTime;
	/**
	 * 承保时间
	 */
	private Date approvedTime;
	/**
	 * 生效时间
	 */
	private Date enforceTime;
	/**
	 * 应缴时间
	 */
	private Date payableTime;
	/**
	 * 实缴时间
	 */
	private Date realityTime;
	/**
	 * 回访状态 是否需要录入回访 1需要;0不需要
	 */
	private Integer revisitStatus;
	/**
	 * 回访时间
	 */
	private Date revisitTime;
	/**
	 * 回执状态 是否需要录入回执 1需要;0不需要
	 */
	private Integer receiptStatus;
	/**
	 * 回执时间
	 */
	private Date receiptTime;
	/**
	 * 投保人姓名
	 */
	private String applicantName;

	/**
	 * 投保人手机号码
	 */
	private String applicantMobile;

	/**
	 * 投保主体证件号
	 */
	private String applicantIdCard;

	/**
	 * 投保人出生日期
	 */
	private Integer applicantGender;

	/**
	 * 投保人出生日期
	 */
	private Date applicantBirthday;

	/**
	 * 被保人年龄
	 */
	private Integer insuredPolicyAge;

	/**
	 * 被保人姓名
	 */
	private String insuredName;

	/**
	 * 被保人手机号码
	 */
	private String insuredMobile;

	/**
	 * 被保人证件号码
	 */
	private String insuredIdCard;

	/**
	 * 投保人出生日期
	 */
	private Integer insuredGender;

	/**
	 * 投保人出生日期
	 */
	private Date insuredBirthday;

	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 协议编号
	 */
	private String protocolCode;
	/**
	 * 协议产品编码
	 */
	private String protocolProductCode;
	/**
	 * 协议产品名称
	 */
	private String protocolProductName;
	/**
	 * 计划编码
	 */
	private String planCode;
	/**
	 * 费率标识
	 */
	private String premCode;
	/**
	 * 计划名称
	 */
	private String planName;
	/**
	 * 长短险标记 0短险1长险
	 */
	private Integer longShortFlag;
	/**
	 * 险种大类
	 */
	private String productGroup;

	/**
	 * 二级分类编码
	 */
	private String level2Code;
	/**
	 * 三级分类编码
	 */
	private String level3Code;

	/**
	 * 险种类型
	 */
	private String productType;
	/**
	 * 是否主险
	 */
	private Integer mainInsurance;
	/**
	 * 附加险类型;0-其他类型附加险 1-附加投保人豁免
	 */
	private Integer additionalRisksType;
	/**
	 * 险种生效日期
	 */
	private Date effectiveDate;
	/**
	 * 险种截止时间
	 */
	private String endDate;
	/**
	 * 续期年期
	 */
	private Integer renewalYear;
	/**
	 * 续期期数
	 */
	private Integer renewalPeriod;
	/**
	 * 保额
	 */
	private BigDecimal coverage;
	/**
	 * 保额单位;保额单位，0：元，1：份，2：元/天
	 */
	private Integer coverageUnit;
	/**
	 * 保额单位名称
	 */
	private String coverageUnitName;
	/**
	 * 保障期间类型
	 */
	private String insuredPeriodType;
	/**
	 * 保障时长
	 */
	private Integer insuredPeriod;
	/**
	 * 缴费方式;年交/半年交/季交/月交/趸交/不定期交/短险一次交清
	 */
	private String periodType;
	/**
	 * 缴费期间类型
	 */
	private String paymentPeriodType;
	/**
	 * 缴费时长
	 */
	private Integer paymentPeriod;
	/**
	 * 年金领取年龄
	 */
	private String drawAge;
	/**
	 * 保费
	 */
	private BigDecimal premium;
	/**
	 * 险种总保费
	 */
	private BigDecimal productPremiumTotal;
	/**
	 * 折标保费
	 */
	private BigDecimal discountPremium;
	/**
	 * 份数
	 */
	private Integer copies;
	/**
	 * 保全申请编号
	 */
	private String preservationCode;
	/**
	 * 批单号
	 */
	private String endorsementNo;
	/**
	 * 批改时间
	 */
	private Date endorsementTime;
	/**
	 * 保全类型
	 */
	private String preservationType;
	/**
	 * 保全项目
	 */
	private String preservationProject;
	/**
	 * 保全变更原因
	 */
	private String preservationWhy;
	/**
	 * 保全生效时间
	 */
	private Date preservationEffectTime;
	/**
	 * 保全期数
	 */
	private Integer preservationPeriod;
	/**
	 * 退保时间
	 */
	private Date surrenderTime;
	/**
	 * 退保金额
	 */
	private BigDecimal surrenderAmount;
	/**
	 * 主代理人编码
	 */
	private String mainAgentCode;
	/**
	 * 主代理人机构编码
	 */
	private String orgCode;
	/**
	 * 渠道推荐人类型;0:推荐人 1:代理人
	 */
	private Integer referrerType;
	/**
	 * 渠道推荐人编码
	 */
	private String referrerCode;
	/**
	 * 渠道推荐人姓名
	 */
	private String referrerName;
	/**
	 * 销售渠道编码码
	 */
	private String channelCode;
	/**
	 * 销售渠道名称
	 */
	private String channelName;

	/**
	 * 渠道推荐人分支编
	 */
	private String channelBranchCode;
	/**
	 * 渠道推荐人分支名称
	 */
	private String channelBranchName;

	/**
	 * 结算方式
	 */
	private Integer settlementRateMethod =0;
	/**
	 * 监管报送类型0:报送保费和收入 1:仅报送收入 2:仅报送保费
	 */
	private Integer regulatoryReportType = 0;
	/**
	 * 税费
	 */
	private BigDecimal settlementRateTax;
	/**
	 * 结算比例
	 */
	private String settlementRate;
	/**
	 * 结算金额
	 */
	private BigDecimal settlementAmount;
	/**
	 * 结算月份
	 */
	private String settlementMonth;

	/**
	 * 内部签署方编码
	 */
	private String innerSignatoryCode;
	/**
	 * 内部签署方名称
	 */
	private String innerSignatoryName;
	/**
	 * 外部签署方类型字典
	 */
	private String externalSignatoryType;
	/**
	 * 外部签署方编码
	 */
	private String externalSignatoryCode;
	/**
	 * 外部签署方名称
	 */
	private String externalSignatoryName;

	/**
	 * 可对账状态;0不可对账1可对账
	 */
	private Integer reconcileExecuteStatus;
	/**
	 * 可对账日期
	 */
	private Date reconcileExecuteTime;
	/**
	 * 可对账说明
	 */
	private String reconcileExecuteDesc;
	/**
	 * 完成对账单号
	 */
	private String reconcileCode;
	/**
	 * 对账状态;0未对账1对账中2已完成对账
	 */
	private Integer reconcileStatus;
	/**
	 * 对账操作员
	 */
	private String reconcileUser;
	/**
	 * 对账完成时间
	 */
	private Date reconcileTime;
	/**
	 * 事件来源编码
	 */
	private String eventSourceCode;
	/**
	 * 是否删除;0有效-1删除
	 */
	private Integer rectificationMark;
	/**
	 * 结算月度
	 */
	private String postponedMonth;

	/**
	 * 挂起状态0:未挂起 1:挂起
	 */
	private Integer hangStatus;

	/**
	 * 挂起对账单编码
	 */
	private String hangReconcileCode;

	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;

}
