package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApportionEntity;

/**
 * 科目范围(动态科目)分摊明细
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
public interface SettlementCostSubjectDataDynamicApportionService extends IService<SettlementCostSubjectDataDynamicApportionEntity> {

}

