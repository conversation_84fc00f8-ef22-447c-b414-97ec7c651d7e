package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsProductDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummarySupervisorPerformance;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/2/3 23:56
 * @Version 1.0
 */

@Service
public class DetailSummarySupervisorPerformanceServiceImpl extends AbsProductDimensionService<List<DetailSummarySupervisorPerformance>> {
    @Override
    public List<DetailSummarySupervisorPerformance> querySummary(PageSettlementDetailParams p) {

        DetailSummarySupervisorPerformance r = costAutoInfoDao.supervisorPerformanceSummary(p);
        if (Objects.nonNull(r)) {
            r.setMonth(calLastMonth(p));
        }
        return Lists.newArrayList(r);
    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.SUPERVISOR_PERFORMANCE);
    }
}
