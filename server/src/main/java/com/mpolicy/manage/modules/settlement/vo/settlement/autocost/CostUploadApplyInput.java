package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 装载结算文件导入申请信息
 *
 * <AUTHOR>
 * @date 2023-12-18 20:48
 */
@Data
@ApiModel(value = "装载结算文件导入申请信息", description = "装载结算文件导入申请信息")
public class CostUploadApplyInput implements Serializable {

    private static final long serialVersionUID = 1;
    /**
     * 文件编码
     */
    @ApiModelProperty(value = "文件编码", required = true, example = "oss20231218204322ClEHFv")
    @NotBlank(message = "文件编码不能为空")
    private String fileCode;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true, example = "结佣项导入浮动科目模板-v1.1.xlsx")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    /**
     * 文件地址
     */
    @ApiModelProperty(value = "文件地址", required = true, example = "other/portrait/20231218/0c19605b8e584645ae149127ecd73edf/结佣项导入浮动科目模板-v1.1.xlsx")
    @NotBlank(message = "文件地址不能为空")
    private String filePath;


    /**
     * 文件地址域名
     */
    @ApiModelProperty(value = "文件地址", required = true, example = "https://oss-xjxhserver.xiaowhale.com/other/portrait/20231218/0c19605b8e584645ae149127ecd73edf/结佣项导入浮动科目模板-v1.1.xlsx")
    @NotBlank(message = "文件地址不能为空")
    private String domainPath;

    /**
     * 操作员
     */
    @ApiModelProperty(value = "操作员", required = true, example = "张三")
    @NotBlank(message = "操作员不能为空")
    private String userName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true, example = "FIRST_BASIC_COMM")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    /**
     * 操作月份
     */
    private String opMonth;


}
