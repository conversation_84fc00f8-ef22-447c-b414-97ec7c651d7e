package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementEventJobEntity;
import com.mpolicy.manage.modules.settlement.vo.HandleRefreshVo;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobInfoOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListOut;

/**
 * 结算交互事件受理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-05 14:34:54
 */
public interface SettlementEventJobService extends IService<SettlementEventJobEntity> {

    /**
     * 获取结算交互事件受理表列表
     *
     * @param input
     * @return
     */
    PageUtils<SettlementEventJobListOut> findSettlementEventJobList(SettlementEventJobListInput input);

    /**
     * 获取结算交互事件受理表详情
     *
     * @param id
     * @return
     */
    SettlementEventJobInfoOut findSettlementEventJobById(Integer id);

    /**
     * 处理任务的刷新和重置
     * @param vo 请求参数
     */
    void handleRefresh(HandleRefreshVo vo);

    /**
     * 冲正处理
     * @param id 数据id
     */
    void rectificationById(Integer id);
}

