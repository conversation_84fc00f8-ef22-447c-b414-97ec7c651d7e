package com.mpolicy.manage.modules.settlement.pattern.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductProductService;
import com.mpolicy.manage.modules.protocol.vo.InsuranceProductProductMapOut;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementSubjectEntity;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateFactory;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateHandler;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInfoService;
import com.mpolicy.manage.modules.settlement.service.SettlementSubjectService;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人身险-横向
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CurrentReconcileTemplate extends ReconcileTemplateHandler {

    private final SettlementReconcileInfoService settlementReconcileInfoService;
    private final IEpProtocolInsuranceProductProductService protocolInsuranceProductProductService;
    private final SettlementSubjectService settlementSubjectService;
    private final EpPolicyContractInfoService policyContractInfoService;

    private ReconcileTemplateEnum reconcileTemplate;

    private final List<String> TEMPLATE_TITLE =
            CollUtil.newArrayList("保单号", "批单号", "小鲸险种编码", "新单/续期", "续期期次", "应税/免税", "实收保费");
    private static List<String> RECONCILE_SUBJECT_LIST =
            CollUtil.newArrayList(ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getCode(),
                    ReconcileSubjectOnlineEnum.CONSULTING_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.PROMOTION_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.MODERN_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.EXTENSION_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.TECHNICAL_SERVICE_FEE.getCode(),
                    ReconcileSubjectOnlineEnum.SERVICE_FEE.getCode());

    /**
     * 读取文件: 注意读取文件的模版一定要正确 标题可以追加 不可修改 横版:
     * 保单号	批单号	险种名称	保单状态	新单/续期	续期期次	应税/免税	实收保费	费用类型	手续费比率	手续费	费用类型	手续费比率	手续费	费用类型	手续费比率
     * 手续费	费用类型	手续费比率	手续费	费用类型	手续费比率	手续费
     * 竖版/财险: 保单号	批单号	险种名称	保单状态	新单/续期	续期期次	应税/免税	实收保费	费用类型	手续费比率	手续费
     *
     * @param inputStream   文件
     * @param reconcileCode 对账单编码
     * @return
     */
    @Override
    public List<ReconcileRuleFileTemplate> readFile(InputStream inputStream, String reconcileCode) {
        //  判断对账单是否存在
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                        settlementReconcileInfoService.lambdaQuery()
                                .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单不存在")));
        List<ReconcileRuleFileTemplate> resultList = new ArrayList<>();
        List<SettlementSubjectEntity> settlementSubjectList = settlementSubjectService.list();
        Map<String, String> settlementSubjectMapName = settlementSubjectList.stream().collect(Collectors.toMap(SettlementSubjectEntity::getSubjectName, SettlementSubjectEntity::getSubjectCode));
        try {
            // 读取表格数据,将表格数据转成纵向
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> read = reader.read();
            if (CollUtil.isEmpty(read)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件内容为空"));
            }
            //第一行数据不做处理 标题
            List<String> titleList = (List<String>) (List) read.get(0);
            if (titleList.size() < TEMPLATE_TITLE.size()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件模版异常"));
            }
            //判断模版前面是不是正确的,如果不是正确的提示模版错误
            for (int i = 0; i < TEMPLATE_TITLE.size(); i++) {
                if (!titleList.get(i).equals(TEMPLATE_TITLE.get(i))) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("模版第[{}]列列名称错误,应该是[{}]", i + 1, TEMPLATE_TITLE.get(i))));
                }
            }
            // 开始读取文件问文件
            for (int i = 1; i < read.size(); i++) {
                //这是这一行的数据
                List<Object> rows = read.get(i);
                //保单号 为空的时候他是非法数据,直接丢弃
                String policyNo = Objects.toString(rows.get(0), "");
                if (StrUtil.isBlank(policyNo)) {
                    throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文件第{}行没有保单号", (i + 1))));
                }
                //批次号,这个不做校验
                String batchCode = Objects.toString(rows.get(1), "");
                // 小鲸险种编码
                String productCode = Objects.toString(rows.get(2), "");
                // 新单/续期 如果是线下单 这个值是必填的
                String insuranceType = Objects.toString(rows.get(3), null);
                //续期期次 新单空这个值1  续期从2开始
                Integer renewalPeriod =
                        Integer.parseInt(StrUtil.isBlankIfStr(rows.get(4)) ? "1" : rows.get(4).toString());
                //应税/免税
                String dutyType = "应税".equals(Objects.toString(rows.get(5), "免税")) ? "1" : "0 ";
                //实收保费
                String realityPremium = Objects.toString(rows.get(6), "0").replace("%", "").trim();
                //
                for (int j = 0; j < titleList.size(); j++) {
                    try {
                        // 标题为费用类型,且费用类型有值 往后取2个值 手续费比率	手续费
                        if ("费用类型".equals(titleList.get(j)) && !StrUtil.isBlankIfStr(rows.get(j))) {
                            //一行的列数小于 标题数量的时候直接跳出循环
                            if (rows.size() < j) {
                                break;
                            }
                            if (StrUtil.isBlank(Objects.toString(rows.get(j)))) {
                                continue;
                            }
                            String subjectName = Objects.toString(rows.get(j), "");

                            if (!settlementSubjectMapName.containsKey(subjectName)) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("文件第{}行科目名称{}系统未配置,请联系开发人员进行配置后再上传",
                                                (i + 1))));
                            }
                            // 非线上对账单必须填写新单/续期
                            if (!RECONCILE_SUBJECT_LIST.contains(settlementSubjectMapName.get(subjectName)) && StrUtil.isBlank(
                                    insuranceType)) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("文件第{}行非线上对账单必须填写新单/续期", (i + 1))));
                            }
                            if (StrUtil.isBlank(insuranceType)) {
                                insuranceType = "新单";
                            }
                            //保单号	批单号	险种名称	保单状态	新单/续期	续期期次	应税/免税	实收保费	费用类型	手续费比率	手续费
                            ReconcileRuleFileTemplate template = new ReconcileRuleFileTemplate();
                            //保单号
                            template.setPolicyNo(policyNo);
                            //批次号
                            template.setBatchCode(batchCode);
                            //协议产品编码
                            template.setProductCode(productCode);
                            //新单/续期
                            template.setInsuranceType(insuranceType);
                            //续期期次
                            template.setRenewalPeriod(renewalPeriod);
                            //应税/免税 0:免税 1:应税
                            template.setDutyType(dutyType);
                            //实收保费
                            template.setRealityPremium(new BigDecimal(realityPremium));
                            //科目编码和描述信息
                            template.setReconcileSubjectCode(settlementSubjectMapName.get(subjectName));
                            template.setReconcileSubjectName(subjectName);
                            // 处理百分号 如果存在 将结果% 100
                            String settlementRate = Objects.toString(rows.get(j + 1), "0").trim();
                            String companyAmount = Objects.toString(rows.get(j + 2), "0").trim();
                            if (settlementRate.contains("%")) {
                                template.setSettlementRate(
                                        new BigDecimal(settlementRate.replace("%", "").trim()).divide(new BigDecimal("100")));
                            } else {
                                template.setSettlementRate(new BigDecimal(settlementRate));
                            }
                            if (companyAmount.contains("%")) {
                                template.setCompanyAmount(
                                        new BigDecimal(companyAmount.replace("%", "").trim()).divide(new BigDecimal("100")));
                            } else {
                                template.setCompanyAmount(new BigDecimal(companyAmount));
                            }
                            //判断手续费金额是否大于2位小数
                            if (template.getCompanyAmount().scale() > 2) {
                                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                        StrUtil.format("文件第{}行手续费金额不能大于2位小数", (i + 1))));
                            }
                            resultList.add(template);
                        }
                    } catch (GlobalException e) {
                        log.warn("读取文件异常,异常数据={}", JSONUtil.toJsonStr(rows), e);
                        throw e;
                    } catch (Exception e) {
                        log.warn("读取文件异常,异常数据={}", JSONUtil.toJsonStr(rows), e);
                        throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文件第{}行读取数据失败,请检查", (i + 1))));
                    }
                }
            }
            //如果是小鲸保单需要填写险种信息,不然没有办法生成明细信息
            /*ReconcileTemplateEnum reconcileTemplate = this.getReconcileTemplate();
            if (reconcileTemplate == ReconcileTemplateEnum.XIAOWHALE_RULE) {
                //获取保单号集合
                List<String> policyNoList = resultList.stream().map(ReconcileRuleFileTemplate::getPolicyNo).distinct
                ().collect(Collectors.toList());
                long count = policyContractInfoService.lambdaQuery()
                        .in(PolicyContractInfoEntity::getPolicyNo, policyNoList)
                        .list().stream().map(PolicyContractInfoEntity::getPolicyNo).distinct().count();
            }*/
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            log.warn("读取文件异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件异常"));
        }
        if (resultList.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取为文件内容为空"));
        }
        // 获取线下保单列表
        List<ReconcileRuleFileTemplate> xxPolicyList =
                resultList.stream().filter(f -> !RECONCILE_SUBJECT_LIST.contains(f.getReconcileSubjectCode()))
                        .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(xxPolicyList)) {
            //获取保单主险信息(只处理线下单)
            List<String> policyNoList = xxPolicyList.stream().map(ReconcileRuleFileTemplate::getPolicyNo).distinct()
                    .collect(Collectors.toList());
            //分组去查询数据库 分摊压力
            Map<String, EpPolicyContractInfoEntity> policyMap = new HashMap<>(1);
            CollUtil.split(policyNoList, 1000).forEach(policyNos -> {
                policyMap.putAll(policyContractInfoService.lambdaQuery()
                        .select(EpPolicyContractInfoEntity::getPolicyNo, EpPolicyContractInfoEntity::getMainProductCode,
                                EpPolicyContractInfoEntity::getMainProductName)
                        .in(EpPolicyContractInfoEntity::getPolicyNo, policyNos).list().stream()
                        .collect(Collectors.toMap(EpPolicyContractInfoEntity::getPolicyNo, v -> v, (v1, v2) -> {
                            // 这里处理自己的逻辑
                            return v2;
                        })));
            });
            resultList.forEach(action -> {
                // 获取到了保单信息,那么就开始补全险种信息
                if (!RECONCILE_SUBJECT_LIST.contains(action.getReconcileSubjectCode()) && policyMap.containsKey(
                        action.getPolicyNo())) {
                    action.setProductCode(policyMap.get(action.getPolicyNo()).getMainProductCode());
                    action.setProductName(policyMap.get(action.getPolicyNo()).getMainProductName());
                }
            });
        }
        //校验文件是否正确和补全属性
        List<String> productCodeList =
                resultList.stream().map(ReconcileRuleFileTemplate::getProductCode).filter(StrUtil::isNotBlank).distinct()
                        .collect(Collectors.toList());
        if (!productCodeList.isEmpty()) {
            List<InsuranceProductProductMapOut> protocolInsuranceProductProductList =
                    protocolInsuranceProductProductService.findReconcileInsuranceProductList(productCodeList,
                            settlementReconcileInfo.getReconcileType());
            if (protocolInsuranceProductProductList.isEmpty()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("小鲸险种编码[{}]没有配置{}关联险种", JSONUtil.toJsonStr(productCodeList),
                                settlementReconcileInfo.getReconcileType() == 0 ? "协议" : "合约")));
            }
            Map<String, InsuranceProductProductMapOut> protocolInsuranceProductProductMap =
                    protocolInsuranceProductProductList.stream()
                            .collect(Collectors.toMap(InsuranceProductProductMapOut::getProductCode, Function.identity()));

            resultList.forEach(action -> {
                if (StrUtil.isNotBlank(action.getProductCode())) {
                    if (protocolInsuranceProductProductMap.containsKey(action.getProductCode())) {
                        InsuranceProductProductMapOut protocolInsuranceProductProduct =
                                protocolInsuranceProductProductMap.get(action.getProductCode());
                        action.setProductName(protocolInsuranceProductProduct.getProductName());
                        action.setInsuranceProductCode(protocolInsuranceProductProduct.getInsuranceProductCode());
                        action.setInsuranceProductName(protocolInsuranceProductProduct.getInsuranceProductName());
                    } else {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("小鲸险种编码[{}]没有配置关联{}产品", action.getProductCode(),
                                        settlementReconcileInfo.getReconcileType() == 0 ? "协议" : "合约")));
                    }
                }
            });
        }
        log.info("解析文件完成={}", JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    /**
     * 获取当前使用的模版
     *
     * @return
     */
    @Override
    public ReconcileTemplateEnum getReconcileTemplate() {
        return this.reconcileTemplate;
    }

    /**
     * 储存模版信息
     *
     * @param reconcileTemplate 模版
     */
    @Override
    public void setReconcileTemplate(ReconcileTemplateEnum reconcileTemplate) {
        this.reconcileTemplate = reconcileTemplate;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ReconcileTemplateFactory.register(ReconcileTemplateEnum.LIFE_INSURANCE_HORIZONTAL, this);
        ReconcileTemplateFactory.register(ReconcileTemplateEnum.LIFE_INSURANCE_VERTICAL, this);
        ReconcileTemplateFactory.register(ReconcileTemplateEnum.PROPERTY_INSURANCE_TAX, this);
    }
}
