package com.mpolicy.manage.modules.settlement.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.settlement.core.common.autocost.*;

import javax.servlet.http.HttpServletResponse;

public interface SettlementCostImportRecordService {

    PageUtils<SettlementCostImportRecord> pageSettlementCostImportRecord(SettlementCostImportRecordQuery query);
    PageUtils<SettlementCostImportOperationRecord> pageSettlementCostImportOperationRecord(SettlementCostImportOperationRecordQuery query);

    String deleteImportRecord(SettlementCostImportRecordDelete recordDelete);

    void exportSettlementCostImportErrorData(String applyCode,String name, HttpServletResponse response);
}
