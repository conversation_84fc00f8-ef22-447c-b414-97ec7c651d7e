package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.redis.redisson.RedissLockUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.enums.*;
import com.mpolicy.manage.feign.client.DingTalkMessageClient;
import com.mpolicy.manage.feign.model.CommonResult;
import com.mpolicy.manage.feign.model.MsgPush2User;
import com.mpolicy.manage.modules.invoice.entity.SettlementReconcileRedInvoice;
import com.mpolicy.manage.modules.invoice.service.impl.FttmServiceImpl;
import com.mpolicy.manage.modules.invoice.vo.InvoiceApplyVO;
import com.mpolicy.manage.modules.invoice.vo.third.FttmInvoiceGenerateResVo;
import com.mpolicy.manage.modules.invoice.vo.third.FttmInvoiceOrderInfoVo;
import com.mpolicy.manage.modules.invoice.vo.third.Result;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInfoDingTalkDao;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInvoiceDao;
import com.mpolicy.manage.modules.settlement.entity.*;
import com.mpolicy.manage.modules.settlement.service.*;
import com.mpolicy.manage.modules.settlement.vo.invoice.*;
import com.mpolicy.manage.modules.sys.dao.MpDictionaryDao;
import com.mpolicy.manage.modules.sys.entity.MpDictionaryEntity;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.manage.modules.sys.service.SysUserService;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.manage.common.Constant.FINANCIAL_STAFF_JOB_NUMBERS;
import static com.mpolicy.manage.constant.AdminPublicConstant.DIC_INVOICE_PROJECT_DETAIL;

@Slf4j
@Service("settlementReconcileInvoiceService")
public class SettlementReconcileInvoiceServiceImpl extends ServiceImpl<SettlementReconcileInvoiceDao, SettlementReconcileInvoiceEntity> implements SettlementReconcileInvoiceService {
    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;
    @Autowired
    private SettlementReconcileInvoiceEnclosureService settlementReconcileInvoiceEnclosureService;


    @Autowired
    private SettlementReconcileInvoiceMapService settlementReconcileInvoiceMapService;

    @Autowired
    private SettlementReconcileInvoiceTrackService settlementReconcileInvoiceTrackService;


    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;

    @Autowired
    private SysDocumentService sysDocumentService;


    @Autowired
    private TransactionTemplate transactionTemplate;


    @Autowired
    private FttmServiceImpl fttmService;


    @Autowired
    private MpDictionaryDao dictionaryDao;


    @Autowired
    private StorageService storageService;


    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;


    @Autowired
    private SettlementReconcileSubjectService settlementReconcileSubjectService;


    @Autowired
    private DingTalkMessageClient dingTalkMessageClient;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SettlementReconcileInfoDingTalkDao settlementReconcileInfoDingTalkDao;

    @Autowired
    private SettlementReconcileInvoiceMailboxService settlementReconcileInvoiceMailboxService;


    @Value("${mp.download.folder:logs/}")
    String FILE_PATH;


    /**
     * 获取对账单发票信息列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<SettlementReconcileInvoiceListOut> findSettlementReconcileInvoiceList(SettlementReconcileInvoiceListInput input) {
        // 判断当前用户的权限信息
        Long userId = ShiroUtils.getUserId();
        SysUserEntity userInfo = sysUserService.getById(userId);
        if (userInfo.getInvoicePermission() == 1) {
            input.setReconcileType(0);
            // 只获取协议数据
        } else if (userInfo.getInvoicePermission() == 2) {
            // 只获取合约数据
            input.setReconcileType(1);
        }
        IPage<SettlementReconcileInvoiceListOut> page = baseMapper.findSettlementReconcileInvoiceList(new Page<SettlementReconcileInvoiceListOut>(input.getPage(), input.getLimit()), input);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<String> invoiceCodeList = page.getRecords().stream().map(SettlementReconcileInvoiceListOut::getInvoiceCode).collect(Collectors.toList());
            Map<String, Long> settlementReconcileInvoiceMap = settlementReconcileInvoiceMapService.lambdaQuery().in(SettlementReconcileInvoiceMapEntity::getInvoiceCode, invoiceCodeList).list().stream().collect(Collectors.groupingBy(SettlementReconcileInvoiceMapEntity::getInvoiceCode, Collectors.counting()));
            page.getRecords().forEach(action -> {
                settlementReconcileInvoiceMap.putIfAbsent(action.getInvoiceCode(), 0L);
                action.setReconcileInvoiceMapNum(settlementReconcileInvoiceMap.get(action.getInvoiceCode()));
            });
        }
        return new PageUtils(page);
    }

    /**
     * 获取对账单发票信息详情
     *
     * @param invoiceCode 发票编码
     * @return
     */
    @Override
    public SettlementReconcileInvoiceInfoOut findSettlementReconcileInvoiceByInvoiceCode(String invoiceCode) {
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(
                lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, invoiceCode)
                        .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        // 获取发票附件信息
        List<SettlementReconcileInvoiceEnclosureEntity> enclosureList = settlementReconcileInvoiceEnclosureService
                .lambdaQuery().eq(SettlementReconcileInvoiceEnclosureEntity::getInvoiceCode, invoiceCode)
                .list();
        SettlementReconcileInvoiceInfoOut result = BeanUtil.copyProperties(settlementReconcileInvoice, SettlementReconcileInvoiceInfoOut.class);
        result.setMailEnclosureList(enclosureList.stream().filter(f -> f.getInvoiceType() == 1).collect(Collectors.toList()));
        result.setInvoiceEnclosureList(enclosureList.stream().filter(f -> f.getInvoiceType() == 0).collect(Collectors.toList()));
        result.setInvoiceList(enclosureList.stream().filter(f -> f.getInvoiceType() == 3).filter(x -> Arrays.stream(settlementReconcileInvoice.getInvoiceFileType().split(",")).anyMatch(y -> Objects.equals(y, x.getFileExt()))).collect(Collectors.toList()));
        // 邮箱信息
        List<InvoiceMailboxVo> invoiceMailboxList = settlementReconcileInvoiceMailboxService.lambdaQuery()
                .eq(SettlementReconcileInvoiceMailboxEntity::getInvoiceCode, invoiceCode)
                .list().stream().map(m -> BeanUtil.copyProperties(m, InvoiceMailboxVo.class))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(invoiceMailboxList)) {
            InvoiceMailboxVo invoiceMailbox = new InvoiceMailboxVo();
            invoiceMailbox.setIsDefault(1);
            invoiceMailbox.setMailbox(settlementReconcileInvoice.getMailAddress());
            invoiceMailboxList.add(invoiceMailbox);
        }
        result.setInvoiceMailboxList(invoiceMailboxList);
        // 获取发票对账单数据
        List<ReconcileInvoiceSettlementReconcileOut> invoicingReconcileList = this.findReconcileInvoiceSettlementReconcileList(invoiceCode);
        result.setInvoicingReconcileList(invoicingReconcileList);
        // todo 如果是开票状态获取开票信息
        if (ReconcileInvoiceApplyStatusEnum.INVOICE_COMPLETED.getCode().equals(settlementReconcileInvoice.getApplyStatus())) {
            // 发票文件啥的
        }
        return result;
    }

    /**
     * 新增对账单发票信息数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSettlementReconcileInvoice(SettlementReconcileInvoiceSaveInput input) {
        String tryKey = DateUtil.date().toString("yyyyMMddHHmm");
        boolean lock = RedissLockUtil.tryLock(tryKey, 200, -1);
        if (!lock) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("系统开票中,稍后再试..."));
        }
        try {
            // 获取对账单信息
            Map<String, BigDecimal> reconcileMap = input.getInvoicingReconcileList().stream().collect(Collectors.toMap(ApplyForInvoicingReconcileItem::getReconcileCode, ApplyForInvoicingReconcileItem::getApplyAmount));
            // 申请开票金额合计小于0的时候不允许开票
            if (reconcileMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) <= 0) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("本次开票金额必须大于0"));
            }
            // 获取对账单列表,没有命中到的都是不合规的
            List<SettlementReconcileInfoEntity> settlementReconcileInfoList = settlementReconcileInfoService.lambdaQuery().in(SettlementReconcileInfoEntity::getReconcileCode, reconcileMap.keySet()).list();
            //校验金额
            List<SettlementReconcileInvoiceMapEntity> settlementReconcileInvoiceMapList = new ArrayList<>();
            SettlementReconcileInvoiceEntity settlementReconcileInvoice = new SettlementReconcileInvoiceEntity();
            String invoiceCode = "BXFPSQ" + System.currentTimeMillis();
            settlementReconcileInvoice.setInvoiceCode(invoiceCode);
            settlementReconcileInvoice.setInvoiceAmount(BigDecimal.ZERO);
            settlementReconcileInfoList.forEach(action -> {
                BigDecimal applyAmount = reconcileMap.get(action.getReconcileCode());
                if (BigDecimal.ZERO.compareTo(action.getInvoicableAmount()) == 0) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】可开票金额为0"));
                }
                if (BigDecimal.ZERO.compareTo(applyAmount) == 0) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】存在开票金额为0的数据,请检查"));
                }
                if (BigDecimal.ZERO.compareTo(action.getInvoicableAmount()) > 0) {
                    if (action.getInvoicableAmount().compareTo(applyAmount) > 0) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】可开票金额不足"));
                    }
                } else {
                    if (action.getInvoicableAmount().compareTo(applyAmount) < 0) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】可开票金额不足"));
                    }
                }
                // 设置开票中金额增加 可开票金额减少
                action.setInvoiceAmount(action.getInvoiceAmount().add(applyAmount));
                action.setInvoicableAmount(action.getInvoicableAmount().subtract(applyAmount));
                // 判断是全部开票还是部分开票 如果可开票金额=0的时候那么就是已经全部开票了.
                if (BigDecimal.ZERO.compareTo(action.getInvoicableAmount()) == 0) {
                    action.setInvoiceStatus(SettlementInvoiceStatusEnum.FULL_INVOICING.getCode());
                } else {
                    action.setInvoiceStatus(SettlementInvoiceStatusEnum.PARTIAL_INVOICING.getCode());
                }
                settlementReconcileInvoice.setInvoiceAmount(settlementReconcileInvoice.getInvoiceAmount().add(applyAmount));
                // 生成一个明细记录
                SettlementReconcileInvoiceMapEntity settlementReconcileInvoiceMap = BeanUtil.copyProperties(action, SettlementReconcileInvoiceMapEntity.class);
                settlementReconcileInvoiceMap.setApplyAmount(applyAmount);
                settlementReconcileInvoiceMap.setInvoiceCode(invoiceCode);
                settlementReconcileInvoiceMapList.add(settlementReconcileInvoiceMap);
            });
            //保存记录数据
            settlementReconcileInvoiceMapService.saveBatch(settlementReconcileInvoiceMapList);
            // 添加附件信息
            if (CollUtil.isNotEmpty(input.getInvoiceEnclosureList())) {
                List<String> fileCodeList = input.getInvoiceEnclosureList().stream().map(InvoiceEnclosureItem::getFileCode).collect(Collectors.toList());
                List<SettlementReconcileInvoiceEnclosureEntity> invoiceEnclosureList = sysDocumentService.lambdaQuery().in(SysDocumentEntity::getFileCode, fileCodeList).list().stream().map(m -> {
                    SettlementReconcileInvoiceEnclosureEntity res = BeanUtil.copyProperties(m, SettlementReconcileInvoiceEnclosureEntity.class);
                    res.setInvoiceCode(invoiceCode);
                    res.setInvoiceType(0);
                    return res;
                }).collect(Collectors.toList());
                settlementReconcileInvoiceEnclosureService.saveBatch(invoiceEnclosureList);
            }
            // 添加邮箱信息
            List<SettlementReconcileInvoiceMailboxEntity> invoiceMailboxList = input.getInvoiceMailboxList().stream().map(m -> {
                SettlementReconcileInvoiceMailboxEntity res = BeanUtil.copyProperties(m, SettlementReconcileInvoiceMailboxEntity.class);
                res.setInvoiceCode(invoiceCode);
                return res;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(invoiceMailboxList)) {
                settlementReconcileInvoiceMailboxService.saveBatch(invoiceMailboxList);
                String mailAddress = invoiceMailboxList.stream()
                        .sorted(Comparator.comparing(SettlementReconcileInvoiceMailboxEntity::getIsDefault))
                        .map(SettlementReconcileInvoiceMailboxEntity::getMailbox)
                        .collect(Collectors.joining(";"));
                // 更新默认邮箱
                settlementReconcileInvoice.setMailAddress(mailAddress);
            }
            String username = ShiroUtils.getUserEntity().getUsername();
            Long userId = ShiroUtils.getUserEntity().getUserId();
            // 插入发票信息
            BeanUtil.copyProperties(input, settlementReconcileInvoice);
            settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.WAIT_FINANCE_AUDIT.getCode());
            // 判断是否需要发送邮箱
            if (StatusEnum.NORMAL.getCode().equals(settlementReconcileInvoice.getIsSendMailbox())) {
                settlementReconcileInvoice.setSendMailboxStatus(1);
            }
            settlementReconcileInvoice.setApplyTime(new Date());
            settlementReconcileInvoice.setApplyUserName(username);
            settlementReconcileInvoice.setApplyUserId(userId);
            settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.INVOICE_APPLY.getCode());
            settlementReconcileInvoice.setExpressStatus(Integer.parseInt(input.getNeedMail()));
            settlementReconcileInvoice.setInvoicingRemark(input.getInvoicingRemark());
            baseMapper.insert(settlementReconcileInvoice);

            //更新开票中金额信息
            settlementReconcileInfoService.updateBatchById(settlementReconcileInfoList);

            // 轨迹信息
            SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
            settlementReconcileInvoiceTrack.setInvoiceCode(invoiceCode);
            settlementReconcileInvoiceTrack.setOperateUser(username);
            settlementReconcileInvoiceTrack.setOperateDesc("申请开票");
            settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);

            // 发送钉钉提醒
            sendDingTalkMessage(1, settlementReconcileInvoice);
        } finally {
            RedissLockUtil.unlock(tryKey);
        }

    }

    /**
     * 修改对账单发票信息数据
     * 目前修改开票信息是指财务驳回进行的修改,可以继续修改开票金额信息等
     *
     * @param input 申请信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementReconcileInvoiceById(SettlementReconcileInvoiceUpdateInput input) {
        String tryKey = DateUtil.date().toString("yyyyMMddHHmm");
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, input.getInvoiceCode()).one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        // 设置开票合计金额为0
        settlementReconcileInvoice.setInvoiceAmount(BigDecimal.ZERO);
        // 获取对账单信息
        Map<String, BigDecimal> reconcileMap = input.getInvoicingReconcileList().stream().collect(Collectors.toMap(ApplyForInvoicingReconcileItem::getReconcileCode, ApplyForInvoicingReconcileItem::getApplyAmount));
        // 申请开票金额合计小于0的时候不允许开票
        if (reconcileMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) <= 0) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("本次开票金额必须大于0"));
        }
        boolean lock = RedissLockUtil.tryLock(tryKey, 200, -1);
        if (!lock) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("系统开票中,稍后再试..."));
        }
        try {
            // 获取对账单列表,没有命中到的都是不合规的
            List<SettlementReconcileInfoEntity> settlementReconcileInfoList = settlementReconcileInfoService.lambdaQuery().in(SettlementReconcileInfoEntity::getReconcileCode, reconcileMap.keySet()).list();
            //校验金额
            List<SettlementReconcileInvoiceMapEntity> settlementReconcileInvoiceMapList = new ArrayList<>();
            settlementReconcileInfoList.forEach(action -> {
                BigDecimal applyAmount = reconcileMap.get(action.getReconcileCode());
                if (BigDecimal.ZERO.compareTo(action.getInvoicableAmount()) == 0) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】可开票金额为0"));
                }
                if (BigDecimal.ZERO.compareTo(applyAmount) > 0) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】存在开票金额为0的数据,请检查"));
                }
                if (BigDecimal.ZERO.compareTo(action.getInvoicableAmount()) > 0) {
                    if (action.getInvoicableAmount().compareTo(applyAmount) > 0) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】可开票金额不足"));
                    }
                } else {
                    if (action.getInvoicableAmount().compareTo(applyAmount) < 0) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单【" + action.getReconcileCode() + "】可开票金额不足"));
                    }
                }
                // 设置开票中金额增加 可开票金额减少
                action.setInvoiceAmount(action.getInvoiceAmount().add(applyAmount));
                action.setInvoicableAmount(action.getInvoicableAmount().subtract(applyAmount));
                // 判断是全部开票还是部分开票 如果可开票金额=0的时候那么就是已经全部开票了.
                if (BigDecimal.ZERO.compareTo(action.getInvoicableAmount()) == 0) {
                    action.setInvoiceStatus(SettlementInvoiceStatusEnum.FULL_INVOICING.getCode());
                } else {
                    action.setInvoiceStatus(SettlementInvoiceStatusEnum.PARTIAL_INVOICING.getCode());
                }
                settlementReconcileInvoice.setInvoiceAmount(settlementReconcileInvoice.getInvoiceAmount().add(applyAmount));
                // 生成一个明细记录
                SettlementReconcileInvoiceMapEntity settlementReconcileInvoiceMap = BeanUtil.copyProperties(action, SettlementReconcileInvoiceMapEntity.class);
                settlementReconcileInvoiceMap.setApplyAmount(applyAmount);
                settlementReconcileInvoiceMap.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
                settlementReconcileInvoiceMapList.add(settlementReconcileInvoiceMap);
            });
            // 删除之前的记录
            settlementReconcileInvoiceMapService.lambdaUpdate()
                    .eq(SettlementReconcileInvoiceMapEntity::getInvoiceCode, settlementReconcileInvoice.getInvoiceCode())
                    .remove();
            //保存记录数据
            settlementReconcileInvoiceMapService.saveBatch(settlementReconcileInvoiceMapList);

            // 删除附件
            settlementReconcileInvoiceEnclosureService.lambdaUpdate()
                    .eq(SettlementReconcileInvoiceEnclosureEntity::getInvoiceType, 0)
                    .eq(SettlementReconcileInvoiceEnclosureEntity::getInvoiceCode, input.getInvoiceCode())
                    .remove();
            // 重新写入附件信息
            if (CollUtil.isNotEmpty(input.getInvoiceEnclosureList())) {
                List<String> fileCodeList = input.getInvoiceEnclosureList().stream().map(InvoiceEnclosureItem::getFileCode).collect(Collectors.toList());
                List<SettlementReconcileInvoiceEnclosureEntity> invoiceEnclosureList = sysDocumentService.lambdaQuery().in(SysDocumentEntity::getFileCode, fileCodeList).list().stream().map(m -> {
                    SettlementReconcileInvoiceEnclosureEntity res = BeanUtil.copyProperties(m, SettlementReconcileInvoiceEnclosureEntity.class);
                    res.setInvoiceCode(input.getInvoiceCode());
                    res.setInvoiceType(0);
                    return res;
                }).collect(Collectors.toList());
                settlementReconcileInvoiceEnclosureService.saveBatch(invoiceEnclosureList);
            }
            BeanUtil.copyProperties(input, settlementReconcileInvoice);
            // 添加邮箱信息
            settlementReconcileInvoiceMailboxService.lambdaUpdate()
                    .eq(SettlementReconcileInvoiceMailboxEntity::getInvoiceCode, input.getInvoiceCode())
                    .remove();
            List<SettlementReconcileInvoiceMailboxEntity> invoiceMailboxList = input.getInvoiceMailboxList().stream().map(m -> {
                SettlementReconcileInvoiceMailboxEntity res = BeanUtil.copyProperties(m, SettlementReconcileInvoiceMailboxEntity.class);
                res.setInvoiceCode(input.getInvoiceCode());
                return res;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(invoiceMailboxList)) {
                settlementReconcileInvoiceMailboxService.saveBatch(invoiceMailboxList);
                String mailAddress = invoiceMailboxList.stream()
                        .sorted(Comparator.comparing(SettlementReconcileInvoiceMailboxEntity::getIsDefault))
                        .map(SettlementReconcileInvoiceMailboxEntity::getMailbox)
                        .collect(Collectors.joining(";"));
                // 更新默认邮箱
                settlementReconcileInvoice.setMailAddress(mailAddress);
            }
            String username = ShiroUtils.getUserEntity().getUsername();
            Long userId = ShiroUtils.getUserEntity().getUserId();
            // 复制属性信息
            Date date = new Date();

            settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.WAIT_FINANCE_AUDIT.getCode());
            settlementReconcileInvoice.setApplyTime(date);
            settlementReconcileInvoice.setApplyUserName(username);
            settlementReconcileInvoice.setApplyUserId(userId);
            settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.INVOICE_APPLY.getCode());
            // 更新申请时间
            settlementReconcileInvoice.setApplyTime(date);
            settlementReconcileInvoice.setExpressStatus(Integer.parseInt(input.getNeedMail()));
            // 判断是否需要发送邮箱
            if (StatusEnum.NORMAL.getCode().equals(settlementReconcileInvoice.getIsSendMailbox())) {
                settlementReconcileInvoice.setSendMailboxStatus(1);
            }
            // 更新数据
            baseMapper.updateById(settlementReconcileInvoice);

            //更新开票中金额信息
            settlementReconcileInfoService.updateBatchById(settlementReconcileInfoList);

            // 轨迹信息
            SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
            settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
            settlementReconcileInvoiceTrack.setOperateUser(username);
            settlementReconcileInvoiceTrack.setOperateDesc("申请开票");
            settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);

            // 发送钉钉提醒
            sendDingTalkMessage(1, settlementReconcileInvoice);
        } finally {
            RedissLockUtil.unlock(tryKey);
        }
    }

    /**
     * 财务审核
     *
     * @param input 审核信息
     */
    @Override
    public void financeExamine(FinanceExamineVo input) {
        // 获取发票信息
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, input.getInvoiceCode()).one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        // 判断当前状态是否为财务审核状态 如果不是 那么不能操作
        if (!ReconcileInvoiceApplyStatusEnum.WAIT_FINANCE_AUDIT.getCode().equals(settlementReconcileInvoice.getApplyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前状态不是待财务审核状态，不能操作财务审核操作"));
        }
        // 复制属性信息
        BeanUtil.copyProperties(input, settlementReconcileInvoice, CopyOptions.create().setIgnoreNullValue(false));
        // 执行数据写入
        Boolean sendResult = transactionTemplate.execute((status) -> {
            try {

                String username = ShiroUtils.getUserEntity().getUsername();
                // 如果是审批通过 变更状态去,如果是驳回就要重新搞...
                if (ReconcileInvoicingFinanceStatusEnum.AUDIT_PASS.getCode().equals(input.getInvoicingFinanceStatus())) {
                    //审核通过
                    SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
                    settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
                    settlementReconcileInvoiceTrack.setOperateUser(username);
                    settlementReconcileInvoiceTrack.setOperateDesc("财务审核通过");
                    settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);
                    // 设置申请状态为 开票处理中
                    settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.INVOICE_PROCESSING.getCode());
                } else if (ReconcileInvoicingFinanceStatusEnum.AUDIT_REJECT.getCode().equals(input.getInvoicingFinanceStatus())) {
                    // 驳回
                    SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
                    settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
                    settlementReconcileInvoiceTrack.setOperateUser(username);
                    settlementReconcileInvoiceTrack.setOperateDesc("财务审核不通过,原因:" + input.getInvoicingOpinion());
                    settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.INVOICE_APPLY.getCode());
                    settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);

                    // 设置申请状态为驳回 退回可开票金额信息和开票中信息
                    releaseOccupationMoney(input.getInvoiceCode());
                    settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.REJECT.getCode());

                    // 发送钉钉提醒
                    sendDingTalkMessage(2, settlementReconcileInvoice);
                } else {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("审核状态异常，不能操作财务审核操作"));
                }
                settlementReconcileInvoice.setInvoicingOperateUser(username);
                settlementReconcileInvoice.setInvoicingOperateTime(new Date());
                // 更新数据
                baseMapper.updateById(settlementReconcileInvoice);
                return Boolean.TRUE;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.warn("财务审核失败,申请编码={}：", input.getInvoiceCode(), e);
                return false;
            }
        });
        if (Boolean.FALSE.equals(sendResult)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("财务审核失败,申请编码={}", input.getInvoiceCode())));
        }
        //如果是审核通过 并且没有开票要求 了那么就去请求系统开票接口
        if (ReconcileInvoicingFinanceStatusEnum.AUDIT_PASS.getCode().equals(input.getInvoicingFinanceStatus())) {
            // 没有时间要求
            if (StatusEnum.INVALID.getCode().equals(input.getInvoicingTimeAsk())) {
                // 系统开票处理，这里要try...catch下
                this.invoiceToFttm(input.getInvoiceCode());
            } else {
                DateTime endTime = DateUtil.endOfDay(new Date());
                // 判断开票时间和当前时间是否一致 同一天 如果是同一天
                if (DateUtil.compare(input.getInvoicingTime(), endTime) < 1) {
                    this.invoiceToFttm(input.getInvoiceCode());
                }
            }
        }
    }

    /**
     * 系统回退
     *
     * @param input 系统回退请求参数
     */
    @Override
    public void systemRollback(SystemRollbackVo input) {
        // 获取发票信息
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, input.getInvoiceCode()).one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        // 开票异常可进行系统回退操作
        if (!ReconcileInvoiceApplyStatusEnum.INVOICE_FAILED.getCode().equals(settlementReconcileInvoice.getApplyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前状态不是开票失败状态，不能进行系统回退操作"));
        }
        // 执行数据写入
        Boolean sendResult = transactionTemplate.execute((status) -> {
            try {
                String username = ShiroUtils.getUserEntity().getUsername();
                // 驳回
                SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
                settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
                settlementReconcileInvoiceTrack.setOperateUser(username);
                settlementReconcileInvoiceTrack.setOperateDesc("系统回退:" + input.getInvoicingFallbackReason());
                settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);

                // 回退金额
                releaseOccupationMoney(input.getInvoiceCode());

                settlementReconcileInvoice.setInvoicingFallbackReason(input.getInvoicingFallbackReason());
                settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.INVOICE_APPLY.getCode());
                settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.SYSTEM_ROLLBACK.getCode());
                settlementReconcileInvoice.setInvoicingSystemStatus(4);
                // 更新数据
                baseMapper.updateById(settlementReconcileInvoice);
                return Boolean.TRUE;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.warn("系统回退,申请编码={}：", input.getInvoiceCode(), e);
                return false;
            }
        });
        if (Boolean.FALSE.equals(sendResult)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("系统回退失败,申请编码={}", input.getInvoiceCode())));
        }

    }

    /**
     * 金额重置
     *
     * @param invoiceCode 发票编码
     */
    public void releaseOccupationMoney(String invoiceCode) {
        List<SettlementReconcileInvoiceMapEntity> reconcileInvoiceMapList = settlementReconcileInvoiceMapService.lambdaQuery().eq(SettlementReconcileInvoiceMapEntity::getInvoiceCode, invoiceCode).list();
        List<String> reconcileCodeList = reconcileInvoiceMapList.stream().map(SettlementReconcileInvoiceMapEntity::getReconcileCode).collect(Collectors.toList());
        Map<String, SettlementReconcileInfoEntity> settlementReconcileInfoMap = settlementReconcileInfoService.lambdaQuery().in(SettlementReconcileInfoEntity::getReconcileCode, reconcileCodeList).list().stream().collect(Collectors.toMap(SettlementReconcileInfoEntity::getReconcileCode, Function.identity()));

        List<SettlementReconcileInfoEntity> settlementReconcileInfoList = new ArrayList<>();
        reconcileInvoiceMapList.forEach(action -> {
            SettlementReconcileInfoEntity settlementReconcileInfo = settlementReconcileInfoMap.get(action.getReconcileCode());
            // 开票中金额减少
            settlementReconcileInfo.setInvoiceAmount(settlementReconcileInfo.getInvoiceAmount().subtract(action.getApplyAmount()));
            // 可开票金额增加
            settlementReconcileInfo.setInvoicableAmount(settlementReconcileInfo.getInvoicableAmount().add(action.getApplyAmount()));
            // 更新开票状态
            // 可开票金额 = 保司对账单金额的时候 说明没有进行过开票,设置为待开票,其他设置成部分开票
            if (settlementReconcileInfo.getInvoicableAmount().compareTo(settlementReconcileInfo.getCompanyAmount()) == 0) {
                settlementReconcileInfo.setInvoiceStatus(SettlementInvoiceStatusEnum.PENDING_INVOICING.getCode());
            } else {
                settlementReconcileInfo.setInvoiceStatus(SettlementInvoiceStatusEnum.PARTIAL_INVOICING.getCode());
            }
            settlementReconcileInfoList.add(settlementReconcileInfo);
        });
        // 更新对账单信息
        settlementReconcileInfoService.updateBatchById(settlementReconcileInfoList);
    }

    @Override
    public List<InvoiceReturnDetail> findSettlementReconcileInvoiceByReconcileCode(String reconcileCode) {

        // 获取对账单列表,没有命中到的都是不合规的
        SettlementReconcileInfoEntity settlementReconcileInfo = settlementReconcileInfoService.lambdaQuery().eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one();

        if (Objects.isNull(settlementReconcileInfo)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单信息不存在"));
        }

        // 获取对账单信息
        List<SettlementReconcileInvoiceMapEntity> invoicingReconcileList = settlementReconcileInvoiceMapService.lambdaQuery().eq(SettlementReconcileInvoiceMapEntity::getReconcileCode, reconcileCode).list();

        if (CollUtil.isEmpty(invoicingReconcileList)) {
            return Collections.emptyList();
        }
        List<String> invoiceCodeList = invoicingReconcileList.stream().map(SettlementReconcileInvoiceMapEntity::getInvoiceCode).collect(Collectors.toList());

        List<SettlementReconcileInvoiceEntity> invoiceList = this.lambdaQuery().in(SettlementReconcileInvoiceEntity::getInvoiceCode, invoiceCodeList).eq(SettlementReconcileInvoiceEntity::getApplyStatus, ReconcileInvoiceApplyStatusEnum.INVOICE_COMPLETED.getCode()).list();


        if (CollectionUtil.isEmpty(invoiceList)) {
            return Collections.emptyList();
        }

        return invoiceList.stream().map(x -> {
            InvoiceReturnDetail invoiceReturnDetail = new InvoiceReturnDetail();
            invoiceReturnDetail.setInvoiceMoney(x.getInvoiceAmount());
            invoiceReturnDetail.setInvoiceCode(x.getInvoiceCode());
            invoiceReturnDetail.setInvoiceTitle(x.getInvoiceTitle());
            invoiceReturnDetail.setReconcileMonth(settlementReconcileInfo.getReconcileMonth());
            invoiceReturnDetail.setCompanyName(settlementReconcileInfo.getCompanyName());
            invoiceReturnDetail.setReconcileCode(settlementReconcileInfo.getReconcileCode());
            return invoiceReturnDetail;
        }).collect(Collectors.toList());


    }


    /**
     * 发送至保司
     *
     * @param input 发送至保司信息
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void sendToCompany(SendToCompanyVo input) {
        // 获取发票信息
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, input.getInvoiceCode()).one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        // 判断当前状态是否为财务审核状态 如果不是 那么不能操作
        if (!ReconcileInvoiceApplyStatusEnum.INVOICE_COMPLETED.getCode().equals(settlementReconcileInvoice.getApplyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前状态不是已开票状态，不能操作发送保司信息"));
        }
        // 删除附件
        settlementReconcileInvoiceEnclosureService.lambdaUpdate()
                .eq(SettlementReconcileInvoiceEnclosureEntity::getInvoiceType, 1)
                .in(SettlementReconcileInvoiceEnclosureEntity::getInvoiceCode, input.getInvoiceCode())
                .remove();
        // 复制数据
        BeanUtil.copyProperties(input, settlementReconcileInvoice);

        if (CollUtil.isNotEmpty(input.getMailEnclosureList())) {
            // 重新写入附件信息
            List<String> fileCodeList = input.getMailEnclosureList().stream().map(InvoiceEnclosureItem::getFileCode).collect(Collectors.toList());
            List<SettlementReconcileInvoiceEnclosureEntity> invoiceEnclosureList = sysDocumentService.lambdaQuery().in(SysDocumentEntity::getFileCode, fileCodeList).list().stream().map(m -> {
                SettlementReconcileInvoiceEnclosureEntity res = BeanUtil.copyProperties(m, SettlementReconcileInvoiceEnclosureEntity.class);
                res.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
                res.setInvoiceType(1);
                return res;
            }).collect(Collectors.toList());
            settlementReconcileInvoiceEnclosureService.saveBatch(invoiceEnclosureList);
            // 判断是否需要真的发送邮件,如果需要发的话 那在发
            if (StatusEnum.NORMAL.getCode().equals(settlementReconcileInvoice.getIsSendMailbox())) {
                // 下载文件 然后打包发送
                List<File> fileList = new ArrayList<>();
                // 创建临时目录
                String tempPath = FILE_PATH + "mailFile/" + System.currentTimeMillis();
                File file = new File(tempPath);
                if (!file.exists()) {
                    FileUtil.mkdir(file);
                }
                // 递归创建文件夹
                invoiceEnclosureList.forEach(document -> {
                    String localFilePath = tempPath + "/" + (document.getFileName());
                    log.info("ossPath={}, localFilePath={}", document.getFilePath(), localFilePath);
                    storageService.downloadFileToLocal(document.getFilePath(), localFilePath);
                    fileList.add(new File(localFilePath));
                });
                asyncSendText(input.getMailAddress(), input.getMailSubject(), input.getMailContent(), fileList.toArray(new File[0]));
                settlementReconcileInvoice.setSendMailboxStatus(2);
            }

        } else {
            // 发送邮件
            if (StatusEnum.NORMAL.getCode().equals(settlementReconcileInvoice.getIsSendMailbox())) {
                asyncSendText(input.getMailAddress(), input.getMailSubject(), input.getMailContent(), null);
                settlementReconcileInvoice.setSendMailboxStatus(2);
            }
        }

        // 设置发送状态为已发总
        settlementReconcileInvoice.setPushCompanyStatus(1);
        settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.SEND_TO_COMPANY.getCode());
        // 更新发送状态
        baseMapper.updateById(settlementReconcileInvoice);

        SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
        settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
        if (StatusEnum.NORMAL.getCode().equals(settlementReconcileInvoice.getIsSendMailbox())) {
            String username = ShiroUtils.getUserEntity().getUsername();
            settlementReconcileInvoiceTrack.setOperateUser(username);
            settlementReconcileInvoiceTrack.setOperateDesc("发送至保司");
        } else {
            settlementReconcileInvoiceTrack.setOperateUser("系统");
            settlementReconcileInvoiceTrack.setOperateDesc("无须发送邮件");
        }
        settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);

    }

    /**
     * 更新快递信息
     *
     * @param input 快递信息
     */
    @Override
    public void updateExpress(UpdateExpressVo input) {
        // 获取发票信息
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, input.getInvoiceCode()).one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        // 判断当前状态是否为财务审核状态 如果不是 那么不能操作
        if (!ReconcileInvoiceApplyStatusEnum.INVOICE_COMPLETED.getCode().equals(settlementReconcileInvoice.getApplyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前状态不是已开票状态，不能操作发送保司信息"));
        }
        String username = ShiroUtils.getUserEntity().getUsername();
        SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
        settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
        settlementReconcileInvoiceTrack.setOperateUser(username);
        settlementReconcileInvoiceTrack.setOperateDesc("填写快递信息");
        settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);


        BeanUtil.copyProperties(input, settlementReconcileInvoice);
        settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.EXPRESS.getCode());
        settlementReconcileInvoice.setExpressStatus(ReconcileInvoiceExpressStatusEnum.SENDED.getCode());
        baseMapper.updateById(settlementReconcileInvoice);
    }

    /**
     * 获取开票轨迹列表
     *
     * @param invoiceCode 申请编码
     * @return 轨迹信息
     */
    @Override
    public List<ReconcileInvoiceTrackListOut> findReconcileInvoiceTrackList(String invoiceCode) {
        return settlementReconcileInvoiceTrackService.lambdaQuery().eq(SettlementReconcileInvoiceTrackEntity::getInvoiceCode, invoiceCode).orderByAsc(SettlementReconcileInvoiceTrackEntity::getId).list().stream().map(m -> BeanUtil.copyProperties(m, ReconcileInvoiceTrackListOut.class)).collect(Collectors.toList());
    }

    @Override
    public void invoiceToFttmRetry(String invoiceNum) {
        SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
        settlementReconcileInvoiceTrack.setInvoiceCode(invoiceNum);
        settlementReconcileInvoiceTrack.setOperateUser("系统");
        settlementReconcileInvoiceTrack.setOperateDesc("系统重试");
        settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);
        invoiceToFttm(invoiceNum);
    }

    @Override
    public void invoiceToFttm(String invoiceNum) {
        // todo 通知开票 默认直接之成功的 后面在跟无锡对接
        // 获取发票信息
        SettlementReconcileInvoiceEntity settlementReconcileInvoice = Optional.ofNullable(lambdaQuery()
                        .eq(SettlementReconcileInvoiceEntity::getInvoiceCode, invoiceNum).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发票信息不存在")));
        if (!ReconcileInvoicingFinanceStatusEnum.AUDIT_PASS.getCode()
                .equals(settlementReconcileInvoice.getInvoicingFinanceStatus())) {
            // 系统开票处理，这里要try...catch下
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前审核未经过财务审核"));
        }
        try {
            InvoiceApplyVO applyVO = constructRequestVO(settlementReconcileInvoice);
            // 处理一下邮箱信息
            applyVO.setMailAddress(Arrays.asList(settlementReconcileInvoice.getMailAddress().split("[;；]")).get(0));
            List<DicCacheHelper.DicEntity> dicEntityList = DicCacheHelper.getSons(DIC_INVOICE_PROJECT_DETAIL);
            if (CollectionUtil.isNotEmpty(dicEntityList) && dicEntityList.stream().anyMatch(x -> Objects.equals(x.getKey().substring(x.getKey().lastIndexOf(":") + 1), settlementReconcileInvoice.getInvoicingOrgCode().substring(settlementReconcileInvoice.getInvoicingOrgCode().lastIndexOf(":")+1)))) {
                applyVO.setDj(applyVO.getHjse());
                applyVO.setDw("次");
                applyVO.setSpsl("1");
            }

            Result<FttmInvoiceGenerateResVo> invoiceResp = fttmService.invoice(applyVO);
            log.info("开票信息返回-{}", invoiceResp);
            if (!invoiceResp.isSuccess()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("[开票中台]-{}", invoiceResp.getMsg())));
            }

            if (Objects.equals(FttmServiceImpl.APPLY_DULPLICATE_EXCEPTION, invoiceResp.getData().getZtdm())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("[开票中台]-{}", "暂不支持请求,请求批次号已存在")));
            }

            if (Objects.equals(FttmServiceImpl.APPLY_HANDLE_EXCEPTION, invoiceResp.getData().getZtdm()) || Objects.equals(FttmServiceImpl.APPLY_VALIDATION_EXCEPTION, invoiceResp.getData().getZtdm())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("[开票中台]-{}", invoiceResp.getData().getZtxx())));
            }

            // 开通成功
            settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.INVOICE_PROCESSING.getCode());
            settlementReconcileInvoice.setInvoicingSystemResult("开票成功");
            baseMapper.updateById(settlementReconcileInvoice);
            // 发送钉钉提醒
//            sendDingTalkMessage(4, settlementReconcileInvoice);
        } catch (Exception e) {
            SettlementReconcileInvoiceTrackEntity settlementReconcileInvoiceTrack = new SettlementReconcileInvoiceTrackEntity();
            settlementReconcileInvoiceTrack.setInvoiceCode(settlementReconcileInvoice.getInvoiceCode());
            settlementReconcileInvoiceTrack.setOperateUser("系统");
            settlementReconcileInvoiceTrack.setOperateDesc("开票失败" + e.getMessage());
            settlementReconcileInvoiceTrackService.save(settlementReconcileInvoiceTrack);

            // 开票失败 + 填写失败原因
            settlementReconcileInvoice.setApplyStatus(ReconcileInvoiceApplyStatusEnum.INVOICE_FAILED.getCode());
            settlementReconcileInvoice.setInvoicingSystemResult(e.getMessage());
            settlementReconcileInvoice.setInvoiceStep(ReconcileInvoiceStepEnum.SYSTEM_INVOICE.getCode());
            baseMapper.updateById(settlementReconcileInvoice);
            // 发送钉钉提醒
            sendDingTalkMessage(3, settlementReconcileInvoice);


        }
    }

    /**
     * 根据发票编码获取发票关联对账单信息
     *
     * @param invoiceCode 发票编码
     * @return 列表数据
     */
    @Override
    public List<ReconcileInvoiceSettlementReconcileOut> findReconcileInvoiceSettlementReconcileList(String invoiceCode) {
        // 获取对账单信息
        List<SettlementReconcileInvoiceMapEntity> invoicingReconcileList = settlementReconcileInvoiceMapService.lambdaQuery().eq(SettlementReconcileInvoiceMapEntity::getInvoiceCode, invoiceCode).list();
        if (CollUtil.isEmpty(invoicingReconcileList)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单信息不存在"));
        }
        List<String> reconcileCodeList = invoicingReconcileList.stream().map(SettlementReconcileInvoiceMapEntity::getReconcileCode).distinct().collect(Collectors.toList());
        List<ReconcileInvoiceSettlementReconcileOut> resultList = invoicingReconcileList.stream().map(m -> BeanUtil.copyProperties(m, ReconcileInvoiceSettlementReconcileOut.class)).collect(Collectors.toList());
        //
        Map<String, SettlementReconcileInfoEntity> settlementReconcileInfoMap = settlementReconcileInfoService.lambdaQuery().in(SettlementReconcileInfoEntity::getReconcileCode, reconcileCodeList).list().stream().collect(Collectors.toMap(SettlementReconcileInfoEntity::getReconcileCode, Function.identity()));


        Map<String, List<String>> settlementReconcileSubjectMap = settlementReconcileSubjectService.lambdaQuery().in(SettlementReconcileSubjectEntity::getReconcileCode, reconcileCodeList).list().stream().collect(Collectors.groupingBy(SettlementReconcileSubjectEntity::getReconcileCode)).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(SettlementReconcileSubjectEntity::getSubjectRuleCode).distinct().sorted().collect(Collectors.toList())));
        List<String> subjectRuleCodeList = settlementReconcileSubjectMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<String, String> subjectMap = new HashMap<>();
        if (!subjectRuleCodeList.isEmpty()) {
            subjectMap.putAll(settlementReconcileCompanySubjectService.lambdaQuery().in(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCodeList).list().stream().filter(f -> StrUtil.isNotBlank(f.getRemark())).collect(Collectors.toMap(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, SettlementReconcileCompanySubjectEntity::getRemark, (v1, v2) -> v1)));
        }

        // 补全数据...
        resultList.forEach(action -> {
            if (settlementReconcileInfoMap.containsKey(action.getReconcileCode())) {
                BeanUtil.copyProperties(settlementReconcileInfoMap.get(action.getReconcileCode()), action);
            }
            if (!subjectMap.isEmpty()) {
                String subjectRemark = settlementReconcileSubjectMap.get(action.getReconcileCode()).stream().filter(subjectMap::containsKey).map(subjectMap::get).distinct().collect(Collectors.joining(","));
                action.setSubjectRemark(subjectRemark);
            }
        });
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleBlueCallback(com.mpolicy.manage.modules.invoice.vo.third.Result<FttmInvoiceOrderInfoVo> vo) {

        FttmInvoiceOrderInfoVo fttmInvoiceOrderInfoVo = vo.getData();
        //更新状态
        FttmInvoiceOrderInfoVo.OrderSummary orderSummary = fttmInvoiceOrderInfoVo.getDdkzxx().get(0);

        String invoiceApplyCode = orderSummary.getDdh();


        SettlementReconcileInvoiceEntity reconcileInvoiceEntity = baseMapper.selectOne(Wrappers.<SettlementReconcileInvoiceEntity>lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, invoiceApplyCode));
        if (Objects.isNull(reconcileInvoiceEntity)) {
            log.warn("蓝票回调对应申请编码-{}不存在", invoiceApplyCode);
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("蓝票回调对应申请编码-{}不存在", invoiceApplyCode)));
        }


        //添加附件
        List<FttmInvoiceOrderInfoVo.FileItem> fileItemList = fttmInvoiceOrderInfoVo.getFileitemlist();
        if (CollectionUtil.isEmpty(fileItemList)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("蓝票回调缺少附件信息对应申请编码-{}", invoiceApplyCode)));
        }

        if (vo.isSuccess()) {
            reconcileInvoiceEntity.setApplyStatus(ReconcileInvoiceApplyStatusEnum.INVOICE_COMPLETED.getCode());
            if (CollUtil.isNotEmpty(fttmInvoiceOrderInfoVo.getFileitemlist())) {
                List<SettlementReconcileInvoiceEnclosureEntity> attachmentList = fttmInvoiceOrderInfoVo.getFileitemlist().stream().map(x -> {
                    SettlementReconcileInvoiceEnclosureEntity enclosureItem = new SettlementReconcileInvoiceEnclosureEntity();
                    enclosureItem.setInvoiceType(0);
                    enclosureItem.setFilePath(x.getFilecontenturl());
                    enclosureItem.setFileType(x.getWjlx());
                    enclosureItem.setInvoiceCode(invoiceApplyCode);
                    return enclosureItem;
                }).collect(Collectors.toList());
                reconcileInvoiceEntity.setBlueElectricCode(fttmInvoiceOrderInfoVo.getDdfpxx().getSdfphm());
                reconcileInvoiceEntity.setInvoiceDate(fttmInvoiceOrderInfoVo.getDdfpxx().getKprq());
                baseMapper.updateById(reconcileInvoiceEntity);
                settlementReconcileInvoiceEnclosureService.saveBatch(attachmentList);
            }
        }

        if (!vo.isSuccess()) {
            reconcileInvoiceEntity.setApplyStatus(ReconcileInvoiceApplyStatusEnum.INVOICE_FAILED.getCode());
            reconcileInvoiceEntity.setInvoicingSystemResult(vo.getMsg());
        }

        baseMapper.updateById(reconcileInvoiceEntity);

    }


    public InvoiceApplyVO constructRequestVO(SettlementReconcileInvoiceEntity settlementReconcileInvoice) {

        InvoiceApplyVO applyVO = new InvoiceApplyVO();
        applyVO.setInvoiceType(settlementReconcileInvoice.getInvoiceType());
        applyVO.setTitleType(settlementReconcileInvoice.getInvoiceTitleType());
        applyVO.setTitle(settlementReconcileInvoice.getInvoiceTitle());
        applyVO.setTaxpayerNum(settlementReconcileInvoice.getTaxpayerNum());
        applyVO.setDepositBank(settlementReconcileInvoice.getDepositBank());
        applyVO.setBankAccount(settlementReconcileInvoice.getBankAccount());
        applyVO.setCompanyPhone(settlementReconcileInvoice.getCompanyPhone());
        applyVO.setCompanyAddress(settlementReconcileInvoice.getCompanyAddress());
        applyVO.setMailAddress(settlementReconcileInvoice.getMailAddress());
        applyVO.setNeedMail(settlementReconcileInvoice.getNeedMail());
        applyVO.setReceiverName(settlementReconcileInvoice.getReceiverName());
        applyVO.setReceiverPhone(settlementReconcileInvoice.getReceiverPhone());
        applyVO.setReceiverAddress(settlementReconcileInvoice.getReceiverAddress());
        applyVO.setInvoiceFileType(settlementReconcileInvoice.getInvoiceType());
        applyVO.setInvoiceTimeRequire(settlementReconcileInvoice.getInvoiceTimeRequire());
        applyVO.setRemark(settlementReconcileInvoice.getRemark());
        applyVO.setRequestBatchNo(UUID.fastUUID().toString(Boolean.TRUE));
        applyVO.setSalesTaxNum(settlementReconcileInvoice.getInvoicingOrgCode().split(":")[1]);
        applyVO.setSpid(settlementReconcileInvoice.getInvoicingItemCode());
        MpDictionaryEntity dictionaryEntity = dictionaryDao.selectOne(Wrappers.<MpDictionaryEntity>lambdaQuery().eq(MpDictionaryEntity::getDicKey, settlementReconcileInvoice.getInvoicingOrgCode()));
        if (Objects.isNull(dictionaryEntity)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("开票机构不存在-{}", settlementReconcileInvoice.getInvoicingOrgCode())));
        }
        applyVO.setSalesTaxNumCompanyName(dictionaryEntity.getDicValue());
        applyVO.setInvoiceProjectCode(settlementReconcileInvoice.getInvoicingItemCode());
//        applyVO.setInvoiceProjectName();
//        applyVO.setInvoiceProcessType();
        applyVO.setBusinessCode(settlementReconcileInvoice.getInvoiceCode());
        applyVO.setJshj(settlementReconcileInvoice.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP).toPlainString());
        BigDecimal hjje = settlementReconcileInvoice.getInvoiceAmount().multiply(settlementReconcileInvoice.getInvoicingTaxRate()).setScale(2, RoundingMode.HALF_UP);
        applyVO.setHjje(hjje.toPlainString());
        applyVO.setHjse(settlementReconcileInvoice.getInvoiceAmount().subtract(hjje).setScale(2, RoundingMode.HALF_UP).toPlainString());
        applyVO.setInvoiceRemark(settlementReconcileInvoice.getInvoicingRemark());
//        applyVO.setRedSourceInvoiceNum();
//        applyVO.setSpid(settlementReconcileInvoice.getInvoicingItemCode());
        return applyVO;
    }

    /**
     * 发送钉钉消息
     *
     * @param templateType 1:发票申请提交后需要财务人员审核
     *                     2:财务人员驳回发票申请
     *                     3:系统开票失败
     *                     4:系统开票成功
     */
    @Override
    public <T> void sendDingTalkMessage(Integer templateType, T t) {

        log.info("接收消息类型-{}", templateType);
        String title = "";
        String content = "";
        List<String> jobNumbers = new ArrayList<>();

        SettlementReconcileInvoiceEntity settlementReconcileInvoice = null;
        SettlementReconcileRedInvoice redInvoice = null;
        if (Lists.newArrayList(1, 2, 3, 4).contains(templateType)) {
            settlementReconcileInvoice = (SettlementReconcileInvoiceEntity) t;
        } else {
            redInvoice = (SettlementReconcileRedInvoice) t;
        }
        String invoiceCode;
        if (Objects.nonNull(settlementReconcileInvoice)) {
            invoiceCode = settlementReconcileInvoice.getInvoiceCode();
        } else {
            invoiceCode = redInvoice.getInvoiceApplyNum();
        }


        switch (templateType) {
            // 发票申请提交后需要财务人员审核
            case 1: {
                title = "【开票审核通知】";
                content = StrUtil.format("您有新的发票申请需要审核，请及时处理。发票抬头{}，申请开票金额{}元。", settlementReconcileInvoice.getInvoiceTitle(), settlementReconcileInvoice.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP));
                // 财务人员
                String jobNumberConfig = ConstantCacheHelper.getValue(FINANCIAL_STAFF_JOB_NUMBERS, null);
                if (StrUtil.isNotBlank(jobNumberConfig)) {
                    jobNumbers = StrUtil.split(jobNumberConfig, '#');
                }
                break;
            }
            // 财务人员驳回发票申请
            case 2: {
                title = "【开票驳回通知】";
                content = StrUtil.format("您的发票申请被驳回，驳回原因{}，请及时处理。发票抬头{}，申请开票金额{}元。", settlementReconcileInvoice.getInvoicingOpinion(), settlementReconcileInvoice.getInvoiceTitle(), settlementReconcileInvoice.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP));
                //发票申请人员
                SysUserEntity userInfo = sysUserService.getById(settlementReconcileInvoice.getApplyUserId());
                if (userInfo != null && StrUtil.isNotBlank(userInfo.getJobNumber())) {
                    jobNumbers = Collections.singletonList(userInfo.getJobNumber());
                }
                break;

            }
            // 系统开票失败
            case 3: {
                log.info("开票失败通知处理");
                title = "【开票失败通知】";
                content = StrUtil.format("系统开票失败，原因{}，请及时处理。发票抬头{}，申请开票金额{}元。", settlementReconcileInvoice.getInvoicingSystemResult(), settlementReconcileInvoice.getInvoiceTitle(), settlementReconcileInvoice.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP));
                //发票申请人员、财务人员
                String jobNumberConfig = ConstantCacheHelper.getValue(FINANCIAL_STAFF_JOB_NUMBERS, null);
                if (StrUtil.isNotBlank(jobNumberConfig)) {
                    jobNumbers = StrUtil.split(jobNumberConfig, '#');
                }
                SysUserEntity userInfo = sysUserService.getById(settlementReconcileInvoice.getApplyUserId());
                if (userInfo != null && StrUtil.isNotBlank(userInfo.getJobNumber())) {
                    jobNumbers.add(userInfo.getJobNumber());
                }
                break;

            }
            // 系统开票成功
            case 4: {
                log.info("开票成功通知处理");
                title = "【开票成功通知】";
                content = StrUtil.format("开票成功。发票抬头{}，申请开票金额{}元，开票时间{}。", settlementReconcileInvoice.getInvoiceTitle(), settlementReconcileInvoice.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP), DateUtil.date(settlementReconcileInvoice.getApplyTime()).toString());
                //发票申请人员、财务人员
                String jobNumberConfig = ConstantCacheHelper.getValue(FINANCIAL_STAFF_JOB_NUMBERS, null);
                if (StrUtil.isNotBlank(jobNumberConfig)) {
                    jobNumbers = StrUtil.split(jobNumberConfig, '#');
                }
                SysUserEntity userInfo = sysUserService.getById(settlementReconcileInvoice.getApplyUserId());
                if (userInfo != null && StrUtil.isNotBlank(userInfo.getJobNumber())) {
                    jobNumbers.add(userInfo.getJobNumber());
                }
                break;

            }
            // 红冲:系统开票失败
            case 5: {
                log.info("红冲失败失败处理");
                String blueInvoiceNum = redInvoice.getInvoiceApplyNum();

                SettlementReconcileInvoiceEntity blueInfo = this.lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, blueInvoiceNum).one();
                if (Objects.isNull(blueInfo)) {
                    return;
                }
                title = "【红冲失败通知】";

                content = StrUtil.format(
                        "红冲失败，原因{}，请及时处理。发票抬头{}，红冲金额{}元，原发票号码{}。"
                        , redInvoice.getRemark()
                        , blueInfo.getInvoiceTitle()
                        , blueInfo.getInvoiceAmount().setScale(2, RoundingMode.FLOOR)
                        , blueInfo.getBlueElectricCode()
                );
                //发票申请人员、财务人员
                String jobNumberConfig = ConstantCacheHelper.getValue(FINANCIAL_STAFF_JOB_NUMBERS, null);
                if (StrUtil.isNotBlank(jobNumberConfig)) {
                    jobNumbers = StrUtil.split(jobNumberConfig, '#');
                }
                SysUserEntity userInfo = sysUserService.getById(blueInfo.getApplyUserId());
                if (userInfo != null && StrUtil.isNotBlank(userInfo.getJobNumber())) {
                    jobNumbers.add(userInfo.getJobNumber());
                }
                break;

            }
            // 红冲: 系统开票成功
            case 6: {

                log.info("红冲成功通知处理");
                String blueInvoiceNum = redInvoice.getInvoiceApplyNum();

                SettlementReconcileInvoiceEntity blueInfo = this.lambdaQuery().eq(SettlementReconcileInvoiceEntity::getInvoiceCode, blueInvoiceNum).one();
                if (Objects.isNull(blueInfo)) {
                    log.info("蓝票信息不存在-{}", blueInvoiceNum);
                    return;
                }
                title = "【红冲成功通知】";
                content = StrUtil.format("红冲成功。发票抬头{}，红冲金额{}，原发票号码{}。", blueInfo.getInvoiceTitle(), blueInfo.getInvoiceAmount().setScale(2, RoundingMode.FLOOR), blueInfo.getBlueElectricCode());
                //发票申请人员、财务人员
                String jobNumberConfig = ConstantCacheHelper.getValue(FINANCIAL_STAFF_JOB_NUMBERS, null);
                if (StrUtil.isNotBlank(jobNumberConfig)) {
                    jobNumbers = StrUtil.split(jobNumberConfig, '#');
                }
                SysUserEntity userInfo = sysUserService.getById(blueInfo.getApplyUserId());
                if (userInfo != null && StrUtil.isNotBlank(userInfo.getJobNumber())) {
                    jobNumbers.add(userInfo.getJobNumber());
                }
                break;

            }
            default:
                break;
        }

        if (CollUtil.isEmpty(jobNumbers)) {
            log.info("没有获取到员工工号,不发送钉钉信息");
            return;
        }
        jobNumbers = jobNumbers.stream().distinct().collect(Collectors.toList());
        try {
            MsgPush2User msgPush2User = new MsgPush2User();
            msgPush2User.setContent(title + "\n" + content);
            msgPush2User.setMsgType("markdown");
            msgPush2User.setJobNumbers(jobNumbers);
            msgPush2User.setTitle(title);
            CommonResult<String> stringCommonResult = dingTalkMessageClient.sendMsgPush2User(msgPush2User);
            //保存发送记录
            SettlementReconcileInfoDingTalkEntity settlementReconcileInfoDingTalk = new SettlementReconcileInfoDingTalkEntity();
            settlementReconcileInfoDingTalk.setSendMsg(JSONUtil.toJsonStr(msgPush2User));
            settlementReconcileInfoDingTalk.setSendResult(JSONUtil.toJsonStr(stringCommonResult));
            settlementReconcileInfoDingTalk.setEventCode(templateType);
            settlementReconcileInfoDingTalk.setInvoiceCode(invoiceCode);
            settlementReconcileInfoDingTalk.setSendMsgType("markdown");
            settlementReconcileInfoDingTalk.setJobNumbers(JSONUtil.toJsonStr(jobNumbers));
            settlementReconcileInfoDingTalkDao.insert(settlementReconcileInfoDingTalk);
        } catch (Exception e) {
            log.warn("钉钉消息推送异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("发送钉钉消息异常"));
        }

    }

    /**
     * 多收件人
     *
     * @param to      收件人(多人;分割)
     * @param subject 标题
     * @param content 内容
     * @param files   附件列表
     */
    public void asyncSendText(String to, String subject, String content, File[] files) {
        // 先处理一波中文分号替换
        List<String> toList = Arrays.asList(to.split("[;；]"));
        // 异步执行发送邮件
        taskExecutor.submit(() -> {
            try {
                log.info("邮件发送中.....");
                MailAccount mailAccount = new MailAccount();
                mailAccount.setAuth(true);
                mailAccount.setSslEnable(true);
                mailAccount.setHost("smtp.xiaowhale.com");
                mailAccount.setFrom("张盼<<EMAIL>>");
                mailAccount.setUser("<EMAIL>");
                mailAccount.setPass("NiGqbw3t4qJQQ8U9");
                MailUtil.send(mailAccount, toList, subject, content, false, files);
                log.info("邮件发送成功!!!");
            } catch (Exception e) {
                log.warn("发送邮件失败：", e);
            }
        });
    }

    public static void main(String[] args) {
        List<String> toList = Arrays.asList("<EMAIL>;<EMAIL>;<EMAIL>".split("[;；]"));
        String subject = "subject";
        String content = "content";
        log.info("邮件发送中.....");
        MailAccount mailAccount = new MailAccount();
        mailAccount.setAuth(true);
        mailAccount.setSslEnable(true);
        mailAccount.setHost("smtp.xiaowhale.com");
        mailAccount.setFrom("张盼<<EMAIL>>");
        mailAccount.setUser("<EMAIL>");
        mailAccount.setPass("NiGqbw3t4qJQQ8U9");
        MailUtil.send(mailAccount, toList, subject, content, false);
        log.info("邮件发送成功!!!");
    }
}
