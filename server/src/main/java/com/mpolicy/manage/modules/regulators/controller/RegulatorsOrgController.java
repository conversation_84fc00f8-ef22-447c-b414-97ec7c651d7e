package com.mpolicy.manage.modules.regulators.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportOrgService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportService;
import com.mpolicy.manage.modules.regulators.strategy.ReportStrategyFactory;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportData;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportInput;
import com.mpolicy.manage.modules.regulators.vo.OrgRegulatorsReportList;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportData;
import com.mpolicy.manage.utils.AdminCommonUtils;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * 保司协议签署管理控制器
 *
 * <AUTHOR>
 * @date 2021-12-04 11:14:35
 */
@RestController
@RequestMapping("regulators/org")
@Api(tags = "监管报表管理")
@Slf4j
public class RegulatorsOrgController {

    @Autowired
    private RegulatorsReportOrgService regulatorsReportOrgService;

    @Autowired
    private ReportStrategyFactory reportStrategyFactory;

    /***
     * oss文件操作
     */
    @Autowired
    private StorageService storageService;

    @Value("${mp.download.folder:logs/}")
    String destPath;

    /**
     * <p>
     * 协议管理列表分页查询
     * </p>
     */
    @ApiOperation(value = "月度机构报备列表", notes = "月度机构报备列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "orgCode", dataType = "String", value = "机构编码"),
            @ApiImplicitParam(paramType = "query", name = "reportType", dataType = "String", value = "报告内容编码"),
            @ApiImplicitParam(paramType = "query", name = "uploadDate", dataType = "String", value = "上传时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list/{regulatorsNo}")
    @RequiresPermissions("regulators:list")
    public Result<PageUtils<OrgRegulatorsReportList>> list(
            @PathVariable("regulatorsNo") @ApiParam(name = "regulatorsNo", value = "报备月度编码") String regulatorsNo,
            @ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("获取月度机构报备列表，查询条件={}", params);
        PageUtils<OrgRegulatorsReportList> page = regulatorsReportOrgService.queryOrgRegulatorsReportList(regulatorsNo, params);
        return Result.success(page);
    }


    @ApiOperation(value = "新增机构报备报告", notes = "新增机构报备报告")
    @PostMapping("/add")
    @RequiresPermissions("regulators:edit")
    public Result<String> add(@RequestBody @ApiParam(name = "orgRegulatorsReportInput", value = "机构报备数据") OrgRegulatorsReportInput orgRegulatorsReportInput) {
        log.info("新增机构报备报告，请求报文={}", JSON.toJSONString(orgRegulatorsReportInput));
        // 1 基础校验
        ValidatorUtils.validateEntity(orgRegulatorsReportInput);

        RegulatorsReportTypeEnum regulatorsReportType = RegulatorsReportTypeEnum.deCode(orgRegulatorsReportInput.getReportType());
        if (regulatorsReportType == null) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("报备内容类型获取错误,reportType={}", orgRegulatorsReportInput.getReportType())));
        }
        // 判断当前机构报备情况 + 报告类型是否存在
        RegulatorsReportOrgEntity orgReport = Optional.ofNullable(
                regulatorsReportOrgService.lambdaQuery()
                        .eq(RegulatorsReportOrgEntity::getRegulatorsNo, orgRegulatorsReportInput.getRegulatorsNo())
                        .eq(RegulatorsReportOrgEntity::getReportType, regulatorsReportType.getCode())
                        .eq(RegulatorsReportOrgEntity::getOrgCode, orgRegulatorsReportInput.getOrgCode())
                        .one()
        ).orElse(null);
        if (orgReport != null) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("同一报送内容文档无法在同一分支机构内上传多个。"));
        }

        // 2 构建报告模式需要用的请求数据
        RegulatorsReportData regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);

        // 3 获取报备报告服务
        RegulatorsReportService<RegulatorsReportData> regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(regulatorsReportType, RegulatorsReportData.class);

        // 4 执行机构报备操作
        String orgRegulatorsNo = regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);
        log.info("新增机构报备报告，完成。 结构报备编号={}", orgRegulatorsNo);
        return Result.success(orgRegulatorsNo);
    }

    @ApiOperation(value = "重新机构报备报告", notes = "重新机构报备报告")
    @PostMapping("/update")
    @RequiresPermissions("regulators:edit")
    public Result<String> update(@RequestBody @ApiParam(name = "orgRegulatorsReportInput", value = "重新机构报备数据") OrgRegulatorsReportInput orgRegulatorsReportInput) {
        log.debug("重新机构报备报告，请求报文={}", JSON.toJSONString(orgRegulatorsReportInput));
        // 1 基础校验
        ValidatorUtils.validateEntity(orgRegulatorsReportInput, UpdateGroup.class);
        RegulatorsReportTypeEnum regulatorsReportType = RegulatorsReportTypeEnum.deCode(orgRegulatorsReportInput.getReportType());
        if (regulatorsReportType == null) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("报备内容类型获取错误,reportType={}", orgRegulatorsReportInput.getReportType())));
        }
        // 判断当前机构报备情况 + 报告类型是否存在
        RegulatorsReportOrgEntity orgReport = Optional.ofNullable(
                regulatorsReportOrgService.lambdaQuery()
                        .eq(RegulatorsReportOrgEntity::getRegulatorsNo, orgRegulatorsReportInput.getRegulatorsNo())
                        .eq(RegulatorsReportOrgEntity::getReportType, regulatorsReportType.getCode())
                        .eq(RegulatorsReportOrgEntity::getOrgCode, orgRegulatorsReportInput.getOrgCode())
                        .one()
        ).orElse(null);
        if (orgReport == null) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("修改报备纪录信息不存在."));
        }

        // 2 构建报告模式需要用的请求数据
        RegulatorsReportData regulatorsReportData = builderRegulatorsReportData(orgRegulatorsReportInput);

        // 3 获取报备报告服务
        RegulatorsReportService<RegulatorsReportData> regulatorsReportService = reportStrategyFactory.getRegulatorsReportService(regulatorsReportType, RegulatorsReportData.class);

        // 4 执行机构报备操作
        String orgRegulatorsNo = regulatorsReportService.uploadOrgRegulatorsReport(regulatorsReportData);
        log.debug("重新上传重新机构报备报告报备对象，完成。 结构报备编号={}", orgRegulatorsNo);
        return Result.success(orgRegulatorsNo);
    }

    @ApiOperation(value = "机构报备报告详情", notes = "重新机构报备对象")
    @GetMapping("/info/{orgRegulatorsNo}")
    @RequiresPermissions("regulators:list")
    public Result<OrgRegulatorsReportData> orgRegulatorsInfo(@PathVariable("orgRegulatorsNo") @ApiParam(name = "orgRegulatorsNo", value = "机构报备报告唯一编码") String orgRegulatorsNo) {
        log.info("重新上传报备对象，机构报备报告唯一编码={}", orgRegulatorsNo);
        return Result.success(regulatorsReportOrgService.queryOrgRegulatorsReport(orgRegulatorsNo));
    }

    /**
     * <p>
     * 月度机构批量导出
     * </p>
     */
    @ApiOperation(value = "月度机构批量导出", notes = "月度机构批量导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orgCode", dataType = "String", value = "机构编码"),
            @ApiImplicitParam(paramType = "query", name = "reportType", dataType = "String", value = "报告内容编码"),
            @ApiImplicitParam(paramType = "query", name = "uploadDate", dataType = "String", value = "上传时间"),
    })
    @GetMapping("/export/{regulatorsNo}")
    @RequiresPermissions("regulators:list")
    public Result<String> export(
            @PathVariable("regulatorsNo") @ApiParam(name = "regulatorsNo", value = "报备月度编码") String regulatorsNo,
            @ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("获取月度月度机构批量导出，查询条件={}", params);
        params.put("limit", "-1");
        PageUtils<OrgRegulatorsReportList> page = regulatorsReportOrgService.queryOrgRegulatorsReportList(regulatorsNo, params);
        // 获取结果列表
        List<OrgRegulatorsReportList> list = page.getList();
        if(list.isEmpty()){
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("查询无数据，无需导出"));
        }

        String targetPath = destPath.concat(regulatorsNo);
        // 多线程下载机构报备文件
        list.parallelStream().forEach(x ->{
            String orgTargetPath = destPath.concat(regulatorsNo).concat("/").concat(x.getOrgName());
            File dirFile = new File(orgTargetPath);
            if (!dirFile.exists()) {
                dirFile.mkdirs();
            }
            AdminCommonUtils.saveUrlFile(x.getDomainPath(),orgTargetPath.concat("/").concat(x.getReportFileName()));
        });

        File zipFileDir = new File(destPath.concat("zip"));
        if (!zipFileDir.exists()) {
            zipFileDir.mkdirs();
        }
        // 生成压缩文件
        File zipFile = ZipUtil.zip(System.getProperty("user.dir").concat("/").concat(targetPath), System.getProperty("user.dir").concat("/").concat(destPath).concat("zip/").concat(regulatorsNo).concat(".zip"),true);
        log.info("压缩zip文件完成");
        // 上传oss
        OssBaseOut ossBaseOut = storageService.uploadFileInputSteam(FileModelEnum.OTHER.ossObjectFileName(regulatorsNo, zipFile.getName()), zipFile);
        log.info("zip文件上传完成，上传结果={}",JSON.toJSONString(ossBaseOut));
        // 删除临时目录
        FileUtil.del(System.getProperty("user.dir").concat("/").concat(targetPath));
        return Result.success(ossBaseOut.getAccessDomainPath());
    }



    /**
     * <p>
     * 构建报告模式需要用的请求数据
     * </p>
     *
     * @param orgRegulatorsReportInput 页面机构报备所属对象数据
     * @return com.mpolicy.manage.modules.regulators.vo.RegulatorsReportData
     * <AUTHOR>
     * @since 2022/1/20
     */
    private RegulatorsReportData builderRegulatorsReportData(OrgRegulatorsReportInput orgRegulatorsReportInput) {
        RegulatorsReportData result = new RegulatorsReportData();
        BeanUtils.copyProperties(orgRegulatorsReportInput, result);
        result.setReportFileCode(orgRegulatorsReportInput.getFileCode());
        result.setReportType(RegulatorsReportTypeEnum.deCode(orgRegulatorsReportInput.getReportType()));
        return result;
    }
}
