package com.mpolicy.manage.modules.settlement.service.detail;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.dao.SettlementCostAutoInfoDao;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailPolicyDimensionVo;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2024/2/26 09:52
 * @Version 1.0
 */
public abstract class AbsPolicyProductDimensionService<S> extends AbsCommonParamsSettlementDetail<S, SettlementDetailPolicyDimensionVo>{
    @Autowired
    protected SettlementCostAutoInfoDao costAutoInfoDao;

    @Override
    public IPage<SettlementDetailPolicyDimensionVo> queryDetail(PageSettlementDetailParams p) {
        return costAutoInfoDao.pagePolicyDimension(new Page<>(p.getPage(), p.getLimit()), p);
    }


}
