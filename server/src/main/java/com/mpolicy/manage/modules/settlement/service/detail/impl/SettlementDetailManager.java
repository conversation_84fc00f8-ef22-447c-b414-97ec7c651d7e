package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsCommonParamsSettlementDetail;
import com.mpolicy.manage.modules.settlement.service.detail.BaseDynamicShowType;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/31 14:13
 * @Version 1.0
 */

@Service
public class SettlementDetailManager {

    private Map<String, AbsCommonParamsSettlementDetail<?, ?>> policyDimensionServiceMap;

    private Map<String, AbsCommonParamsSettlementDetail<?, ?>> dynamicSubjectServiceMap;

    @Autowired
    public void construct(@Autowired List<AbsCommonParamsSettlementDetail<?, ?>> commonParamsSettlementDetailList) {

        policyDimensionServiceMap = commonParamsSettlementDetailList.stream()
                .collect(
                        HashMap::new
                        , (map, t) -> map.putAll(t.subjectType().stream().filter(x -> !Objects.equals(x, CostSubjectEnum.DYNAMIC_SUBJECT))
                                                         .map(CostSubjectEnum::getCode)
                                                         .collect(Collectors.toMap(Function.identity(), v -> t)))
                        , HashMap::putAll
                );

        dynamicSubjectServiceMap = commonParamsSettlementDetailList.stream()
                .filter(BaseDynamicShowType.class::isInstance)
                .collect(
                        Collectors.toMap(x -> ((BaseDynamicShowType)x).dynamicShowType(), Function.identity())
                );
    }


    public PageUtils<?> showDetailPage(PageSettlementDetailParams p) {
        IPage<?> page;
        if (CostSubjectEnum.DYNAMIC_SUBJECT.getCode().equals(p.getParentSubjectCode())) {
            page = dynamicSubjectServiceMap.get(p.getCostDataType()).queryDetail(p);
            return new PageUtils<>(page.getRecords(), (int) page.getTotal(), (int) page.getPages(), (int) page.getCurrent());
        }

        if (!policyDimensionServiceMap.containsKey(p.getCostSubjectCode())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该科目详情查询未实现"));
        }
        page = policyDimensionServiceMap.get(p.getCostSubjectCode()).queryDetail(p);
        return new PageUtils<>(page.getRecords(), (int) page.getTotal(), (int) page.getPages(), (int) page.getCurrent());
    }


    public Object summary(PageSettlementDetailParams p) {

        if (CostSubjectEnum.DYNAMIC_SUBJECT.getCode().equals(p.getParentSubjectCode())) {
            return dynamicSubjectServiceMap.get(p.getCostDataType()).querySummary(p);
        }


        if (!policyDimensionServiceMap.containsKey(p.getCostSubjectCode())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该科目详情查询未实现"));
        }
        return policyDimensionServiceMap.get(p.getCostSubjectCode()).querySummary(p);
    }

}
