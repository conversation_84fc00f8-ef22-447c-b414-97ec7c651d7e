package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoRecordEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/1/26 10:35
 * @Version 1.0
 */

@Data
public class PageSettlementInstitutionKeyVO {

    @ApiModelProperty(value = "结佣月份")
    private String settlementMonth;

    /**
     * 结佣机构编码  当全部汇总是为空
     */
    @ApiModelProperty(value = "结佣机构编码")
    private String settlementInstitution;

    /**
     * 结佣机构名称
     */
    @ApiModelProperty(value = "结佣机构名称")
    private String settlementInstitutionName;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    private String regionCode;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String regionName;

    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码")
    private String objectOrgCode;

    /**
     * 分支
     */
    @ApiModelProperty(value = "分支")
    private String objectOrgName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String sendObjectCode;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String sendObjectName;


    public static PageSettlementInstitutionKeyVO from(SettlementCostAutoInfoVo autoInfoEntity) {

        PageSettlementInstitutionKeyVO settlementInstitutionKeyVO = new PageSettlementInstitutionKeyVO();
        settlementInstitutionKeyVO.setSettlementMonth(autoInfoEntity.getSettlementMonth());
        settlementInstitutionKeyVO.setSettlementInstitution(autoInfoEntity.getSettlementInstitution());
        settlementInstitutionKeyVO.setSettlementInstitutionName(autoInfoEntity.getSettlementInstitutionName());
        settlementInstitutionKeyVO.setRegionCode(autoInfoEntity.getRegionCode());
        settlementInstitutionKeyVO.setRegionName(autoInfoEntity.getRegionName());
        settlementInstitutionKeyVO.setObjectOrgCode(autoInfoEntity.getObjectOrgCode());
        settlementInstitutionKeyVO.setObjectOrgName(autoInfoEntity.getObjectOrgName());
        settlementInstitutionKeyVO.setSendObjectCode(autoInfoEntity.getSendObjectCode());
        settlementInstitutionKeyVO.setSendObjectName(autoInfoEntity.getSendObjectName());
        return settlementInstitutionKeyVO;

    }

    public static PageSettlementInstitutionKeyVO fromAutoRecord(SettlementCostAutoRecordEntity costAutoRecordEntity) {

        PageSettlementInstitutionKeyVO settlementInstitutionKeyVO = new PageSettlementInstitutionKeyVO();
        settlementInstitutionKeyVO.setSettlementMonth(costAutoRecordEntity.getCostSettlementCycle());
        settlementInstitutionKeyVO.setSettlementInstitution(costAutoRecordEntity.getSettlementInstitution());
        settlementInstitutionKeyVO.setSettlementInstitutionName(costAutoRecordEntity.getSettlementInstitutionName());
//        settlementInstitutionKeyVO.setRegionCode(costAutoRecordEntity.getRegionCode());
//        settlementInstitutionKeyVO.setRegionName(costAutoRecordEntity.getRegionName());
        settlementInstitutionKeyVO.setObjectOrgCode(costAutoRecordEntity.getObjectOrgCode());
        settlementInstitutionKeyVO.setObjectOrgName(costAutoRecordEntity.getObjectOrgName());
        settlementInstitutionKeyVO.setSendObjectCode(costAutoRecordEntity.getSendObjectCode());
        settlementInstitutionKeyVO.setSendObjectName(costAutoRecordEntity.getSendObjectName());
        return settlementInstitutionKeyVO;

    }

}
