package com.mpolicy.manage.modules.regulators.strategy;

import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportService;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportBasic;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 监管报备-机构报告策略工厂
 *
 * <AUTHOR>
 * @date 2022-01-20 15:09
 */
@Component
public class ReportStrategyFactory implements ApplicationContextAware {

    private static Map<String, RegulatorsReportService> reportServiceMap = new HashMap<>();

    private ApplicationContext context;

    @PostConstruct
    public void register() {
        Map<String, RegulatorsReportService> solverMap = context.getBeansOfType(RegulatorsReportService.class);
        for (RegulatorsReportService solver : solverMap.values()) {
            reportServiceMap.put(solver.getRegulatorsReportType().getCode(), solver);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }

    /**
     * <p>
     * 根据监管报告内容类型枚举获取报告处理服务
     * </p>
     *
     * @param regulatorsReportTypeEnum 监管报告内容类型枚举
     * @param clazz 报备报告数据对象
     * @return com.mpolicy.manage.modules.regulators.service.RegulatorsReportService<T>
     * <AUTHOR>
     * @since 2022/1/21
     */
    public <T extends RegulatorsReportBasic> RegulatorsReportService<T> getRegulatorsReportService(RegulatorsReportTypeEnum regulatorsReportTypeEnum, Class<T> clazz) {
        if (regulatorsReportTypeEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("监管报告内容类型不能为空"));
        }

        RegulatorsReportService regulatorsReportService = reportServiceMap.get(regulatorsReportTypeEnum.getCode());
        if (regulatorsReportService == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("类型{}监管报告服务未能获取到", regulatorsReportTypeEnum.getName())));
        }
        return (RegulatorsReportService<T>) regulatorsReportService;
    }
}
