package com.mpolicy.manage.modules.agentApply.controller;

import cn.hutool.core.util.URLUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineJoinApplyService;
import com.mpolicy.manage.modules.agentApply.vo.*;
import com.mpolicy.web.common.annotation.Lock;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.AddGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;


/**
 * 代理人线上入职用户申请
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
@RestController
@RequestMapping("agent/apply")
@Api(tags = "代理人线上入职信息")
public class BlAgentOnlineJoinApplyController {

    @Autowired
    private BlAgentOnlineJoinApplyService blAgentOnlineJoinApplyService;

    /**
     * 分页获取代理人入职申请信息
     * @param input
     * @return
     */
    @GetMapping("pageList")
    @RequiresPermissions(value = {"agent:apply:info"})
    @ApiOperation(value = "分页获取代理人线上申请信息列表", notes = "分页获取代理人线上申请信息列表")
    public Result<PageUtils<AgentApplyPageListOut>> pageList(AgentApplyPageListVO input) {
        PageUtils<AgentApplyPageListOut> page = blAgentOnlineJoinApplyService.pageList(input);
        return Result.success(page);
    }

    /**
     * 导出代理人入职申请信息列表
     * @param input
     * @return
     */
    @GetMapping("export")
    @ApiOperation(value = "代理人申请信息导出", notes = "代理人申请信息导出",produces = "application/octet-stream")
    public Result export(AgentApplyPageListVO input, HttpServletResponse response) {
        List<AgentApplyExportVo> list = blAgentOnlineJoinApplyService.export(input);
        ExcelUtil.writeExcel(response, list, URLUtil.encode("代理人申请信息列表", StandardCharsets.UTF_8), "sheet1", new AgentApplyExportVo());
        return Result.success();
    }

    /**
     * 根据code获取代理人申请信息
     * @param agentCode
     * @return
     */
    @ApiOperation(value = "根据code获取代理人申请信息")
    @GetMapping("/info/{agentCode}")
    @RequiresPermissions(value = {"agent:apply:info"})
    public Result<AgentApplyInfoOut> info(@ApiParam(value = "代理人编码", required = true)
                                                                                  @PathVariable("agentCode") String agentCode) {
        AgentApplyInfoOut result = blAgentOnlineJoinApplyService.queryDetail(agentCode);
        return Result.success(result);
    }

    /**
     * 代理人申请信息修改
     * @param update
     * @return
     */
    @ApiOperation(value = "修改代理人申请信息", notes = "修改代理人申请信息")
    @PostMapping("update")
    @RequiresPermissions(value = {"agent:apply:update"})
    @Lock(keys = "#update.agentCode", attemptTimeout = 10)
    public Result updateAgentApplyInfo(@ApiParam(value = "修改经纪人信息的对象", required = true)
                                  @RequestBody @Valid AgentApplyUpdateInfoVO update) {
        blAgentOnlineJoinApplyService.update(update);
        return Result.success();
    }

    /**
     * 代理人申请完成面试接口
     * @param finish
     * @return
     */
    @ApiOperation(value = "完成面试", notes = "完成面试")
    @PostMapping("finishInterview")
    @RequiresPermissions(value = {"agent:apply:interview"})
    public Result finishInterview(@ApiParam(value = "完成面试信息", required = true)
                                       @RequestBody @Valid AgentApplyFinishInterviewVO finish) {
        blAgentOnlineJoinApplyService.finishInterview(finish);
        return Result.success();
    }

    /**
     * 代理人完成培训
     * @param vo
     * @return
     */
    @ApiOperation(value = "完成培训", notes = "完成培训")
    @PostMapping("finishTrain")
    @RequiresPermissions(value = {"agent:apply:train"})
    @Lock(keys = "#vo.agentCode", attemptTimeout = 10)
    public Result finishTrain(@ApiParam(value = "完成培训信息", required = true)
                                  @RequestBody @Valid AgentApplyFinishTrainVO vo) {
        blAgentOnlineJoinApplyService.finishTrain(vo);
        return Result.success();
    }

    /**
     * 补充信息
     */
    @ApiOperation(value = "补充信息", notes = "补充信息")
    @PostMapping("replenishInfo")
    @RequiresPermissions(value = {"agent:apply:replenish"})
    @Lock(keys = "#vo.agentCode", attemptTimeout = 10)
    public Result replenishInfo(@ApiParam(value = "补充信息", required = true)
                              @RequestBody @Valid AgentApplyReplenishVO vo) {
        if(vo.getIsStaging() == 1){
            //暂存时候不校验相关信息
        }else{
            if(Objects.isNull(vo.getLongTerm())&&Objects.isNull(vo.getEndDate())){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("证件结束期和是否长期有效不能都为空"));
            }
            ValidatorUtils.validateEntity(vo, AddGroup.class);
        }
        blAgentOnlineJoinApplyService.replenishInfo(vo);
        return Result.success();
    }
    /**
     * 复审通过
     */
    @ApiOperation(value = "复审通过")
    @GetMapping("/pass/{agentCode}")
    @RequiresPermissions(value = {"agent:apply:pass"})
    public Result pass(@ApiParam(value = "代理人编码", required = true)
                                          @PathVariable("agentCode") String agentCode) {
        blAgentOnlineJoinApplyService.pass(agentCode);
        return Result.success();
    }
    /**
     * 复审驳回
     */
    @ApiOperation(value = "复审驳回", notes = "复审驳回")
    @PostMapping("reject")
    @RequiresPermissions(value = {"agent:apply:reject"})
    @Lock(keys = "#vo.agentCode", attemptTimeout = 10)
    public Result reject(@ApiParam(value = "复审驳回", required = true)
                                @RequestBody @Valid AgentApplyRejectVO vo) {
        blAgentOnlineJoinApplyService.reject(vo);
        return Result.success();
    }

    /**
     * 关闭申请
     * @param agentCode 代理人编码
     * @return
     */
    @ApiOperation(value = "关闭申请")
    @GetMapping("/close/{agentCode}")
    @RequiresPermissions(value = {"agent:apply:close"})
    public Result close(@ApiParam(value = "代理人编码", required = true)
                       @PathVariable("agentCode") String agentCode) {
        blAgentOnlineJoinApplyService.close(agentCode);
        return Result.success();
    }
}
