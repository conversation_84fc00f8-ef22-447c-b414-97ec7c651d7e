package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.CompanyTypeEnum;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectProductEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanyInfoService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectProductService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectService;
import com.mpolicy.manage.modules.settlement.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service("settlementReconcileCompanyInfoService")
public class SettlementReconcileCompanyInfoServiceImpl extends ServiceImpl<SettlementReconcileCompanyInfoDao, SettlementReconcileCompanyInfoEntity> implements SettlementReconcileCompanyInfoService {

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;

    @Autowired
    private SettlementReconcileCompanySubjectProductService settlementReconcileCompanySubjectProductService;

    @Autowired
    private OrgInfoService orgInfoService;

    /**
     * 分页获取数据
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils<ReconcileCompanyInfoListOut> queryPage(ReconcileCompanyInfoListInput params) {
        if (StrUtil.isNotBlank(params.getSubjectRuleCode())) {
            SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject = settlementReconcileCompanySubjectService.lambdaQuery()
                    .eq(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, params.getSubjectRuleCode())
                    .one();
            if (settlementReconcileCompanySubject != null) {
                params.setSubjectReconcileCompanyCode(settlementReconcileCompanySubject.getReconcileCompanyCode());
            } else {
                params.setSubjectReconcileCompanyCode("-");
            }
        }
        IPage<ReconcileCompanyInfoListOut> page = baseMapper.queryPage(new Page<ReconcileCompanyInfoListOut>(params.getPage(), params.getLimit()), params);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageUtils(page);
        }
        page.getRecords().forEach(action -> {
            CompanyTypeEnum companyTypeEnum = CompanyTypeEnum.deCode(action.getExternalSignatoryType());
            if (companyTypeEnum != null) {
                action.setExternalSignatoryTypeDesc(companyTypeEnum.getName());
            }
        });
        return new PageUtils(page);
    }

    /**
     * 获取详情
     *
     * @param id 主键id
     * @return
     */
    @Override
    public SettlementReconcileCompanyInfo info(Integer id) {
        SettlementReconcileCompanyInfoEntity settlementReconcileCompanyInfo = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保司配置不存在")));
        // 判断一下结算机构是什么类型的
        InsuranceCompanyEntity settlementReconcileCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                .eq(InsuranceCompanyEntity::getCompanyCode, settlementReconcileCompanyInfo.getSettlementCompanyCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算保司不存在")));

        SettlementReconcileCompanyInfo result = BeanUtil.copyProperties(settlementReconcileCompanyInfo, SettlementReconcileCompanyInfo.class);
        // 外部签署方
        List<List<String>> externalSignatoryCodeArr = StrUtil.split(settlementReconcileCompanyInfo.getExternalSignatoryCode(), ',')
                .stream().map(code -> insuranceCompanyService.getParentNode(code))
                .collect(Collectors.toList());
        result.setExternalSignatoryCodeArr(externalSignatoryCodeArr);
        // 内部签署方
        result.setInnerSignatoryCodeArr(orgInfoService.getParentNode(settlementReconcileCompanyInfo.getInnerSignatoryCode()));
        // 结算机构
        result.setSettlementCompanyType(settlementReconcileCompany.getCompanyType());
        result.setSettlementCompanyCodeArr(insuranceCompanyService.getParentNode(settlementReconcileCompanyInfo.getSettlementCompanyCode()));
        result.setCompanyCodeArr(StrUtil.split(settlementReconcileCompanyInfo.getCompanyCode(), ','));
        return result;
    }

    /**
     * 新增结算保司信息
     *
     * @param params
     */
    @Override
    public void saveReconcileCompanyInfo(SaveReconcileCompanyInfo params) {
        String reconcileCompanyCode = CommonUtils.createCodeLastNumber("SR");
        SettlementReconcileCompanyInfoEntity save = BeanUtil.copyProperties(params, SettlementReconcileCompanyInfoEntity.class);
        save.setReconcileCompanyCode(reconcileCompanyCode);
        // 内部签署方
        OrgInfoEntity innerSignatory = Optional.ofNullable(orgInfoService.lambdaQuery()
                .eq(OrgInfoEntity::getOrgCode, params.getInnerSignatoryCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("内部签署方不存在")));
        save.setInnerSignatoryName(innerSignatory.getOrgName());

        // 外部签署方
        String externalSignatoryName = insuranceCompanyService.lambdaQuery()
                .in(InsuranceCompanyEntity::getCompanyCode, StrUtil.split(params.getExternalSignatoryCode(), ','))
                .eq(InsuranceCompanyEntity::getCompanyType, params.getExternalSignatoryType())
                .list().stream().map(InsuranceCompanyEntity::getCompanyName).collect(Collectors.joining(","));
        save.setExternalSignatoryName(externalSignatoryName);

        //结算保司
        InsuranceCompanyEntity insuranceSettlementCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                .eq(InsuranceCompanyEntity::getCompanyCode, params.getSettlementCompanyCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算保司不存在")));
        save.setSettlementCompanyName(insuranceSettlementCompany.getCompanyName());

        //产品所属分公司
        List<String> companyNameArr = insuranceCompanyService.lambdaQuery()
                .in(InsuranceCompanyEntity::getCompanyCode, params.getCompanyCodeArr())
                .list().stream().map(InsuranceCompanyEntity::getCompanyName)
                .collect(Collectors.toList());
        save.setCompanyName(CollUtil.join(companyNameArr, ","));
        save.setCompanyCode(CollUtil.join(params.getCompanyCodeArr(), ","));

        //保存
        save(save);
    }

    /**
     * 修改协议保司配置
     *
     * @param params
     */
    @Override
    public void updateReconcileCompanyInfo(UpdateReconcileCompanyInfo params) {
        SettlementReconcileCompanyInfoEntity update = BeanUtil.copyProperties(params, SettlementReconcileCompanyInfoEntity.class);
        // 内部签署方
        OrgInfoEntity innerSignatory = Optional.ofNullable(orgInfoService.lambdaQuery()
                .eq(OrgInfoEntity::getOrgCode, params.getInnerSignatoryCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("内部签署方不存在")));
        update.setInnerSignatoryName(innerSignatory.getOrgName());

        // 外部签署方
        String externalSignatoryName = insuranceCompanyService.lambdaQuery()
                .in(InsuranceCompanyEntity::getCompanyCode, StrUtil.split(params.getExternalSignatoryCode(), ','))
                .eq(InsuranceCompanyEntity::getCompanyType, params.getExternalSignatoryType())
                .list().stream().map(InsuranceCompanyEntity::getCompanyName).collect(Collectors.joining(","));
        update.setExternalSignatoryName(externalSignatoryName);


        //结算保司
        InsuranceCompanyEntity insuranceSettlementCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                .eq(InsuranceCompanyEntity::getCompanyCode, params.getSettlementCompanyCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算保司不存在")));
        update.setSettlementCompanyName(insuranceSettlementCompany.getCompanyName());

        //产品所属分公司
        List<String> companyNameArr = insuranceCompanyService.lambdaQuery()
                .in(InsuranceCompanyEntity::getCompanyCode, params.getCompanyCodeArr())
                .list().stream().map(InsuranceCompanyEntity::getCompanyName)
                .collect(Collectors.toList());
        update.setCompanyName(CollUtil.join(companyNameArr, ","));
        update.setCompanyCode(CollUtil.join(params.getCompanyCodeArr(), ","));


        //修改
        updateById(update);
    }

    /**
     * 删除协议保司配置
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReconcileCompanyInfo(Integer id) {
        // 保司配置
        SettlementReconcileCompanyInfoEntity settlementReconcileCompanyInfo = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保司配置不存在")));
        removeById(id);

        // 保司科目配置
        List<String> subjectRuleCodeList = settlementReconcileCompanySubjectService.lambdaQuery()
                .eq(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, settlementReconcileCompanyInfo.getReconcileCompanyCode())
                .list().stream().map(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode).collect(Collectors.toList());
        if (!subjectRuleCodeList.isEmpty()) {
            // 删除保司科目配置
            settlementReconcileCompanySubjectService.lambdaUpdate()
                    .in(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, subjectRuleCodeList)
                    .remove();

            // 删除科目关联产品
            settlementReconcileCompanySubjectProductService.lambdaUpdate()
                    .in(SettlementReconcileCompanySubjectProductEntity::getSubjectRuleCode, subjectRuleCodeList)
                    .remove();
        }

    }
}
