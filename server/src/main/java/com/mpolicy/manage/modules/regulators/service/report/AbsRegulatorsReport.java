package com.mpolicy.manage.modules.regulators.service.report;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportStatusEnum;
import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportInfoService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportOrgService;
import com.mpolicy.manage.modules.regulators.service.RegulatorsReportService;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsReportBasic;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 监管报备报告抽象类
 *
 * <AUTHOR>
 * @date 2022-01-20 14:48
 */
@Slf4j
public abstract class AbsRegulatorsReport<T extends RegulatorsReportBasic> implements RegulatorsReportService<T> {

    @Autowired
    protected RegulatorsReportInfoService regulatorsReportInfoService;

    @Autowired
    protected RegulatorsReportOrgService regulatorsReportOrgService;

    /**
     * 文件存储service
     */
    @Autowired
    protected SysDocumentService sysDocumentService;

    @Autowired
    protected StorageService storageService;

    @Autowired
    protected IRedisService redisService;

    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Override
    public final String uploadOrgRegulatorsReport(T t) {
        // 1 校验监管报备的序号纪录
        RegulatorsReportInfoEntity reportInfo = Optional.ofNullable(
                regulatorsReportInfoService.lambdaQuery()
                        .eq(RegulatorsReportInfoEntity::getRegulatorsNo, t.reportRegulatorsNo())
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("监管报备月度纪录不存在，编号={}", t.reportRegulatorsNo()))));

        if (reportInfo.getReportStatus().equals(RegulatorsReportStatusEnum.FINISH.getCode())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("月度报备编码={} 已报送状态，无法进行报备处理", t.reportRegulatorsNo())));
        }
        // 设置报备年度 + 月度
        t.setRegulatorsYear(reportInfo.getRegulatorsYear());
        t.setRegulatorsMonth(reportInfo.getRegulatorsMonth());

        // 2 获取报备文档纪录
        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, t.getReportFileCode())
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("报备文档记录信息不存在")));

        // 3 校验文件+入库redis
        boolean fileStatus = checkRegulatorsReportFile(document, true);
        if (!fileStatus) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文件编码={} 报备文件内容存在错误，请核对修改后重试", t.getReportFileCode())));
        }

        // 4 获取目前机构报备集合
        List<RegulatorsReportOrgEntity> orgRegulatorsList = regulatorsReportOrgService.lambdaQuery().eq(RegulatorsReportOrgEntity::getRegulatorsNo, t.reportRegulatorsNo()).list();
        // 是否需要 包月阅读新增机构数量
        boolean needAddCount = orgRegulatorsList.stream().noneMatch(x -> StringUtils.equals(x.getOrgCode(), t.getReportOrgCode()));

        // 5 不是修改，需要生成机构报备唯一编号
        if (StringUtils.isBlank(t.getOrgRegulatorsNo())) {
            String orgRegulatorsNo = CommonUtils.createCodeLastNumber("ORG");
            t.setOrgRegulatorsNo(orgRegulatorsNo);
        }
        // 6 生成机构报备唯一编码 + 执行报备业务数据操作
        generatorReport(reportInfo, t, document);

        // 7 机构报备纪录上传成功后，后置处理
        afterReport(reportInfo, needAddCount);
        return t.getOrgRegulatorsNo();
    }

    /**
     * <p>
     * 后置处理
     * 1 根据监管报备月度编号 获取机构报备情况
     * 2 如果监管机构都满足 各类型的报备，状态设置为：已完整上传 否则为：未完整上传
     * </p>
     *
     * @param reportInfo        报备月度信息
     * @param needAddCount      是否需要更新 报备月度机构数量+1
     * @return void
     * <AUTHOR>
     * @since 2022/1/20
     */
    private void afterReport(RegulatorsReportInfoEntity reportInfo, boolean needAddCount) {

        // 1 获取月度报备 机构报备的列表 + 进行分组
        List<RegulatorsReportOrgEntity> orgRegulatorsList = regulatorsReportOrgService.lambdaQuery().eq(RegulatorsReportOrgEntity::getRegulatorsNo, reportInfo.getRegulatorsNo()).list();
        Map<String, List<RegulatorsReportOrgEntity>> orgMap = orgRegulatorsList.stream()
                .collect(Collectors.groupingBy(RegulatorsReportOrgEntity::getOrgCode));

        // 2 判断机构的报备是否都上传了 ： 暂时是用数量进行判断，是否太暴力?
        boolean regulatorsStatus = orgMap.entrySet().stream().allMatch(x -> x.getValue().size() == RegulatorsReportTypeEnum.values().length);

        log.info("报备月度编码={},变更上传状态为={}", reportInfo.getRegulatorsNo(), regulatorsStatus);
        // 3 更新监控机构月度报备上传状态 及 判断是否需要新增机构数量 + 1
        if (regulatorsStatus) {
            regulatorsReportInfoService.update(new LambdaUpdateWrapper<RegulatorsReportInfoEntity>()
                    .set(RegulatorsReportInfoEntity::getUploadCompleteStatus, 1)
                    .set(RegulatorsReportInfoEntity::getUpdateTime, new Date())
                    .set(needAddCount, RegulatorsReportInfoEntity::getRegulatorsOrgCount, reportInfo.getRegulatorsOrgCount() + 1)
                    .eq(RegulatorsReportInfoEntity::getRegulatorsNo, reportInfo.getRegulatorsNo()));
        } else {
            regulatorsReportInfoService.update(new LambdaUpdateWrapper<RegulatorsReportInfoEntity>()
                    .set(RegulatorsReportInfoEntity::getUploadCompleteStatus, 0)
                    .set(RegulatorsReportInfoEntity::getUpdateTime, new Date())
                    .set(needAddCount, RegulatorsReportInfoEntity::getRegulatorsOrgCount, reportInfo.getRegulatorsOrgCount() + 1)
                    .eq(RegulatorsReportInfoEntity::getRegulatorsNo, reportInfo.getRegulatorsNo()));
        }
    }

    /**
     * <p>
     * 执行生成报告
     * </p>
     *
     * @param reportInfo 月度报备信息
     * @param t          t
     * @param document   报备文档信息
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    abstract void generatorReport(RegulatorsReportInfoEntity reportInfo, T t, SysDocumentEntity document);
}
