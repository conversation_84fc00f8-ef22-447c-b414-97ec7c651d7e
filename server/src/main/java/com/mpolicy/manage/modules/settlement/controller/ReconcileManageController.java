package com.mpolicy.manage.modules.settlement.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.enums.SettlementInvoiceStatusEnum;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileConfirmEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInfoEntity;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateFactory;
import com.mpolicy.manage.modules.settlement.pattern.ReconcileTemplateHandler;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileConfirmService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInfoService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcilePolicyFileService;
import com.mpolicy.manage.modules.settlement.vo.*;
import com.mpolicy.manage.modules.settlement.vo.manage.*;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import com.mpolicy.web.common.annotation.PassArgsLog;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 保司对账单管理
 *
 * <AUTHOR>
 * @since 2023-05-24 15:59
 */
@RestController
@RequestMapping("/settlement/reconcile/manage")
@Api(tags = "保司对账单管理")
@Slf4j
public class ReconcileManageController extends ReconcileBaseController {

    /**
     * 保司对账单服务
     */
    @Autowired
    private SettlementReconcileInfoService settlementReconcileInfoService;
    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;


    /***
     * oss文件操作
     */
    @Autowired
    private StorageService storageService;

    /***
     * 文件存储service
     */
    @Autowired
    private SysDocumentService sysDocumentService;

    /***
     * 文件存储service
     */
    @Autowired
    private SettlementReconcilePolicyFileService settlementReconcilePolicyFileService;

    /***
     * 文件存储信息保存
     * @param: file 文件
     * @param: fileModelEnum 文件业务
     * @param: modelCode    文件模块
     * @param: ossResult    oss存储地址
     * @return com.mpolicy.manage.modules.sys.entity.SysDocumentEntity
     * <AUTHOR>
     * @date 2021/1/25 2:22 下午
     */
    private SysDocumentEntity saveFileDocument(MultipartFile file, FileModelEnum fileModelEnum, String modelCode, String relationCode, OssBaseOut ossResult) {
        SysDocumentEntity bean = new SysDocumentEntity();
        // 文件大小
        bean.setFileSize(file.getSize());
        bean.setFileName(file.getOriginalFilename());
        bean.setFileType(file.getContentType());
        bean.setFileExt(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1));
        // 文件编码
        bean.setFileCode(CommonUtils.createCode("oss"));
        // oss访问
        bean.setFilePath(ossResult.getFilePath());
        bean.setDomainPath(ossResult.getAccessDomainPath());
        bean.setFileSystem(fileModelEnum.getFileSystem());
        bean.setFileModule(modelCode);
        bean.setRelationCode(relationCode);
        sysDocumentService.save(bean);
        return bean;
    }

    /**
     * 分页查询保单对账单管理列表
     *
     * @param params 分页查询条件
     * @return com.mpolicy.common.result.Result<com.mpolicy.common.utils.PageUtils < com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileInfo>>
     * <AUTHOR>
     * @since 2023/5/25 01:19
     */
    @ApiOperation(value = "分页查询保单对账单管理列表", notes = "分页查询保单对账单管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码", example = "XT202201"),
            @ApiImplicitParam(paramType = "query", name = "externalSignatoryType", dataType = "String", value = "外部签署方类型字典", example = "xxx"),
            @ApiImplicitParam(paramType = "query", name = "reconcileMonth", dataType = "String", value = "对账月度", example = "2023年05期"),
            @ApiImplicitParam(paramType = "query", name = "reconcileStatus", dataType = "String", value = "对账单状态", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id"),
    })
    @PostMapping("/list")
    @RequiresPermissions("settlement:reconcile:all")
    public Result<PageUtils<SettlementReconcileInfo>> list(@ApiParam(hidden = true) @RequestBody Map<String, Object> params) {
        log.debug("获取保司对账单列表，查询条件={}", params);
        PageUtils<SettlementReconcileInfo> page = settlementReconcileInfoService.querySettlementReconcileInfoList(params);
        return Result.success(page);
    }

    /**
     * 修改责任人姓名
     *
     * @param vo
     * @return
     */
    @PostMapping("/updateReconcileHeadName")
    public Result updateReconcileHeadName(@RequestBody @Valid UpdateReconcileHeadNameVo vo) {
        settlementReconcileInfoService.lambdaUpdate()
                .eq(SettlementReconcileInfoEntity::getReconcileCode, vo.getReconcileCode())
                .set(SettlementReconcileInfoEntity::getHeadName, vo.getHeadName())
                .update();
        return Result.success();
    }

    /**
     * 对账单文件上传
     *
     * @param reconcileCode         对账单唯一编号
     * @param fileSystem            文件所属模块
     * @param modelCode             文件用途
     * @param reconcileTemplateCode 文件规则类型编码
     * @param file                  file
     * @return com.mpolicy.common.result.Result<com.mpolicy.manage.modules.sys.entity.SysDocumentEntity>
     * <AUTHOR>
     * @since 2023/5/29 11:23
     */
    @ApiOperation(value = "对账单文件上传", notes = "对账单文件上传")
    @RequestMapping(value = "/upload/{reconcileCode}", method = RequestMethod.POST)
    @PassArgsLog
    public Result<SettlementReconcileFile> uploadFileSimple(
            @PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
            @RequestParam("fileSystem") @ApiParam(name = "fileSystem", required = true, value = "文件所属模块", example = "customer") String fileSystem,
            @RequestParam("modelCode") @ApiParam(name = "modelCode", value = "文件用途", example = "portrait") String modelCode,
            @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode", value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
            @RequestParam(value = "file") MultipartFile file) throws IOException {
        // 上传保司对账单的时候判断一下 是否已经开票了,处于开票中.
        SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(settlementReconcileInfoService.lambdaQuery()
                        .eq(SettlementReconcileInfoEntity::getReconcileCode,reconcileCode)
                        .one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("对账单信息不存在")));
        if (SettlementInvoiceStatusEnum.FULL_INVOICING.getCode().equals(settlementReconcileInfo.getInvoiceStatus()) ||
                SettlementInvoiceStatusEnum.PARTIAL_INVOICING.getCode().equals(settlementReconcileInfo.getInvoiceStatus())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("已存在开票记录不允许上传对账单"));
        }
        log.info("对账单文件上传 reconcileCode = {} reconcileTemplateCode={} ", reconcileCode, reconcileTemplateCode);
        ReconcileTemplateEnum reconcileTemplateEnum = ReconcileTemplateEnum.matchSearchCode(reconcileTemplateCode);
        if (reconcileTemplateEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("对账单文件类型不识别"));
        }
        FileModelEnum fileModelEnum = FileModelEnum.decode(fileSystem);
        if (fileModelEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件操作分类参数错误"));
        }
        // 验证文件格式
        if (!fileModelEnum.checkFileType(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("上传文件格式不支持"));
        }
        // 文件格式校验
        ReconcileTemplateHandler handler = ReconcileTemplateFactory.getInvokeStrategy(reconcileTemplateEnum.getCode());
        handler.readFile(file.getInputStream(), reconcileCode);
        // 校验通过后，写入到oss
        long beginTime = System.currentTimeMillis();
        OssBaseOut ossResult = storageService.uploadBytesFile(fileModelEnum.ossObjectFileName(modelCode, file.getOriginalFilename()), file.getBytes());
        // 保存文件存储信息
        SysDocumentEntity document = saveFileDocument(file, fileModelEnum, modelCode, reconcileCode, ossResult);
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        log.info("单文件上传成功,耗时={}毫秒", time);
        SettlementReconcileFile result = new SettlementReconcileFile();
        result.setFileName(document.getFileName());
        result.setReconcileCode(reconcileCode);
        result.setFileUrl(document.getDomainPath());
        result.setReconcileFileCode(document.getFileCode());
        result.setReconcileFileName(reconcileTemplateEnum.getLabel());
        result.setReconcileFileType(reconcileTemplateEnum.getCode());
        result.setCreateTime(document.getCreateTime());
        result.setCreateUser(document.getCreateUser());
        return Result.success(result);
    }

    /**
     * 分页差异列表
     *
     * @param reconcileCode 保司对账单唯一编号
     * @param params        分页条件
     * @return com.mpolicy.common.result.Result<com.mpolicy.common.utils.PageUtils < com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff>>
     * <AUTHOR>
     * @since 2023/5/25 01:19
     */
    @ApiOperation(value = "分页差异列表", notes = "分页差异列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "diffType", dataType = "String", value = "差异类型", example = "SETTLEMENT:RECONCILE:DIFFTYPE:1"),
            @ApiImplicitParam(paramType = "query", name = "diffStatus", dataType = "String", value = "差异处理状态0待处理1已处理", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "followStatus", dataType = "String", value = "后续待办处理状态0待处理1处理完成2无需处理", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id"),
            @ApiImplicitParam(paramType = "query", name = "signChange", dataType = "String", value = "是否标记变更，1是0否", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "zeroPremium", dataType = "String", value = "是否保费为0，1是0否", example = "1"),

    })
    @GetMapping("/diff/{reconcileCode}")
    // @RequiresPermissions("settlement:reconcile:manage:diff")
    public Result<PageUtils<SettlementReconcileDiff>> list(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
                                                           @ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("获取保司对账单差异列表，对账单号={}", reconcileCode);
        PageUtils<SettlementReconcileDiff> page = settlementReconcileInfoService.querySettlementReconcileDiffList(reconcileCode, params);
        return Result.success(page);
    }

    /**
     * 获取差异详情
     *
     * @param billCode
     * @return
     */
    /*@GetMapping("/diff/info/{billCode}")
    public Result<SettlementReconcileDiff> list(@PathVariable @ApiParam(name = "billCode", value = "RC202305230101010101") String billCode) {
        SettlementReconcileDiff page = settlementReconcileInfoService.querySettlementReconcileDiffInfo(billCode);
        return Result.success(page);
    }*/


    /**
     * 对账单文件管理列表
     *
     * @param reconcileCode 对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileFile>>
     * <AUTHOR>
     * @since 2023/5/30 15:20
     */
    @ApiOperation(value = "对账单文件管理列表", notes = "对账单文件管理列表")
    @PostMapping("/file/list/{reconcileCode}")
    public Result<List<SettlementReconcileFile>> reconcileFileList(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode) {
        return Result.success(settlementReconcileInfoService.reconcileFileList(reconcileCode));
    }

    /**
     * 上传对账单文件
     *
     * @param reconcileCode 对账单唯一编号
     * @param fileCode      文件编码
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 18:37
     */
  /*  @ApiOperation(value = "上传对账单文件", notes = "上传保司对账单文件")
    @PostMapping("/file/upload/{reconcileCode}")
    public Result<String> uploadReconcileFile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
                                              @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode", value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
                                              @RequestParam(value = "fileCode") @ApiParam(name = "fileCode", value = "保司对账单文件编码", example = "F001") String fileCode) {

        ReconcileTemplateEnum reconcileTemplateEnum = ReconcileTemplateEnum.matchSearchCode(reconcileTemplateCode);
        settlementReconcileInfoService.uploadCompanyReconcileFile(reconcileCode, reconcileTemplateEnum, fileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success("success");
    }
*/
    /**
     * 重新上传对账单文件
     *
     * @param reconcileCode  对账单唯一编号
     * @param sourceFileCode 原文件编码
     * @param fileCode       文件编码
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 18:38
     */
    /*@ApiOperation(value = "重新上传对账单文件", notes = "重新上传保司对账单文件")
    @PostMapping("/file/retry_upload/{reconcileCode}")
    public Result<String> retryUploadReconcileFile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
                                                   @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode", value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
                                                   @RequestParam(value = "sourceFileCode") @ApiParam(name = "sourceFileCode", value = "保司对账单文件编码", example = "F001") String sourceFileCode,
                                                   @RequestParam(value = "fileCode") @ApiParam(name = "fileCode", value = "保司对账单文件编码", example = "F001") String fileCode) {

        ReconcileTemplateEnum reconcileTemplateEnum = ReconcileTemplateEnum.matchSearchCode(reconcileTemplateCode);
        settlementReconcileInfoService.retryUploadCompanyReconcileFile(reconcileCode, reconcileTemplateEnum, sourceFileCode, fileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success("success");
    }*/

    /**
     * 删除对账单规则文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileFileCode 文件编码
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 18:38
     */
    /*@ApiOperation(value = "删除对账关联对账文件", notes = "删除对账关联对账文件")
    @PostMapping("/file/remove/{reconcileCode}/{reconcileFileCode}")
    public Result<String> removeReconcileFile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
                                              @PathVariable @ApiParam(name = "reconcileFileCode", value = "oss202305023123466") String reconcileFileCode) {
        settlementReconcileInfoService.removeReconcileFile(reconcileCode, reconcileFileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success("success");
    }*/


    /**
     * 操作开始对账
     *
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 01:19
     */
    @ApiOperation(value = "开始对账", notes = "开始对账")
    @PostMapping("start_reconcile")
    public Result<String> startReconcile(@RequestBody @Valid StartReconcileVo input) {
        // 获取对账单
        SettlementReconcileInfo reconcileInfo = settlementReconcileInfoService.querySettlementReconcileInfo(input.getReconcileCode());
        ReconcileStatusEnum reconcileStatusEnum = ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
        if (reconcileStatusEnum == ReconcileStatusEnum.GENERATING_GENERATE_ING) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单生成中，无法开启对账")));
        }
        if (reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_FINISH || reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_CLOSE) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单已经完成对账或已关闭，无法重新对账")));
        }
        // 执行开始对账
        String uuid = settlementReconcileInfoService.startReconcile(input);
        return Result.success(uuid);
    }

    /**
     * 操作重新对账
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 01:19
     */
    @ApiOperation(value = "重新对账", notes = "重新对账")
    @PostMapping("/retry_reconcile/{reconcileCode}")
    // @RequiresPermissions("settlement:reconcile:manage:ope")
    public Result<String> retryStartReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode) {
        log.debug("执行重新对账，对账单号={}", reconcileCode);
        // 获取对账单
        SettlementReconcileInfo reconcileInfo = settlementReconcileInfoService.querySettlementReconcileInfo(reconcileCode);
        ReconcileStatusEnum reconcileStatusEnum = ReconcileStatusEnum.decode(reconcileInfo.getReconcileStatus());
        if (reconcileStatusEnum == ReconcileStatusEnum.GENERATING_GENERATE_ING) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单生成中，无法开启对账")));
        }
        if (reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_FINISH || reconcileStatusEnum == ReconcileStatusEnum.RECONCILE_CLOSE) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("对账单已经完成对账或已关闭，无法重新对账")));
        }
        String uuid = settlementReconcileInfoService.retryStartReconcile(reconcileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success(uuid);
    }


    /**
     * 对账单批量调整精度
     *
     * @param input 批量处理对账单精度信息
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 17:52
     */
    @ApiOperation(value = "批量调整精度", notes = "批量调整精度")
    @PostMapping("/reconcile_amount_accuracy/{reconcileCode}")
    // @RequiresPermissions("settlement:reconcile:manage:ope")
    public Result<String> reconcileAmountAccuracy(@RequestBody @ApiParam(name = "input", value = "批量处理对账单精度信息") AmountAccuracyInput input) {
        ValidatorUtils.validateEntity(input);
        log.debug("批量调整精度，对账单号={}", input.getReconcileCode());
        String uuid = settlementReconcileInfoService.reconcileAmountAccuracy(input,
                ShiroUtils.getUserEntity().getUsername());
        return Result.success(uuid);
    }


    @ApiOperation(value = "对账单保单科目列表信息", notes = "对账单保单科目列表信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号", example = "XT202201"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/confirm/list/{reconcileCode}")
    public Result<PageUtils<SettlementReconcileConfirm>> confirmReconcileList(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode,
                                                                              @ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("对账单详情信息，对账单号={}", reconcileCode);
        return Result.success(settlementReconcileInfoService.confirmReconcileList(reconcileCode, params));
    }

    /**
     * 对账单总览
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.common.result.Result<com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileConfirmSubject>
     * <AUTHOR>
     * @since 2023/6/1 01:03
     */
    @ApiOperation(value = "对账单总览", notes = "对账单总览")
    @GetMapping("/reconcile_overview/{reconcileCode}")
    // @RequiresPermissions("settlement:reconcile:manage:ope")
    public Result<SettlementReconcileConfirmSummary> reconcileOverview(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode) {
        log.debug("对账单总览，对账单号={}", reconcileCode);
        return Result.success(settlementReconcileInfoService.reconcileOverview(reconcileCode));
    }


    /**
     * 完成对账
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 18:47
     */
    @ApiOperation(value = "完成对账", notes = "完成对账")
    @PostMapping("/finish_reconcile/{reconcileCode}")
    // @RequiresPermissions("settlement:reconcile:manage:ope")
    public Result<String> finishReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode) {
        log.debug("获取保司对账单差异列表，对账单号={}", reconcileCode);
        String result = settlementReconcileInfoService.finishReconcile(reconcileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success(result);
    }

    /**
     * 刷新对账单保单数据
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/6/27 18:47
     */
    @ApiOperation(value = "刷新对账单保单数据", notes = "刷新对账单保单数据")
    @PostMapping("/refresh_policy/{reconcileCode}")
    public Result<String> reconcileRefreshPolicy(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode) {
        log.debug("刷新对账单保单数据，对账单号={}", reconcileCode);
        String uuid = settlementReconcileInfoService.reconcileRefreshPolicy(reconcileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success(uuid);
    }

    /**
     * 关闭对账
     *
     * @param reconcileCode 保司对账单唯一编号
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/6/27 18:47
     */
    @ApiOperation(value = "关闭对账", notes = "关闭对账")
    @PostMapping("/close/{reconcileCode}")
    public Result<String> closeReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode) {
        log.debug("关闭对账，对账单号={}", reconcileCode);
        settlementReconcileInfoService.closeReconcile(reconcileCode, ShiroUtils.getUserEntity().getUsername());
        return Result.success("success");
    }

    /**
     * 对账单导出
     *
     * @param reconcileCode 对账单编码
     * @param response      response
     * <AUTHOR>
     * @since 2023/5/25 01:09
     */
    @PostMapping("/exportReconcileFile/{reconcileCode}")
    @ApiOperation(value = "对账单导出", notes = "对账单导出")
    public void exportReconcileFile(HttpServletResponse response, @PathVariable(value = "reconcileCode") String reconcileCode) throws IOException {

        settlementReconcileInfoService.exportReconcileFile(reconcileCode, response);
    }

    /**
     * 对账单差异导出
     *
     * @param reconcileCode 保司对账单唯一编号
     * <AUTHOR>
     * @since 2023/5/25 13:59
     */
    @ApiOperation(value = "对账单差异导出", notes = "对账单差异导出")
    @GetMapping("/export_diff/{reconcileCode}")
    public void exportDiffReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "RC202305230101010101") String reconcileCode, HttpServletResponse response) throws IOException {
        // 获取对账单详情
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        OutputStream out = null;
        try {
            SettlementReconcileInfoEntity settlementReconcileInfo = Optional.ofNullable(
                    settlementReconcileInfoService.lambdaQuery()
                            .eq(SettlementReconcileInfoEntity::getReconcileCode, reconcileCode).one()).orElseThrow(
                    () -> new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("结算编码={} 结算信息不存在", reconcileCode))));
            out = response.getOutputStream();
            String fileName = StrUtil.format("结算编码{}差异列表-{}.xlsx", reconcileCode, DateUtil.today());
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode(fileName));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, SettlementReconcileDiffExcel.class);
            sheet.setSheetName("差异列表");
            int page = 1;
            while (true) {
                // 分批查询
                List<SettlementReconcileConfirmEntity> records = settlementReconcileConfirmService.lambdaQuery()
                        .eq(SettlementReconcileConfirmEntity::getReconcileCode, reconcileCode)
                        .eq(SettlementReconcileConfirmEntity::getDiffFlag, 1)
                        .eq(SettlementReconcileConfirmEntity::getDiffStatus, 0)
                        .page(new Page<>(page, 3000)).getRecords();
                List<SettlementReconcileDiffExcel> dataList =
                        settlementReconcileInfoService.exportSettlementReconcileDiffList(settlementReconcileInfo, records);
                if (!dataList.isEmpty()) {
                    writer.write(dataList, sheet);
                    page++;
                } else {
                    log.info("{} 差异列表，退出构建，执行导出", reconcileCode);
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("{} 差异文件生成完成 .....耗时={}", reconcileCode, millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @ApiOperation(value = "差异处理申请", notes = "差异处理申请")
    @PostMapping("/diff_backlog")
    // @RequiresPermissions("settlement:reconcile:manage:list")
    public Result<String> diffBacklog(@RequestBody @ApiParam(name = "diffBacklogInput", value = "差异处理申请信息") ReconcileDiffBacklogInput diffBacklogInput) {
        // 基础校验
        ValidatorUtils.validateEntity(diffBacklogInput);
        // 差异处理申请
        settlementReconcileInfoService.diffBacklog(diffBacklogInput, ShiroUtils.getUserEntity().getUsername());
        return Result.success("success");
    }


    /**
     * 导出缺失保单
     *
     * @param input
     * @param response
     * @throws IOException
     */
    @ApiOperation(value = "导出缺失保单", notes = "对账单差异导出")
    @PostMapping("/export_deletion_policy")
    @RequiresPermissions(value = {"settlement:reconcile:all"})
    public void exportDeletionPolicy(@RequestBody @Valid ExportDeletionPolicyVo input, HttpServletResponse response) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode("缺失保单.xlsx"));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, ExportDeletionPolicyOut.class);
            sheet.setSheetName("缺失保单");
            List<ExportDeletionPolicyOut> resultList = settlementReconcileInfoService.exportDeletionPolicy(input);
            writer.write(resultList, sheet);
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("缺失保单生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 获取溯源信息
     *
     * @param billCode
     * @throws IOException
     */
    @GetMapping("findTraceSource/{billCode}")
    @ApiOperation(value = "获取溯源信息", notes = "获取溯源信息")
    public Result<TraceSourceOut> findTraceSource(@PathVariable(value = "billCode") String billCode) throws IOException {
        TraceSourceOut result = settlementReconcileInfoService.findTraceSource(billCode);
        return Result.success(result);
    }

    /**
     * 刷新定时任务
     *
     * @param input
     * @return
     * @throws IOException
     */
    @PostMapping("refreshSettlementEventJob")
    @ApiOperation(value = "刷新定时任务", notes = "刷新定时任务")
    public Result<TraceSourceOut> refreshSettlementEventJob(@RequestBody RefreshJobVo input) throws IOException {
        settlementReconcileInfoService.refreshSettlementEventJob(input);
        return Result.success();
    }

    /**
     * 刷新明细
     *
     * @param input
     * @return
     * @throws IOException
     */
    @PostMapping("handleSettlementPolicyInfo")
    @ApiOperation(value = "刷新明细", notes = "刷新明细")
    public Result<TraceSourceOut> handleSettlementPolicyInfo(@RequestBody RefreshPolicyVo input) throws IOException {
        settlementReconcileInfoService.handleSettlementPolicyInfo(input);
        return Result.success();
    }

    /**
     * 批量刷新保费费率
     *
     * @return
     * @throws IOException
     */
    @PostMapping("batchRefreshRate/{reconcileCode}")
    @ApiOperation(value = "批量刷新保费费率", notes = "批量刷新保费费率")
    public Result<String> batchRefreshRate(@PathVariable(required = false) @NotBlank(message = "结算单编码不能为空") String reconcileCode) throws IOException {
        String uuid = settlementReconcileInfoService.batchRefreshRate(reconcileCode);
        return Result.success(uuid);
    }

    /**
     * 设置标记状态
     *
     * @return
     * @throws IOException
     */
    @PostMapping("updateMarkStatus/{billCode}")
    @ApiOperation(value = "批量刷新保费费率", notes = "批量刷新保费费率")
    public Result updateMarkStatus(@PathVariable(required = false) @NotBlank(message = "结算单编码不能为空") String billCode) throws IOException {
        settlementReconcileInfoService.updateMarkStatus(billCode);
        return Result.success();
    }

    /**
     * 创建对账单
     *
     * @return
     * @throws IOException
     */
    @PostMapping("createReconcile")
    @ApiOperation(value = "创建对账单", notes = "创建对账单")
    public Result createReconcile(@RequestBody @Valid CreateReconcileVo vo) throws IOException {
        settlementReconcileInfoService.createReconcile(vo);
        return Result.success();
    }

    /**
     * 设置延期下个月
     *
     * @param input
     * @return
     * @throws IOException
     */
    @PostMapping("nextPostponedMonth")
    @ApiOperation(value = "设置延期下个月", notes = "设置延期下个月")
    public Result nextPostponedMonth(@RequestBody @Valid NextPostponedMonthVo input) throws IOException {
        settlementReconcileInfoService.nextPostponedMonth(input);
        return Result.success();
    }

    /**
     * 批量刷新异常数据
     *
     * @return
     * @throws IOException
     */
    @PostMapping("retry/list")
    @ApiOperation(value = "批量刷新异常数据", notes = "批量刷新异常数据")
    public Result handleExceptionTask(@RequestBody List<String> billCodeList) throws IOException {
        settlementReconcileInfoService.batchRefreshFailEvent(billCodeList);
        return Result.success();
    }

    @PostMapping("retry/{billCode}")
    @ApiOperation(value = "批量刷新异常数据", notes = "批量刷新异常数据")
    public Result handleExceptionTask(@PathVariable(required = false) @NotBlank(message = "结算单编码不能为空") String billCode) throws IOException {
        return Result.success();
    }

    /**
     * 设置标记状态
     *
     * @return
     * @throws IOException
     */
    @PostMapping("batch/delay")
    @ApiOperation(value = "批量延期对账", notes = "批量延期对账")
    public Result batchDelayReconcile(@RequestBody ReconcileBatchDelayInput delayInput) throws IOException {
        settlementReconcileInfoService.batchDelayReconcile(delayInput);
        return Result.success();
    }

    /**
     * 设置标记状态
     *
     * @return
     * @throws IOException
     */
    @PostMapping("batch/needless/delay")
    @ApiOperation(value = "批量无须对账", notes = "批量无须对账")
    public Result batchNeedlessReconcile(@RequestBody BatchNeedlessReconcileInput needlessReconcileInput) throws IOException {
        settlementReconcileInfoService.batchNeedlessReconcile(needlessReconcileInput);
        return Result.success();
    }

    /**
     * 设置数据挂起
     */
    @PostMapping("hangUp")
    @ApiOperation(value = "设置数据挂起", notes = "设置数据挂起")
    public Result hangUp(@RequestBody ReconcileHangUpVo input) throws IOException {
        settlementReconcileInfoService.hangUp(input);
        return Result.success();
    }

    /**
     * 取消数据挂起
     */
    @PostMapping("unHangUp")
    @ApiOperation(value = "取消数据挂起", notes = "取消数据挂起")
    public Result unHangUp(@RequestBody ReconcileHangUpVo input) throws IOException {
        settlementReconcileInfoService.unHangUp(input);
        return Result.success();
    }
    /**
     * 处理仅报送保费数据
     */
    @PostMapping("handleOnlySubmitPremium")
    @ApiOperation(value = "处理仅报送保费数据", notes = "处理仅报送保费数据")
    public Result handleOnlySubmitPremium(@RequestBody ReconcileHangUpVo input) throws IOException {
        settlementReconcileInfoService.handleOnlySubmitPremium(input);
        return Result.success();
    }
    /**
     * 设置对账单为待对账
     */
    @PostMapping("setToBeReconciled")
    @ApiOperation(value = "设置对账单为待对账", notes = "设置对账单为待对账")
    public Result setToBeReconciled(@RequestBody ReconcileHangUpVo input) throws IOException {
        settlementReconcileInfoService.setToBeReconciled(input);
        return Result.success();
    }
}