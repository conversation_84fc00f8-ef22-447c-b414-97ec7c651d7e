package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoRecordEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.*;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.*;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 10:39 上午
 * @Version 1.0
 */
public interface SettlementCostAutoInfoDao extends ImsBaseMapper<SettlementCostAutoInfoEntity> {

    /**
     * 分页获取每个人每个机构下的汇总数据
     *
     * @param page
     * @param input
     * @return
     */
    IPage<SettlementCostAutoInfoEntity> pageSettlementCostAutoInfoBySummary(@Param("page") Page<SettlementCostAutoInfoEntity> page, @Param("input") SettlementCostAutoInput input);

    List<SettlementCostAutoInfoEntity> pageSettlementCostAutoInfoBySummaryExport(@Param("input") SettlementCostAutoInput input, @Param("start") Integer start,
                                                                                 @Param("batchSize") Integer batchSize);

    List<SettlementDynamicSubjectVO> queryAllSummaryDynamicSubject(SettlementCostAutoInput params);

    List<SettlementDynamicSubjectVO> queryAllSummarySubject(SettlementCostAutoInput params);

    /**
     * @param monthList      结佣月份
     * @param objectCodeList 结算对象
     * @return
     */
    List<SettlementCostAutoRecordEntity> queryInstitutionSub(@Param("monthList") Collection<String> monthList, @Param("objectCodeList") Collection<String> objectCodeList);

    IPage<SettlementDetailPolicyDimensionVo> pagePolicyDimension(@Param("page") Page<SettlementDetailPolicyDimensionVo> page, @Param("p") PageSettlementDetailParams p);

    IPage<SettlementDetailProductDimensionVO> pagePolicyProductDimension(@Param("page") Page<SettlementDetailPolicyDimensionVo> page, @Param("p") PageSettlementDetailParams p);

    IPage<SettlementDetailProductDimensionVO> pageProductDimension(@Param("page") Page<SettlementDetailPolicyDimensionVo> page, @Param("p") PageSettlementDetailParams p);

    BigDecimal shortSummary(@Param("p") PageSettlementDetailParams p);

    DetailSummaryLongPromotion longPromotionSummary(@Param("p") PageSettlementDetailParams p);

    DetailSummaryLongReissuePromotion longReissuePromotionSummary(@Param("p") PageSettlementDetailParams p);

    DetailSummarySupervisorPerformance supervisorPerformanceSummary(@Param("p") PageSettlementDetailParams p);

    DetailSummaryPcoAllowance pcoAllowanceSummary(@Param("p") PageSettlementDetailParams p);

    DetailSummaryPcoPerformance pcoPerformanceSummary(@Param("p") PageSettlementDetailParams p);

    List<SettlementConfirmInstitutionPageVO> listConfirmInstitutionPage(List<String> cycleList);

    List<SettlementDetailAllSubjectVO> listSettlementDetailAllSubjectVO(SettlementCostAutoInput params);
}
