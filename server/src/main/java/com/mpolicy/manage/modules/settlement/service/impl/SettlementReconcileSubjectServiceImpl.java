package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileSubjectDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileSubjectEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileSubjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保司结算对账单科目
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileSubjectService")
public class SettlementReconcileSubjectServiceImpl extends ServiceImpl<SettlementReconcileSubjectDao, SettlementReconcileSubjectEntity> implements SettlementReconcileSubjectService {

}
