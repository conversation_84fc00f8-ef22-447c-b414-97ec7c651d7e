package com.mpolicy.manage.modules.agentApply.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Description 代理人线上签约模块枚举
 * @Date 13:49 2024/3/13
 * @Param
 * <AUTHOR>
 * @return
 **/
@Getter
public enum AgentSignModelEnum {
    //代理人线上签约状态 1 代理人线上入职 2投保告知书 3互联网告知书
    MODEL1(1, "代理人线上入职","online"),
    MODEL2(2, "投保告知书","insure"),
    MODEL3(3, "互联网告知书","internet"),
    ;

    private final Integer code;
    private final String name;
    private final String strategy;

    AgentSignModelEnum(Integer code, String name, String strategy) {
        this.code = code;
        this.name = name;
        this.strategy = strategy;
    }

    public static AgentSignModelEnum getNameByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
