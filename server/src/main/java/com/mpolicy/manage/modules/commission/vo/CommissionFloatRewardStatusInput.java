package com.mpolicy.manage.modules.commission.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CommissionFloatRewardStatusInput implements Serializable {
    private static final long serialVersionUID = -6922189963320635266L;

    @NotNull(message = "ID不能为空")
    private Integer id;

    @NotNull(message = "状态不能为空")
    private Integer programmeStatus;
}
