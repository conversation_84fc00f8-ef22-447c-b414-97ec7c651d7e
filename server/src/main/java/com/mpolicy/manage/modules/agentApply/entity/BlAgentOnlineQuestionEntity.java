package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人入职申请题目表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-07 13:40:42
 */
@TableName("bl_agent_online_question")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineQuestionEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 题目编码
     */
    private String code;
    /**
     * 所属部分(1:一 2:二 3:三)
     */
    private Integer part;
    /**
     * 题型(1:单选题 2:多选题 3:判断题)
     */
    private Integer type;
    /**
     * 分值
     */
    private String score;
    /**
     * 题目
     */
    private String title;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
