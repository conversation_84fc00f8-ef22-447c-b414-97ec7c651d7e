package com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/1/31 10:32
 * @Version 1.0
 */
@Data
public class DetailSummaryLongReissuePromotion {
    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("长险推广费")
    private BigDecimal longReissuePromotion;

    @ApiModelProperty("长期险续期率")
    private BigDecimal longRenewalRate;

    @ApiModelProperty("发放比例")
    private BigDecimal costRate;

    @ApiModelProperty("发放金额")
    private BigDecimal costAmount;


}
