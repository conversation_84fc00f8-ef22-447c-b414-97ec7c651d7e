package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 科目范围(动态科目)分摊明细
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
@TableName("settlement_cost_subject_data_dynamic_apportion")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectDataDynamicApportionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 动态科目申请编号
	 */
	private String applyCode;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 动态科目编码
	 */
	private String dynamicSubjectCode;
	/**
	 * 动态科目名称
	 */
	private String dynamicSubjectName;
	/**
	 * 区域编码
	 */
	private String regionCode;
	/**
	 * 区域名称
	 */
	private String regionName;
	/**
	 * 分支机构编码
	 */
	private String orgCode;
	/**
	 * 分支机构名称
	 */
	private String orgName;
	/**
	 * 结算机构编码
	 */
	private String settlementInstitution;
	/**
	 * 结算机构名称
	 */
	private String settlementInstitutionName;
	/**
	 * 员工编码
	 */
	private String employeeCode;
	/**
	 * 员工名称
	 */
	private String employeeName;
	/**
	 * 保单号
	 */
	private String policyCode;
	/**
	 * 批单号
	 */
	private String endorsementNo;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 续期期次
	 */
	private Integer renewalPeriod;
	/**
	 * 保费
	 */
	private BigDecimal premium;
	/**
	 * 分摊金额
	 */
	private BigDecimal apportionCash;
	/**
	 * 分摊备注
	 */
	private String apportionDesc;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
