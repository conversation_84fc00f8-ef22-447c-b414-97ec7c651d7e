package com.mpolicy.manage.modules.settlement.service.detail;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;

/**
 * <AUTHOR>
 * @Date 2024/1/31 14:24
 * @Version 1.0
 */
public abstract class AbsCommonParamsSettlementDetail<S, L> implements SettlementDetailService<S, L, PageSettlementDetailParams> {

    public String calLastMonth(PageSettlementDetailParams p) {
        DateTime date = DateUtil.parse(p.getSettlementMonth(), "yyyyMM");
        DateTime lastMonth = DateUtil.offsetMonth(date, -1);

        return DateUtil.month(lastMonth) + 1 + "月";
    }

    public String calLastMonthCycle(PageSettlementDetailParams p) {
        DateTime date = DateUtil.parse(p.getSettlementMonth(), "yyyyMM");
        DateTime lastMonth = DateUtil.offsetMonth(date, -1);
        return DateUtil.format(lastMonth, "yyyyMM");
    }

    public String calMonthBeforeLastMonth(PageSettlementDetailParams p) {
        DateTime date = DateUtil.parse(p.getSettlementMonth(), "yyyyMM");

        DateTime lastMonth = DateUtil.offsetMonth(date, -1);
        DateTime monthBeforeLastMonth = DateUtil.offsetMonth(lastMonth, -1);

        return DateUtil.month(monthBeforeLastMonth) + 1 + "月";
    }




    public String calMonthBeforeBeforeLastMonth(PageSettlementDetailParams p) {
        DateTime date = DateUtil.parse(p.getSettlementMonth(), "yyyyMM");

        DateTime lastMonth = DateUtil.offsetMonth(date, -1);
        DateTime monthBeforeLastMonth = DateUtil.offsetMonth(lastMonth, -1);
        DateTime monthBeforeBeforeLastMonth = DateUtil.offsetMonth(monthBeforeLastMonth, -1);

        return DateUtil.month(monthBeforeBeforeLastMonth) + 1 + "月";
    }


}
