package com.mpolicy.manage.modules.settlement.controller;



import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.settlement.service.SettlementPolicyProductFeePremService;
import com.mpolicy.manage.modules.settlement.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @Date 2025/2/26 18:43
 * @Version 1.0
 */
@RestController
@RequestMapping("settlement/policy/product/tax/prem")
@Api(tags = "税率费率")
public class SettlementPolicyProductFeePremController {


    @Autowired
    private SettlementPolicyProductFeePremService feePremService;


//    @ApiOperation(value = "上传一单一议税率", notes = "上传一单一议保单税率")
//    @PostMapping("/upload/fee/premium")
//    @RequiresPermissions(value = {"settlement:policy:all"})
    public Result<Void> uploadPremiumFile(UploadPolicyProductPremInput input) {


        FileModelEnum fileModelEnum = FileModelEnum.decode(input.getFileSystem());
        if (fileModelEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件操作分类参数错误"));
        }
        MultipartFile file = input.getFile();
        // 验证文件格式
        if (!fileModelEnum.checkFileType(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("上传文件格式不支持"));
        }
        // 上传文件
//        feePremService.uploadPremiumFile(input, , );
        return Result.success();
    }

    @PostMapping("list")
    @RequiresPermissions(value = {"settlement:policy:all"})
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "POST")
    public Result<PageUtils<SettlementPolicyProductTaxPremListOut>> list(@RequestBody SettlementPolicyProductTaxPremListInput input){
        PageUtils<SettlementPolicyProductTaxPremListOut> page = feePremService.pageList(input);
        return Result.success(page);
    }



}
