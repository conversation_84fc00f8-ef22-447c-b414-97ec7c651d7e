package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanyProductService;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;


/**
 * 保司产品映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 14:29:16
 */
@RestController
@RequestMapping("settlement/reconcile/company/product")
@Api(tags = "结算保司配置对应规则科目表")
public class SettlementReconcileCompanyProductController {

    @Autowired
    private SettlementReconcileCompanyProductService settlementReconcileCompanyProductService;

    /**
     * 获取全部保司产品映射列表
     *
     * @param input 请求参数
     * @return
     */
    @GetMapping("list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    @RequiresPermissions(value = {"settlement:product:all"})
    public Result<PageUtils<SettlementReconcileCompanyProductListOut>> list(SettlementReconcileCompanyProductListInput input) {
        PageUtils<SettlementReconcileCompanyProductListOut> page = settlementReconcileCompanyProductService.findSettlementReconcileCompanyProductList(input);
        return Result.success(page);
    }


    /**
     * 获取保司产品映射详情
     *
     * @param id 主键ID
     * @return
     */
    @GetMapping("info/{id}")
    @RequiresPermissions(value = {"settlement:product:all"})
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<SettlementReconcileCompanyProductInfoOut> info(@PathVariable(value = "id", required = false)
                                                                 @NotNull(message = "ID不能为空")
                                                                 @ApiParam(value = "ID") Integer id) {
        SettlementReconcileCompanyProductInfoOut info = settlementReconcileCompanyProductService.findSettlementReconcileCompanyProductInfo(id);
        return Result.success(info);
    }

    /**
     * 新增保司产品映射信息
     *
     * @param input 请求参数
     * @return
     */
    @SysLog("新增保司产品映射信息")
    @PostMapping("save")
    @RequiresPermissions(value = {"settlement:product:all"})
    @ApiOperation(value = "新增信息", notes = "新增信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid SettlementReconcileCompanyProductSaveInput input) {
        settlementReconcileCompanyProductService.saveSettlementReconcileCompanyProduct(input);
        return Result.success();
    }

    /**
     * 修改保司产品映射信息
     *
     * @param input 请求参数
     * @return
     */
    @SysLog("修改保司产品映射信息")
    @PostMapping("update")
    @RequiresPermissions(value = {"settlement:product:all"})
    @ApiOperation(value = "修改信息", notes = "修改保司产品映射信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid SettlementReconcileCompanyProductUpdateInput input) {
        settlementReconcileCompanyProductService.updateSettlementReconcileCompanyProduct(input);
        return Result.success();
    }


    @SysLog("删除保司产品映射信息")
    @PostMapping("delete/{id}")
    @RequiresPermissions(value = {"settlement:product:all"})
    @ApiOperation(value = "删除保司产品映射信息", notes = "删除保司产品映射信息", httpMethod = "POST")
    public Result delete(
            @PathVariable(required = false, value = "id")
            @NotNull(message = "保司产品映射ID不能为空")
            @ApiParam(value = "保司产品映射ID") Integer id) {
        settlementReconcileCompanyProductService.removeById(id);
        return Result.success();
    }

}
