package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ClassName: AgentApplyPageListOut
 * Description: 代理人入职申请分页返回
 * date: 2022/11/28 14:26
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyPageListOut implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(value = "代理人工号")
    private String agentCode;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "代理人组织编码")
    private String orgCode;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "代理人姓名/被增员人姓名")
    private String agentName;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "代理人邮箱")
    private String email;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "代理人联系电话")
    private String mobile;
    /**
     * 证件号
     */
    @ApiModelProperty(value = "代理人身份证号码/被增员人身份证号码")
    private String idCard;
    /**
     * 职位
     */
    @ApiModelProperty(value = "代理人职位/被增员人类型")
    private String agentType;
    @ApiModelProperty(value = "代理人职位名称/被增员人类型名称")
    private String agentTypeName;
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String businessCode;
    /**
     * 增员人编码
     */
    @ApiModelProperty(value = "增员人业务编码")
    private String recruitBusinessCode;
    /**
     * 增员人名称
     */
    @ApiModelProperty(value = "增员人名称")
    private String recruitName;

    /**
     * 申请状态(1:已提交 2:完成面试 3:待完成考试 4:待线上签约 5:待审核 6:待复核 7:待修改信息 8:已完成)
     */
    @ApiModelProperty(value = "申请状态(1:已提交 2:完成面试 3:待完成考试 4:待线上签约 5:待审核 6:待复核 7:待修改信息 8:已完成)")
    private Integer status;
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "驳回原因")
    private String reason;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 已签署合同url
     */
    @ApiModelProperty(value = "已签署合同url")
    private String agreementUrl;

    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁/版本号")
    private long revision;

    @ApiModelProperty(value = "答题分数")
    private String score;

    /**
     * 互联网告知书签署状态(0:未签约 1:入职签约 2:单独签约)
     */
    @ApiModelProperty(value = "互联网告知书签署状态(0:未签约 1:入职签约 2:单独签约)")
    private Integer marketingSignStatus;

    @ApiModelProperty(value = "互联网告知书url")
    private String marketingUrl;
}
