package com.mpolicy.manage.modules.agentApply.vo;

import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentOnlineFileInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentApplyInfoOut
 * Description: 代理人申请信息详情
 * date: 2022/11/29 10:56
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyInfoOut implements Serializable {

    @ApiModelProperty("代理人编码")
    private String agentCode;
    @ApiModelProperty("附件列表")
    private List<AgentApplyAttachmentOut> accessoryList;
    @ApiModelProperty(value = "代理人基本信息")
    private AgentApplyBasicInfoOut agentUserInfo;
    @ApiModelProperty(value = "代理人扩展信息", required = true)
    private AgentApplyExtendInfoOut agentExtend;
    @ApiModelProperty(value = "代理人线上入职信息")
    private AgentOnlineFileInfoVo agentOnlineFileInfo;
    @ApiModelProperty(value = "代理人工作室信息")
    private AgentOnlineStudioIpVo agentOnlineStudioIpVo;
}
