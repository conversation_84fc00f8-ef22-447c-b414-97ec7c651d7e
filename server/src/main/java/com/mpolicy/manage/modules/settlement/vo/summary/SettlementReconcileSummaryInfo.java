package com.mpolicy.manage.modules.settlement.vo.summary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算汇总单列表信息
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "结算汇总单列表信息", description = "结算汇总单列表信息")
public class SettlementReconcileSummaryInfo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 结算汇总编号
     */
    @ApiModelProperty(value = "结算汇总编号", example = "JSHZ-023-052")
    private String summaryCode;
    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023-05")
    private String reconcileMonth;
    /**
     * 内部签署方名称
     */
    @ApiModelProperty(value = "内部签署方名称", example = "内部签署方")
    private String innerSignatoryName;
    /**
     * 外部签署方名称
     */
    @ApiModelProperty(value = "外部签署方名称", example = "外部签署方")
    private String externalSignatoryName;
    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "保险公司")
    private String companyName;
    /**
     * 科目名称集合;科目名称逗号分割
     */
    @ApiModelProperty(value = "科目名称集合", example = "科目名称")
    private String summarySubjectNames;

    /**
     * 汇总图片
     */
    @ApiModelProperty(value = "汇总图片", example = "汇总图片")
    private String summaryPicUrl;
    /**
     * 汇总xls
     */
    @ApiModelProperty(value = "汇总xls", example = "汇总xls")
    private String summaryXlsUrl;
    /**
     * 生成时间
     */
    @ApiModelProperty(value = "生成时间", example = "2023-05-12 12:12:12")
    private String createTime;
}
