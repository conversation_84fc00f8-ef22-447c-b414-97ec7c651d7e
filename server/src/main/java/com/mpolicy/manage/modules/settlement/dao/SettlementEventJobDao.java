package com.mpolicy.manage.modules.settlement.dao;

import com.mpolicy.manage.modules.settlement.entity.SettlementEventJobEntity;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListOut;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 结算交互事件受理表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-05 14:34:54
 */
public interface SettlementEventJobDao extends BaseMapper<SettlementEventJobEntity> {


    /**
     * 获取结算交互事件受理表列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<SettlementEventJobListOut> findSettlementEventJobList(@Param("page") Page<SettlementEventJobListOut> page, @Param("input") SettlementEventJobListInput input);

}
