package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ClassName: AgentApplyInfoOut
 * Description: 代理人考试信息答案信息详情
 * date: 2022/11/29 10:56
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyAnswerOut implements Serializable {

    /**
     * 答案
     */
    @ApiModelProperty("答案")
    @NotBlank(message = "答案不能为空")
    private String answer;
    /**
     * 是否正确(0:不正确 1:正确)
     */
    @ApiModelProperty("是否正确(0:不正确 1:正确)")
    @NotNull(message = "是否正确不能为空")
    @Min(value = 0,message = "是否正确传值不正确")
    @Max(value = 1,message = "是否正确传值不正确")
    private Integer isRight;
}
