package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInvoiceMailboxDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInvoiceMailboxEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileInvoiceMailboxService;
import org.springframework.stereotype.Service;

@Service
public class SettlementReconcileInvoiceMailboxServiceImpl extends ServiceImpl<SettlementReconcileInvoiceMailboxDao, SettlementReconcileInvoiceMailboxEntity> implements SettlementReconcileInvoiceMailboxService {
}
