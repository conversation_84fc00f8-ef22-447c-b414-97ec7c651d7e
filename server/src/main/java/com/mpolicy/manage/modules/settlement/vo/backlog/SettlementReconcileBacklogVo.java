package com.mpolicy.manage.modules.settlement.vo.backlog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 保司结算对账单差异详情
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "保司结算对账单差异详情", description = "保司结算对账单差异详情")
public class SettlementReconcileBacklogVo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 差异受理编号
     */
    @ApiModelProperty(value = "差异受理编号")
    @NotBlank(message = "差异受理编号不能为空")
    private String backlogCode;
    /**
     * 差异类型名称
     */
    @ApiModelProperty(value = "差异处理说明", example = "已经补单")
    @NotBlank(message = "差异处理说明不能为空")
    private String diffDesc;

}
