package com.mpolicy.manage.modules.settlement.vo.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/10/9 16:48
 * @Version 1.0
 */
@Data
public class InvoiceReturnDetail {

    @ApiModelProperty(value = "对账单号", example = "RE23isLD")
    private String reconcileCode;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "泰康保险公司")
    private String companyName;

    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023年06期")
    private String reconcileMonth;

    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    @ApiModelProperty(value = "发票编码")
    private String invoiceCode;

    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceMoney;

}
