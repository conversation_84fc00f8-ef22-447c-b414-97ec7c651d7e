package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsPolicyDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryLongPromotion;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryLongReissuePromotion;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/1/31 16:57
 * @Version 1.0
 */
@Service
public class LongReissuePromotionPolicyDimensionServiceImpl extends AbsPolicyDimensionService<List<DetailSummaryLongReissuePromotion>> {
    @Override
    public List<DetailSummaryLongReissuePromotion> querySummary(PageSettlementDetailParams p) {
        DetailSummaryLongReissuePromotion summaryLongReissuePromotion = costAutoInfoDao.longReissuePromotionSummary(p);

        DetailSummaryLongReissuePromotion longPromotion = null;
        if (Objects.nonNull(summaryLongReissuePromotion)) {
            summaryLongReissuePromotion.setMonth(calMonthBeforeBeforeLastMonth(p) + "（补发）");

            //查询上个周期的暂发
            PageSettlementDetailParams lastCycleParams = JSONObject.parseObject(JSONObject.toJSONString(p), PageSettlementDetailParams.class);
            lastCycleParams.setSettlementMonth(calLastMonthCycle(p));
            DetailSummaryLongPromotion result = costAutoInfoDao.longPromotionSummary(lastCycleParams);

            longPromotion = new DetailSummaryLongReissuePromotion();
            longPromotion.setLongReissuePromotion(result.getPromotion());
            longPromotion.setMonth(calMonthBeforeBeforeLastMonth(p) + "（暂发）");
            longPromotion.setCostAmount(result.getCostAmount());
            longPromotion.setCostRate(result.getCostRate());
            longPromotion.setLongRenewalRate(result.getLongRenewalRate());

        }
        DetailSummaryLongReissuePromotion totalSummary = new DetailSummaryLongReissuePromotion();
        totalSummary.setMonth(calMonthBeforeBeforeLastMonth(p));
        Optional<DetailSummaryLongReissuePromotion> summaryLongReissuePromotionOpt = Optional.ofNullable(summaryLongReissuePromotion);
        Optional<DetailSummaryLongReissuePromotion> longPromotionOpt = Optional.ofNullable(longPromotion);

        totalSummary.setLongReissuePromotion(
                summaryLongReissuePromotionOpt.map(DetailSummaryLongReissuePromotion::getLongReissuePromotion).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummaryLongReissuePromotion::getLongReissuePromotion).orElse(BigDecimal.ZERO))
        );

        totalSummary.setCostAmount(
                summaryLongReissuePromotionOpt.map(DetailSummaryLongReissuePromotion::getCostAmount).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummaryLongReissuePromotion::getCostAmount).orElse(BigDecimal.ZERO))
        );

        totalSummary.setLongRenewalRate(
                summaryLongReissuePromotionOpt.map(DetailSummaryLongReissuePromotion::getCostAmount).orElse(BigDecimal.ZERO)
        );
        totalSummary.setCostRate(
                summaryLongReissuePromotionOpt.map(DetailSummaryLongReissuePromotion::getCostRate).orElse(BigDecimal.ZERO)
                        .add(longPromotionOpt.map(DetailSummaryLongReissuePromotion::getCostRate).orElse(BigDecimal.ZERO))
        );

        return Lists.newArrayList(totalSummary, longPromotion, summaryLongReissuePromotion);


    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.LONG_REISSUE_PROMOTION);
    }

}
