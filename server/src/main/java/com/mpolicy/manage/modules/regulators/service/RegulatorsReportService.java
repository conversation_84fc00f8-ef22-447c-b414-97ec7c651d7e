package com.mpolicy.manage.modules.regulators.service;

import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;

/**
 * 监管报备机构-报告服务接口
 *
 * <AUTHOR>
 * @date 2022-01-23 14:34:19
 */
public interface RegulatorsReportService<T> {

    /**
     * <p>
     * 机构报备上传处理
     * </p>
     *
     * @param t t
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/20
     */
    String uploadOrgRegulatorsReport(T t);

    /**
     * <p>
     * 机构报备合法文件校验
     * </p>
     *
     * @param fileCode fileCode
     * @param insertDataRedis 通过后是否数据入库redis, 默认为不校验 key={fileCode}
     * @return boolean
     * <AUTHOR>
     * @since 2022/1/20
     */
    boolean checkRegulatorsReportFile(String fileCode, Boolean insertDataRedis);

    /**
     * <p>
     * 机构报备合法文件校验
     * </p>
     *
     * @param document 文档对象
     * @param insertDataRedis insertDataRedis
     * @return boolean
     * <AUTHOR>
     * @since 2022/1/20
     */
    boolean checkRegulatorsReportFile(SysDocumentEntity document, Boolean insertDataRedis);

    /**
     * <p>
     * 获取监管报备的报告类型
     * </p>
     *
     * @return com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum
     * <AUTHOR>
     * @since 2022/1/20
     */
    RegulatorsReportTypeEnum getRegulatorsReportType();
}

