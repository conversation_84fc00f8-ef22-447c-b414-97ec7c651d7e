package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ClassName: AgentApplyFinishInterview
 * Description: 代理人复审驳回信息
 * date: 2022/11/30 12:30
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyRejectVO implements Serializable {

    @ApiModelProperty(value = "代理人编码",required = true)
    @NotBlank(message = "代理人编码不能为空")
    private String agentCode;

    /**
     * 驳回原因
     */
    @ApiModelProperty(value = "驳回原因",required = true)
    @NotBlank(message = "驳回原因不能为空")
    private String reason;
}
