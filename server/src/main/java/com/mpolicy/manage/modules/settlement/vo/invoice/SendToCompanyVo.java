package com.mpolicy.manage.modules.settlement.vo.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class SendToCompanyVo implements Serializable {
    private static final long serialVersionUID = -964642693629964408L;

    @NotBlank(message = "发票编码不能为空")
    @ApiModelProperty(value = "发票编码")
    private String invoiceCode;

    /**
     * 邮箱主题
     */
    @NotBlank(message = "邮箱主题不能为空")
    private String mailSubject;

    /**
     * 邮件内容
     */
    @NotBlank(message = "邮箱内容不能为空")
    private String mailContent;

    /**
     * 邮件内容
     */
    @NotBlank(message = "邮箱地址不能为空")
    private String mailAddress;


    /**
     * 邮件附件
     */
    private List<InvoiceEnclosureItem> mailEnclosureList;
}
