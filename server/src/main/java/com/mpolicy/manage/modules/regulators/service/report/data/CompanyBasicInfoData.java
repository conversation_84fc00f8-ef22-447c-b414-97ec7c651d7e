package com.mpolicy.manage.modules.regulators.service.report.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 基本情况表报告数据
 *
 * <AUTHOR>
 * @date 2022-01-21 13:26
 */
@Data
@ApiModel(value = "基本情况表报告数据")
public class CompanyBasicInfoData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "情况项目",example = "公司现有人数")
    private String basicName;

    @ApiModelProperty(value = "行数",example = "1")
    private String lineNumber;

    @ApiModelProperty(value = "12.56",example = "1")
    private String numberDesc;
}
