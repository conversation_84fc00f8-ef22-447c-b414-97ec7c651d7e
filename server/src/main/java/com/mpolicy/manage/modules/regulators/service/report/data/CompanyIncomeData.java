package com.mpolicy.manage.modules.regulators.service.report.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 利润表报告数据
 *
 * <AUTHOR>
 * @date 2022-01-21 13:26
 */
@Data
@ApiModel(value = "利润表报告数据")
public class CompanyIncomeData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资产项目",example = "主要业务收入")
    private String incomeProject;

    @ApiModelProperty(value = "资产行次",example = "1")
    private String lineNumber;

    @ApiModelProperty(value = "资产父节点行次",example = "1")
    private String parentLineNumber;

    @ApiModelProperty(value = "本月数",example = "10")
    private String monthNumber;

    @ApiModelProperty(value = "本年累计",example = "100")
    private String yearNumber;
}
