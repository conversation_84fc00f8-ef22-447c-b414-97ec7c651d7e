package com.mpolicy.manage.modules.settlement.vo.manage;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司对账单差异列表管理
 *
 * <AUTHOR>
 * @since 2023-05-25 01:32
 */
@Data
@ApiModel(value = "保司对账单差异列表管理", description = "保司对账单差异列表管理")
public class SettlementReconcileDiff extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    @ExcelProperty(value = "对账唯一单号")
    private String reconcileCode;

    /**
     * 对账单名称
     */
    @ApiModelProperty(value = "对账单名称", example = "众安2023020203")
    @ExcelProperty(value = "对账单名称")
    private String reconcileName;

    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023年06期")
    @ExcelProperty(value = "对账月度")
    private String reconcileMonth;

    /**
     * 差异受理编号
     */
    @ApiModelProperty(value = "差异受理单号", example = "RC2023052302020200")
    @ExcelProperty(value = "差异受理单号")
    private String backlogCode;

    /**
     * 汇总对账纪录编号
     */
    @ApiModelProperty(value = "对账员明细纪录编号", example = "RC2023052302020200")
    @ExcelProperty(value = "对账员明细纪录编号")
    private String billCode;

    /**
     * 系统差异类型
     */
    @ApiModelProperty(value = "系统差异类型", example = "SETTLEMENT:RECONCILE:DIFFTYPE:1")
    private String diffType;

    /**
     * 差异类型名称
     */
    @ApiModelProperty(value = "差异类型名称", example = "保费不一致")
    @ExcelProperty(value = "差异类型名称")
    private String diffName;
    /**
     * 平账原因
     */
    private String diffWhyDesc;

    /**
     * 保险公司编码
     */
    @ApiModelProperty(value = "保险公司编码", example = "TK2020202020")
    private String companyCode;

    /**
     * 投保时间
     */
    @ExcelProperty(value = "投保时间")
    private Date applicantTime;
    /**
     * 交单时间
     */
    @ExcelProperty(value = "交单时间")
    private Date orderTime;
    /**
     * 承保时间
     */
    @ExcelProperty(value = "承保时间")
    private Date approvedTime;
    /**
     * 生效时间
     */
    @ExcelProperty(value = "生效时间")
    private Date enforceTime;

    @ExcelProperty(value = "投保人姓名")
    private String applicantName;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "泰康保险有限公司")
    @ExcelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 差异保单编号
     */
    @ApiModelProperty(value = "差异保单编号", example = "TK2020202")
    @ExcelProperty(value = "差异保单编号")
    private String policyNo;

    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号", example = "TK2020202")
    @ExcelProperty(value = "批单号")
    private String endorsementNo;

    /**
     * 保全生效时间
     */
    @ApiModelProperty(value = "保全生效时间", example = "2023-05-23 11:11:11")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "保全生效时间")
    private Date preservationEffectTime;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码", example = "TK2020202")
    private String reconcileSubjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "对账科目名称", example = "首期年佣金")
    @ExcelProperty(value = "对账科目名称")
    private String reconcileSubjectName;

    /**
     * 险种编码
     */
    @ApiModelProperty(value = "险种编码", example = "XT202020202")
    @ExcelProperty(value = "险种编码")
    private String productCode;

    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称", example = "泰康百万医疗重疾")
    @ExcelProperty(value = "险种名称")
    private String productName;

    /**
     * 协议产品编码
     */
    @ApiModelProperty(value = "协议产品编码", example = "XT2020")
    @ExcelProperty(value = "协议产品编码")
    private String protocolProductCode;

    /**
     * 协议产品名称
     */
    @ApiModelProperty(value = "协议产品名称", example = "泰康百万医疗重疾2020款")
    @ExcelProperty(value = "协议产品名称")
    private String protocolProductName;

    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码", example = "PLANCODE20202")
    @ExcelProperty(value = "计划编码")
    private String planCode;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称", example = "豪华版")
    @ExcelProperty(value = "计划名称")
    private String planName;

    /**
     * 小鲸手续费金额
     */
    @ApiModelProperty(value = "小鲸保费金额", example = "1222.12")
    @ExcelProperty(value = "小鲸保费金额")
    private BigDecimal premium;

    @ApiModelProperty(value = "小鲸手续费比例", example = "1222.12")
    @ExcelProperty(value = "小鲸手续费比例")
    private String xiaowhaleSettlementRate;

    @ApiModelProperty(value = "小鲸手续费金额", example = "1222.12")
    @ExcelProperty(value = "小鲸手续费金额")
    private BigDecimal xiaowhaleAmount;
    /**
     * 结算比例
     */
    @ExcelProperty(value = "结算方式")
    private String settlementRateMethod;
    /**
     * 结算金额
     */
    @ExcelProperty(value = "税率")
    private String settlementRateTax;

    /**
     * 保司结算费率
     */
    private String companySettlementRate;

    /**
     * 保司保费
     */
    private BigDecimal companyPremium;

    /**
     * 保司手续费金额
     */
    @ApiModelProperty(value = "保司手续费金额", example = "1222.12")
    @ExcelProperty(value = "保司手续费金额")
    private BigDecimal companyAmount;

    /**
     * 是否自动冲正
     */
    @ApiModelProperty(value = "是否自动冲正", example = "1")
    private Integer autoCorrect;

    /**
     * 受理用户编号
     */
    @ApiModelProperty(value = "受理用户编号", example = "U102020020")
    @ExcelProperty(value = "受理用户编号")
    private String acceptUserCode;

    /**
     * 受理用户名称
     */
    @ApiModelProperty(value = "受理用户名称", example = "张无忌")
    @ExcelProperty(value = "受理用户名称")
    private String acceptUserName;

    /**
     * 操作人员
     */
    private String diffOpeUserName;

    /**
     * 差异处理状态0待处理1已处理
     */
    @ApiModelProperty(value = "差异处理状态0待处理1已处理", example = "1")
    private Integer diffStatus;

    @ApiModelProperty(value = "标记状态0:已标记 1:未标记", example = "1")
    private Integer markStatus;

    /**
     * 差异处理状态
     */
    @ApiModelProperty(value = "差异处理状态", example = "已处理")
    @ExcelProperty(value = "差异处理状态")
    private String diffStatusDesc;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额", example = "11")
    @ExcelProperty(value = "手续费差额")
    private BigDecimal diffAmount;

    /**
     * 差异处理说明
     */
    private String diffDesc;

    /**
     * 后续待办处理状态0待处理1处理完成2无需处理
     */
    @ApiModelProperty(value = "后续待办处理状态", example = "已处理")
    @ExcelProperty(value = "后续待办处理状态")
    private String followStatus;

    /**
     * 差异处理结果
     */
    private String diffResult;

    /**
     * 差异处理完成时间
     */
    @ApiModelProperty(value = "差异处理完成时间", example = "2023-05-23 11:11:11")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "差异处理完成时间")
    private Date diffFinishTime;

    /**
     * 是否启用;0未启用1启用
     */
    private Integer backlogEnable;
    /**
     * 更新时间
     */
    private Date updateTime;


}