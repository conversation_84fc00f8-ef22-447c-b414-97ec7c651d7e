package com.mpolicy.manage.modules.settlement.vo.policy;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保司对账单列表信息
 *
 * <AUTHOR>
 * @since 2023-05-25 16:02
 */
@Data
@ApiModel(value = "保司对账单列表信息", description = "保司对账单列表信息")
public class SettlementReconcilePolicyInfo extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 单据明细唯一编号
     */
    @ApiModelProperty(value = "单据明细唯一编号", example = "DJ2023052302020200")
    @ExcelProperty(value = "单据编号")
    private String settlementCode;

    @ExcelProperty(value = "对账单编码")
    private String reconcileCode;
    /**
     * 记账日期
     */
    @ApiModelProperty(value = "记账日期", example = "2022/10/10 10:10")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "记账时间")
    private Date settlementDate;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "wert567890-lkjh")
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 续期期数
     */
    @ApiModelProperty(value = "投保/续期期数", example = "wert567890-lkjh")
    @ExcelProperty(value = "投保期数")
    private Integer renewalPeriod;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称", example = "中华个人意外")
    @ExcelProperty(value = "产品名称")
    private String commodityName;

    /**
     * 保费
     */
    @ApiModelProperty(value = "保费", example = "120.00")
    @ExcelProperty(value = "保费")
    private BigDecimal premium;
    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态", example = "已承保")
    @ExcelProperty(value = "保单状态")
    private String policyStatus;
    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名", example = "张三")
    @ExcelProperty(value = "投保人")
    private String applicantName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名", example = "李四")
    @ExcelProperty(value = "被保人")
    private String insuredName;
    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "太平洋")
    @ExcelProperty(value = "保司")
    private String companyName;

    /**
     * 产品项目分类;个团车财
     */
    @ApiModelProperty(value = "产品项目分类", example = "财险")
    @ExcelProperty(value = "产品项目分类")
    private String policyProductType;
    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称", example = "A计划")
    @ExcelProperty(value = "计划名称")
    private String planName;
    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码", example = "AHJ267899")
    @ExcelProperty(value = "计划编码")
    private String planCode;
    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称", example = "意外险种")
    @ExcelProperty(value = "险种名称")
    private String productName;
    /**
     * 险种编码
     */
    @ApiModelProperty(value = "险种编码", example = "AYX09876543")
    @ExcelProperty(value = "险种编码")
    private String productCode;
    /**
     * 险种类型
     */
    @ApiModelProperty(value = "险种类型", example = "险种类型")
    private String productType;

    @ApiModelProperty(value = "险种类型", example = "险种类型")
    @ExcelProperty(value = "险种类型")
    private String productTypeDesc;

    @ApiModelProperty(value = "险种渠道", example = "险种渠道")
    private String productChannel;

    @ExcelProperty(value = "险种渠道")
    private String productChannelName;
    /**
     * 保障期间类型
     */
    @ApiModelProperty(value = "保障期限", example = "终身")
    @ExcelProperty(value = "保障期限")
    private String insuredPeriodType;
    /**
     * 缴费方式;年交/半年交/季交/月交/趸交/不定期交/短险一次交清
     */
    @ApiModelProperty(value = "缴费方式", example = "短险一次交清")
    @ExcelProperty(value = "缴费方式")
    private String periodType;
    /**
     * 缴费期间类型
     */
    @ApiModelProperty(value = "缴费期限", example = "十年")
    @ExcelProperty(value = "缴费期限")
    private String paymentPeriodType;
    //险种状态
    /**
     * 承保时间
     */
    @ApiModelProperty(value = "承保时间", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "承保时间")
    private Date approvedTime;
    /**
     * 交单时间
     */
    @ApiModelProperty(value = "交单时间", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "交单时间")
    private Date orderTime;
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "起保时间", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "起保时间")
    private Date enforceTime;
    //终保时间
    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号", example = "XJXH12345678")
    @ExcelProperty(value = "批单号")
    private String endorsementNo;
    /**
     * 保全生效时间
     */
    private Date preservationEffectTime;
    /**
     * 批改时间
     */
    @ApiModelProperty(value = "批改时间", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "批改时间")
    private Date endorsementTime;
    //批改类型
    //保险标的
    /**
     * 销售渠道名称
     */
    @ApiModelProperty(value = "销售渠道名称", example = "XJXH")
    @ExcelProperty(value = "销售渠道")
    private String channelName;
    //区域
    /**
     * 渠道推荐人姓名
     */
    @ApiModelProperty(value = "渠道推荐人", example = "李四")
    @ExcelProperty(value = "渠道推荐人")
    private String referrerName;
    /**
     * 主代理人编码
     */
    @ApiModelProperty(value = "代理人", example = "王五")
    @ExcelProperty(value = "代理人")
    private String mainAgentName;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "H230305049412220149554")
    @ExcelProperty(value = "订单号")
    private String serialNumber;
    /**
     * 协议编码
     */
    @ApiModelProperty(value = "协议编码", example = "XY230305049412220149554")
    @ExcelProperty(value = "协议编码")
    private String protocolCode;
    //内部签署方
    /**
     * 回访时间
     */
    @ApiModelProperty(value = "回访时间", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "回访时间")
    private Date revisitTime;
    /**
     * 回访状态 是否需要录入回访 1需要;0不需要
     */
    @ApiModelProperty(value = "回访情况", example = "完成")
    @ExcelProperty(value = "回访情况")
    private String revisitStatus;
    /**
     * 退保时间
     */
    @ApiModelProperty(value = "退保时间", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "退保时间")
    private Date surrenderTime;

    @ApiModelProperty(value = "实收日期", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @ApiModelProperty(value = "实收日期", example = "2023/1/1 12:12:12")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date realityTime;
    /**
     * 退保金额
     */
    @ApiModelProperty(value = "退保金额", example = "123.11")
    @ExcelProperty(value = "退保金额")
    private BigDecimal surrenderAmount;
    /**
     * 是否犹豫期退保;-1无0否1是
     */
    @ApiModelProperty(value = "是否犹豫期退保", example = "是")
    @ExcelProperty(value = "是否犹豫期退保")
    private String hesitateSurrender;
    /**
     * 记账事件编码;新单、续期、保全、费率调整、其他
     */
    @ApiModelProperty(value = "记账事件编码;新单、续期、保全、费率调整、其他", example = "新单")
    @ExcelProperty(value = "新单/续期")
    private String settlementEventCode;
    //续期缴费时间
    //续期期间
    /**
     * 科目名称
     */
    @ApiModelProperty(value = "结算科目", example = "首期佣金")
    @ExcelProperty(value = "结算科目")
    private String settlementSubjectName;
    /**
     * 结算比例
     */
    @ApiModelProperty(value = "结算比例", example = "12")
    @ExcelProperty(value = "结算比例")
    private String settlementRate;
    /**
     * 结算金额
     */
    @ApiModelProperty(value = "结算金额", example = "12.00")
    @ExcelProperty(value = "结算金额")
    private BigDecimal settlementAmount;
    /**
     * 结算月份
     */
    @ApiModelProperty(value = "结算月份", example = "2023-05")
    @ExcelProperty(value = "结算月份")
    private String settlementMonth;
    /**
     * 对账状态;0未对账1对账中2已完成对账
     */
    /**
     * 对账状态;0未对账1对账中2已完成对账
     */
    private Integer reconcileStatus;
    @ApiModelProperty(value = "对账状态;0未对账1对账中2已完成对账", example = "已完成对账")
    @ExcelProperty(value = "对账状态")
    private String reconcileStatusDesc;

    @ApiModelProperty(value = "对账状态;0未对账1对账中2已完成对账", example = "已完成对账")
    private Integer reconcileExecuteStatus;

    @ApiModelProperty(value = "对账状态;0未对账1对账中2已完成对账", example = "已完成对账")
    private String reconcileExecuteDesc;

    /**
     * 对账完成时间
     */
    @ApiModelProperty(value = "对账时间", example = "2023/1/1")
    @JSONField(format = "yyyy-MM-dd")
    @ExcelProperty(value = "对账时间")
    private Date reconcileTime;

    @ExcelProperty(value = "延期月份")
    private String postponedMonth;
    /**
     * 对账操作员
     */
    @ApiModelProperty(value = "对账操作人", example = "陈六")
    @ExcelProperty(value = "对账操作人")
    private String reconcileUser;
    /**
     * 调整业务类型
     */
    @ApiModelProperty(value = "业务数据类型", example = "已完成对账")
    @ExcelProperty(value = "业务数据类型")
    private String sourceSettlementType;
    /**
     * 原单据明细编号
     */
    @ApiModelProperty(value = "原单据明细编号", example = "QRR234567654")
    @ExcelProperty(value = "原单据明细编号")
    private String sourceSettlementCode;
    /**
     * 调整业务说明;同步保单保全信息（修改保费）
     */
    @ApiModelProperty(value = "调整说明", example = "修改保费")
    @ExcelProperty(value = "调整说明")
    private String sourceSettlementDesc;

    private Integer regulatoryReportType;

}
