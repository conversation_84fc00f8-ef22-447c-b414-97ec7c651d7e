package com.mpolicy.manage.modules.settlement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.CompanyTypeEnum;
import com.mpolicy.manage.modules.contract.entity.ContractBasicInfoEntity;
import com.mpolicy.manage.modules.contract.entity.ContractInnerSignatoryEntity;
import com.mpolicy.manage.modules.contract.service.ContractBasicInfoService;
import com.mpolicy.manage.modules.contract.service.ContractInnerSignatoryService;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyPersonEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyPersonService;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileContractInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileContractInfoEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileCompanySubjectService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileContractInfoService;
import com.mpolicy.manage.modules.settlement.vo.settlement.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service("settlementReconcileContractInfoService")
public class SettlementReconcileContractInfoServiceImpl extends ServiceImpl<SettlementReconcileContractInfoDao, SettlementReconcileContractInfoEntity> implements SettlementReconcileContractInfoService {

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private ContractInnerSignatoryService contractInnerSignatoryService;

    @Autowired
    private InsuranceCompanyPersonService insuranceCompanyPersonService;

    @Autowired
    private SettlementReconcileCompanySubjectService settlementReconcileCompanySubjectService;

    @Autowired
    private ContractBasicInfoService contractBasicInfoService;


    /**
     * 获取结算合约配置表列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<SettlementReconcileContractInfoListOut> findSettlementReconcileContractInfoList(SettlementReconcileContractInfoListInput input) {
        if (StrUtil.isNotBlank(input.getSubjectRuleCode())) {
            SettlementReconcileCompanySubjectEntity settlementReconcileCompanySubject = settlementReconcileCompanySubjectService.lambdaQuery().eq(SettlementReconcileCompanySubjectEntity::getSubjectRuleCode, input.getSubjectRuleCode()).one();
            if (settlementReconcileCompanySubject != null) {
                input.setSubjectReconcileCompanyCode(settlementReconcileCompanySubject.getReconcileCompanyCode());
            } else {
                input.setSubjectReconcileCompanyCode("-");
            }
        }
        IPage<SettlementReconcileContractInfoListOut> page = baseMapper.findSettlementReconcileContractInfoList(new Page<SettlementReconcileContractInfoListOut>(input.getPage(), input.getLimit()), input);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<String> reconcileCompanyCodeList = page.getRecords().stream().map(SettlementReconcileContractInfoListOut::getReconcileCompanyCode).collect(Collectors.toList());
            Map<String, Long> subjectMap = settlementReconcileCompanySubjectService.lambdaQuery()
                    .in(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, reconcileCompanyCodeList)
                    .list().stream().collect(Collectors.groupingBy(SettlementReconcileCompanySubjectEntity::getReconcileCompanyCode, Collectors.counting()));
            page.getRecords().forEach(action -> {
                //科目数量
                if (subjectMap.containsKey(action.getReconcileCompanyCode())) {
                    action.setSubjectRuleCount(subjectMap.get(action.getReconcileCompanyCode()).intValue());
                } else {
                    action.setSubjectRuleCount(0);
                }
                action.setInnerSignatoryTypeDesc("0".equals(action.getInnerSignatoryType()) ? "个人" : "企业");
                action.setExternalSignatoryTypeDesc(CompanyTypeEnum.deCode(action.getExternalSignatoryType()).getName());
            });
        }
        return new PageUtils(page);
    }

    /**
     * 获取结算合约配置表详情
     *
     * @param id
     * @return
     */
    @Override
    public SettlementReconcileContractInfoInfoOut findSettlementReconcileContractInfoById(Integer id) {
        SettlementReconcileContractInfoEntity settlementReconcileContractInfo = baseMapper.selectById(id);
        if (settlementReconcileContractInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在"));
        }
        SettlementReconcileContractInfoInfoOut out = new SettlementReconcileContractInfoInfoOut();
        BeanUtil.copyProperties(settlementReconcileContractInfo, out);
        if (CompanyTypeEnum.THAT_PERSON.getCode().equals(settlementReconcileContractInfo.getExternalSignatoryType())) {
            out.setExternalSignatoryCodeArr(CollUtil.newArrayList(settlementReconcileContractInfo.getExternalSignatoryCode()));
        } else {
            out.setExternalSignatoryCodeArr(insuranceCompanyService.getParentNode(settlementReconcileContractInfo.getExternalSignatoryCode()));
        }

        return out;
    }

    /**
     * 新增结算合约配置表数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSettlementReconcileContractInfo(SettlementReconcileContractInfoSaveInput input) {
        //判断一下是否存在合约信息
        Integer count = contractBasicInfoService.lambdaQuery()
                .eq(ContractBasicInfoEntity::getInnerSignatoryType, input.getInnerSignatoryType())
                .eq(ContractBasicInfoEntity::getInnerSignatoryCode, input.getInnerSignatoryCode())
                .eq(ContractBasicInfoEntity::getExternalSignatoryCode, input.getExternalSignatoryCode())
                .eq(ContractBasicInfoEntity::getExternalSignatoryType, input.getExternalSignatoryType())
                .count();
        if (count == null || count == 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("内外部签署方的合约信息不存在,请先创建合约"));
        }

        SettlementReconcileContractInfoEntity save = new SettlementReconcileContractInfoEntity();
        BeanUtil.copyProperties(input, save);
        save.setReconcileCompanyCode(CommonUtils.createCode("SR"));
        //内外部签署方姓名
        ContractInnerSignatoryEntity innerSignatory = Optional.ofNullable(contractInnerSignatoryService.lambdaQuery()
                .eq(ContractInnerSignatoryEntity::getInnerSignatoryCode, input.getInnerSignatoryCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("内部签署方信息不存在")));
        save.setInnerSignatoryName(innerSignatory.getInnerSignatoryName());

        // 外部签署方
        if (CompanyTypeEnum.THAT_PERSON.getCode().equals(input.getExternalSignatoryType())) {
            InsuranceCompanyPersonEntity insuranceCompanyPerson = Optional.ofNullable(insuranceCompanyPersonService.lambdaQuery().eq(InsuranceCompanyPersonEntity::getPersonCode, input.getExternalSignatoryCode()).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("外部签署方信息不存在")));
            save.setExternalSignatoryName(insuranceCompanyPerson.getUserName());
        } else {
            InsuranceCompanyEntity insuranceCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery().eq(InsuranceCompanyEntity::getCompanyCode, input.getExternalSignatoryCode()).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("外部签署方信息不存在")));
            save.setExternalSignatoryName(insuranceCompany.getCompanyName());
        }
        baseMapper.insert(save);
    }

    /**
     * 修改结算合约配置表数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSettlementReconcileContractInfoById(SettlementReconcileContractInfoUpdateInput input) {
        SettlementReconcileContractInfoEntity settlementReconcileContractInfo = baseMapper.selectById(input.getId());
        if (settlementReconcileContractInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("修改的信息不存在"));
        }
        // 如果变更了内外部签署方 需要校验一下是否签订合约
        if (!settlementReconcileContractInfo.getExternalSignatoryCode().equals(input.getExternalSignatoryCode())
                || !settlementReconcileContractInfo.getInnerSignatoryCode().equals(input.getInnerSignatoryCode())) {
            Integer count = contractBasicInfoService.lambdaQuery()
                    .eq(ContractBasicInfoEntity::getInnerSignatoryType, input.getInnerSignatoryType())
                    .eq(ContractBasicInfoEntity::getInnerSignatoryCode, input.getInnerSignatoryCode())
                    .eq(ContractBasicInfoEntity::getExternalSignatoryCode, input.getExternalSignatoryCode())
                    .eq(ContractBasicInfoEntity::getExternalSignatoryType, input.getExternalSignatoryType())
                    .count();
            if (count == null || count == 0) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("内外部签署方的合约信息不存在,请先创建合约"));
            }
        }
        SettlementReconcileContractInfoEntity update = new SettlementReconcileContractInfoEntity();
        BeanUtil.copyProperties(input, update);
        //内外部签署方姓名
        ContractInnerSignatoryEntity innerSignatory = Optional.ofNullable(contractInnerSignatoryService.lambdaQuery()
                .eq(ContractInnerSignatoryEntity::getInnerSignatoryCode, input.getInnerSignatoryCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("内部签署方信息不存在")));
        update.setInnerSignatoryName(innerSignatory.getInnerSignatoryName());

        // 外部签署方
        if (CompanyTypeEnum.THAT_PERSON.getCode().equals(input.getExternalSignatoryType())) {
            InsuranceCompanyPersonEntity insuranceCompanyPerson = Optional.ofNullable(insuranceCompanyPersonService.lambdaQuery().eq(InsuranceCompanyPersonEntity::getPersonCode, input.getExternalSignatoryCode()).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("外部签署方信息不存在")));
            update.setExternalSignatoryName(insuranceCompanyPerson.getUserName());
        } else {
            InsuranceCompanyEntity insuranceCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery().eq(InsuranceCompanyEntity::getCompanyCode, input.getExternalSignatoryCode()).one())
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("外部签署方信息不存在")));
            update.setExternalSignatoryName(insuranceCompany.getCompanyName());
        }
        baseMapper.updateById(update);
    }
}
