package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileSummaryInfoDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileSummaryInfoEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileSummaryInfoService;
import com.mpolicy.manage.modules.settlement.vo.summary.SettlementReconcileSummaryInfo;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 保司结算对账单汇总申请单
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileSummaryInfoService")
public class SettlementReconcileSummaryInfoServiceImpl extends ServiceImpl<SettlementReconcileSummaryInfoDao, SettlementReconcileSummaryInfoEntity> implements SettlementReconcileSummaryInfoService {


    @Override
    public PageUtils<SettlementReconcileSummaryInfo> querySettlementSummaryInfoServiceList(Map<String, Object> params) {

        //结算月份
        String reconcileMonth = RequestUtils.objectValueToString(params, "reconcileMonth");
        //内部签署方编码
        String innerSignatoryCode = RequestUtils.objectValueToString(params, "innerSignatoryCode");
        //外部签署方编码
        String externalSignatoryCode = RequestUtils.objectValueToString(params, "externalSignatoryCode");
        //保险公司编码
        String companyCode = RequestUtils.objectValueToString(params, "companyCode");
        IPage<SettlementReconcileSummaryInfoEntity> pageResult = this.page(
                new Query<SettlementReconcileSummaryInfoEntity>().getPage(params),
                new LambdaQueryWrapper<SettlementReconcileSummaryInfoEntity>()
                        .eq(StringUtils.isNotBlank(reconcileMonth), SettlementReconcileSummaryInfoEntity::getReconcileMonth, reconcileMonth)
                        .eq(StringUtils.isNotBlank(innerSignatoryCode), SettlementReconcileSummaryInfoEntity::getInnerSignatoryCode, innerSignatoryCode)
                        .eq(StringUtils.isNotBlank(externalSignatoryCode), SettlementReconcileSummaryInfoEntity::getExternalSignatoryCode, externalSignatoryCode)
                        .orderByDesc(SettlementReconcileSummaryInfoEntity::getCreateTime)
        );
        // 构建vo
        List<SettlementReconcileSummaryInfo> resultData = pageResult.getRecords().stream().map(x -> {
            SettlementReconcileSummaryInfo bean = new SettlementReconcileSummaryInfo();
            BeanUtils.copyProperties(x, bean);
            bean.setCreateTime(DateUtils.format(x.getCreateTime()));
            return bean;
        }).collect(Collectors.toList());

        // 重新构建分页返回对象
        return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(), (int) pageResult.getCurrent());

    }

    @Override
    public void deleteSettlementSummaryInfo(String summaryCode) {
        SettlementReconcileSummaryInfoEntity entity = Optional.ofNullable(this.lambdaQuery()
                .eq(SettlementReconcileSummaryInfoEntity::getSummaryCode, summaryCode)
                .last("limit 1")
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取汇总单失败")));
        this.removeById(entity);
    }
}
