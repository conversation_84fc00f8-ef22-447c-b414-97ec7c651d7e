package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsProductDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryPcoAllowance;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/2/4 00:21
 * @Version 1.0
 */

@Service
public class PcoAllowanceServiceImpl extends AbsProductDimensionService<List<DetailSummaryPcoAllowance>> {
    @Override
    public List<DetailSummaryPcoAllowance> querySummary(PageSettlementDetailParams p) {
        DetailSummaryPcoAllowance pcoAllowance = costAutoInfoDao.pcoAllowanceSummary(p);
        if (Objects.nonNull(pcoAllowance)) {
            pcoAllowance.setMonth(calLastMonth(p));
        }
        return Lists.newArrayList(pcoAllowance);
    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.PCO_ALLOWANCE);
    }
}
