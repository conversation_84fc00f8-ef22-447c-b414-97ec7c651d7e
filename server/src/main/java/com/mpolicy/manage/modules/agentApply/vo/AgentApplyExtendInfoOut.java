package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentApplyExtendInfoOut
 * Description:
 * date: 2022/11/30 11:12
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyExtendInfoOut implements Serializable {
    /**
     * 执业证编码
     */
    @ApiModelProperty(value = "执业证编码", required = true)
    @NotBlank(message = "执业证编码不能为空")
    private String certificateNum;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull(message = "执业证开始日期不能为空")
    private String startDate;
    /**
     * 是否长期有效 1:长期;0:非长期
     */
    @ApiModelProperty(value = "是否长期有效", required = true)
    private boolean longTerm;
    /**
     * 截至日期
     */
    @ApiModelProperty(value = "截至日期")
    private String endDate;
    /**
     * 银行所在省份代码
     */
    @ApiModelProperty(value = "银行所在省份代码")
    private String bandProvinceCode;
    /**
     * 银行所在地区代码
     */
    @ApiModelProperty(value = "银行所在地区代码", required = true)
    @NotBlank(message = "银行所在地区代码不能为空")
    private String bandLocationCode;
    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行", required = true)
    @NotBlank(message = "开户银行不能为空")
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]{1,30})$", message = "开户银行只能为汉字")
    private String bandName;
    /**
     * 开户支行名称
     */
    @ApiModelProperty(value = "开户支行名称", required = true)
    @NotBlank(message = "开户支行名称不能为空")
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]{1,30})$", message = "开户支行只能为汉字")
    private String bandBranchName;
    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号", required = true)
    @NotBlank(message = "银行卡号不能为空")
    @Pattern(regexp = "^[0-9]*$", message = "银行卡号只能为数字")
    private String bandCardNum;
    /**
     * 联行号
     */
    @ApiModelProperty(value = "联行号", required = true)
    @NotBlank(message = "联行号不能为空")
    @Pattern(regexp = "^[0-9]*$", message = "银行卡号只能为数字")
    private String bankNumber;

    @ApiModelProperty(value = "获得的荣誉", required = true)
    private List<String> honorList;

    @ApiModelProperty(value = "获得的荣誉", required = false, hidden = true)
    private String honor;

    @ApiModelProperty(value = "个人介绍")
    private String profile;
}
