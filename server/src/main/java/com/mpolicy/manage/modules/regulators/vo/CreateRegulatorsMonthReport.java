package com.mpolicy.manage.modules.regulators.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CreateRegulatorsMonthReport implements Serializable {
    private static final long serialVersionUID = 7525476632614451292L;


    @JSONField(format = "yyyy-MM")
    private Date beginMonth;


    @JSONField(format = "yyyy-MM")
    private Date endMonth;
}
