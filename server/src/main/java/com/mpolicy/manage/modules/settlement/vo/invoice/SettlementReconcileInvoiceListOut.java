package com.mpolicy.manage.modules.settlement.vo.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 对账单发票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-09 23:39:52
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileInvoiceListOut implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 发票编码
     */
    private String invoiceCode;
    /**
     * 开票类型
     */
    private Integer reconcileType;

    /**
     * 申请状态
     */
    private Integer applyStatus;
    private Integer invoiceStep;

    /**
     * 是否发送保司
     */
    private Integer pushCompanyStatus;

    /**
     * 红冲状态
     */
    private Integer redFlushStatus;
    /**
     * 方式 1:提前开票 0正常开票
     */
    private Integer invoiceMode;
    /**
     * 开票类型，1:普票，2:专票
     */
    private String invoiceType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 是否需要发送邮件
     */
    private String needMail;
    /**
     * 申请金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 开票组织
     */
    private String invoicingOrgName;

    /**
     * 关联对账单数量
     */
    private Long reconcileInvoiceMapNum;

    /**
     * 申请人
     */
    private String applyUserName;
    /**
     * 快递公司
     */
    private String expressName;
    /**
     * 快递状态
     */
    private Integer expressStatus;

    /**
     * 邮件发送状态
     */
    private Integer sendMailboxStatus;
    /**
     * 快递单号
     */
    private String expressWaybillNumber;

    /**
     * 申请时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;
}
