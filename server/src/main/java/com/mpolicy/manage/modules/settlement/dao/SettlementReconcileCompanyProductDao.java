package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyProductEntity;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileCompanyProductListInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileCompanyProductListOut;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 结算保司配置对应规则科目表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 14:29:16
 */
public interface SettlementReconcileCompanyProductDao extends ImsBaseMapper<SettlementReconcileCompanyProductEntity> {

    /**
     * 获取分页数据
     * @param page
     * @param input
     * @return
     */
    IPage<SettlementReconcileCompanyProductListOut> findSettlementReconcileCompanyProductList(@Param("page") Page<SettlementReconcileCompanyProductListOut> page,
                                                                                              @Param("input") SettlementReconcileCompanyProductListInput input);
}
