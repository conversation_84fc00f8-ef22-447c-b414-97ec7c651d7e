package com.mpolicy.manage.modules.settlement.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BillSubjectApplyOut implements Serializable {
    private static final long serialVersionUID = 6464246530571299846L;

    /**
     * 申请编码
     */
    private String applyCode;

    /**
     * 手续费结算编码
     */
    private String reconcileBillCode;

    /**
     * 结算科目编码
     */
    private String subjectCode;

    /**
     * 结算科目名称
     */
    private String subjectName;

    /**
     * 保司编码
     */
    private String companyCode;

    /**
     * 承保结束时间
     */
    private Date approvedEndTime;

    /**
     * 回执结束时间
     */
    private Date receiptEndTime;

    /**
     * 回访结束时间
     */
    private Date revisitEndTime;
}
