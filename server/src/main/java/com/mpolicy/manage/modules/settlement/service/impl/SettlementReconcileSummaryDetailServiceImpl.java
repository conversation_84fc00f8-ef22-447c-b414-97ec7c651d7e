package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileSummaryDetailDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileSummaryDetailEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileSummaryDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保司结算对账单汇总明细
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileSummaryDetailService")
public class SettlementReconcileSummaryDetailServiceImpl extends ServiceImpl<SettlementReconcileSummaryDetailDao, SettlementReconcileSummaryDetailEntity> implements SettlementReconcileSummaryDetailService {

}
