package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinAttachmentDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinAttachmentEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineJoinAttachmentService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("blAgentOnlineJoinAttachmentService")
public class BlAgentOnlineJoinAttachmentServiceImpl extends ServiceImpl<BlAgentOnlineJoinAttachmentDao, BlAgentOnlineJoinAttachmentEntity> implements BlAgentOnlineJoinAttachmentService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<BlAgentOnlineJoinAttachmentEntity> page = this.page(
                new Query<BlAgentOnlineJoinAttachmentEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
