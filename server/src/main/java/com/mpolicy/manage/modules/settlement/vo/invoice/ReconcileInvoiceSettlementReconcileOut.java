package com.mpolicy.manage.modules.settlement.vo.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReconcileInvoiceSettlementReconcileOut implements Serializable {
    private static final long serialVersionUID = -3578033633763145262L;

    /**
     * 对账唯一单号
     */
    private String reconcileCode;
    /**
     * 负责人
     */
    private String headName;
    /**
     * 结算类型0:小鲸 1:非小鲸
     */
    private Integer reconcileType;
    /**
     * 对账单名称
     */
    private String reconcileName;
    /**
     * 对账月度
     */
    private String reconcileMonth;
    @ApiModelProperty(value = "科目编码集合", example = "TK20202020")
    private String subjectRuleCodes;
    /**
     * 生成账单日
     */
    private String createReconcileDay;
    /**
     * 对账保单月度
     */
    private Integer policyMonth;
    /**
     * 账单日
     */
    private Integer reconcileDay;
    /**
     * 保司配置编码
     */
    private String reconcileCompanyCode;
    /**
     * 保险公司编码
     */
    private String productCompanyCode;
    /**
     * 保险公司名称
     */
    private String productCompanyName;
    /**
     * 结算保险公司编码
     */
    private String companyCode;
    /**
     * 结算保险公司名称
     */
    private String companyName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
    /**
     * 内部签署方类型 0 :个人 1:企业 3 小鲸组织机构
     */
    private String innerSignatoryType;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 外部签署方类型字典
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 保司对账单文件数量
     */
    private Integer companyBillCount;
    /**
     * 对账保单数量
     */
    private Integer policyCount;
    /**
     * 小鲸手续费金额
     */
    private BigDecimal xiaowhaleAmount;
    /**
     * 保司手续费金额
     */
    private BigDecimal companyAmount;
    /**
     * 差异金额
     */
    private BigDecimal diffAmount;
    /**
     * 差异冲正金额
     */
    private BigDecimal reversalAmount;
    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmount;
    /**
     * 开票中金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 申请开票金额
     */
    private BigDecimal applyAmount;
    /**
     * 可开票金额
     */
    private BigDecimal invoicableAmount;
    /**
     * 开票状态0:不可开票 1:待开票 2:部分开票 3:全部开票
     */
    private Integer invoiceStatus;
    /**
     * 对账单下载地址
     */
    private String reconcileXlsUrl;

    /**
     * 对账单保单信息下载地址
     */
    private String reconcilePolicyUrl;

    /**
     * 对账单状态;0待对账1账单生成中2对账中3对账已完成4对账单生成失败
     */
    private Integer reconcileStatus;
    /**
     * 对账操作员
     */
    private String reconcileUser;
    /**
     * 备注信息
     */
    private String subjectRemark;
}
