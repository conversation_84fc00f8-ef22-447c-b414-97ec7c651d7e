package com.mpolicy.manage.modules.settlement.common;

import com.mpolicy.common.redis.key.BasePrefix;

/**
 * <AUTHOR>
 * @description
 * @date 2024/01/28 10:39 上午
 * @Version 1.0
 */
public class SettlementKeys extends BasePrefix {

    private SettlementKeys(String prefix) {
        super(prefix);
    }

    private SettlementKeys(int expireSeconds, String prefix) {
        super(expireSeconds, prefix);
    }

    /**
     * 区域信息缓存(24小时分钟)
     */
    public static SettlementKeys EMPLOYEE_REGION_ORG = new SettlementKeys(60 * 60 * 24, "EMPLOYEE_REGION_ORG");
}
