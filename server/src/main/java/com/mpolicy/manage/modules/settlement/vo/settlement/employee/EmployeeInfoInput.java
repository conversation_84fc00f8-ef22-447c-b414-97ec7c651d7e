package com.mpolicy.manage.modules.settlement.vo.settlement.employee;

import com.mpolicy.common.exception.ValidatorException;
import com.mpolicy.common.result.BasicCodeMsg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/30 2:09 上午
 * @Version 1.0
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "员工信息查询", description = "员工信息查询")
public class EmployeeInfoInput implements Serializable {
    @ApiModelProperty("分支（总部员工为最近一级部门）")
    private String branchCode;
    @ApiModelProperty("片区")
    private String zoneCode;
    @ApiModelProperty("区域（总部员工为最上层部门）")
    private String regionCode;
    @ApiModelProperty("是否包含离职")
    private Boolean includeLevelFlag;

    public void validParams(){
        if(StringUtils.isBlank(branchCode) && StringUtils.isBlank(zoneCode) && StringUtils.isBlank(regionCode)){
            throw new ValidatorException(BasicCodeMsg.PARAMETER_ERROR.setMsg("员工机构信息不能为空"));
        }
    }
}
