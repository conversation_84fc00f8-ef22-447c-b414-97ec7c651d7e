package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 法大大客户签约合同信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-27 11:20:06
 */
@TableName("fdd_customer_contract")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FddCustomerContractEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 客户id
     */
    private Integer userId;
    /**
     * 所属模块(1-代理人线上入职 2-投保告知书)
     */
    private Integer model;
    /**
     * 业务合同编号
     */
    private String buzContractNo;
    /**
     * 业务编号
     */
    private String buzNo;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 合同下载地址
     */
    private String downloadUrl;
    /**
     * 合同本地下载地址
     */
    private String url;
    /**
     * 标题
     */
    private String title;

    /**
     * 法大大签署类型
     */
    private String signType;
    /**
     * 法大大合同类型
     */
    private String contractType;
    /**
     * 法大大签署状态
     */
    private String signStatus;
    /**
     * 合同状态(1-签署中 2-已完成)
     */
    private Integer status;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
