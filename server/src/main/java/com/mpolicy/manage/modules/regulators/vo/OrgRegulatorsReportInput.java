package com.mpolicy.manage.modules.regulators.vo;

import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 监管报备报告处理data
 *
 * <AUTHOR>
 * @date 2022-01-20 15:23
 */
@Data
@ApiModel(value = "机构报备操作对象")
public class OrgRegulatorsReportInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报备月度编码
     */
    @ApiModelProperty(value = "报备月度编码",example = "BB10000000")
    @NotBlank(message = "报备月度编码不能为空")
    private String regulatorsNo;

    /**
     * 机构报备唯一编码
     */
    @ApiModelProperty(value = "机构报备唯一编码",example = "ORG10000000")
    @NotBlank(message = "机构报备唯一编码缺失", groups = UpdateGroup.class)
    private String orgRegulatorsNo;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码",example = "ORG10000000")
    @NotBlank(message = "机构编码不能为空")
    private String orgCode;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称",example = "小鲸向海")
    @NotBlank(message = "机构名称不能为空")
    private String orgName;

    /**
     * 报备文件编码
     */
    @ApiModelProperty(value = "报备文件编码",example = "SSO2021021021020")
    @NotBlank(message = "报备文件编码不能为空")
    private String fileCode;

    /**
     * 机构报备内容编码
     */
    @ApiModelProperty(value = "机构报备内容编码",example = "CORE_SYSTEM:REGULATORS:REPORT_TYPE:PROPERTY_COMPANY_BUSINESS")
    @NotBlank(message = "机构报备内容编码编码不能为空")
    private String reportType;
}
