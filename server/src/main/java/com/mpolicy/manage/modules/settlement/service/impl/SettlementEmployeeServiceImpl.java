package com.mpolicy.manage.modules.settlement.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.manage.modules.common.service.impl.PublicBaseService;
import com.mpolicy.manage.modules.settlement.common.SettlementKeys;
import com.mpolicy.manage.modules.settlement.service.SettlementEmployeeService;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.EmployeeInfoInput;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.EmployeeInfoVo;
import com.mpolicy.manage.modules.settlement.vo.settlement.employee.OrganizationCache;
import com.mpolicy.open.common.cfpamf.hr.EmployeeByOrgCodeRequest;
import com.mpolicy.open.common.cfpamf.hr.EmployeeResp;
import com.mpolicy.open.common.cfpamf.hr.OrganizationRequest;
import com.mpolicy.open.common.cfpamf.hr.OrganizationResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/28 10:39 上午
 * @Version 1.0
 */
@Service("settlementEmployeeService")
@Slf4j
public class SettlementEmployeeServiceImpl implements SettlementEmployeeService {

    public static String REDIS_LIST_KEY = "LIST";

    public static Integer QUERY_SIZE = 200;
    @Autowired
    IRedisService redisService;
    @Autowired
    private PublicBaseService publicBaseService;

    /**
     * 初始换机构分支缓存
     *
     * <AUTHOR>
     * @since 2023/11/28 12:33
     */
    private void initOrganizationCache() {
        if (!redisService.exists(SettlementKeys.EMPLOYEE_REGION_ORG, REDIS_LIST_KEY)) {
            List<OrganizationCache> cacheData = new ArrayList<>();
            // 1 获取所有的机构分支信息
            OrganizationRequest orgRequest = new OrganizationRequest();
            Integer page = 1;
            // 2 业务处理
            while (true) {
                orgRequest.setPage(page);
                orgRequest.setSize(QUERY_SIZE);
                orgRequest.setYear(null);
                orgRequest.setMonth(null);
                // 2-1 分页获取分支信息
                List<OrganizationResp> orgList = publicBaseService.queryBranchInfo(orgRequest, true);
                if (orgList.isEmpty()) {
                    break;
                }
                orgList.forEach(x -> {
                    OrganizationCache bean = new OrganizationCache();
                    BeanUtils.copyProperties(x, bean);
                    cacheData.add(bean);
                });
                page++;
            }
            redisService.set(SettlementKeys.EMPLOYEE_REGION_ORG, REDIS_LIST_KEY, JSONArray.toJSON(cacheData));
        }
    }

    /**
     * 获取所有区域信息
     * @return 区域分支信息
     * <AUTHOR>
     * @since 2023/11/29 15:18
     */
    @Override
    public List<OrganizationCache> listAllRegion() {
        // 初始化缓存
        initOrganizationCache();
        // 从redis 获取
        String cacheData = redisService.get(SettlementKeys.EMPLOYEE_REGION_ORG, REDIS_LIST_KEY, String.class);
        List<OrganizationCache> list = JSONArray.parseArray(cacheData, OrganizationCache.class);

        if (list.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        return list;
    }

    @Override
    public List<EmployeeInfoVo> listEmployeeInfo(EmployeeInfoInput input){
        //参数验证
        input.validParams();
        EmployeeByOrgCodeRequest orgRequest = new EmployeeByOrgCodeRequest();
        BeanUtils.copyProperties(input,orgRequest);
        List<EmployeeInfoVo> infoVos = new ArrayList<>();
        int page = 1;
        while (true) {
            orgRequest.setPage(page);
            orgRequest.setSize(QUERY_SIZE);
            orgRequest.setYear(null);
            orgRequest.setMonth(null);
            // 2-1 分页获取分支信息
            List<EmployeeResp> orgList = publicBaseService.getByEmployeeListByPage(orgRequest, true);
            if (orgList.isEmpty()) {
                break;
            }
            orgList.forEach(x -> {
                EmployeeInfoVo bean = new EmployeeInfoVo();
                BeanUtils.copyProperties(x, bean);
                infoVos.add(bean);
            });
            page++;
        }
        return infoVos;
    }


}
