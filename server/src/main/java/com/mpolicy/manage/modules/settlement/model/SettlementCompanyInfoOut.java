package com.mpolicy.manage.modules.settlement.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SettlementCompanyInfoOut implements Serializable {
    private static final long serialVersionUID = 2317624650888388146L;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 结算保司配置编码
     */
    private String settlementCompanyCode;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 外部签署方类型
     */
    private String externalSignatoryType;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署机构
     */
    private String innerSignatoryBranch;
    /**
     * 生效状态0:未生效 1:已生效
     */
    private Integer effectiveStatus;
    /**
     * 每月账单日
     */
    private Integer billDay;
    /**
     * 是否是月底最后一天:0不是  1:是
     */
    private Integer isMonthEnd;

    /**
     *
     */
    private long revision;

    /**
     * 科目id
     */
    private List<Integer> subjectIdList;
}
