package com.mpolicy.manage.modules.regulators.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.regulators.entity.RegulatorsMonthReportEntity;
import com.mpolicy.manage.modules.regulators.vo.CreateRegulatorsMonthReport;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportInput;
import com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportOut;

public interface RegulatorsMonthReportService extends IService<RegulatorsMonthReportEntity> {

    /**
     * 数据列表
     * @param input 请求参数
     * @return 列表结果
     */
    PageUtils<RegulatorsMonthReportOut> queryPage(RegulatorsMonthReportInput input);

    /**
     * 创建监管月报表
     * @param input 过滤参数
     */
    void createRegulatorsMonthReport(CreateRegulatorsMonthReport input);
}
