package com.mpolicy.manage.modules.settlement.service.detail.impl;

import com.google.common.collect.Lists;
import com.mpolicy.manage.modules.settlement.enums.CostSubjectEnum;
import com.mpolicy.manage.modules.settlement.service.detail.AbsPolicyDimensionService;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.PageSettlementDetailParams;
import com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryLongPromotion;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/1/31 15:48
 * @Version 1.0
 */

@Service
public class LongPromotionPolicyDimensionServiceImpl extends AbsPolicyDimensionService<List<DetailSummaryLongPromotion>> {
    @Override
    public List<DetailSummaryLongPromotion> querySummary(PageSettlementDetailParams p) {

        DetailSummaryLongPromotion result = costAutoInfoDao.longPromotionSummary(p);

        if (Objects.nonNull(result)) {
            result.setMonth(calMonthBeforeLastMonth(p) + "（暂发）");
        }

        return Lists.newArrayList(result);
    }

    @Override
    public List<CostSubjectEnum> subjectType() {
        return Lists.newArrayList(CostSubjectEnum.LONG_PROMOTION);
    }
}
