package com.mpolicy.manage.modules.regulators.service.report.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.modules.regulators.service.report.data.CompanyBasicInfoData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 公司基本情况表数据解析
 *
 * <AUTHOR>
 * @date 2022-01-21 18:48
 */
@Slf4j
public class CompanyBasicInfoListener extends ReportObjectEventListener<Object, CompanyBasicInfoData> {

    List<CompanyBasicInfoData> datas = new ArrayList<>();

    @Override
    public List<CompanyBasicInfoData> getReadData() {
        return datas;
    }

    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.debug("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);

        // 暂时用判断 超过14行，不解析
        if(context.getCurrentRowNum() < 14) {
            JSONArray lineData = JSON.parseArray(xlsLineData);
            if(lineData.size() < 3){
                return;
            }
            // 解析赋值对象
            CompanyBasicInfoData bean = new CompanyBasicInfoData();
            bean.setBasicName(lineData.getString(0));
            bean.setLineNumber(lineData.getString(1));
            bean.setNumberDesc(lineData.getString(2));
            datas.add(bean);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
