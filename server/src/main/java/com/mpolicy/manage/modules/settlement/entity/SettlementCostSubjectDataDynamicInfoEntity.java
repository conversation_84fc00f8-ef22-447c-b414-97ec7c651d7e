package com.mpolicy.manage.modules.settlement.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 科目范围(动态科目)数据
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
@TableName("settlement_cost_subject_data_dynamic_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectDataDynamicInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 动态科目申请编号
	 */
	private String applyCode;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 序号
	 */
	private Integer serialNumber;
	/**
	 * 动态科目编号
	 */
	private String dynamicSubjectCode;
	/**
	 * 动态科目名称
	 */
	private String dynamicSubjectName;
	/**
	 * 动态科目总金额
	 */
	private BigDecimal dynamicSubjectCash;
	/**
	 * 动态科目备注
	 */
	private String dynamicSubjectDesc;
	/**
	 * 结算机构编码
	 */
	private String settlementInstitution;
	/**
	 * 结算机构名称
	 */
	private String settlementInstitutionName;
	/**
	 * 员工编码
	 */
	private String employeeCode;
	/**
	 * 员工名称
	 */
	private String employeeName;
	/**
	 * 区域编码
	 */
	private String regionCode;
	/**
	 * 区域名称
	 */
	private String regionName;
	/**
	 * 分支机构编码
	 */
	private String orgCode;
	/**
	 * 分支机构名称
	 */
	private String orgName;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
