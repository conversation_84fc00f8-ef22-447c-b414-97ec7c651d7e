package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileConfirmService;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileDiffBacklogService;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogInfo;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogOut;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogVo;
import com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 结算待办事项
 *
 * <AUTHOR>
 * @since 2023-05-24 15:59
 */
@RestController
@RequestMapping("/settlement/reconcile/diff_backlog")
@Api(tags = "结算待办事项")
@Slf4j
public class ReconcileDiffBacklogController extends ReconcileBaseController {


    @Autowired
    private SettlementReconcileDiffBacklogService settlementReconcileDiffBacklogService;

    @Autowired
    private SettlementReconcileConfirmService settlementReconcileConfirmService;


    @ApiOperation(value = "分页查询结算待办事项列表", notes = "分页查询结算待办事项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "createTimeStart", dataType = "String", value = "创建时间开始"),
            @ApiImplicitParam(paramType = "query", name = "createTimeEnd", dataType = "String", value = "创建时间结束"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query", name = "diffType", dataType = "String", value = "差异类型"),
            @ApiImplicitParam(paramType = "query", name = "acceptUserName", dataType = "String", value = "受理用户名称"),
            @ApiImplicitParam(paramType = "query", name = "diffStatus", dataType = "String", value = "差异处理状态0待处理1已处理"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions("settlement:reconcile:diff_backlog:list")
    public Result<PageUtils<SettlementReconcileBacklogInfo>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.debug("获取结算待办事项信息列表，查询条件={}", params);
        PageUtils<SettlementReconcileBacklogInfo> page = settlementReconcileDiffBacklogService.querySettlementBacklogInfoServiceList(params);
        return Result.success(page);
    }

    /**
     * 删除待办
     *
     * @param backlogCode:
     * @return : com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/5/26 16:21
     */
    @ApiOperation(value = "删除待办", notes = "删除待办")
    @GetMapping("/delete/{backlogCode}")
    @RequiresPermissions("settlement:reconcile:diff_backlog:delete")
    public Result<String> delete(@PathVariable @ApiParam(name = "backlogCode", value = "差异受理编号") String backlogCode) {
        settlementReconcileDiffBacklogService.deleteSettlementBacklogInfo(backlogCode);
        return Result.success();
    }

    /**
     * 查看待办
     *
     * @param billCode:
     * @return : com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/5/26 16:21
     */
    @ApiOperation(value = "查看待办", notes = "查看待办")
    @GetMapping("/info/{billCode}")
    public Result<SettlementReconcileDiff> info(@PathVariable @ApiParam(name = "billCode", value = "汇总对账纪录编号") String billCode) {
        return Result.success(settlementReconcileConfirmService.querySettlementReconcileDiff(billCode));
    }

    /**
     * 处理待办
     *
     * @param settlementReconcileBacklogVo:
     * @return : com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/5/26 16:21
     */
    @ApiOperation(value = "查看待办", notes = "查看待办")
    @PostMapping("/handleDiff")
    @RequiresPermissions("settlement:reconcile:diff_backlog:handleDiff")
    public Result<String> handleDiff(@RequestBody @ApiParam(name = "settlementReconcileBacklogVo", value = "处理待办信息", required = true) SettlementReconcileBacklogVo settlementReconcileBacklogVo) {
        ValidatorUtils.validateEntity(settlementReconcileBacklogVo);
        return Result.success(settlementReconcileDiffBacklogService.handleDiff(settlementReconcileBacklogVo));
    }
}
