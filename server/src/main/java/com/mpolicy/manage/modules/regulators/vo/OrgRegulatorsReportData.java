package com.mpolicy.manage.modules.regulators.vo;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 监管报备报告处理data
 *
 * <AUTHOR>
 * @date 2022-01-20 15:23
 */
@Data
@ApiModel(value = "监管报备月度机构报备详情")
public class OrgRegulatorsReportData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报备月度编码
     */
    @ApiModelProperty(value = "报备月度编码",example = "BB10000000")
    private String regulatorsNo;

    /**
     * 机构报备唯一编码
     */
    @ApiModelProperty(value = "机构报备唯一编码",example = "ORG10000000")
    private String orgRegulatorsNo;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码",example = "ORG10000000")
    private String orgCode;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称",example = "小鲸向海")
    private String orgName;

    /**
     * 机构报备内容编码
     */
    @ApiModelProperty(value = "机构报备内容编码",example = "CORE_SYSTEM:REGULATORS:REPORT_TYPE:PROPERTY_COMPANY_BUSINESS")
    private String reportType;

    /**
     * 机构报备内容名称
     */
    @ApiModelProperty(value = "机构报备内容名称",example = "代理产险公司业务表")
    private String reportTypeName;

    /**
     * 机构报送月度
     */
    @ApiModelProperty(value = "报送月度",example = "2022年1月")
    private String orgMonthDesc;

    /**
     * 报告文件下载路径
     */
    @ApiModelProperty(value = "报告文件下载路径",example = "https://modao.cc/app/kjv8uk8wcp6jlg#screen=skyl644m3niij8f")
    private String domainPath;

    /**
     * 报告文件名称
     */
    @ApiModelProperty(value = "报告文件名称",example = "报告文件名称.xls")
    private String reportFileName;

    /**
     * 报备状态 0未报送1已报送
     */
    @ApiModelProperty(value = "报送状态编码",example = "1")
    private Integer reportStatusCode;

    /**
     * 报备状态 0未报送1已报送
     */
    @ApiModelProperty(value = "报送状态",example = "已报送")
    private String reportStatus;

    /**
     * 报备报告内容
     */
    @ApiModelProperty(value = "报备报告内容",example = "保险专业中介机构月度报表（2021年12月）-利润表")
    private String reportTitle;

    /**
     * 报备报告内容
     */
    @ApiModelProperty(value = "报备报告内容",example = "{}")
    private JSONObject reportData;

    /**
     * 最后编辑时间
     */
    @ApiModelProperty(value = "最后编辑时间", example = "2019-01-01")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;
}
