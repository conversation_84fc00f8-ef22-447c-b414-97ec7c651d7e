package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/20 12:47 上午
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettlementPcoInfoExportVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    @ExcelProperty(value="序号", index = 0)
    Integer num;

    @ApiModelProperty("区域名称")
    @ExcelProperty(value = "区域名称", index = 1)
    String regionName;

    @ApiModelProperty("分支名称")
    @ExcelProperty(value = "分支名称", index = 2)
    String orgName;

    @ApiModelProperty("工号")
    @ExcelProperty(value = "工号", index = 3)
    String employeeCode;

    @ApiModelProperty("姓名")
    @ExcelProperty(value = "姓名", index = 4)
    String employeeName;


    /**
     * 生服业务相关知识（是否合格）
     */
    @ApiModelProperty("生服业务相关知识（是否合格）")
    @ExcelProperty(value="生服业务相关知识（是否合格）", index = 5)
    private String businessKnowledgeQualifyFlag;
    /**
     * 酒水/家清破损理赔提交及时性（是否合格）
     */
    @ApiModelProperty("家清破损理赔提交及时性（是否合格）")
    @ExcelProperty(value="家清破损理赔提交及时性（是否合格）", index = 6)
    private String claimTimelinessQualifyFlag;


}
