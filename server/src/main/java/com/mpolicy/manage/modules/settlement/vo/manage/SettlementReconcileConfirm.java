package com.mpolicy.manage.modules.settlement.vo.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 对账单保单科目列表信息
 *
 * <AUTHOR>
 * @since 2023/6/1 00:44
 */
@Data
@ApiModel(value = "对账单保单科目列表信息", description = "对账单保单科目列表信息")
public class SettlementReconcileConfirm implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "SC2020220202020")
    private String reconcileCode;

    /**
     * 对账保单业务汇总明细编号
     */
    @ApiModelProperty(value = "单据编号", example = "12345tfdser")
    private String billCode;

    /**
     * 对账月度
     */
    @ApiModelProperty(value = "对账月度", example = "2023-05")
    private String reconcileMonth;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称", example = "太平洋")
    private String companyName;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "AS23456723456")
    private String policyNo;

    /**
     * 对账模式;线上对账、线下对账
     */
    @ApiModelProperty(value = "对账模式;线上对账、线下对账", example = "线下对账")
    private String reconcileModel;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称", example = "首期年佣金")
    private String reconcileSubjectName;

    /**
     * 险种名称
     */
    @ApiModelProperty(value = "产品名称", example = "意外险")
    private String productName;

    /**
     * 保费
     */
    @ApiModelProperty(value = "保费", example = "529.00")
    private BigDecimal premium;

    @ApiModelProperty(value = "保司保费", example = "529.00")
    private BigDecimal companyPremium;


    /**
     * 结算费率
     */
    @ApiModelProperty(value = "结算费率", example = "0.09")
    private String settlementRate;

    /**
     * 结算金额
     */
    @ApiModelProperty(value = "结算金额", example = "529.00")
    private BigDecimal settlementAmount;

    @ApiModelProperty(value = "结算保费", example = "529.00")
    private BigDecimal settlementPremium;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差异金额", example = "110")
    private BigDecimal diffAmount;

    /**
     * 是否为差异纪录 0否1是
     */
    @ApiModelProperty(value = "是否为差异纪录 0否1是", example = "0")
    private Integer diffFlag;

    /**
     * 系统差异类型
     */
    @ApiModelProperty(value = "系统差异类型", example = "线下对账")
    private String diffType;

    /**
     * 差异处理状态0待处理1已处理
     */
    @ApiModelProperty(value = "差异处理状态0待处理1已处理", example = "0")
    private Integer diffStatus;

    @ApiModelProperty(value = "标记状态0未标记 1已标记", example = "0")
    private Integer markStatus;
}
