package com.mpolicy.manage.modules.settlement.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.service.SettlementEventJobService;
import com.mpolicy.manage.modules.settlement.vo.HandleRefreshVo;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobInfoOut;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 结算交互事件受理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-05 14:34:54
 */
@RestController
@RequestMapping("settlement/event/job")
@Api(tags = "结算交互事件受理表")
public class SettlementEventJobController {

    @Autowired
    private SettlementEventJobService settlementEventJobService;


    @GetMapping("list")
    @RequiresPermissions("settlement:event-job:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<SettlementEventJobListOut>> list(SettlementEventJobListInput input) {
        PageUtils<SettlementEventJobListOut> page = settlementEventJobService.findSettlementEventJobList(input);
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @RequiresPermissions("settlement:event-job:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<SettlementEventJobInfoOut> info(@PathVariable(value = "id", required = false)
                                                  @NotNull(message = "操作的数据id不能为空")
                                                  @ApiParam(value = "详情ID") Integer id) {
        SettlementEventJobInfoOut settlementEventJob = settlementEventJobService.findSettlementEventJobById(id);
        return Result.success(settlementEventJob);
    }

    /**
     * 处理任务的刷新和重置
     * @param vo 请求参数
     * @return
     */
    @PostMapping("handleRefresh")
    @RequiresPermissions("settlement:event-job:info")
    @ApiOperation(value = "处理任务的刷新和重置", notes = "处理任务的刷新和重置", httpMethod = "GET")
    public Result handleRefresh(@RequestBody @Valid HandleRefreshVo vo) {
        settlementEventJobService.handleRefresh(vo);
        return Result.success();
    }

    @PostMapping("rectificationById/{id}")
    @ApiOperation(value = "冲正处理", notes = "冲正处理", httpMethod = "GET")
    public Result rectificationById(@PathVariable Integer id) {
        settlementEventJobService.rectificationById(id);
        return Result.success();
    }
}
