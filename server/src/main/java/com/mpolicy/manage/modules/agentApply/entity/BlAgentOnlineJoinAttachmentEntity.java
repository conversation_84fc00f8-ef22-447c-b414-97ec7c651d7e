package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人线上入职附件信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
@TableName("bl_agent_online_join_attachment")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineJoinAttachmentEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    private String agentCode;
    /**
     * 附件类型
     */
    private String identificationType;
    /**
     * 附件格式
     */
    private String suffix;
    /**
     * 文件编码
     */
    private String fileCode;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
