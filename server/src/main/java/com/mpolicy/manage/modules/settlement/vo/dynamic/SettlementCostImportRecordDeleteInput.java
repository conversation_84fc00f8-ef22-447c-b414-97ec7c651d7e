package com.mpolicy.manage.modules.settlement.vo.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
@Data
@ApiModel(value = "结算文件删除信息", description = "结算文件删除信息")
public class SettlementCostImportRecordDeleteInput implements Serializable {

    private static final long serialVersionUID = 1;

    private Integer id;

    /**
     * 所属周期
     */
    @ApiModelProperty(value = "所属周期", required = true, example = "202312")
    @NotBlank(message = "所属周期不能为空")
    private String costSettlementCycle;

    /**
     * 文件编码
     */
    @ApiModelProperty(value = "文件编码 动态科目：DYNAMIC，PCO等级、人均单量信息：PCO_INFO，佣金发放方式：COMMISSION_GRANT_WAY", required = true, example = "oss20231218204322ClEHFv")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @ApiModelProperty(value = "所属周期", hidden = true)
    private String userName;
}
