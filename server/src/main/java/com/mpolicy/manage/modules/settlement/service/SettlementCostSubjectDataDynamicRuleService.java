package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicRuleEntity;

/**
 * 科目范围(动态科目)规则
 *
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
public interface SettlementCostSubjectDataDynamicRuleService extends IService<SettlementCostSubjectDataDynamicRuleEntity> {

}

