package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileConfirmEntity;
import com.mpolicy.manage.modules.settlement.vo.confirm.SettlementReconcileConfirmInfo;
import com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff;

import java.util.Map;

/**
 * 保司结算对账单汇总表
 *
 * <AUTHOR>
 * @date 2023-05-23 14:32:21
 */
public interface SettlementReconcileConfirmService extends IService<SettlementReconcileConfirmEntity> {

    /**
     * 分页查询结算确认信息列表
     *
     * @param params:
     * @return : com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.settlement.vo.policy.SettlementReconcilePolicyInfo>
     * <AUTHOR>
     * @date 2023/5/26 11:54
     */
    PageUtils<SettlementReconcileConfirmInfo> querySettlementConfirmInfoServiceList(Map<String, Object> params);


    /**
     * 根据对账员明细纪录编号获取详情
     *
     * @param billCode 对账员明细纪录编号
     * @return com.mpolicy.manage.modules.settlement.vo.manage.SettlementReconcileDiff
     * <AUTHOR>
     * @since 2023/6/1 11:01
     */
    SettlementReconcileDiff querySettlementReconcileDiff(String billCode);
}

