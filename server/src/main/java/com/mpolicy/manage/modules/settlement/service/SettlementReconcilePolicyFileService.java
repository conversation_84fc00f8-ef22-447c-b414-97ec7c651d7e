package com.mpolicy.manage.modules.settlement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileFileEntity;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcilePolicyFileEntity;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;

/**
 * <AUTHOR>
 */
public interface SettlementReconcilePolicyFileService extends IService<SettlementReconcilePolicyFileEntity> {

    void saveReconcileRuleFileTemplate(SettlementReconcileFileEntity settlementReconcileFile, SysDocumentEntity documentInfo);
}
