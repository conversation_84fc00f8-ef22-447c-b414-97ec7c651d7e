package com.mpolicy.manage.modules.settlement.vo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 结算保司配置对应规则科目表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 14:29:16
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementReconcileCompanyProductSaveInput implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司对账单产品名称
     */
    private String companyProductName;
    /**
     * 协议险种编码
     */
    private String insuranceProductCode;
    /**
     * 险种编码
     */
    private String productCode;

}
