package com.mpolicy.manage.modules.settlement.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.settlement.entity.SettlementPolicyReviseEntity;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseInput;
import com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseListOut;
import org.apache.ibatis.annotations.Param;

public interface SettlementPolicyReviseDao extends BaseMapper<SettlementPolicyReviseEntity> {

    /**
     * 分页获取数据列表
     * @param page 分页
     * @param input 查询条件
     * @return 分页结果
     */
    IPage<SettlementPolicyReviseListOut> findSettlementPolicyReviseList(@Param("page") Page<SettlementPolicyReviseListOut> page,@Param("input")  SettlementPolicyReviseInput input);
}
