package com.mpolicy.manage.modules.agentApply.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ClassName: AgentApplyAttachmentVo
 * Description:
 * date: 2022/12/1 13:46
 *
 * <AUTHOR>
 */
@Data
public class AgentApplyAttachmentVo implements Serializable {

    /**
     * 附件格式
     */
    @ApiModelProperty(value = "附件格式",required = true)
    @NotBlank(message = "附件格式不能为空")
    private String suffix;
    @ApiModelProperty(value = "文件编码",required = true)
    @NotBlank(message = "文件编码不能为空")
    private String fileCode;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件地址",required = true)
    @NotBlank(message = "文件地址不能为空")
    private String filePath;
}
