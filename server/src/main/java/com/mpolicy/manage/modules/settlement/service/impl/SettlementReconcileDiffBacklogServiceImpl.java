package com.mpolicy.manage.modules.settlement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.settlement.dao.SettlementReconcileDiffBacklogDao;
import com.mpolicy.manage.modules.settlement.entity.SettlementReconcileDiffBacklogEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementReconcileDiffBacklogService;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogInfo;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogOut;
import com.mpolicy.manage.modules.settlement.vo.backlog.SettlementReconcileBacklogVo;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 保司结算对账单差异待办
 *
 * <AUTHOR>
 * @date 2023-05-21 22:28:34
 */
@Slf4j
@Service("settlementReconcileDiffBacklogService")
public class SettlementReconcileDiffBacklogServiceImpl extends ServiceImpl<SettlementReconcileDiffBacklogDao, SettlementReconcileDiffBacklogEntity> implements SettlementReconcileDiffBacklogService {


    @Override
    public PageUtils<SettlementReconcileBacklogInfo> querySettlementBacklogInfoServiceList(Map<String, Object> params) {
        //创建时间
        String createTimeStart = RequestUtils.objectValueToString(params, "createTimeStart");
        String createTimeEnd = RequestUtils.objectValueToString(params, "createTimeEnd");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //差异类型
        String diffType = RequestUtils.objectValueToString(params, "diffType");
        //受理用户名称
        String acceptUserName = RequestUtils.objectValueToString(params, "acceptUserName");
        //差异处理状态0待处理1已处理
        String diffStatus = RequestUtils.objectValueToString(params, "diffStatus");

        // 分页查询基础数据
        IPage<SettlementReconcileDiffBacklogEntity> pageResult = this.page(
                new Query<SettlementReconcileDiffBacklogEntity>().getPage(params),
                new LambdaQueryWrapper<SettlementReconcileDiffBacklogEntity>()
                        .eq(StringUtils.isNotBlank(diffStatus), SettlementReconcileDiffBacklogEntity::getDiffStatus, diffStatus)
                        .eq(StringUtils.isNotBlank(policyNo), SettlementReconcileDiffBacklogEntity::getPolicyNo, policyNo)
                        .eq(StringUtils.isNotBlank(diffType), SettlementReconcileDiffBacklogEntity::getDiffType, diffType)
                        .eq(StringUtils.isNotBlank(acceptUserName), SettlementReconcileDiffBacklogEntity::getAcceptUserName, acceptUserName)
                        .apply(StringUtils.isNotBlank(createTimeStart), "date_format(create_time,'%Y-%m-%d') >= {0}", createTimeStart)
                        .apply(StringUtils.isNotBlank(createTimeEnd), "date_format(create_time,'%Y-%m-%d') <= {0}", createTimeEnd)
                        .orderByDesc(SettlementReconcileDiffBacklogEntity::getCreateTime)
        );

        // 构建vo
        List<SettlementReconcileBacklogInfo> resultData = pageResult.getRecords().stream().map(x -> {
            SettlementReconcileBacklogInfo bean = new SettlementReconcileBacklogInfo();
            BeanUtils.copyProperties(x, bean);
            return bean;
        }).collect(Collectors.toList());

        // 重新构建分页返回对象
        return new PageUtils(resultData, (int) pageResult.getTotal(), (int) pageResult.getSize(), (int) pageResult.getCurrent());
    }

    @Override
    public void deleteSettlementBacklogInfo(String backlogCode) {
        SettlementReconcileDiffBacklogEntity entity = Optional.ofNullable(this.lambdaQuery()
                .eq(SettlementReconcileDiffBacklogEntity::getBacklogCode, backlogCode)
                .last("limit 1")
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取代办项失败")));
        this.removeById(entity);
    }

    @Override
    public SettlementReconcileBacklogOut querySettlementBacklogInfo(String backlogCode) {
        SettlementReconcileDiffBacklogEntity entity = Optional.ofNullable(this.lambdaQuery()
                .eq(SettlementReconcileDiffBacklogEntity::getBacklogCode, backlogCode)
                .last("limit 1")
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取代办项失败")));
        SettlementReconcileBacklogOut out = new SettlementReconcileBacklogOut();
        BeanUtils.copyProperties(entity, out);
        return out;
    }

    @Override
    public String handleDiff(SettlementReconcileBacklogVo settlementReconcileBacklogVo) {
        return null;
    }
}
