package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinHistoryDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinHistoryEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineJoinHistoryService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("blAgentOnlineJoinHistoryService")
public class BlAgentOnlineJoinHistoryServiceImpl extends ServiceImpl<BlAgentOnlineJoinHistoryDao, BlAgentOnlineJoinHistoryEntity> implements BlAgentOnlineJoinHistoryService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<BlAgentOnlineJoinHistoryEntity> page = this.page(
                new Query<BlAgentOnlineJoinHistoryEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
