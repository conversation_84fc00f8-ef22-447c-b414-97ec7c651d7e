package com.mpolicy.manage.modules.agentApply.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人线上入职用户申请信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
@TableName("bl_agent_online_join_apply")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlAgentOnlineJoinApplyEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    private String agentCode;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 姓名
     */
    private String agentName;
    /**
     * 姓名拼音
     */
    private String pinyin;
    /**
     * 性别:0:女 1:男 -1:未知
     */
    private Integer gender;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 联系电话
     */
    private String mobile;
    /**
     * 代理人昵称
     */
    private String agentNickName;
    /**
     * 证件类型 0:身份证  1:其他
     */
    private String idType;
    /**
     * 证件号
     */
    private String idCard;
    /**
     * 证件开始时间
     */
    private Date idStartDate;
    /**
     * 证件结束时间
     */
    private Date idEndDate;
    /**
     * 身份证是否长期有效0:否 1:是
     */
    private Integer idLongTerm;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 人员类型
     */
    private String agentType;
    /**
     * 入职方式
     */
    private String entryType;
    /**
     * 入职日期
     */
    private Date entryDate;
    /**
     * 职位
     */
    private String position;
    /**
     * 职位等级
     */
    private String positionDegree;
    /**
     * 学历
     */
    private String degree;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 婚姻状态
     */
    private String marital;
    /**
     * 政治面貌
     */
    private String politics;
    /**
     * 民族
     */
    private String nation;
    /**
     * 毕业学校
     */
    private String school;
    /**
     * 微信账号(非微信昵称)
     */
    private String weChat;
    /**
     * 从业年限
     */
    private String workTime;
    /**
     * 初始化服务人数
     */
    private Integer initServiceNum;
    /**
     * 服务属性1:专属线上服务
     */
    private String serviceAttribute;
    /**
     * 擅长领域
     */
    private String expertise;
    /**
     * 是否完成公司面试(0:已完成 -1:未完成)
     */
    private Integer isFinish;
    /**
     * 面试日期
     */
    private Date interview;
    /**
     * 面试申请表扫描件文件编码
     */
    private String interviewUrlFileCode;
    /**
     * 面试申请表扫描件
     */
    private String interviewUrl;
    /**
     * 身份证正面图片文件编码
     */
    private String idCardFrontFileCode;
    /**
     * 身份证正面图片
     */
    private String idCardFront;
    /**
     * 身份证反面图片文件编码
     */
    private String idCardBackFileCode;
    /**
     * 身份证反面图片
     */
    private String idCardBack;
    /**
     * 标准证件照片文件编码
     */
    private String certificatesPhoneFileCode;
    /**
     * 标准证件照片
     */
    private String certificatesPhone;
    /**
     * 银行卡正面照片文件编码
     */
    private String bankCardPhoneFileCode;
    /**
     * 银行卡正面照片
     */
    private String bankCardPhone;
    /**
     * 最高学历毕业证照片文件编码
     */
    private String degreePhoneFileCode;
    /**
     * 最高学历毕业证照片
     */
    private String degreePhone;
    /**
     * 增员人编码
     */
    private String recruitCode;
    /**
     * 申请状态(1:已提交 2:完成面试 3:待完成考试 4:待线上签约 5:待审核 6:待复核 7:待修改信息 8:已完成)
     */
    private Integer status;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

    private Integer isOptional;

    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 推荐状态0:不推荐  1:推荐
     */
    private Integer recommendStatus;
    /**
     * 国家
     */
    private String country;
    /**
     * 居住区域编码
     */
    private String areaCode;
    /**
     * 服务城市编码
     */
    private String cityCode;
    /**
     * 人员性质 字典
     */
    private String agentNature;
    /**
     * 人员类别:字典
     */
    private String agentCategory;
    private String acquisitionArea;
    /**
     * 用户标签,号分割
     */
    private String agentLabel;
}
