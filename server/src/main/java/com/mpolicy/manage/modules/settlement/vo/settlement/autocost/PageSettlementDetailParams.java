package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/1/31 09:54
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageSettlementDetailParams extends BasePage {

    @ApiModelProperty(value = "结佣月份")
    private String settlementMonth;

    /**
     * 结佣机构编码  当全部汇总是为空
     */
    @ApiModelProperty(value = "结佣机构编码")
    private String settlementInstitution;

    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码")
    private String objectOrgCode;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String sendObjectCode;

    @ApiModelProperty(value = "科目编码")
    private String costSubjectCode;

    @ApiModelProperty(value = "父级节点")
    private String parentSubjectCode;

    @ApiModelProperty(value = "展示类型")
    private String costDataType;

}
