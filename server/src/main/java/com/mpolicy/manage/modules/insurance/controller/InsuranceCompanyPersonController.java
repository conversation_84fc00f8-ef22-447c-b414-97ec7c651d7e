package com.mpolicy.manage.modules.insurance.controller;

import cn.hutool.core.util.IdcardUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyPersonEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyPersonService;
import com.mpolicy.manage.modules.insurance.vo.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 个人合作方管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-07 14:35:53
 */
@RestController
@RequestMapping("insurance/company/person")
@Api(tags = "个人合作方管理")
public class InsuranceCompanyPersonController {

    @Autowired
    private InsuranceCompanyPersonService insuranceCompanyPersonService;


    @GetMapping("list")
    @RequiresPermissions("insurance:company-person:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<InsuranceCompanyPersonListOut>> list(InsuranceCompanyPersonListInput input) {
        PageUtils<InsuranceCompanyPersonListOut> page = insuranceCompanyPersonService.findInsuranceCompanyPersonList(input);
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @RequiresPermissions("insurance:company-person:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<InsuranceCompanyPersonInfoOut> info(@PathVariable(value = "id", required = false)
                                                      @NotNull(message = "操作的数据id不能为空")
                                                      @ApiParam(value = "详情ID") Integer id) {
        InsuranceCompanyPersonInfoOut insuranceCompanyPerson = insuranceCompanyPersonService.findInsuranceCompanyPersonById(id);
        return Result.success(insuranceCompanyPerson);
    }

    @SysLog("保存个人合作方管理数据")
    @PostMapping("save")
    @RequiresPermissions("insurance:company-person:save")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid InsuranceCompanyPersonSaveInput input) {
        //校验一下身份证号是否正确
        if (!IdcardUtil.isValidCard(input.getIdCard())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("身份证号错误"));
        }
        insuranceCompanyPersonService.saveInsuranceCompanyPerson(input);
        return Result.success();
    }

    @SysLog("修改个人合作方管理数据")
    @PostMapping("update")
    @RequiresPermissions("insurance:company-person:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid InsuranceCompanyPersonUpdateInput input) {
        if (!IdcardUtil.isValidCard(input.getIdCard())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("身份证号错误"));
        }
        insuranceCompanyPersonService.updateInsuranceCompanyPersonById(input);
        return Result.success();
    }

    @SysLog("删除个人合作方管理信息")
    @PostMapping("delete")
    @RequiresPermissions("insurance:company-person:delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false)
                         @NotEmpty(message = "删除的数据ids不能为空")
                         @ApiParam(value = "批量删除的ID") Integer[] ids) {
        insuranceCompanyPersonService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }

    @SysLog("修改个人合作方状态")
    @PostMapping("updateUserStatus")
    @RequiresPermissions("insurance:company-person:update")
    @ApiOperation(value = "修改个人合作方状态", notes = "修改个人合作方状态", httpMethod = "POST")
    public Result updateUserStatus(@RequestBody(required = false) @Valid UpdateUserStatusInput input) {
        insuranceCompanyPersonService.lambdaUpdate()
                .eq(InsuranceCompanyPersonEntity::getId, input.getId())
                .set(InsuranceCompanyPersonEntity::getUserStatus, input.getUserStatus())
                .update();
        return Result.success();
    }
}
