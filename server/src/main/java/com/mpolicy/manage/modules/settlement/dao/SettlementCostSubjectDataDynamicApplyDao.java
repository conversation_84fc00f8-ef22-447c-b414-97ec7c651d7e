package com.mpolicy.manage.modules.settlement.dao;

import com.mpolicy.manage.modules.settlement.entity.SettlementCostSubjectDataDynamicApplyEntity;
import com.mpolicy.service.common.mapper.ImsBaseMapper;

/**
 * 科目范围(动态科目)数据申请记录
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
public interface SettlementCostSubjectDataDynamicApplyDao extends ImsBaseMapper<SettlementCostSubjectDataDynamicApplyEntity> {
	
}
