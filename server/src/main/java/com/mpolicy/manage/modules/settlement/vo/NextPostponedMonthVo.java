package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class NextPostponedMonthVo implements Serializable {

    @NotBlank(message = "确认单编码不能为空")
    private String billCode;

    @NotEmpty(message = "没有选择数据")
    private List<String> eventSourceCodeList;

    @NotBlank(message = "结算月份不能为空")
    private String postponedMonth;
}
