package com.mpolicy.manage.modules.settlement.vo.settlement;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ExportDeletionPolicyOut  extends BaseRowModel implements Serializable {

    @ExcelProperty(value = "对账编码")
    private String reconcileCode;



    @ExcelProperty(value = "结算保司")
    private String settlementCompanyName;

    @ExcelProperty(value = "外部签署方")
    private String externalSignatoryName;

    @ExcelProperty(value = "负责人")
    private String headName;

    @ExcelProperty(value = "保单号")
    private String policyNo;

    /**
     * 保全编码
     */
    @ExcelProperty(value = "保全号")
    private String endorsementNo;

    /**
     * 续期期次
     */
    @ExcelProperty(value = "续期期次")
    private Integer renewalPeriod;

    /**
     * 实收保费
     */
    @ExcelProperty(value = "实收保费")
    private BigDecimal realityPremium;

}
