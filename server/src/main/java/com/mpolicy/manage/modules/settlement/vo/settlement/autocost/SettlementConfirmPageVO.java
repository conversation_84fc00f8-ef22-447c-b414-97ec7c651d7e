package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/2/19 10:00
 * @Version 1.0
 */
@Data
public class SettlementConfirmPageVO {

    @ApiModelProperty("结佣月份")
    private String costSettlementCycle;

    @ApiModelProperty("汇总金额")
    private String totalGrantAmount;

    /**
     * 确认结算状态;0未确认1确认中2已确认
     */
    @ApiModelProperty("确认结算状态;0未确认1确认中2已确认")
    private String confirmStatus;

    /**
     *确认结算操作员
     */
    @ApiModelProperty("确认结算操作员")
    private String confirmUser;

    /**
     * 确认结算完成时间
     */
    @ApiModelProperty("确认结算完成时间")
    private Date confirmTime;

    @ApiModelProperty("子信息-结算机构")
    private List<SettlementConfirmInstitutionPageVO> list;


    public String getTotalGrantAmount() {
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream()
                    .map(SettlementConfirmInstitutionPageVO::getGrantAmount)
                    .map(x -> Objects.isNull(x) ? BigDecimal.ZERO : new BigDecimal(x))
                    .reduce(BigDecimal.ZERO, BigDecimal::add).toPlainString();
        }
        return null;
    }
}
