package com.mpolicy.manage.modules.settlement.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RefreshPolicyVo implements Serializable {

    private String billCode;

    private List<String> eventSourceCodeList;

    private Integer reconcileExecuteStatus;

    private String reconcileExecuteDesc;

    /**
     * 处理单号
     */
    private List<Integer> ids;
}
