package com.mpolicy.manage.modules.endorsement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.endorsement.entity.EndorsementOrderInfoEntity;
import com.mpolicy.manage.modules.endorsement.vo.EndorsementOrderInfo;
import com.mpolicy.manage.modules.endorsement.vo.EndorsementRecallOut;
import com.mpolicy.order.common.endorsement.PolicyEndorsementOrderData;

import java.util.Map;

/**
 * 保全订单信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-11 11:10:55
 */
public interface EndorsementOrderInfoService extends IService<EndorsementOrderInfoEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<EndorsementOrderInfo> queryPage(Map<String, Object> paramMap);

    /**
     * 保全订单详情
     *
     * <AUTHOR>
     * @date 2024/1/11 15:38
     * @param endorsementCode: 保全编号
     * @return : com.mpolicy.order.common.endorsement.PolicyEndorsementOrderData
     */
    PolicyEndorsementOrderData queryEndorsementDetail(String endorsementCode);

    /**
     * 保全订单回溯信息
     *
     * <AUTHOR>
     * @date 2024/1/11 15:49
     * @param endorsementCode: 保全编号
     * @return : com.mpolicy.manage.modules.endorsement.vo.EndorsementRecallOut
     */
    EndorsementRecallOut queryEndorsementRecallData(String endorsementCode);
}

