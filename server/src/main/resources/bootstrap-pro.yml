spring:
  application:
    name: admin-center
  cloud:
    config:
      discovery:
        enabled: true
        service-id: CONFIG
      profile: ${spring.profiles.active}
    # 修复github webhook 只能刷新config server 无法刷新config client的问题
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 总上传的数据大小
      max-request-size: 100MB
# 端口
server:
  port: 8092
  servlet:
    context-path: /api
# log文件写入路径配置
logfile:
  dir: logs
  additivity: true
# 注册中心
eureka:
  client:
    # client间隔多久去拉取服务注册信息，默认为30秒，如果要迅速获取服务注册状态，可以缩小该值，比如5秒
    registry-fetch-interval-seconds: 5
    service-url:
      #      defaultZone: http://***********:8761/eureka/,http://***********:8762/eureka/
      # SAE域名地址
      defaultZone: http://eureka1.internal.xiaowhale.net:8761/eureka/,http://eureka2.internal.xiaowhale.net:8762/eureka/
      # K8S集群环境地址
    #      defaultZone: http://ims-eureka-1/eureka,http://ims-eureka-2/eureka
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 消息开关
xjxh:
  rabbitmq:
    switch: true
# 定时任务 xxl:
xxl:
  job:
    switch: true # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    #    addresses: http://***********:8089/xxl-job-admin
    # SAE域名地址
    addresses: http://xxl-prod.internal.xiaowhale.net:8089/xxl-job-admin
    # K8S集群迁移后打开
    #    addresses: http://ims-xxl-job/xxl-job-admin
    appname: xxl-job-executor-admin # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8192 # 定时任务调度器器和执⾏器交互端⼝，⾮应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# feign日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
    com.mpolicy.manage.modules.sys.dao: debug
    com.mpolicy.order.client: debug
    io.swagger.models.parameters.AbstractSerializableParameter: error
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 80000
        readTimeout: 80000
        #loggerLevel: basic
hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 70000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 40000
wx:
  miniapp:
    appid: wxf4f3efd0ea7c9cd4
mp:
  download:
    folder: logs/
  domain:
    h5: https://app.xiaowhale.com/
    oss: https://oss-xjxhserver.xiaowhale.com/
    access:
      oss: https://xjxhserver.oss-cn-beijing-internal.aliyuncs.com
# 文件临时存储
admin:
  temp-file-path: logs/

alilog:
  accessId: LTAI5tGWtfnJ2rTYpDrM1Zr8
  accessKey: ******************************
  host: cn-beijing.log.aliyuncs.com
insurance:
  feign:
    operation: http://insurance-operation.osg.cfpamf.com
    bff-project: http://insurance-bff-project.osg.cfpamf.com
fttm:
  biz:
    url: http://fttm-service.osg.cfpamf.com/
consult:
  message:
    templateId: _LfaZ4unfORMKQl3clzQ1uwYFvN4RBwCnHFwolL6FdQ
    jumpUrl: pages/public/publicWeb?url=%2FinsureHolder%2FgroupConsult&from=whaleMini&id=
dingTalkRobot:
  protocolExpireNotify:
    domain: https://oapi.dingtalk.com
    token: c6de870bb043e691268edda864f51377ba233e474797ffb7ad945041b3bd270c
    secret: SECfd4470fbad938c67699d31e4ce9a2ded5637dbda13ae3e2b606ae2de56cd71a6
    at: 13581687500
    redirectUrl: https://manage.xiaowhale.com/#/product-protocolManage

