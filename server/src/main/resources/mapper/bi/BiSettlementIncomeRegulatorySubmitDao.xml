<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.bi.dao.BiSettlementIncomeRegulatorySubmitDao">
    <!--获取数据列表-->
    <select id="findOrgIncomeRegulatorySubmitMonth"
            resultType="com.mpolicy.manage.modules.bi.vo.IncomeRegulatorySubmitMonth">
        SELECT rs.level2_name                                                                      as level2Name,
               level2_code,
               rs.`level3_name`                                                                    as level3Name,
               level3_code,
               rs.report_org_name                                                                  as reportOrgName,
               ROUND(sum(IF(rs.only_report_income = 1, 0, premium))/10000, 2)                            as premium,
               ROUND(sum(`settlement_amount`)/10000, 2)                                                  as settlementAmount,
               ROUND(sum(if(insurance_type = 0, IF(rs.only_report_income = 1, 0, premium), 0))/10000, 2) as newPremium,
               ROUND(sum(if(insurance_type = 0, `settlement_amount`, 0))/10000, 2)                       as newSettlementAmount,
               ROUND(sum(if(insurance_type = 1, IF(rs.only_report_income = 1, 0, premium), 0))/10000, 2) as renewalPremium,
               ROUND(sum(if(insurance_type = 1, `settlement_amount`, 0))/10000, 2)                       as renewalSettlementAmount,
               ROUND(sum(IF(`sales_type` = 0, IF(rs.only_report_income = 1, 0, premium), 0))/10000, 2)   as onlinePremium,
               ROUND(sum(IF(`sales_type` = 0, `settlement_amount`, 0))/10000, 2)                         as onlineSettlementAmount,
               ROUND(sum(if(insurance_type = 0, IF(`sales_type` = 0, IF(rs.only_report_income = 1, 0, premium), 0),0))/10000, 2) as onlineNewPremium,
               ROUND(sum(if(insurance_type = 0, IF(`sales_type` = 0, `settlement_amount`, 0),0))/10000,2)      as onlineNewSettlementAmount,
               ROUND(sum(if(insurance_type = 1, IF(`sales_type` = 0, IF(rs.only_report_income = 1, 0, premium), 0),0))/10000,2) as onlineRenewalPremium,
               ROUND(sum(if(insurance_type = 1, IF(`sales_type` = 0, `settlement_amount`, 0),0))/10000, 2)     as onlineRenewalSettlementAmount,
               rs.law_type,
               rs.law_type_desc
        FROM `bi_settlement_income_regulatory_submit` rs
        WHERE `reconcile_type` = 0
          and `reconcile_time` &gt;= #{beginTime,jdbcType=TIMESTAMP}
          and reconcile_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        GROUP BY `level2_name`, `level3_name`, `report_org_name`, law_type
    </select>
    <!--获取数据列表-->
    <select id="findIncomeRegulatorySubmitMonth"
            resultType="com.mpolicy.manage.modules.bi.vo.IncomeRegulatorySubmitMonth">
        SELECT rs.level2_name                                                                      as level2Name,
               level2_code,
               rs.`level3_name`                                                                    as level3Name,
               level3_code,
               ROUND(sum(IF(rs.only_report_income = 1, 0, premium))/10000, 2)                            as premium,
               ROUND(sum(`settlement_amount`)/10000, 2)                                                  as settlementAmount,
               ROUND(sum(if(insurance_type = 0, IF(rs.only_report_income = 1, 0, premium), 0))/10000, 2) as newPremium,
               ROUND(sum(if(insurance_type = 0, `settlement_amount`, 0))/10000, 2)                       as newSettlementAmount,
               ROUND(sum(if(insurance_type = 1, IF(rs.only_report_income = 1, 0, premium), 0))/10000, 2) as renewalPremium,
               ROUND(sum(if(insurance_type = 1, `settlement_amount`, 0))/10000, 2)                       as renewalSettlementAmount,
               ROUND(sum(IF(`sales_type` = 0, IF(rs.only_report_income = 1, 0, premium), 0))/10000, 2)   as onlinePremium,
               ROUND(sum(IF(`sales_type` = 0, `settlement_amount`, 0))/10000, 2)                         as onlineSettlementAmount,
               ROUND(sum(if(insurance_type = 0, IF(`sales_type` = 0, IF(rs.only_report_income = 1, 0, premium), 0),0))/10000, 2) as onlineNewPremium,
               ROUND(sum(if(insurance_type = 0, IF(`sales_type` = 0, `settlement_amount`, 0),0))/10000,2)      as onlineNewSettlementAmount,
               ROUND(sum(if(insurance_type = 1, IF(`sales_type` = 0, IF(rs.only_report_income = 1, 0, premium), 0),0))/10000,2) as onlineRenewalPremium,
               ROUND(sum(if(insurance_type = 1, IF(`sales_type` = 0, `settlement_amount`, 0),0))/10000, 2)     as onlineRenewalSettlementAmount,
               rs.law_type,
               rs.law_type_desc
        FROM `bi_settlement_income_regulatory_submit` rs
        WHERE `reconcile_type` = 0
          and `reconcile_time` &gt;= #{beginTime,jdbcType=TIMESTAMP}
          and reconcile_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        GROUP BY `level2_name`, `level3_name`,  law_type
    </select>
</mapper>