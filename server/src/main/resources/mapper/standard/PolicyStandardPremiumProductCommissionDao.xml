<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.standard.dao.PolicyStandardPremiumProductCommissionDao">
    <!--获取代理人扩展信息-->
    <select id="queryProductCommissionList" resultType="com.mpolicy.manage.modules.standard.vo.StandardPremiumProductCommission">
        select
        pc.commission_code,pc.standard_premium_code,pc.premium_rule_code,
        sr.premium_commission_type,
        pc.commission_year,
        pc.commission_rate,
        pc.commission_strategy,
        sr.product_plan,
        sr.applicant_start_age,
        sr.applicant_end_age,
        sr.applicant_gender,
        sr.insured_start_age,
        sr.insured_end_age,
        sr.insured_gender,
        sr.insured_period_type,
        sr.insured_period,
        sr.period_type,
        sr.payment_period,
        sr.payment_period_type
        from
        policy_standard_premium_product sp left join policy_standard_premium_product_rule sr
        on sp.standard_premium_code = sr.standard_premium_code
        left join policy_standard_premium_product_commission pc on sr.premium_rule_code = pc.premium_rule_code and sr.standard_premium_code = pc.standard_premium_code
        ${ew.customSqlSegment}
    </select>

    <select id="queryProductCommissionXlsList" resultType="com.mpolicy.manage.modules.standard.vo.StandardPremiumProductExpXls">
        SELECT
        sp.id,
        sp.standard_premium_code,
        sp.company_code,
        sp.company_name,
        sp.product_code,
        sp.product_name,
        sp.effective_start_date,
        sp.effective_end_date,
        sp.standard_premium_status,
        sp.standard_premium_desc,
        sp.create_time,
        sp.create_user,
        sp.update_time,
        sp.update_user,
        sr.premium_commission_type,
        pc.premium_rule_code,
        pc.commission_code,
        pc.commission_year,
        pc.commission_rate,
        sr.product_plan,
        sr.applicant_start_age,
        sr.applicant_end_age,
        sr.applicant_gender,
        sr.insured_start_age,
        sr.insured_end_age,
        sr.insured_gender,
        sr.insured_period_type,
        sr.insured_period,
        sr.period_type,
        sr.payment_period,
        sr.payment_period_type,
        sr.product_channel_code
        FROM
        policy_standard_premium_product sp
        LEFT JOIN policy_standard_premium_product_rule sr ON sp.standard_premium_code = sr.standard_premium_code
        LEFT JOIN policy_standard_premium_product_commission pc ON sr.premium_rule_code = pc.premium_rule_code
        AND sr.standard_premium_code = pc.standard_premium_code
            ${ew.customSqlSegment}
    </select>
</mapper>