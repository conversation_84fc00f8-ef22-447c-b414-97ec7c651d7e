<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sell.dao.SellProductInfoDao">
    <sql id="product_list">
        <if test="param2.productCode != null and param2.productCode != ''">
            and spi.product_code =#{param2.productCode,jdbcType=VARCHAR}
        </if>
        <if test="param2.portfolioCode != null and param2.portfolioCode != ''">
            and spi.portfolio_code =#{param2.portfolioCode,jdbcType=VARCHAR}
        </if>
        <if test="param2.productName != null and param2.productName != ''">
            and spi.product_name like concat('%',#{param2.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="param2.productStatus != null">
            and  spi.product_status = #{param2.productStatus,jdbcType=INTEGER}
        </if>
        <if test="param2.productType != null and param2.productType != ''">
            and  ipi.portfolio_type =#{param2.productType,jdbcType=VARCHAR}
        </if>
        <if test="param2.clientType != null">
            AND  spi.client_type &amp; #{param2.clientType,jdbcType=INTEGER} = #{param2.clientType,jdbcType=INTEGER}
        </if>
        <if test="param2.beginTime != null">
            and  spi.create_time &gt;= #{param2.beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="param2.endTime != null">
            and  spi.create_time &lt;= #{param2.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="param2.companyName != null and param2.companyName != ''">
            and ic.company_name like concat('%',#{param2.companyName,jdbcType=VARCHAR},'%')
        </if>
        <if test="param2.labelCode != null and param2.labelCode != ''">
            AND splm.label_code  = #{param2.labelCode,jdbcType=VARCHAR}
        </if>
    </sql>
    <!--获取销售商品列表-->
    <select id="findPageList" resultType="com.mpolicy.manage.modules.sell.entity.SellProductListOut">
        SELECT ipi.company_name as companyName,spi.product_code AS productCode,
        spi.product_name AS productName,spi.product_abbreviation AS productAbbreviation,
        spi.portfolio_code AS portfolioCode,ic.company_name AS supplierName,
        ipi.portfolio_type AS  productType,spi.product_status AS  productStatus,
        spi.client_type AS clientType, spi.create_time as createTime,
        spi.rule_upload_status as ruleUploadStatus,ipi.portfolio_name as portfolioName,
        spi.stop_sell_time as productStatusChangeDate
        FROM sell_product_info spi
        LEFT JOIN insurance_portfolio_info ipi ON spi.portfolio_code  = ipi.portfolio_code
        LEFT JOIN insurance_company ic ON ic.company_code = spi.supplier_code
        LEFT JOIN sell_product_label_map splm ON spi.product_code  = splm.product_code
        where spi.deleted = 0
        <include refid="product_list"/>
        GROUP BY spi.product_code ,spi.create_time ORDER BY spi.create_time desc
    </select>
    <!--获取销售产品线详情-->
    <select id="findSellProductInfoByCode" resultType="com.mpolicy.manage.modules.sell.entity.SellProductInfoOut">
        SELECT ipi.company_name as companyName,spi.product_code AS productCode,
        spi.product_name AS productName,spi.product_abbreviation AS productAbbreviation,
        spi.we_chat_mini_product_video_cover AS weChatMiniProductVideoCover,
        spi.we_chat_mini_product_video AS weChatMiniProductVideo,
        spi.we_chat_mini_product_type  AS weChatMiniProductType,
        spi.is_support_h5_continue_insure  AS isSupportH5ContinueInsure,
        spi.continue_insure_url  AS continueInsureUrl,
        spi.portfolio_code AS portfolioCode,ic.company_name AS supplierName,
        spi.product_type AS  productType,spi.product_status AS  productStatus,
        spi.client_type AS clientType, spi.create_time as createTime,spi.product_price_unit AS productPriceUnit,
        spi.rule_upload_status as ruleUploadStatus,spi.product_subtitle AS  productSubtitle,
        ipi.portfolio_name AS portfolioName,spi.insurance_age AS insuranceAge,spi.guarantee_period AS guaranteePeriod,
        spi.product_price AS productPrice,spi.hesitation_period AS hesitationPeriod, spi.is_receipt AS isReceipt,
        spi.is_callback AS isCallback,group_concat(spc.value separator ';') AS applicableGroupList,
        spi.product_label AS productLabel,spi.sort ,spi.agent_sort as agentSort ,spi.sell_mode as sellMode,spi.insure_url_type as insureUrlType,
        spi.insure_url as insureUrl,spi.renewal_url as renewalUrl,spi.supplier_code as supplierCode,spi.product_cover as productCover,
        spi.product_thumbnail as productThumbnail,spi.report_status AS reportStatus,spi.product_details as productDetails,
        spi.product_video as productVideo,spi.product_document_code as productDocumentCode,spi.product_video_poster as productVideoPoster,
        spi.stop_sell_time as stopSellTime,spi.is_real_auth,spi.is_rural as isRural,
        spi.is_bind_agent,spi.insure_status,spi.insure_mode,spi.sell_status,spi.is_rural_hot_sale,
        spi.is_share_plan,spi.is_mobile_verify as isMobileVerify,spi.is_person_review as isPersonReview,spi.is_give as isGive,
        spi.is_renewal as isRenewal,spi.key_word as keyWord,spi.is_level_sale as isLevelSale,spi.is_intelligent_solution as isIntelligentSolution,
        spi.is_recalculate as isRecalculate, spi.invoice_status as invoiceStatus, spi.invoice_template_code as
        invoiceTemplateCode
        ,spi.product_display_block as ProductDisplayBlock,
        spi.display_category as displayCategory,
        spi.operation_model as operationModel,
        spi.input_type as inputType
        FROM sell_product_info spi
        LEFT JOIN insurance_portfolio_info ipi ON spi.portfolio_code  = ipi.portfolio_code
        LEFT JOIN insurance_company ic ON ic.company_code = spi.supplier_code
        LEFT JOIN sell_product_config spc ON spc.product_code = spi.product_code and spc.config_type = 'FOR_THE_CROWD'
        WHERE spi.product_code = #{productCode,jdbcType=VARCHAR}
    </select>
    <!--获取导出销售商品列表-->
    <select id="findExportList" resultType="com.mpolicy.manage.modules.sell.entity.ExportProductListOut">
        SELECT ipi.company_name as companyName,spi.product_code AS productCode,
        spi.product_name AS productName,spi.product_abbreviation AS productAbbreviation,
        spi.portfolio_code AS portfolioCode,ic.company_name AS supplierName,
        ipi.portfolio_type AS  productType,spi.product_status AS  productStatus,
        spi.client_type AS clientType, spi.create_time as createTime,
        spi.rule_upload_status as ruleUploadStatus,ipi.portfolio_name as portfolioName,
        spi.is_rural as isRural
        FROM sell_product_info spi
        LEFT JOIN insurance_portfolio_info ipi ON spi.portfolio_code  = ipi.portfolio_code
        LEFT JOIN insurance_company ic ON ic.company_code = spi.supplier_code
        LEFT JOIN sell_product_label_map splm ON spi.product_code  = splm.product_code
        where spi.deleted = 0
        <include refid="product_list"/>
        GROUP BY spi.product_code ,spi.create_time  ORDER BY spi.create_time desc
    </select>
    <!--获取售卖商品下拉列表-->
    <select id="findSellSelectList" resultType="com.mpolicy.manage.modules.sell.entity.SellSelectListOut">
        SELECT spi.product_code as productCode,spi.product_name AS productName,
        ipi.portfolio_code AS portfolioCode,ipi.portfolio_name AS portfolioName,
        ipi.portfolio_type AS portfolioType
        FROM sell_product_info spi
        LEFT JOIN insurance_portfolio_info ipi ON spi.portfolio_code  = ipi.portfolio_code
        WHERE spi.deleted = 0
            <if test="portfolioType != null and portfolioType != ''">
                and ipi.portfolio_type =#{portfolioType,jdbcType=VARCHAR}
            </if>
            <if test="productStatus != null">
                and spi.product_status =#{productStatus,jdbcType=INTEGER}
            </if>
            <if test="companyCode != null and companyCode != ''">
                and ipi.company_code =#{companyCode,jdbcType=VARCHAR}
            </if>
    </select>
</mapper>
