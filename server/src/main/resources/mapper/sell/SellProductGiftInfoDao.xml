<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sell.dao.SellProductGiftInfoDao">
        <update id="addStock">
            update sell_product_gift_info
            set remaining_gift_quantity = remaining_gift_quantity + #{number}
            where id = #{id}
        </update>

        <update id="subtractStock">
            update sell_product_gift_info
            set remaining_gift_quantity = remaining_gift_quantity - #{number}
            where id = #{id}
            and remaining_gift_quantity >= #{number}
        </update>
</mapper>
