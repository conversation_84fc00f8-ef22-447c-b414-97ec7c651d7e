<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sell.dao.SellProductLabelMapDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.sell.entity.SellProductLabelMapEntity" id="sellProductLabelMapMap">
        <result property="id" column="id"/>
        <result property="productCode" column="product_code"/>
        <result property="labelCode" column="label_code"/>
    </resultMap>

</mapper>