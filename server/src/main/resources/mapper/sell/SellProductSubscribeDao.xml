<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sell.dao.SellProductSubscribeDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.sell.entity.SellProductSubscribeEntity" id="sellProductSubscribeMap">
        <result property="id" column="id"/>
        <result property="productCode" column="product_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="remarks" column="remarks"/>
        <result property="handleStatus" column="handle_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取预约商品信息-->
    <select id="findPageList" resultType="com.mpolicy.manage.modules.sell.entity.SellProductSubscribeListOut">
        SELECT sps.product_code AS productCode,spi.product_name AS productName, sps.agent_code AS agentCode,
        aui.agent_name AS agentName,spi.sell_mode AS sellMode,ipi.company_name AS companyName,
        ipi.portfolio_type AS productType,sps.create_time AS createTime,sps.handle_status AS handleStatus,
        sps.remarks,sps.id
        FROM sell_product_subscribe sps
        LEFT JOIN agent_user_info aui ON  sps.agent_code = aui.agent_code
        LEFT JOIN sell_product_info spi ON sps.product_code = spi.product_code
        LEFT JOIN insurance_portfolio_info ipi ON spi.portfolio_code = ipi.portfolio_code
        <where>
            <if test="param2.sellMode != null and param2.sellMode != ''">
                and spi.sell_mode = #{param2.sellMode,jdbcType=VARCHAR}
            </if>
            <if test="param2.productName != null and param2.productName != ''">
                and spi.product_name like CONCAT('%' ,#{param2.productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="param2.handleStatus != null and param2.handleStatus != ''">
                and sps.handle_status =#{param2.handleStatus,jdbcType=VARCHAR}
            </if>
            <if test="param2.agentName != null and param2.agentName != ''">
                and aui.agent_name like CONCAT('%' ,#{param2.agentName,jdbcType=VARCHAR},'%')
            </if>
            <if test="param2.beginTime != null">
                and sps.create_time &gt;= #{param2.beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="param2.endTime != null">
                and sps.create_time &lt;= #{param2.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="param2.orgList !=null and param2.orgList.size() >0 ">
                AND aui.org_code in
                <foreach collection="param2.orgList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        ORDER BY sps.id desc
    </select>


</mapper>