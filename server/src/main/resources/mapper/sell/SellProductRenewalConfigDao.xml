<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sell.dao.SellProductRenewalConfigDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.sell.entity.SellProductRenewalConfigEntity" id="sellProductRenewalConfigMap">
        <result property="id" column="id"/>
        <result property="renewalChannel" column="renewal_channel"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="sourceProductCode" column="source_product_code"/>
        <result property="sourceProductName" column="source_product_name"/>
        <result property="endBeforeDay" column="end_before_day"/>
        <result property="endAfterDay" column="end_after_day"/>
        <result property="renewalProductCode" column="renewal_product_code"/>
        <result property="renewalProductName" column="renewal_product_name"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>