<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sys.dao.SysUserDao">
	<!-- 查询用户的所有权限 -->
	<select id="queryAllPerms" resultType="string">
		select m.perms from sys_user_role ur 
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id 
			LEFT JOIN sys_menu m on rm.menu_id = m.menu_id 
		where ur.user_id = #{userId}
	</select>
	
	<!-- 查询用户的所有菜单ID --> 
	<select id="queryAllMenuId" resultType="long">
		select distinct rm.menu_id from sys_user_role ur 
			LEFT JOIN sys_role_menu rm on ur.role_id = rm.role_id 
		where ur.user_id = #{userId}
	</select>
	
	<select id="queryByMobile" resultType="com.mpolicy.manage.modules.sys.entity.SysUserEntity">
		select * from sys_user where mobile = #{mobile}
	</select>

</mapper>