<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sys.dao.SysRegionInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.sys.entity.SysRegionInfo" id="sysRegionInfo">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="provinceCode" column="province_code"/>
        <result property="provinceName" column="province_name"/>
    </resultMap>


</mapper>