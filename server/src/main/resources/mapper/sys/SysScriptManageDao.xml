<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sys.dao.SysScriptManageDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.sys.entity.SysScriptManageEntity" id="sysScriptManageMap">
        <result property="id" column="id"/>
        <result property="scriptCode" column="script_code"/>
        <result property="scriptType" column="script_type"/>
        <result property="scriptTitle" column="script_title"/>
        <result property="scriptMethod" column="script_method"/>
        <result property="code" column="code"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findSysScriptManageList" resultType="com.mpolicy.manage.modules.sys.vo.SysScriptManageListOut">
        SELECT id,
               script_code   AS scriptCode,
               script_type   AS scriptType,
               script_title  AS scriptTitle,
               script_method AS scriptMethod,
               code,
               remark,
               update_user   AS updateUser,
               update_time   AS updateTime
        FROM sys_script_manage
        where deleted = 0
        <if test="input.scriptCode != null and input.scriptCode != ''">
            and script_code = #{input.scriptCode,jdbcType=VARCHAR}
        </if>
        <if test="input.scriptType != null">
            and script_type=#{input.scriptType,jdbcType=INTEGER}
        </if>
        <if test="input.scriptTitle != null and input.scriptTitle != ''">
            and script_title like concat('%',#{input.scriptTitle,jdbcType=VARCHAR},'%')
        </if>
    </select>
</mapper>