<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.sys.dao.SysDocumentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.sys.entity.SysDocumentEntity" id="sysDocumentMap">
        <result property="id" column="id"/>
        <result property="fileCode" column="file_code"/>
        <result property="fileSystem" column="file_system"/>
        <result property="fileModule" column="file_module"/>
        <result property="fileType" column="file_type"/>
        <result property="fileName" column="file_name"/>
        <result property="fileExt" column="file_ext"/>
        <result property="fileSize" column="file_size"/>
        <result property="filePath" column="file_path"/>
        <result property="domainPath" column="domain_path"/>
        <result property="relationCode" column="relation_code"/>
        <result property="relationStatus" column="relation_status"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <update id="updateFileActive">
      update sys_document t
      set
          t.relation_status=1,
          t.update_time=now()
      where 1=1
          and t.file_code=#{fileCode}
          and t.relation_status=0
    </update>
</mapper>
