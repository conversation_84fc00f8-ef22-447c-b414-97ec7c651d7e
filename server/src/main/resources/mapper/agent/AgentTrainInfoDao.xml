<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentTrainInfoDao">

    <!--获取培训信息表列表-->
    <select id="findPageList" resultType="com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListOut">
        SELECT ati.sign_status,ati.train_code,ati.train_content,ati.train_duration,
        ati.train_topic,GROUP_CONCAT(aui.agent_name) AS trainHostName,ati.begin_time,ati.end_time,
        ati.id
        FROM agent_train_info ati
        LEFT JOIN agent_train_host ath ON ati.train_code = ath.train_code
        LEFT JOIN agent_user_info aui ON ath.agent_code = aui.agent_code
        WHERE  deleted = 0
             <if test="params.trainHost != null and params.trainHost != ''">
                AND ath.agent_code =#{params.trainHost,jdbcType=VARCHAR}
            </if>
            <if test="params.trainTopic != null and params.trainTopic != ''">
                AND ati.train_topic like CONCAT('%',#{params.trainTopic,jdbcType=VARCHAR},'%')
            </if>
            <if test="params.beginTime != null">
                AND ati.begin_time &gt;= #{params.beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                AND ati.end_time &lt;= #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.isTemp != null">
                AND ati.is_temp = #{params.isTemp,jdbcType=INTEGER}
            </if>
        GROUP BY ati.id ORDER BY ati.id DESC
    </select>

    <!--获取代理人培训时长-->
    <select id="findAgentTrainDuration" resultType="com.mpolicy.manage.modules.agent.vo.train.AgentTrainDurationOut">
        SELECT SUM(ati.train_duration) as sumTrainDuration
        FROM agent_train_sign ats
        LEFT JOIN  agent_train_info ati ON ati.train_code = ats.train_code
        WHERE ati.deleted =0 AND ats.assessment_result =1 AND ats.sign_status = 1
        AND ats.agent_code =#{agentCode,jdbcType=VARCHAR}
    </select>

</mapper>