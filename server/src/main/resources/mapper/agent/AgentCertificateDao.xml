<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentCertificateDao">
    <!--代理人证件列表-->
    <select id="findAgentCertificateList" resultType="com.mpolicy.manage.modules.agent.vo.agentinfo.AgentCertificateVo">
        SELECT ac.identification_type ,ac.certificate_num ,ac.start_date ,
        ac.end_date,ac.long_term
        FROM agent_certificate  ac
        where ac.deleted = 0 and ac.agent_code =#{agentCode,jdbcType=VARCHAR}
    </select>
</mapper>