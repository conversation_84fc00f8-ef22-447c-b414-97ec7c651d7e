<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentUserInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity" id="agentUserInfoMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="orgCode" column="org_code"/>
        <result property="gender" column="gender"/>
        <result property="agentNickName" column="agent_nick_name"/>
        <result property="avatar" column="avatar"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="agentName" column="agent_name"/>
        <result property="pinyin" column="pinyin"/>
        <result property="idType" column="id_type"/>
        <result property="idCard" column="id_card"/>
        <result property="birthday" column="birthday"/>
        <result property="entryDate" column="entry_date"/>
        <result property="position" column="position"/>
        <result property="degree" column="degree"/>
        <result property="address" column="address"/>
        <result property="cityCode" column="city_code"/>
        <result property="marital" column="marital"/>
        <result property="politics" column="politics"/>
        <result property="agentStatus" column="agent_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
        <result property="serviceAttribute" column="service_attribute"/>
        <result property="expertise" column="expertise"/>
        <result property="agentLabel" column="agent_label"/>
        <result property="serviceNum" column="service_num"/>
        <result property="workTime" column="work_time"/>
        <result property="workingTime" column="working_time"/>
        <result property="weChat" column="we_chat"/>
        <result property="introduce" column="introduce"/>
        <result property="nation" column="nation"/>
        <result property="appletsCode" column="applets_code"/>
        <result property="school" column="school"/>
        <result property="areaCode" column="area_code"/>
        <result property="initServiceNum" column="init_service_num"/>
        <result property="hot" column="hot"/>
    </resultMap>
    <sql id="agent_list">
        <if test="input.agentName !=null and input.agentName !='' ">
            AND aui.agent_name like concat('%',#{input.agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.certificateNum !=null and input.certificateNum !='' ">
            AND ae.certificate_num like concat('%',#{input.certificateNum,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.mobile !=null and input.mobile !='' ">
            AND aui.mobile = #{input.mobile,jdbcType=VARCHAR}
        </if>
        <if test="input.businessCode !=null and input.businessCode !='' ">
            AND aui.business_code = #{input.businessCode,jdbcType=VARCHAR}
        </if>
        <if test="input.agentStatus !=null">
            AND aui.agent_status = #{input.agentStatus,jdbcType=INTEGER}
        </if>
        <if test="input.quitStatus !=null">
            AND aui.quit_status = #{input.quitStatus,jdbcType=INTEGER}
        </if>
        <if test="input.orgList !=null and input.orgList.size() >0 ">
            AND aui.org_code in
            <foreach collection="input.orgList" item="orgCode" open="(" close=")" separator=",">
                #{orgCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="input.acquisitionAreas !=null and input.acquisitionAreas.size() >0 ">
            AND aui.acquisition_area in
            <foreach collection="input.acquisitionAreas" item="acquisitionArea" open="(" close=")" separator=",">
                #{acquisitionArea,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>
    <!--获取代理人详情信息-->
    <select id="findAgentInfo" resultType="com.mpolicy.manage.modules.agent.vo.agentinfo.AgentUserInfoVo">
        SELECT aui.avatar,aui.agent_type ,aui.agent_name ,aui.gender ,aui.marital ,aui.politics ,aui.mobile,
        aui.school ,aui.email ,aui.id_type ,aui.id_card ,aui.degree ,aui.we_chat ,aui.recommend_status ,
        live.province_code as liveProvinceCode,live.city_code AS liveCityCode,serve.province_code as serverProvince,
        aui.area_code ,aui.city_code as cityCode,aui.address ,aui.service_attribute ,aui.position ,aui.entry_date ,aui.org_code ,
        aui.business_code ,aui.acquisition_area ,aui.hot ,aui.agent_status ,aui.recruit_code ,aui.work_time ,
        aui.init_service_num ,aui.service_customer_num ,aui.expertise ,aui.agent_label ,aui.introduce,
        aui.is_optional,aui.quit_status,aui.quit_time ,aui.country,aui.agent_nature,aui.agent_category,
        aui.revision,aui.nation,aui.area_manager_region,aui.agent_nick_name,aui.id_start_date ,aui.id_end_date,
        aui.id_long_term,aui.position_degree,aui.subregion,aui.manager_region_remark
        FROM agent_user_info aui
        LEFT JOIN sys_region_info live ON aui.area_code = live.`code`
        LEFT JOIN sys_region_info serve ON aui.city_code = serve.city_code
        where aui.agent_code =#{agentCode,jdbcType=VARCHAR}
        GROUP BY aui.id
    </select>

    <!--分页获取经纪人用户信息表列表-->
    <select id="findAgentPageList" resultType="com.mpolicy.manage.modules.agent.entity.AgentPageListOut">
        SELECT aui.agent_code,aui.agent_name,aui.business_code,aui.quit_status,aui.agent_status,
        aui.mobile,aui.id_card,aui.id_type,aui.entry_date,aui.entry_type,aui.org_code,raui.agent_name as recruitName,
        aui.position,oi.org_name,DATE_FORMAT(aui.quit_time,'%Y-%m-%d') as quitTime,ae.certificate_num,aui.marketing_sign_status,
        aui.acquisition_area as acquisitionArea
        FROM agent_user_info aui
        LEFT JOIN agent_user_info raui on aui.recruit_code = raui.agent_code
        LEFT JOIN org_info oi on aui.org_code = oi.org_code
        LEFT JOIN agent_extend ae on aui.agent_code = ae.agent_code
        <where>
           <include refid="agent_list"/>
        </where>
        ORDER BY aui.quit_status ,aui.agent_status desc ,aui.id desc
    </select>

    <!--获取导出数据列表-->
    <select id="findExportList" resultType="com.mpolicy.manage.modules.agent.vo.AgentUserInfoExportVo">
        select aui.agent_code,aui.agent_name,aui.business_code,aui.gender,aui.degree,
        aui.nation,aui.politics,aui.birthday,aui.id_type,aui.id_card,aui.mobile,aui.email,
        ae.band_card_num,sri.province_name band_location_province,sri.city_name band_location_city,ae.band_name,
        ae.band_branch_name,aui.agent_type,oi.org_name,aui.acquisition_area,aui.agent_status,
        DATE_FORMAT(aui.entry_date,'%Y-%m-%d') AS entryDate,aui2.business_code recruitBusinessCode,
        aui2.agent_name recruitName,ae.certificate_num,aui.quit_status as quitStatus,
        DATE_FORMAT(ae.start_date,'%Y-%m-%d') AS startDate,aui.agent_nick_name,aui.country,ae.bank_number as bankNumber,
        DATE_FORMAT(aui.quit_time,'%Y-%m-%d') as quitTime,aui.position,aui.agent_nature as agentNature,
        DATE_FORMAT(aui.id_start_date ,'%Y-%m-%d') as idStartDate,IF(aui.id_long_term =0, DATE_FORMAT(aui.id_end_date,'%Y-%m-%d') ,'长期有效') AS idEndDate,
        aui.manager_region_remark,aui.area_manager_region,aui.position_degree,
        asi.zhihu_name,asi.weibo_name,asi.wechat_public_platform_name,asi.douyin_name,
        case when aui.marketing_sign_status = 0 then '未签约' when aui.marketing_sign_status = 2 then '已签约' end marketingSignStatus
        from agent_user_info aui
        left join agent_user_info aui2 on aui.recruit_code = aui2.agent_code
        left join org_info oi on aui.org_code = oi.org_code
        left join agent_extend ae on aui.agent_code = ae.agent_code
        left join (select distinct province_code, province_name, city_name, city_code from sys_region_info) sri
        on ae.band_location_code = sri.city_code
        left join agent_studio_ip asi on aui.agent_code = asi.agent_code
        <where>
            <include refid="agent_list"/>
        </where>
        ORDER BY aui.quit_status ,aui.agent_status desc ,aui.id desc
    </select>

    <select id="listFastAgentUserInfo" resultType="com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo">
        select
            agent_code,
            agent_name,
            a.org_code,
            b.org_name,
            agent_type,
            area_manager_region,
            agent_status,
            business_code
        from agent_user_info a left join org_info b on a.org_code=b.org_code
        where 1=1
        <if test="agentCodeList!=null and agentCodeList.size>0">
            and a.agent_code in
            <foreach collection="agentCodeList" separator="," open="(" close=")" item="code">
                #{code}
            </foreach>
        </if>
    </select>
    <!--获取代理人使用情况列表-->
    <select id="findPageAgentUseAppList" resultType="com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut">
        SELECT aui.business_code,aui.agent_name, aui.agent_code,aui.quit_status,aui.position,
               aui.acquisition_area,aui.entry_date,aui.org_code
        FROM agent_user_info aui
        WHERE aui.agent_status in(0,1)
        <if test="input.agentName != null and input.agentName != ''">
            and aui.agent_name like concat('%',#{input.agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.acquisitionArea != null and input.acquisitionArea != ''">
            and aui.acquisition_area = #{input.acquisitionArea,jdbcType=VARCHAR}
        </if>
        <if test="input.agentCode != null and input.agentCode != ''">
            and aui.agent_code = #{input.agentCode,jdbcType=VARCHAR}
        </if>
        <if test="input.businessCode != null and input.businessCode != ''">
            and aui.business_code = #{input.businessCode,jdbcType=VARCHAR}
        </if>
        <if test="input.position != null and input.position != ''">
            and aui.position = #{input.position,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryOne" resultType="com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo">
        select
            agent_code,
            agent_name,
            a.org_code,
            b.org_name,
            agent_type,
            area_manager_region,
            agent_status,
            business_code
        from agent_user_info a left join org_info b on a.org_code=b.org_code
        where agent_code=#{agentCode}
    </select>

    <select id="findOnlineAgentCodeList" resultType="java.lang.String">
        select
            DISTINCT (t.`agent_code`)
        from
            `agent_user_info` t
        where
            t.`entry_date` <![CDATA[<=]]> #{endTime}
            and t.`quit_status` = 0
        union
        select DISTINCT
        (t.`agent_code`)
        from
        `agent_user_info` t
        where
        (
        (
        t.`entry_date` <![CDATA[<]]> #{endTime}
        and t.`quit_time` <![CDATA[>=]]> #{startTime}
        and t.`quit_time` <![CDATA[<=]]> #{endTime}
        )
        or (
        t.`entry_date` <![CDATA[<=]]> #{endTime}
        and t.`quit_time` <![CDATA[>]]> #{endTime}
        )
        )
        and t.`quit_status` = 1
    </select>
    <select id="listByBusinessCode" resultType="com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo">
        select
        agent_code,
        agent_name,
        a.org_code,
        b.org_name,
        agent_type,
        area_manager_region,
        agent_status,
        business_code
        from agent_user_info a left join org_info b on a.org_code=b.org_code
        where deleted=0
        <if test="businessCodeList!=null and businessCodeList.size>0">
            and a.business_code in
            <foreach collection="businessCodeList" separator="," open="(" close=")" item="code">
                #{code}
            </foreach>
        </if>
    </select>
</mapper>
