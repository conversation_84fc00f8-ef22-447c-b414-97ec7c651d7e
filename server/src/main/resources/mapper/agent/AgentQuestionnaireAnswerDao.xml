<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentQuestionnaireAnswerDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireAnswerEntity" id="agentQuestionnaireAnswerMap">
        <result property="id" column="id"/>
        <result property="questionId" column="question_id"/>
        <result property="answer" column="answer"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>