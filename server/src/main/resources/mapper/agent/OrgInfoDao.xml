<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.OrgInfoDao">

    <!--获取组织管理列表-->
    <select id="findOrgInfoList" resultType="com.mpolicy.manage.modules.agent.vo.orginfo.OrgPageListOut">
        SELECT oi.org_code,oi.id,oi.org_type,oi.org_level,oi.org_name,
        oi.org_addr,poi.org_name AS orgSuperiorName,sri.province_name,
		sri.city_name,oi.org_superior_code,oi.org_status
        FROM org_info oi
        LEFT JOIN org_info poi ON oi.org_superior_code = poi.org_code
        LEFT JOIN sys_region_info sri ON oi.org_city = sri.city_code
        WHERE  oi.deleted = 0
        <if test="params.orgName !=null and params.orgName !=''">
            and oi.org_name like CONCAT('%',#{params.orgName,jdbcType=VARCHAR},'%')
        </if>
        <if test="params.orgCode !=null and params.orgCode !=''">
            and oi.org_code = #{params.orgCode,jdbcType=VARCHAR}
        </if>
        <if test="params.provinceCode !=null and params.provinceCode!=''">
             and sri.province_code = #{params.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="params.cityCode !=null and params.cityCode!=''">
            and oi.org_city = #{params.cityCode,jdbcType=VARCHAR}
        </if>
        <if test="params.orgStatus !=null and params.orgStatus!=''">
            and oi.org_status = #{params.orgStatus,jdbcType=INTEGER}
        </if>
        GROUP BY oi.org_status , oi.id
    </select>

    <!--获取组织详情-->
    <select id="findOrgInfoByCode" resultType="com.mpolicy.manage.modules.agent.vo.orginfo.OrgInfoOut">
        SELECT oi.org_code,oi.id,oi.org_type,oi.org_level,oi.org_name,
        oi.org_addr,sri.province_code as orgProvince,oi.org_city,oi.org_type,
        oi.principal_name,oi.contact_name,oi.contact_tel,oi.org_status,
        oi.org_superior_code,oi.revision,oi.org_register_addr,oi.org_licence,oi.org_scope
        FROM org_info oi
        LEFT JOIN sys_region_info sri ON oi.org_city = sri.city_code
        WHERE  oi.deleted = 0 AND oi.org_code = #{orgCode,jdbcType=VARCHAR}
        GROUP BY oi.id
    </select>


</mapper>