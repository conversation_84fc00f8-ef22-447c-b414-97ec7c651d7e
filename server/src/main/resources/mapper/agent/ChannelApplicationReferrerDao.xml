<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.ChannelApplicationReferrerDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity"
               id="channelApplicationReferrerMap">
        <result property="id" column="id"/>
        <result property="applicationCode" column="application_code"/>
        <result property="branchCode" column="branch_code"/>
        <result property="referrerCode" column="referrer_code"/>
        <result property="referrerName" column="referrer_name"/>
        <result property="referrerMobile" column="referrer_mobile"/>
        <result property="referrerRegion" column="referrer_region"/>
        <result property="filePath" column="file_path"/>
        <result property="enabled" column="enabled"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <select id="listFastEntity" resultType="com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer">
        SELECT
            a.referrer_code,
            a.branch_code,
            b.branch_name,
            a.referrer_code,
            a.referrer_name,
            a.referrer_wno,
            a.channel_branch_code,
            a.channel_code,
            c.channel_name,
            a.referrer_ogr_code,
            a.referrer_ogr_name
        FROM `channel_application_referrer` a
        LEFT JOIN channel_branch_info b ON a.branch_code=b.branch_code
        left join channel_info c on a.channel_code=c.channel_code
        where 1=1
        <if test="referrerCodeList!=null and referrerCodeList.size>0">
            and a.referrer_code in
            <foreach collection="referrerCodeList" separator="," item="code" open="(" close=")">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="listByJobNumber" resultType="com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer">
        SELECT
        a.referrer_code,
        a.branch_code,
        b.branch_name,
        a.referrer_code,
        a.referrer_name,
        a.referrer_wno,
        a.channel_branch_code,
        a.channel_code,
        c.channel_name,
        a.referrer_ogr_code,
        a.referrer_ogr_name
        FROM `channel_application_referrer` a
        LEFT JOIN channel_branch_info b ON a.branch_code=b.branch_code and b.deleted=0
        left join channel_info c on a.channel_code=c.channel_code and c.deleted=0
        where a.deleted=0
        <if test="jobNumberList!=null and jobNumberList.size>0">
            and a.referrer_wno in
            <foreach collection="jobNumberList" separator="," item="code" open="(" close=")">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="queryOne" resultType="com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer">
        SELECT
        a.referrer_code,
        a.branch_code,
        b.branch_name,
        a.referrer_code,
        a.referrer_name,
        a.referrer_wno,
        a.channel_branch_code,
        a.channel_code,
        c.channel_name,
        a.referrer_ogr_code,
        a.referrer_ogr_name
        FROM `channel_application_referrer` a
        LEFT JOIN channel_branch_info b ON a.branch_code=b.branch_code
        left join channel_info c on a.channel_code=c.channel_code
        where a.referrer_code=#{referrerCode}
    </select>

</mapper>