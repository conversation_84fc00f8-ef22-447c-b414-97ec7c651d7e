<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentAccessoryDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentAccessoryEntity" id="agentAccessoryMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="identificationType" column="identification_type"/>
        <result property="suffix" column="suffix"/>
        <result property="filePath" column="file_path"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>
    <select id="listGroupByType" resultType="com.mpolicy.manage.modules.agent.vo.resp.AgentAccessoryRespVo">
        SELECT COUNT(id) fileCount, GROUP_CONCAT(distinct `suffix`) suffix, `create_time`, identification_type
        FROM `agent_accessory`
        WHERE `agent_code` = #{agentCode}
          AND deleted = 0
        GROUP BY `identification_type`
    </select>


</mapper>