<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentSignFileCompanyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentSignFileCompanyEntity" id="agentSignFileCompanyMap">
        <result property="id" column="id"/>
        <result property="fileCode" column="file_code"/>
        <result property="agentType" column="agent_type"/>
        <result property="orgCode" column="org_code"/>
        <result property="topOrgCode" column="top_org_code"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="detailPageList" resultType="com.mpolicy.manage.modules.agent.vo.sign.AgentSignFileDetailPageList">
        SELECT
            asfc.file_code as fileCode,
            asfc.idList,
            asfc.org_code as orgCode,
            asfc.top_org_code as topOrgCode,
            oi.org_name as orgName,
            asf.enabled as enabled,
            asf.file_name as fileName
        FROM
            (SELECT
                 file_code,
                group_concat(id) as idList,
                 org_code,
                 top_org_code
             FROM
                 agent_sign_file_company where file_code = #{input.fileCode,jdbcType=VARCHAR}
             GROUP BY org_code) asfc
                LEFT JOIN org_info oi ON asfc.top_org_code = oi.org_code
                LEFT JOIN agent_sign_file asf ON asf.file_code = asfc.file_code
        <where>
            <if test="input.fileName !=null and input.fileName !='' ">
                AND asf.file_name like concat('%',#{input.fileName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.orgCode !=null and input.orgCode !='' ">
                AND asfc.top_org_code = #{input.orgCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY asf.create_time DESC
    </select>
</mapper>