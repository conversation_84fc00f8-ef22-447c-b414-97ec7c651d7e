<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentSignFileDetailDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentSignFileDetailEntity" id="agentSignFileDetailMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="orgCode" column="org_code"/>
        <result property="companyId" column="company_id"/>
        <result property="companyOrgCode" column="company_org_code"/>
        <result property="fileCode" column="file_code"/>
        <result property="filePath" column="file_path"/>
        <result property="signTime" column="sign_time"/>
        <result property="signStatus" column="sign_status"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="personFileList" resultType="com.mpolicy.manage.modules.agent.vo.sign.AgentPersonFilePageList">
        SELECT
            aui.agent_code,
            aui.org_code,
            aui.agent_name,
            aui.agent_nick_name,
            aui.quit_status,
            aui.agent_type,
            ae.certificate_num,
            (SELECT COUNT(*) FROM agent_sign_file_detail WHERE sign_status= 1 AND agent_code = a.agent_code) AS alreadyNum
        FROM
            (SELECT
                agent_code,
                COUNT(*) AS num
            FROM
                agent_sign_file_detail
            GROUP BY agent_code) a
                LEFT JOIN agent_user_info aui
                    ON a.agent_code = aui.agent_code
                LEFT JOIN agent_extend ae
                    ON aui.agent_code = ae.agent_code
        <where>
            <if test="input.agentName !=null and input.agentName !='' ">
                AND aui.agent_name like concat('%',#{input.agentName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.agentType !=null and input.agentType !='' ">
                AND aui.agent_type = #{input.agentType,jdbcType=VARCHAR}
            </if>
            <if test="input.orgCodeList !=null and input.orgCodeList.size() >0 ">
                AND aui.org_code in
                <foreach collection="input.orgCodeList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        ORDER BY alreadyNum DESC,aui.id DESC
    </select>

    <select id="detailPersonFileList" resultType="com.mpolicy.manage.modules.agent.vo.sign.AgentPersonDetailPageList">
        select
            asf.file_code,
            asf.file_name,
            asfd.agent_code,
            asfd.sign_status,
            asfd.sign_time,
            asfd.file_path
        from
            agent_sign_file_detail asfd
        LEFT JOIN agent_sign_file asf on asfd.`file_code` = asf.`file_code`
        <where>
            asfd.agent_code = #{input.agentCode,jdbcType=VARCHAR}
            and asf.enabled = 1
            <if test="input.fileName !=null and input.fileName !='' ">
                AND asf.file_name like concat('%',#{input.fileName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.signStatus !=null and input.signStatus !='' ">
                <if test="input.signStatus == 0">
                    AND asfd.sign_status is null
                </if>
                <if test="input.signStatus != 0">
                    AND asfd.sign_status = #{input.signStatus,jdbcType=INTEGER}
                </if>
            </if>
            <if test="input.signTime !=null and input.signTime !='' ">
                AND DATE_FORMAT(asfd.sign_time,'%Y-%m-%d') = #{input.signTime,jdbcType=VARCHAR}
            </if>
        </where>
            order by asfd.sign_time desc
    </select>

    <select id="queryAllEnabledFile" resultType="com.mpolicy.manage.modules.agent.entity.AgentSignFileDetailEntity">
        SELECT
            asfd.*
        FROM
            agent_sign_file_detail asfd
                LEFT JOIN agent_sign_file asf
                          ON asfd.file_code = asf.file_code
        WHERE asf.enabled = 1 and asfd.agent_code = #{agentCode,jdbcType=VARCHAR}
    </select>
</mapper>