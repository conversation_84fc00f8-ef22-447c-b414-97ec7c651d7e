<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentExtendDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentExtendEntity" id="agentExtendMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="certificateNum" column="certificate_num"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="bandLocationCode" column="band_location_code"/>
        <result property="bandName" column="band_name"/>
        <result property="bandBranchName" column="band_branch_name"/>
        <result property="bandCardNum" column="band_card_num"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <!--获取代理人扩展信息-->
    <select id="findAgentExtendByCode" resultType="com.mpolicy.manage.modules.agent.vo.agentinfo.AgentExtendVo">
        SELECT ae.certificate_num ,ae.start_date ,ae.end_date ,ae.band_location_code ,
        sri.province_code AS bandProvinceCode,ae.band_name ,ae.band_branch_name ,ae.honor,
        ae.band_card_num ,ae.`profile`,IF (ae.long_term = 1,true,false ) AS longTerm,ae.bank_number as bankNumber
        FROM agent_extend ae
        LEFT JOIN sys_region_info sri ON ae.band_location_code = sri.city_code
        WHERE ae.agent_code = #{agentCode,jdbcType=VARCHAR}
        GROUP BY ae.id
    </select>


</mapper>