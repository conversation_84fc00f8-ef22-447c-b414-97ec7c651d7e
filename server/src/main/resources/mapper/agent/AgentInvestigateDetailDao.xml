<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentInvestigateDetailDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentInvestigateDetailEntity" id="agentInvestigateDetailMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="questionnaireId" column="questionnaire_id"/>
        <result property="questionId" column="question_id"/>
        <result property="answerId" column="answer_id"/>
        <result property="isSelected" column="is_selected"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>