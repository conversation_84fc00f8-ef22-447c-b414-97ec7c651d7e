<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentQuestionnaireDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity" id="agentQuestionnaireMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="publishTime" column="publish_time"/>
        <result property="investigateStartTime" column="investigate_start_time"/>
        <result property="investigateEndTime" column="investigate_end_time"/>
        <result property="finishCnt" column="finish_cnt"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <select id="queryQuestionnairePage" parameterType="java.util.Map" resultType="com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionnaireOut">
        select
        a.id as id,
        a.`title` as title,
        DATE_FORMAT(publish_time,'%Y-%m-%d %H:%i:%s') as publish_time,
        DATE_FORMAT(investigate_start_time,'%Y-%m-%d %H:%i:%s') as investigate_start_time,
        DATE_FORMAT(investigate_end_time,'%Y-%m-%d %H:%i:%s') as investigate_end_time,
        count(*) as needInvestigateCnt,
        sum( case when c.id is null then 0 else 1 end ) as submitCnt
        from
        `bl_agent_questionnaire` a
        LEFT JOIN <include refid="joinQueryOnlineAgentCode"></include>
        LEFT JOIN (select * from `bl_agent_investigate_detail` gp  GROUP BY gp.`questionnaire_id`,gp.`agent_code` ) c on t.`agent_code` = c.`agent_code`  and a.id = c.`questionnaire_id` and c.deleted = 0 and a.id = c.`questionnaire_id`
        where
        1 = 1
        and a.deleted = 0
        <if test="params.title != null and params.title != ''">
            and a.title like concat('%',#{params.title,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY a.`id`
        order by a.create_time desc
    </select>

    <select id="queryAgentAnswerPage" resultType="com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionAnswerDetail">
        select
        aq.id,
        aq.`title`,
        b.`answer`,
        IFNULL(d.`is_selected`, 0) as is_selected,
        aq.`type`
        from
        `bl_agent_questionnaire_question` aq
        LEFT JOIN `bl_agent_questionnaire` a on a.id = aq.questionnaire_id
        LEFT JOIN <include refid="joinQueryOnlineAgentCode"></include>
        LEFT JOIN bl_agent_questionnaire_answer b on aq.id = b.`question_id` and b.`deleted` = 0
        LEFT JOIN bl_agent_investigate_detail d on b.id = d.`answer_id` and d.`deleted` = 0
        and d.`question_id` = aq.id
        and d.`questionnaire_id` = a.id
        and t.`agent_code` = d.`agent_code`
        where
        aq.`deleted` = 0
        and t.`agent_code` = #{agentCode}
        and aq.`questionnaire_id` = #{questionnaireId}
        order by b.id asc
    </select>

    <select id="details" resultType="com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionAnswerDetail">
        select
        a.`id`,
        a.`questionnaire_id`,
        a.`title`,
        a.`type`,
        b.`answer`
        from
        `bl_agent_questionnaire_question` a
        INNER JOIN `bl_agent_questionnaire_answer` b on a.id = b.`question_id`
        where
        a.`deleted` = 0
        and b.`deleted` = 0
        and a.`questionnaire_id` = #{questionnaireId}
    </select>

    <sql id="joinQueryOnlineAgentCode">
        `agent_user_info` t
        on
        (
            t.`entry_date` <![CDATA[<=]]> a.`investigate_end_time`
            and t.`quit_status` = 0
        )
        or
        (
            (
                (
                    t.`entry_date` <![CDATA[<]]> a.`investigate_end_time`
                    and t.`quit_time` <![CDATA[>]]> a.`investigate_start_time`
                    and t.`quit_time` <![CDATA[<=]]> a.`investigate_end_time`
                )
                or
                (
                    t.`entry_date` <![CDATA[<=]]> a.`investigate_end_time`
                    and t.`quit_time` <![CDATA[>=]]> a.`investigate_end_time`
                )
                or
                (
                    t.`entry_date` = a.`investigate_start_time` and t.`quit_time` = a.`investigate_start_time`
                )
            )
            and t.`quit_status` = 1
        )
    </sql>

    <select id="queryInvestigatePage" parameterType="java.util.Map" resultType="com.mpolicy.manage.modules.agent.vo.questionnaire.AgentInvestigateOut">
        select
        t.`agent_code` as agentCode,
        t.`agent_name` as agentName,
        b.`create_time` as submitTime,
        b.questionnaire_id as questionnaireId,
        ae.`certificate_num` as practiceCertificateCode
        from
        `bl_agent_questionnaire` a
        LEFT JOIN <include refid="joinQueryOnlineAgentCode"></include>
        LEFT JOIN agent_extend ae on t.agent_code = ae.agent_code
        LEFT JOIN (select * from `bl_agent_investigate_detail` gp GROUP BY gp.`questionnaire_id`, gp.`agent_code`  ) b on b.`agent_code` = t.`agent_code` and b.deleted = 0 and a.id = b.`questionnaire_id`
        where
        a.deleted = 0
        and a.`id` = #{params.questionnaireId}
        order by b.create_time desc
    </select>
</mapper>