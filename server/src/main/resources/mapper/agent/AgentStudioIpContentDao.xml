<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentStudioIpContentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentStudioIpContentEntity" id="agentStudioIpContentMap">
        <result property="id" column="id"/>
        <result property="ipContentCode" column="ip_content_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="platform" column="platform"/>
        <result property="releaseDate" column="release_date"/>
        <result property="isVisible" column="is_visible"/>
        <result property="title" column="title"/>
        <result property="contentText" column="content_text"/>
        <result property="filePath" column="file_path"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>