<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentStudioIpDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentStudioIpEntity" id="agentStudioIpMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="zhihuName" column="zhihu_name"/>
        <result property="zhihuFans" column="zhihu_fans"/>
        <result property="zhihuVerified" column="zhihu_verified"/>
        <result property="zhihuTag" column="zhihu_tag"/>
        <result property="weiboName" column="weibo_name"/>
        <result property="weiboFans" column="weibo_fans"/>
        <result property="weiboVerified" column="weibo_verified"/>
        <result property="weiboIntro" column="weibo_intro"/>
        <result property="wechatPublicPlatformName" column="wechat_public_platform_name"/>
        <result property="wechatPublicPlatformIntro" column="wechat_public_platform_intro"/>
        <result property="douyinName" column="douyin_name"/>
        <result property="douyinFans" column="douyin_fans"/>
        <result property="douyinLikeNum" column="douyin_like_num"/>
        <result property="douyinIntro" column="douyin_intro"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <!--获取代理人工作室信息-->
    <select id="findAgentStudioIpByCode" resultType="com.mpolicy.manage.modules.agent.vo.agentinfo.AgentStudioIpVo">
        SELECT asi.studio_tag ,asi.zhihu_name ,asi.zhihu_fans ,asi.zhihu_verified ,asi.zhihu_tag ,
        asi.zhihu_count ,asi.weibo_name ,asi.weibo_fans ,asi.weibo_verified ,asi.weibo_intro ,
        asi.weibo_count ,asi.wechat_public_platform_name ,asi.wechat_public_platform_intro ,
        asi.wechat_public_platform_count ,asi.douyin_name ,asi.douyin_fans ,asi.douyin_like_num ,
        asi.douyin_verified ,asi.douyin_intro ,asi.douyin_count
        FROM agent_studio_ip asi WHERE  asi.agent_code = #{agentCode,jdbcType=VARCHAR}
    </select>


</mapper>