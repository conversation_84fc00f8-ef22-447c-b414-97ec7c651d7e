<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.ChannelInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity" id="channelInfoMap">
        <result property="id" column="id"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="uniformSocialCreditCode" column="uniform_social_credit_code"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="mobile" column="mobile"/>
        <result property="channelType" column="channel_type"/>
        <result property="enabled" column="enabled"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="bandName" column="band_name"/>
        <result property="bandBranchName" column="band_branch_name"/>
        <result property="bandCardNum" column="band_card_num"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>