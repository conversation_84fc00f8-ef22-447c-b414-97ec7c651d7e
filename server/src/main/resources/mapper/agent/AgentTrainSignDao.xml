<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentTrainSignDao">

    <!--获取培训签到列表-->
    <select id="findTrainSignList" resultType="com.mpolicy.manage.modules.agent.vo.train.TrainSignListOut">
        SELECT ats.assessment_result,ats.sign_status,aui.agent_name,ats.agent_code,
        aui.business_code,ats.id
        FROM agent_train_sign  ats
        LEFT JOIN agent_user_info aui ON ats.agent_code = aui.agent_code
        WHERE ats.train_code =#{trainCode,jdbcType=VARCHAR}
    </select>
    <!--更新培训签到数据-->
    <update id="beachUpdateTrainSignList" >
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            UPDATE agent_train_sign SET
            assessment_result= #{item.assessmentResult,jdbcType=INTEGER},
            sign_status = #{item.signStatus,jdbcType=INTEGER}
            WHERE id=#{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <!--批量新增签到人员-->
    <insert id="beachInsertTrainSign">
        INSERT INTO agent_train_sign (agent_code,train_code) VALUES
        <foreach collection="participants" item="agentCode" index="index" close="" open="" separator=",">
            (#{agentCode,jdbcType=VARCHAR},#{trainCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!--获取代理人培训记录-->
    <select id="findPageList" resultType="com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListOut">
        SELECT ati.train_code,ati.train_content,ati.train_topic,ati.begin_time,
        ati.end_time,ati.train_duration,ats.assessment_result
        FROM agent_train_sign ats
        LEFT JOIN agent_train_info ati ON ats.train_code = ati.train_code
        WHERE  ati.deleted =0 AND ats.sign_status = 1
        AND ats.agent_code = #{params.agentCode,jdbcType=VARCHAR}
    </select>

</mapper>