<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentSignFileDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentSignFileEntity" id="agentSignFileMap">
        <result property="id" column="id"/>
        <result property="fileCode" column="file_code"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="enabled" column="enabled"/>
        <result property="enableTime" column="enable_time"/>
        <result property="expireTime" column="expire_time"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="filePageList" resultType="com.mpolicy.manage.modules.agent.vo.sign.AgentSignFilePageList">
        SELECT
            asf.file_code,
            asf.file_name,
            asf.file_path,
            asf.enabled,
            asf.enable_time,
            asf.create_time,
            IFNULL(b.num,0) AS alreadyNum
        FROM
            agent_sign_file asf
                LEFT JOIN
            (SELECT
                 asfd.file_code,
                 COUNT(*) AS num
             FROM
                 agent_sign_file_detail asfd
             WHERE asfd.sign_status = 1
             GROUP BY asfd.file_code) b
            ON asf.file_code = b.file_code
        <where>
            <if test="input.fileName !=null and input.fileName !='' ">
                AND asf.file_name like concat('%',#{input.fileName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.enabled !=null and input.enabled !='' ">
                AND asf.enabled = #{input.enabled,jdbcType=INTEGER}
            </if>
            <if test="input.createTime !=null and input.createTime !='' ">
                AND date_format(asf.create_time,'%Y-%m-%d') = #{input.createTime,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY b.num DESC
    </select>
</mapper>