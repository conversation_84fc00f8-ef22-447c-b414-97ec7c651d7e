<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentUserAccountDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agent.entity.AgentUserAccountEntity" id="agentUserAccountMap">
        <result property="account" column="account"/>
        <result property="accountType" column="account_type"/>
        <result property="salt" column="salt"/>
        <result property="password" column="password"/>
        <result property="agentCode" column="agent_code"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>