<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agent.dao.AgentTrainHostDao">

    <!--更新培训签到数据-->
    <update id="beachUpdateTrainSignList" >
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            UPDATE agent_train_sign SET
            assessment_result= #{item.assessmentResult,jdbcType=INTEGER},
            sign_status = #{item.signStatus,jdbcType=INTEGER}
            WHERE id=#{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <!--批量新增签到人员-->
    <insert id="beachInsertTrainHost">
        INSERT INTO agent_train_host (agent_code,train_code) VALUES
        <foreach collection="trainHostList" item="agentCode" index="index" close="" open="" separator=",">
            (#{agentCode,jdbcType=VARCHAR},#{trainCode,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>