<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.customer.dao.CustomerBasicInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity" id="customerBasicInfoMap">
        <result property="id" column="id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="nickName" column="nick_name"/>
        <result property="realName" column="real_name"/>
        <result property="pinyin" column="pinyin"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="address" column="address"/>
        <result property="age" column="age"/>
        <result property="gender" column="gender"/>
        <result property="appLanguage" column="app_language"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="bindMobileStatus" column="bind_mobile_status"/>
        <result property="mobile" column="mobile"/>
        <result property="bindTime" column="bind_time"/>
        <result property="certificationStatus" column="certification_status"/>
        <result property="certificationType" column="certification_type"/>
        <result property="certificationTime" column="certification_time"/>
        <result property="identificationType" column="identification_type"/>
        <result property="identificationNum" column="identification_num"/>
        <result property="identificationExpiration" column="identification_expiration"/>
        <result property="certiNo" column="certi_no"/>
        <result property="birthday" column="birthday"/>
        <result property="appId" column="app_id"/>
        <result property="weixinId" column="weixin_id"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="provinceCityDistrict" column="province_city_district"/>
        <result property="districtCode" column="district_code"/>
        <result property="district" column="district"/>
        <result property="channelCode" column="channel_code"/>
        <result property="customerStatus" column="customer_status"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
        <result property="oneId" column="one_id"/>
        <result property="blackFlag" column="black_flag"/>
        <result property="blackFlagInner" column="black_flag_inner"/>
    </resultMap>

    <select id="getBasicInfo" resultType="com.mpolicy.manage.modules.customer.vo.CustomerBasicInfoVo">
        select a.customer_code,
               nick_name,
               real_name,
               a.gender,
               a.mobile,
               certification_status,
               certi_no,
               weixin_id,
               open_id,
               union_id,
               customer_source,
               channel_code,
               channel_name,
               b.agent_code,
               agent_name,
               a.create_time,
               policy_number
        from customer_basic_info a
                 left join customer_agent_map b on a.customer_code = b.customer_code and b.deleted = 0
                 left join agent_user_info c on b.agent_code = c.agent_code
        where a.deleted = 0
          and a.customer_code = #{code}
    </select>
    <select id="queryCustomerPage" resultType="com.mpolicy.manage.modules.customer.vo.CustomerBasicInfoVo">
        select a.customer_code,
               nick_name,
               real_name,
               rural_flag,
               a.gender,
               a.mobile,
               a.referrer_code,
               certification_status,
               agent_flag,
               certi_no,
               weixin_id,
               open_id,
               union_id,
               customer_source,
               a.channel_code,
               a.channel_name,
               cancel_status,
               if(b.agent_code is null, 0, 1) bind_status,
               b.agent_code,
               c.agent_name,
               a.create_time,
               a.one_id,
               a.black_flag,
               policy_number,
               car.referrer_name,
               a.referrer_binding_time,
               a.certification_time
        from customer_basic_info a
                 left join channel_application_referrer car on a.referrer_code = car.referrer_code and car.deleted = 0
                 left join customer_agent_map b on a.customer_code = b.customer_code and b.deleted = 0
                 left join agent_user_info c on b.agent_code = c.agent_code
            ${ew.customSqlSegment}
    </select>

    <select id="queryChannelCustomerPage" resultType="com.mpolicy.manage.modules.agent.vo.resp.ChannelCustomerVo">
        select a.customer_code,
               nick_name,
               real_name,
               inner_referrer_flag,
               if(inner_referrer_flag = 1, '是', '否') innerReferrerFlag,
               trade_policy_number,
               a.gender,
               a.mobile customerMobile,
               b.referrer_code,
               referrer_wno,
               referrer_name,
               referrer_region,
               if(referrer_binding_time is null, a.create_time, referrer_binding_time) bindingTime
        from customer_basic_info a
                 left join channel_application_referrer b on a.referrer_code = b.referrer_code and b.deleted = 0
            ${ew.customSqlSegment}
    </select>
    <!--获取客户最大-->
    <select id="findMaxCustomerId" resultType="int">
        SELECT MAX(id) from customer_basic_info where deleted = 0
    </select>
</mapper>
