<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.customer.dao.CustomerAgentMapDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.customer.entity.CustomerAgentMapEntity" id="customerAgentMapMap">
        <result property="id" column="id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="bindSource" column="bind_source"/>
        <result property="bindTime" column="bind_time"/>
        <result property="serviceNumber" column="service_number"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="findCustomerCodeListByAgentCodeAndRegion"
            resultType="com.mpolicy.manage.modules.agent.vo.agentinfo.CustomerCodeListByAgentCodeAndRegionOut">
        SELECT cbi.customer_code,car.referrer_subregion
        FROM `customer_agent_map` cam
        LEFT JOIN `customer_basic_info` cbi on cam.`customer_code`  = cbi.`customer_code` and cbi.`deleted` = 0
        LEFT JOIN `channel_application_referrer` car on cbi.`referrer_code`  = car.`referrer_code` and car.`deleted` = 0
        WHERE cam.`deleted` =0
        and  cam.`agent_code`  = #{agentCode,jdbcType=VARCHAR}
        and car.`referrer_region`  = #{referrerRegion,jdbcType=VARCHAR}

    </select>
    <!--批量插入-->
    <insert id="saveBatch">
        insert into customer_agent_map (customer_code, agent_code,bind_source,bind_time,deleted,create_user,create_time,update_user,update_time) values
        <foreach collection="batchList" item="item" index="index" separator=",">
            (#{item.customerCode,jdbcType=VARCHAR},
            #{item.agentCode,jdbcType=VARCHAR},
            #{item.bindSource,jdbcType=VARCHAR},
            #{item.bindTime,jdbcType=TIMESTAMP},
            #{item.deleted,jdbcType=INTEGER},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUser,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>