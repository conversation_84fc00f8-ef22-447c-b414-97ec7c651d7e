<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.tools.dao.ToolsModelChangeLogDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.tools.entity.ToolsModelChangeLogEntity" id="toolsModelChangeLogMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="change_id" column="changeId"/>
        <result property="change_nbr" column="changeNbr"/>
        <result property="start_data" column="startData"/>
        <result property="end_data" column="endData"/>
        <result property="deleted" column="deleted"/>
        <result property="create_user" column="createUser"/>
        <result property="create_time" column="createTime"/>
        <result property="update_user" column="updateUser"/>
        <result property="update_time" column="updateTime"/>
    </resultMap>


</mapper>