<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.tools.dao.ToolsImportFailureInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.tools.entity.ToolsImportFailureInfoEntity" id="toolsCommonWordsMap">
        <result property="id" column="id"/>
        <result property="operationSign" column="operation_sign"/>
        <result property="failureInformation" column="failure_information"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


    <insert id="insertListBatch" >
        INSERT INTO `tools_import_failure_info` (`operation_sign`,`failure_information`,`create_user`,`create_time`)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.operationSign},#{item.failureInformation},#{item.createUser},#{item.createTime})
        </foreach>
    </insert>
</mapper>