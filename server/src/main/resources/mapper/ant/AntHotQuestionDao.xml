<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.ant.dao.AntHotQuestionDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.ant.entity.AntHotQuestionEntity" id="antHotQuestionMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="question" column="question"/>
        <result property="type" column="type"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="replyContent" column="reply_content"/>
        <result property="askNum" column="ask_num"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>