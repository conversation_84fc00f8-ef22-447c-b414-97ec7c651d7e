<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.ins.dao.InsPolicyInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.ins.entity.InsPolicyInfoEntity" id="insPolicyInfoMap">
        <result property="id" column="id"/>
        <result property="prePolicyId" column="pre_policy_id"/>
        <result property="prePolicyNo" column="pre_policy_no"/>
        <result property="companyCode" column="company_code"/>
        <result property="channelCode" column="channel_code"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="distributionChannelCode" column="distribution_channel_code"/>
        <result property="innerProductCode" column="inner_product_code"/>
        <result property="policyNo" column="policy_no"/>
        <result property="renewalPolicyNo" column="renewal_policy_no"/>
        <result property="originalPolicyNo" column="original_policy_no"/>
        <result property="applyPolicyNo" column="apply_policy_no"/>
        <result property="personApplyPolicyNo" column="person_apply_policy_no"/>
        <result property="masterApplyPolicyNo" column="master_apply_policy_no"/>
        <result property="companyOrderNo" column="company_order_no"/>
        <result property="elPolicyAddress" column="el_policy_address"/>
        <result property="totalNetPremium" column="total_net_premium"/>
        <result property="addedValueTax" column="added_value_tax"/>
        <result property="expectEffectiveTime" column="expect_effective_time"/>
        <result property="expectExpiredTime" column="expect_expired_time"/>
        <result property="realEffectiveTime" column="real_effective_time"/>
        <result property="realExpiredTime" column="real_expired_time"/>
        <result property="payTime" column="pay_time"/>
        <result property="payNo" column="pay_no"/>
        <result property="payUrl" column="pay_url"/>
        <result property="premium" column="premium"/>
        <result property="totalPremium" column="total_premium"/>
        <result property="mobile" column="mobile"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="idType" column="id_type"/>
        <result property="autoDeductionFlag" column="auto_deduction_flag"/>
        <result property="openDeductionTime" column="open_deduction_time"/>
        <result property="protocolNo" column="protocol_no"/>
        <result property="acceptInsuranceStatus" column="accept_insurance_status"/>
        <result property="masterPolicyNo" column="master_policy_no"/>
        <result property="planCode" column="plan_code"/>
        <result property="planName" column="plan_name"/>
        <result property="insuranceProductCode" column="insurance_product_code"/>
        <result property="insuranceClauseCode" column="insurance_clause_code"/>
        <result property="insuranceDutyCode" column="insurance_duty_code"/>
        <result property="renewalTimes" column="renewal_times"/>
        <result property="bcVersion" column="bc_version"/>
        <result property="bcChannelParam" column="bc_channel_param"/>
        <result property="commodityCode" column="commodity_code"/>
        <result property="productType" column="product_type"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createUserType" column="create_user_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateUserType" column="update_user_type"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <select id="queryAcceptMasterProposalNoList" resultType="java.lang.String">
        select
        distinct t.master_apply_policy_no as masterApplyPolicyNo
        from ins_policy_info t
        <where>
            t.accept_insurance_status = 1
            and t.auto_deduction_flag in (1,3)

            <if test="startTime != null">
                <![CDATA[
                and t.update_time >= #{startTime,jdbcType=DATE}
                ]]>
            </if>

            <if test="organizationCode != null and organizationCode != ''">
                and t.company_code >= #{organizationCode,jdbcType=VARCHAR}
            </if>

        </where>
    </select>

</mapper>
