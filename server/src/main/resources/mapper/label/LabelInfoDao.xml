<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.label.dao.LabelInfoDao">

    <!--获取列表数据-->
    <select id="findPageList" resultType="com.mpolicy.manage.modules.label.entity.LabelInfoListOut">
        SELECT li.label_code AS labelCode, li.label_name AS labelName,
        li.label_group AS labelGroup,li.portfolio_type as portfolioType,
        lli.library_code AS libraryCode,lli.library_name as libraryName,
        li.remark,li.create_time as createTime
        FROM label_info li
        LEFT JOIN label_library_info lli ON li.library_code = lli.library_code
        where  li.deleted= 0
        <if test="param2.labelGroup != null and param2.labelGroup != ''">
            and li.label_group =#{param2.labelGroup,jdbcType=VARCHAR}
        </if>
        <if test="param2.libraryCode != null and param2.libraryCode != ''">
            and li.library_code =#{param2.libraryCode,jdbcType=VARCHAR}
        </if>
        <if test="param2.portfolioType != null and param2.portfolioType != ''">
            and li.portfolio_type =#{param2.portfolioType,jdbcType=VARCHAR}
        </if>
        <if test="param2.labelName != null and param2.labelName != ''">
            and li.label_name like CONCAT('%',#{param2.labelName,jdbcType=VARCHAR},'%')
        </if>
         <if test="param2.beginTime != null">
            and li.create_time &gt;= #{param2.beginTime,jdbcType=TIMESTAMP}
        </if>
         <if test="param2.endTime != null ">
            and li.create_time &lt;= #{param2.endTime,jdbcType=TIMESTAMP}
        </if>
    </select>


</mapper>