<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.regulators.dao.RegulatorsMonthReportDao">
    <!--获取数据列表-->
    <select id="queryPage" resultType="com.mpolicy.manage.modules.regulators.vo.RegulatorsMonthReportOut">
        SELECT * FROM regulators_month_report
        <where>
            <if test="input.reportMonth != null and input.reportMonth != ''">
                and report_month = #{input.reportMonth,jdbcType=VARCHAR}
            </if>
        </where>
        order by id desc
    </select>
</mapper>