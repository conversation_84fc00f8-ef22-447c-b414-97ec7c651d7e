<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.regulators.dao.RegulatorsReportInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.regulators.entity.RegulatorsReportInfoEntity" id="regulatorsReportInfoMap">
        <result property="id" column="id"/>
        <result property="regulatorsNo" column="regulators_no"/>
        <result property="regulatorsName" column="regulators_name"/>
        <result property="regulatorsOrgCount" column="regulators_org_count"/>
        <result property="regulatorsYear" column="regulators_year"/>
        <result property="regulatorsMonth" column="regulators_month"/>
        <result property="uploadCompleteStatus" column="upload_complete_status"/>
        <result property="reportStatus" column="report_status"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>