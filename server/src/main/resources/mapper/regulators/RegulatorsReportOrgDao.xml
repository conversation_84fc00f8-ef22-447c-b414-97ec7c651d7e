<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.regulators.dao.RegulatorsReportOrgDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.regulators.entity.RegulatorsReportOrgEntity" id="regulatorsReportOrgMap">
        <result property="id" column="id"/>
        <result property="orgRegulatorsNo" column="org_regulators_no"/>
        <result property="regulatorsNo" column="regulators_no"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="regulatorsYear" column="regulators_year"/>
        <result property="regulatorsMonth" column="regulators_month"/>
        <result property="reportType" column="report_type"/>
        <result property="reportName" column="report_name"/>
        <result property="reportTitle" column="report_title"/>
        <result property="reportData" column="report_data"/>
        <result property="reportFileCode" column="report_file_code"/>
        <result property="reportFilePath" column="report_file_path"/>
        <result property="reportFileName" column="report_file_name"/>
        <result property="reportStatus" column="report_status"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>