<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.article.dao.MaterialBannerInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.article.entity.MaterialBannerInfoEntity" id="materialBannerInfoMap">
        <result property="id" column="id"/>
        <result property="bannerCode" column="banner_code"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="picUrl" column="pic_url"/>
        <result property="jumpParam" column="jump_param"/>
        <result property="needLogin" column="need_login"/>
        <result property="location" column="location"/>
        <result property="status" column="status"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>