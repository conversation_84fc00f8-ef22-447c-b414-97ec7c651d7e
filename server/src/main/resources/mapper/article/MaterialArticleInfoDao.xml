<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.article.dao.MaterialArticleInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.article.entity.MaterialArticleInfoEntity" id="scqArticleInfoMap">
        <result property="id" column="id"/>
        <result property="articleCode" column="article_code"/>
        <result property="title" column="title"/>
        <result property="articleSource" column="article_source"/>
        <result property="content" column="content"/>
        <result property="top" column="top"/>
        <result property="picUrl" column="pic_url"/>
        <result property="realPageView" column="real_page_view"/>
        <result property="initPageView" column="init_page_view"/>
        <result property="addPageView" column="add_page_view"/>
        <result property="shareNum" column="share_num"/>
        <result property="shareContent" column="share_content"/>
        <result property="statement" column="statement"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>