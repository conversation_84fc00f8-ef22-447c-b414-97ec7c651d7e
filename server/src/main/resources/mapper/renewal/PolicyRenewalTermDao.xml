<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.renewal.PolicyRenewalTermDao">

    <sql id="renewalTermList_query">
        <if test="salesType !=null and salesType !='' ">
            and b.sales_type=#{salesType}
        </if>
        <if test="riskName !=null and riskName!='' ">
            and exists (
            select 1 from ep_policy_product_info i
            where b.contract_code = i.contract_code
            and i.product_name like concat('%',concat(#{riskName},'%'))
            )
        </if>
        <if test="policyNo !=null and policyNo !='' ">
            and a.policy_no=#{policyNo}
        </if>
        <if test="companyCodeList !=null and companyCodeList.size>0 ">
            and b.company_code in
            <foreach collection="companyCodeList" item="company" open="(" close=")" separator=",">
                #{company}
            </foreach>
        </if>
        <if test="status !=null and status !='' ">
            and a.status=#{status}
        </if>
        <if test="applicantName !=null and applicantName !='' ">
            and c.applicant_name = #{applicantName}
        </if>
        <if test="applicantIdCard !=null and applicantIdCard !='' ">
            and c.applicant_id_card = #{applicantIdCard}
        </if>
        <if test="insuredName !=null and insuredName !='' ">
            and exists(
                select 1 from ep_policy_insured_info d
                where a.contract_code = d.contract_code and d.deleted=0
                and d.insured_name = #{insuredName}
            )
        </if>
        <if test="insuredIdCard !=null and insuredIdCard !='' ">
            and exists(
                select 1 from ep_policy_insured_info d
                where a.contract_code = d.contract_code and d.deleted=0
                and d.insured_id_card = #{insuredIdCard}
            )
        </if>
        <if test="agentCodeList !=null and agentCodeList.size>0 ">
            and sales_type = '1'
            and b.agent_code in
            <foreach collection="agentCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="channelCodeList !=null and channelCodeList.size>0 ">
            and b.channel_code in
            <foreach collection="channelCodeList" item="channel" open="(" close=")" separator=",">
                #{channel}
            </foreach>
        </if>

        <if test="authChannelBranchCodeList !=null and authChannelBranchCodeList.size>0 ">
            and b.policy_channel_branch_code in
            <foreach collection="authChannelBranchCodeList" item="channel" open="(" close=")" separator=",">
                #{channel}
            </foreach>
        </if>
        <if test="channelDisCodeList !=null and channelDisCodeList.size>0 ">
            and b.channel_distribution_code in
            <foreach collection="channelDisCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="referrerCodeList !=null and referrerCodeList.size>0 ">
            and b.referrer_code in
            <foreach collection="referrerCodeList" item="referrer" open="(" close=")" separator=",">
                #{referrer}
            </foreach>
        </if>
        <if test="orgCodeList !=null and orgCodeList.size>0 ">
            and b.org_code in
            <foreach collection="orgCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="authOrgCodeList !=null and authOrgCodeList.size>0 ">
            and b.org_code in
            <foreach collection="authOrgCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="duePaymentStartTime !=null">
            and a.due_payment_time &gt;= #{duePaymentStartTime}
        </if>
        <if test="duePaymentEndTime !=null">
            and a.due_payment_time &lt;= #{duePaymentEndTime}
        </if>
        <if test="paymentComparison !=null and paymentComparison == 1 ">
            and a.due_payment_amount = a.payment_amount
        </if>
        <if test="paymentComparison !=null and paymentComparison == 2 ">
            and a.due_payment_amount != a.payment_amount
        </if>
        <if test="paymentSubmitTimeStart !=null and paymentSubmitTimeStart !='' ">
            and a.payment_submit_time >= #{paymentSubmitTimeStart}
        </if>
        <if test="paymentSubmitTimeEnd !=null and paymentSubmitTimeEnd !='' ">
            <![CDATA[
                    and a.payment_submit_time < #{paymentSubmitTimeEnd}
                ]]>
        </if>
        <if test="enforceTimeStart !=null and enforceTimeStart !='' ">
            and b.enforce_time >= #{enforceTimeStart}
        </if>
        <if test="enforceTimeEnd !=null and enforceTimeEnd !='' ">
            <![CDATA[
                and b.enforce_time < #{enforceTimeEnd}
            ]]>
        </if>
        <if test="paymentTimeStart !=null and paymentTimeStart !='' ">
            and a.payment_time >= #{paymentTimeStart}
        </if>
        <if test="paymentTimeEnd !=null and paymentTimeEnd !='' ">
            <![CDATA[
                and a.payment_time < #{paymentTimeEnd}
            ]]>
        </if>
    </sql>

    <select id="countList"
            parameterType="com.mpolicy.manage.modules.policy.vo.renewal.RenewalTermQuery" resultType="java.lang.Integer">
            select
                count(1)
            from ep_policy_renewal_term a
            left join ep_policy_contract_info b  on a.contract_code = b.contract_code
            left join ep_policy_applicant_info c on a.contract_code = c.contract_code
            left join channel_application_referrer e on a.referrer_code = e.referrer_code
            left join agent_user_info f on b.agent_code = f.agent_code
            LEFT JOIN channel_branch_info g ON b.channel_branch_code = g.branch_code
            LEFT JOIN org_info h ON b.channel_branch_code = h.org_code
            where a.period > 1
            and   b.deleted = 0
            <include refid="renewalTermList_query" />
    </select>

    <select id="queryList"
            parameterType="com.mpolicy.manage.modules.policy.vo.renewal.RenewalTermQuery"
            resultType="com.mpolicy.manage.modules.policy.po.renewal.RenewalTermShowPo">
        select
            a.policy_no as policyNo,
            a.contract_code as contractCode,
            b.policy_product_type as policyProductType,
            a.status,
            b.main_product_name as mainProductName,
            b.company_name as companyName,
            b.insured_period_type as insuredPeriodType,
            b.insured_period as insuredPeriod,
            b.period_type as periodType,
            b.sales_type as salesType,
            b.referrer_type referrerType,
            b.policy_status,
            a.policy_period_year as policyPeriodYear,
            a.period,
            a.due_payment_time as duePaymentTime,
            a.due_payment_amount as duePaymentAmount,
            a.payment_time as paymentTime,
            a.payment_amount as paymentAmount,
            a.grace_day as graceDay,
            c.applicant_name as applicantName,
            b.insured_name as insuredName,
            b.referrer_code as referrerCode,
            f.agent_name as agentName,
            f.agent_code as agentCode,
            IF(b.referrer_type = 0, g.branch_name, h.org_name) AS channelBranchName,
            b.channel_name as channelName,
            b.channel_code as channelCode,
            a.operator,
            a.update_time as createTime
        from ep_policy_renewal_term a
        left join ep_policy_contract_info b  on a.contract_code = b.contract_code
        left join ep_policy_applicant_info c on a.contract_code = c.contract_code
        left join channel_application_referrer e on a.referrer_code = e.referrer_code
        left join agent_user_info f on b.agent_code = f.agent_code
        LEFT JOIN channel_branch_info g ON b.channel_branch_code = g.branch_code
        LEFT JOIN org_info h ON b.channel_branch_code = h.org_code
        where a.period > 1
        and   b.deleted = 0
        <include refid="renewalTermList_query" />
        order by due_payment_time desc
        limit #{offset},#{size}
    </select>

    <!-- 导出续期数据 查询条件 -->
    <sql id="renewalTerm_export_query">
        <if test="riskName !=null and riskName!='' ">
            and exists (
            select 1 from ep_policy_product_info l
            where b.contract_code = l.contract_code
            and l.product_name like concat('%',concat(#{riskName},'%'))
            )
        </if>
        <if test="policyNo !=null and policyNo !='' ">
            and a.policy_no=#{policyNo}
        </if>
        <if test="companyCodeList !=null and companyCodeList.size>0 ">
            and b.company_code in
            <foreach collection="companyCodeList" item="company" open="(" close=")" separator=",">
                #{company}
            </foreach>
        </if>
        <if test="status !=null ">
            and a.status=#{status}
        </if>
        <if test="applicantName !=null and applicantName !='' ">
            and j.applicant_name = #{applicantName}
        </if>
        <if test="applicantIdCard !=null and applicantIdCard !='' ">
            and j.applicant_id_card = #{applicantIdCard}
        </if>

        <if test="insuredName !=null and insuredName !='' ">
            and k.insured_name = #{insuredName}
        </if>
        <if test="insuredIdCard !=null and insuredIdCard !='' ">
            and exists(
            select 1 from ep_policy_insured_info d
            where a.contract_code = d.contract_code and d.deleted=0
            and d.insured_id_card = #{insuredIdCard}
            )
        </if>
        <if test="agentCodeList !=null and agentCodeList.size>0 ">
            and sales_type = '1'
            and b.agent_code in
            <foreach collection="agentCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="channelCodeList !=null and channelCodeList.size>0 ">
            and b.channel_code in
            <foreach collection="channelCodeList" item="channel" open="(" close=")" separator="," >
                #{channel}
            </foreach>
        </if>
        <if test="referrerCodeList !=null and referrerCodeList.size>0 ">
            and b.referrer_code in
            <foreach collection="referrerCodeList" item="referrer" open="(" close=")" separator="," >
                #{referrer}
            </foreach>
        </if>
        <if test="authOrgCodeList !=null and authOrgCodeList.size>0 ">
            and b.org_code in
            <foreach collection="authOrgCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="authChannelBranchCodeList !=null and authChannelBranchCodeList.size>0 ">
            and b.policy_channel_branch_code in
            <foreach collection="authChannelBranchCodeList" item="channel" open="(" close=")" separator=",">
                #{channel}
            </foreach>
        </if>
        <if test="orgCodeList !=null and orgCodeList.size>0 ">
            and b.org_code in
            <foreach collection="orgCodeList" item="code" open="(" close=")" separator="," >
                #{code}
            </foreach>
        </if>
        <if test="channelDisCodeList !=null and channelDisCodeList.size>0 ">
            and b.channel_distribution_code in
            <foreach collection="channelDisCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="duePaymentStartTime !=null">
            and a.due_payment_time &gt;= #{duePaymentStartTime}
        </if>
        <if test="duePaymentEndTime !=null">
            and a.due_payment_time &lt;= #{duePaymentEndTime}
        </if>
        <if test="paymentComparison !=null and paymentComparison == 1 ">
            and a.due_payment_amount = a.payment_amount
        </if>
        <if test="paymentComparison !=null and paymentComparison == 2 ">
            and a.due_payment_amount != a.payment_amount
        </if>
        <if test="paymentSubmitTimeStart !=null and paymentSubmitTimeStart !='' ">
            and a.payment_submit_time >= #{paymentSubmitTimeStart}
        </if>
        <if test="paymentSubmitTimeEnd !=null and paymentSubmitTimeEnd !='' ">
            <![CDATA[
                and a.payment_submit_time < #{paymentSubmitTimeEnd}
            ]]>
        </if>
        <if test="enforceTimeStart !=null and enforceTimeStart !='' ">
            and b.enforce_time >= #{enforceTimeStart}
        </if>
        <if test="enforceTimeEnd !=null and enforceTimeEnd !='' ">
            <![CDATA[
                and b.enforce_time < #{enforceTimeEnd}
            ]]>
        </if>
        <if test="paymentTimeStart !=null and paymentTimeStart !='' ">
            and a.payment_time >= #{paymentTimeStart}
        </if>
        <if test="paymentTimeEnd !=null and paymentTimeEnd !='' ">
            <![CDATA[
                and a.payment_time < #{paymentTimeEnd}
            ]]>
        </if>
    </sql>

    <select id="countXlsData"
            parameterType="com.mpolicy.manage.modules.policy.vo.renewal.RenewalTermQuery"
            resultType="java.lang.Integer">
        select
             count(1)
        from ep_policy_renewal_term a
        left join ep_policy_contract_info b  on a.contract_code = b.contract_code
        LEFT JOIN ep_policy_contract_extend b1 ON b.contract_code = b1.contract_code
        left join ep_policy_agent_info d on a.contract_code = d.contract_code AND d.deleted = 0
        LEFT JOIN agent_user_info e ON d.agent_code = e.agent_code
        LEFT JOIN channel_branch_info f ON b.channel_branch_code = f.branch_code
        LEFT JOIN org_info g ON b.channel_branch_code = g.org_code
        LEFT JOIN channel_application_referrer h ON b.referrer_code = h.referrer_code
        LEFT JOIN agent_user_info i ON b.referrer_code = i.agent_code
        left join ep_policy_applicant_info j on a.contract_code = j.contract_code
        LEFT JOIN ep_policy_renewal_term_product c ON  a.contract_code=c.contract_code and a.period=c.period and c.deleted=0
        LEFT JOIN ep_policy_insured_info k ON c.contract_code = k.contract_code and k.insured_code = c.insured_code
        LEFT JOIN ep_policy_product_insured_map o ON c.contract_code=o.contract_code and c.product_code=o.product_code and c.insured_code=o.insured_code
        where a.period > 1
        and   b.deleted = 0
        <include refid="renewalTerm_export_query" />
    </select>

    <select id="queryXlsData"
            parameterType="com.mpolicy.manage.modules.policy.vo.renewal.RenewalTermQuery"
            resultType="com.mpolicy.manage.modules.policy.po.renewal.RenewalTermExportPo">
        select
            a.policy_no as policyNo,
            a.status,
            a.period_start_time as periodStartTime,
            a.period_end_time as periodEndTime,
            a.due_payment_time as duePaymentTime,

            c.due_premium as duePaymentAmount,
            a.payment_time as paymentTime,
            c.premium as paymentAmount,

            a.grace_day as graceDay,
            a.operator,
            a.payment_submit_time as paymentSubmitTime,
            a.policy_period_year as policyPeriodYear,
            a.period,

            b.policy_status as policyStatus,
            b.applicant_policy_no as proposalNo,
            b.sales_type as salesType,
            b.sales_platform as salesPlatform,
            b.company_name as companyName,
            b.referrer_type as referrerType,
            b.main_product_name as mainProductName,
            b.channel_code as channelCode,
            IF(b.referrer_type = 0, f.branch_name, g.org_name) AS channelBranchName,
            b.referrer_code as referrerCode,

            c.protocol_product_name as protocolProductName,
            c.product_name as productName,
            c.product_code as productCode,
            c.main_insurance as mainInsurance,
            c.prod_type_code as prodTypeCode,
            c.insured_period_type as insuredPeriodType,
            c.insured_period as insuredPeriod,
            c.period_type as periodType,
            c.payment_period_type as paymentPeriodType,
            c.payment_period as paymentPeriod,
            ifnull(o.premium,c.due_premium) as premium,
            c.coverage,

            d.agent_code as agentCode,
            d.org_name AS orgName,
            d.main_flag as mainAgentFlag,
            IF(b.sales_type = 1, d.org_code, NULL) AS orgCode,
            d.commission_rate as commissionRate,

            e.business_code as policyReferrerCode,
            e.agent_name as policyReferrerName,

            b.enforce_time  as effectiveTime,
            b1.termination_time as endTime,

            j.applicant_name as applicantName,
            j.applicant_id_type as applicantIdType,
            j.applicant_id_card as applicantIdCard,
            j.applicant_gender as applicantGender,
            j.applicant_mobile as applicantMobile,
            j.applicant_birthday as applicantBirthday,
            j.applicant_address as applicantAddress,
            k.insured_relation as insuredRelation,
            k.insured_name as insuredName,
            k.insured_id_type as insuredIdType,
            k.insured_id_card as insuredIdCard,
            k.insured_gender as insuredGender,
            k.insured_mobile as insuredMobile,
            k.insured_birthday as insuredBirthday,
            k.insured_address as insuredAddress,
            b.settlement_status as settlementStatus,
            b.settlement_year as settlementYear,
            b.settlement_month as settlementMonth,
            cdi.channel_name  As channelDistributionName
        from ep_policy_renewal_term a
        left join ep_policy_contract_info b  on a.contract_code = b.contract_code
        LEFT JOIN ep_policy_contract_extend b1 ON b.contract_code = b1.contract_code
        left join ep_policy_agent_info d on a.contract_code = d.contract_code AND d.deleted = 0
        LEFT JOIN agent_user_info e ON d.agent_code = e.agent_code
        LEFT JOIN channel_branch_info f ON b.channel_branch_code = f.branch_code
        LEFT JOIN org_info g ON b.channel_branch_code = g.org_code
        LEFT JOIN channel_application_referrer h ON b.referrer_code = h.referrer_code
        LEFT JOIN agent_user_info i ON b.referrer_code = i.agent_code
        left join ep_policy_applicant_info j on a.contract_code = j.contract_code
        LEFT JOIN ep_policy_renewal_term_product c ON a.contract_code=c.contract_code and a.period=c.period and c.deleted=0
        LEFT JOIN ep_policy_insured_info k ON c.contract_code = k.contract_code and c.insured_code=k.insured_code and k.deleted =0
        LEFT JOIN ep_policy_product_insured_map o ON c.contract_code=o.contract_code and c.product_code=o.product_code and c.insured_code=o.insured_code and o.deleted=0
        LEFT JOIN channel_distribution_info cdi on b.channel_distribution_code = cdi.channel_code
        where a.period > 1
        and   b.deleted = 0
        <include refid="renewalTerm_export_query" />
        order by a.due_payment_time desc
        limit #{offset},#{size}
    </select>

    <update id="updateByPolicyNo">
        UPDATE ep_policy_renewal_term set policy_no = #{policyNo} WHERE policy_no = #{sourcePolicyNo} and `status` != 1 and deleted = 0
    </update>
    
    <select id="listInsuredProduct" resultType="com.mpolicy.policy.common.ep.policy.product.EpInsuredProductVo">
        select a.*,b.insured_id_card,b.insured_name
        from ep_policy_renewal_term_product a
                 left join ep_policy_insured_info b on a.contract_code=b.contract_code and a.insured_code=b.insured_code and b.deleted=0
                 left join ep_policy_renewal_term c on a.contract_code=c.contract_code and a.period=c.period
        where a.deleted=0
          and   c.status = 1
          and   a.premium is not null
          and   c.policy_no=#{policyNo}
          and   a.period=#{period}
    </select>

</mapper>
