<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinHistoryDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinHistoryEntity" id="blAgentOnlineJoinHistoryMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="reason" column="reason"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>