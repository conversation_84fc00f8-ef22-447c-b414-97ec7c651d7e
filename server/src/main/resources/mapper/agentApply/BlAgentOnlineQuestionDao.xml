<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineQuestionDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineQuestionEntity" id="blAgentOnlineQuestionMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="part" column="part"/>
        <result property="type" column="type"/>
        <result property="score" column="score"/>
        <result property="title" column="title"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>