<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineAnswerJoinDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineAnswerJoinEntity" id="blAgentOnlineAnswerJoinMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="qCode" column="q_code"/>
        <result property="isPass" column="is_pass"/>
        <result property="answer" column="answer"/>
        <result property="score" column="score"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>