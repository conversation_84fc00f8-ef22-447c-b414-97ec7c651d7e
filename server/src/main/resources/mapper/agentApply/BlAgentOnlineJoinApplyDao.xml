<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinApplyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinApplyEntity" id="blAgentOnlineJoinApplyMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="orgCode" column="org_code"/>
        <result property="agentName" column="agent_name"/>
        <result property="pinyin" column="pinyin"/>
        <result property="gender" column="gender"/>
        <result property="avatar" column="avatar"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="agentNickName" column="agent_nick_name"/>
        <result property="idType" column="id_type"/>
        <result property="idCard" column="id_card"/>
        <result property="idStartDate" column="id_start_date"/>
        <result property="idEndDate" column="id_end_date"/>
        <result property="idLongTerm" column="id_long_term"/>
        <result property="birthday" column="birthday"/>
        <result property="agentType" column="agent_type"/>
        <result property="entryType" column="entry_type"/>
        <result property="entryDate" column="entry_date"/>
        <result property="position" column="position"/>
        <result property="positionDegree" column="position_degree"/>
        <result property="degree" column="degree"/>
        <result property="address" column="address"/>
        <result property="marital" column="marital"/>
        <result property="politics" column="politics"/>
        <result property="nation" column="nation"/>
        <result property="school" column="school"/>
        <result property="weChat" column="we_chat"/>
        <result property="workTime" column="work_time"/>
        <result property="initServiceNum" column="init_service_num"/>
        <result property="serviceAttribute" column="service_attribute"/>
        <result property="expertise" column="expertise"/>
        <result property="isFinish" column="is_finish"/>
        <result property="interview" column="interview"/>
        <result property="interviewUrlFileCode" column="interview_url_file_code"/>
        <result property="interviewUrl" column="interview_url"/>
        <result property="idCardFrontFileCode" column="id_card_front_file_code"/>
        <result property="idCardFront" column="id_card_front"/>
        <result property="idCardBackFileCode" column="id_card_back_file_code"/>
        <result property="idCardBack" column="id_card_back"/>
        <result property="certificatesPhoneFileCode" column="certificates_phone_file_code"/>
        <result property="certificatesPhone" column="certificates_phone"/>
        <result property="bankCardPhoneFileCode" column="bank_card_phone_file_code"/>
        <result property="bankCardPhone" column="bank_card_phone"/>
        <result property="degreePhoneFileCode" column="degree_phone_file_code"/>
        <result property="degreePhone" column="degree_phone"/>
        <result property="recruitCode" column="recruit_code"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
        <result property="businessCode" column="business_code"/>
        <result property="isOptional" column="is_optional"/>
        <result property="recommendStatus" column="recommend_status"/>
        <result property="country" column="country"/>
        <result property="acquisitionArea" column="acquisition_area"/>
        <result property="areaCode" column="area_code"/>
        <result property="cityCode" column="city_code"/>
        <result property="agentNature" column="agent_nature"/>
        <result property="agentCategory" column="agent_category"/>
        <result property="agentLabel" column="agent_label"/>
    </resultMap>

    <!-- 代理人分页查询 查询条件 -->
    <sql id="agent_apply_list">
        <if test="input.agentCode !=null">
            AND aoa.agent_code = #{input.agentCode,jdbcType=VARCHAR}
        </if>
        <if test="input.recruitCode !=null">
            AND aoa.recruit_code = #{input.recruitCode,jdbcType=VARCHAR}
        </if>
        <if test="input.orgCodeList !=null and input.orgCodeList.size() >0 ">
            AND aoa.org_code in
            <foreach collection="input.orgCodeList" item="orgCode" open="(" close=")" separator=",">
                #{orgCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="input.marketingSignStatus !=null">
            <if test="input.marketingSignStatus == 0">
                AND aoa.marketing_sign_status = 0
            </if>
            <if test="input.marketingSignStatus == 2">
                AND aoa.marketing_sign_status in(1,2)
            </if>
        </if>
    </sql>

    <!--代理人申请分页查询 -->
    <select id="pageList" resultType="com.mpolicy.manage.modules.agentApply.vo.AgentApplyPageListOut">
        SELECT  aoa.*,aui.business_code as recruitBusinessCode,aui.agent_name as recruitName,
        (SELECT baoaj.score FROM bl_agent_online_answer_join baoaj WHERE aoa.agent_code = baoaj.agent_code ORDER BY baoaj.id DESC LIMIT 1) AS score
        FROM bl_agent_online_join_apply aoa
        LEFT JOIN agent_user_info aui on aui.agent_code = aoa.recruit_code
        <where>
            <include refid="agent_apply_list"/>
        </where>
        ORDER BY aoa.id desc
    </select>

    <!--代理人导出信息查询 -->
    <select id="export" resultType="com.mpolicy.manage.modules.agentApply.vo.AgentApplyExportVo">
        SELECT  aoa.*,aui.business_code as businessCode,aui.agent_name as recruitName
        FROM bl_agent_online_join_apply aoa
        LEFT JOIN agent_user_info aui on aui.agent_code = aoa.recruit_code
        <where>
            <include refid="agent_apply_list"/>
        </where>
        ORDER BY aoa.id desc
    </select>

    <!-- 获取代理人区域信息 -->
    <select id="findArea" resultType="com.mpolicy.manage.modules.agentApply.vo.AgentApplyBasicInfoOut">
        SELECT baoja.*,
               live.province_code as liveProvinceCode,live.city_code AS liveCityCode,serve.province_code as serverProvince
        FROM bl_agent_online_join_apply baoja
                 LEFT JOIN sys_region_info live ON baoja.area_code = live.`code`
                 LEFT JOIN sys_region_info serve ON baoja.city_code = serve.city_code
        where baoja.agent_code =#{agentCode,jdbcType=VARCHAR}
        GROUP BY baoja.id
    </select>
</mapper>