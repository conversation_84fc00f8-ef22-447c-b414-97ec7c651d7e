<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinAttachmentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinAttachmentEntity" id="blAgentOnlineJoinAttachmentMap">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="identificationType" column="identification_type"/>
        <result property="suffix" column="suffix"/>
        <result property="fileCode" column="file_code"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>