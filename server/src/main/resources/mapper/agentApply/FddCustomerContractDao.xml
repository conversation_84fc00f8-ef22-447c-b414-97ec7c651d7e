<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.FddCustomerContractDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.FddCustomerContractEntity" id="fddCustomerContractMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="model" column="model"/>
        <result property="buzContractNo" column="buz_contract_no"/>
        <result property="buzNo" column="buz_no"/>
        <result property="contractNo" column="contract_no"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="url" column="url"/>
        <result property="title" column="title"/>
        <result property="signType" column="sign_type"/>
        <result property="contractType" column="contract_type"/>
        <result property="signStatus" column="sign_status"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>