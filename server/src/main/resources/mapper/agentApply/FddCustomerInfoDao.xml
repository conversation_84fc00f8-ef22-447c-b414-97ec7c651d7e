<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.agentApply.dao.FddCustomerInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.agentApply.entity.FddCustomerInfoEntity" id="fddCustomerInfoMap">
        <result property="id" column="id"/>
        <result property="model" column="model"/>
        <result property="custName" column="cust_name"/>
        <result property="mobile" column="mobile"/>
        <result property="loanCustId" column="loan_cust_id"/>
        <result property="idNo" column="id_no"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>