<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.dao.OrderApplicantInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.order.entity.OrderApplicantInfoEntity" id="orderApplicantInfoMap">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="applicantIdCard" column="applicant_id_card"/>
        <result property="applicantIdType" column="applicant_id_type"/>
        <result property="applicantMobile" column="applicant_mobile"/>
        <result property="applicantGender" column="applicant_gender"/>
        <result property="applicantBirthday" column="applicant_birthday"/>
        <result property="applicantAge" column="applicant_age"/>
        <result property="holderJob" column="holder_job"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
        <result property="createUser" column="create_user"/>
    </resultMap>


</mapper>