<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.order.dao.TraceSerialRecallDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.order.entity.TraceSerialRecallDataEntity" id="traceSerialRecallDataMap">
        <result property="id" column="id"/>
        <result property="traceSerialId" column="trace_serial_id"/>
        <result property="shotImgPath" column="shot_img_path"/>
        <result property="operatePage" column="operate_page"/>
        <result property="currentUrl" column="current_url"/>
        <result property="pageEvent" column="page_event"/>
        <result property="enterPageTime" column="enter_page_time"/>
        <result property="quitPageTime" column="quit_page_time"/>
        <result property="platform" column="platform"/>
        <result property="operateIp" column="operate_ip"/>
        <result property="recallType" column="recall_type"/>
        <result property="recordId" column="record_id"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>
