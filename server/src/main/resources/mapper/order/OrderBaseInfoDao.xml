<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.order.dao.OrderBaseInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.order.entity.OrderBaseInfoEntity" id="orderBaseInfoMap">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="insAdvancePolicyCode" column="ins_advance_policy_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="commodityName" column="commodity_name"/>
        <result property="commodityCode" column="commodity_code"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="policyCode" column="policy_code"/>
        <result property="orderStatusMsg" column="order_status_msg"/>
        <result property="orderType" column="order_type"/>
        <result property="policyEffectiveTime" column="policy_effective_time"/>
        <result property="policyEndTime" column="policy_end_time"/>
        <result property="orderCash" column="order_cash"/>
        <result property="policySnapshotUrl" column="policy_snapshot_url"/>
        <result property="deviceType" column="device_type"/>
        <result property="ip" column="ip"/>
        <result property="orderStatus" column="order_status"/>
        <result property="contractCode" column="contract_code"/>
        <result property="customField" column="custom_field"/>
        <result property="appName" column="app_name"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


    <select id="findPageList" resultType="com.mpolicy.manage.modules.order.vo.RecallList">
        select
        base.policy_code as 'policyNO',
        base.order_code as 'orderCode',
        base.commodity_name as 'productName',
        base.company_name as 'companyName',
        base.order_status as 'orderStatus',
        date_format(base.create_time,'%Y-%m-%d') as 'applicantTime',
        applicant.applicant_name as 'applicantName',
        applicant.applicant_id_card as 'applicantIdCard'
        from order_base_info base
        left join  order_applicant_info applicant on base.order_code = applicant.order_code
        where base.deleted = 0
        <if test="param2.companyName != null and param2.companyName != ''">
            and base.company_name =#{param2.companyName,jdbcType=VARCHAR}
        </if>
        <if test="param2.orderCode != null and param2.orderCode != ''">
            and base.order_code =#{param2.orderCode,jdbcType=VARCHAR}
        </if>
        <if test="param2.policyNO != null and param2.policyNO != ''">
            and base.policy_code =#{param2.policyNO,jdbcType=VARCHAR}
        </if>
        <if test="param2.applicantTime != null and param2.applicantTime != ''">
            and date_format(base.create_time,'%Y-%m-%d') =#{param2.applicantTime,jdbcType=VARCHAR}
        </if>

        <if test="param2.orderStatus != null and param2.orderStatus != ''">
            and base.order_status =#{param2.orderStatus,jdbcType=VARCHAR}
        </if>
        <if test="param2.productName != null and param2.productName != ''">
            and base.commodity_name =#{param2.productName,jdbcType=VARCHAR}
        </if>

          <if test="param2.applicantName != null and param2.applicantName != ''">
            and applicant.applicant_name like concat('%',#{param2.applicantName,jdbcType=VARCHAR},'%')
        </if>
        <if test="param2.applicantIdCard != null and param2.applicantIdCard != ''">
            and applicant.applicant_id_card =#{param2.applicantIdCard,jdbcType=VARCHAR}
        </if>
        order by base.update_time DESC
    </select>

</mapper>