<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcilePolicyDao">


    <!--获取结算差异列表-->
    <select id="findSettlementReconcileDiffList"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileDiffExcel">
        SELECT srp.policy_no,srp.`endorsement_no` , srp.`bill_code` ,spi.`applicant_name` ,spi.`applicant_time` ,
        spi.`approved_time` ,spi.`enforce_time` ,spi.`order_time` ,spi.`preservation_effect_time` ,spi.`settlement_subject_code` ,
        spi.`settlement_subject_name` ,spi.`protocol_product_code` ,spi.`protocol_product_name` ,Sum(spi.`premium`) premium ,spi.`settlement_rate` ,
        sum(spi.`settlement_amount`) AS  settlement_amount ,spi.`settlement_rate_method`
        ,spi.`settlement_rate_tax`,spi.company_name,srp.`settlement_code`,spi.plan_name
        FROM `settlement_reconcile_policy` srp
        LEFT JOIN `settlement_policy_info` spi on srp.`settlement_code` = spi.`settlement_code` and spi.`deleted` = 0
        WHERE srp.`deleted` = 0 and `bill_code` in
        <foreach collection="billCodeList" item="billCode" open="(" close=")" separator=",">
            #{billCode,jdbcType=VARCHAR}
        </foreach>
        GROUP BY srp.`policy_no` , srp.`endorsement_no` , srp.`protocol_product_code`
    </select>
</mapper>