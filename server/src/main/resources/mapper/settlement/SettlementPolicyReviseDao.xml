<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementPolicyReviseDao">

    <!--分页获取数据列表-->
    <select id="findSettlementPolicyReviseList"
            resultType="com.mpolicy.manage.modules.settlement.vo.SettlementPolicyReviseListOut">
        select * from settlement_policy_revise where settlement_code = #{input.settlementCode,jdbcType=VARCHAR}
    </select>
</mapper>