<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementEventJobDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.settlement.entity.SettlementEventJobEntity" id="settlementEventJobMap">
        <result property="id" column="id"/>
        <result property="eventType" column="event_type"/>
        <result property="eventName" column="event_name"/>
        <result property="eventDesc" column="event_desc"/>
        <result property="eventBusinessCode" column="event_business_code"/>
        <result property="pushEventCode" column="push_event_code"/>
        <result property="contractCode" column="contract_code"/>
        <result property="eventSource" column="event_source"/>
        <result property="eventRequest" column="event_request"/>
        <result property="eventResponse" column="event_response"/>
        <result property="eventMessage" column="event_message"/>
        <result property="eventCostMillis" column="event_cost_millis"/>
        <result property="eventFinishTime" column="event_finish_time"/>
        <result property="eventStatus" column="event_status"/>
        <result property="businessSignType" column="business_sign_type"/>
        <result property="mixSign" column="mix_sign"/>
        <result property="incomeEventStatus" column="income_event_status"/>
        <result property="incomeEventMessage" column="income_event_message"/>
        <result property="incomeFinishTime" column="income_finish_time"/>
        <result property="mixIncomeEventStatus" column="mix_income_event_status"/>
        <result property="mixIncomeEventMessage" column="mix_income_event_message"/>
        <result property="mixIncomeFinishTime" column="mix_income_finish_time"/>
        <result property="costEventStatus" column="cost_event_status"/>
        <result property="costEventMessage" column="cost_event_message"/>
        <result property="costFinishTime" column="cost_finish_time"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
        <result property="contractIncomeEventStatus" column="contract_income_event_status"/>
        <result property="contractIncomeEventMessage" column="contract_income_event_message"/>
        <result property="contractIncomeFinishTime" column="contract_income_finish_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findSettlementEventJobList"
            resultType="com.mpolicy.manage.modules.settlement.vo.SettlementEventJobListOut">
        SELECT id,
               event_type                    AS eventType,
               event_name                    AS eventName,
               event_desc                    AS eventDesc,
               event_business_code           AS eventBusinessCode,
               push_event_code               AS pushEventCode,
               contract_code                 AS contractCode,
               event_source                  AS eventSource,
               event_request                 AS eventRequest,
               event_response                AS eventResponse,
               event_message                 AS eventMessage,
               event_cost_millis             AS eventCostMillis,
               event_finish_time             AS eventFinishTime,
               event_status                  AS eventStatus,
               business_sign_type            AS businessSignType,
               mix_sign                      AS mixSign,
               income_event_status           AS incomeEventStatus,
               income_event_message          AS incomeEventMessage,
               income_finish_time            AS incomeFinishTime,
               mix_income_event_status       AS mixIncomeEventStatus,
               mix_income_event_message      AS mixIncomeEventMessage,
               mix_income_finish_time        AS mixIncomeFinishTime,
               cost_event_status             AS costEventStatus,
               cost_event_message            AS costEventMessage,
               cost_finish_time              AS costFinishTime,
               deleted,
               create_user                   AS createUser,
               create_time                   AS createTime,
               update_user                   AS updateUser,
               update_time                   AS updateTime,
               contract_income_event_status  AS contractIncomeEventStatus,
               contract_income_event_message AS contractIncomeEventMessage,
               contract_income_finish_time   AS contractIncomeFinishTime
        FROM settlement_event_job sej
        where sej.deleted = 0
            <if test="input.eventType != null and input.eventType != ''">
                and sej.event_type = #{input.eventType,jdbcType=VARCHAR}
            </if>
            <if test="input.contractCode != null and input.contractCode != ''">
                and sej.contract_code = #{input.contractCode,jdbcType=VARCHAR}
            </if>
            <if test="input.eventBusinessCode != null and input.eventBusinessCode != ''">
                and sej.event_business_code = #{input.eventBusinessCode,jdbcType=VARCHAR}
            </if>
            <if test="input.pushEventCode != null and input.pushEventCode != ''">
                and sej.push_event_code = #{input.pushEventCode,jdbcType=VARCHAR}
            </if>
            <if test="input.eventSource != null and input.eventSource != ''">
                and sej.event_source = #{input.eventSource,jdbcType=VARCHAR}
            </if>
            <if test="input.eventStatus != null">
                and sej.event_status = #{input.eventStatus,jdbcType=INTEGER}
            </if>
            <if test="input.incomeEventStatus != null">
                and sej.income_Event_Status = #{input.incomeEventStatus,jdbcType=INTEGER}
            </if>
            <if test="input.costEventStatus != null">
                and sej.cost_event_status = #{input.costEventStatus,jdbcType=INTEGER}
            </if>
            <if test="input.contractIncomeEventStatus != null">
                and sej.contract_income_event_status = #{input.contractIncomeEventStatus,jdbcType=INTEGER}
            </if>
            <if test="input.businessSignType != null">
                and sej.business_sign_type = #{input.businessSignType,jdbcType=INTEGER}
            </if>
    </select>
</mapper>