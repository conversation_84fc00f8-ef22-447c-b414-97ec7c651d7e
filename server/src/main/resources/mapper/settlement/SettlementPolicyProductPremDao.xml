<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementPolicyProductPremDao">
    <!--分页获取数据-->
    <select id="findSettlementPolicyProductPremList"
            resultType="com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductPremListOut">
        SELECT id,
        policy_no       AS policyNo,
        batch_code      AS batchCode,
        product_code      ,
        product_name     ,
        insurance_product_code     ,
        insurance_product_name     ,
        premium,
        year,
        period,
        settlement_method,
        tax_rate,
        tax_after_premium,
        year_rate,
        create_user     AS createUser,
        create_time     AS createTime,
        update_user     AS updateUser,
        update_time     AS updateTime
        FROM settlement_policy_product_prem
        <where>
            <if test="input.batchCode != null and input.batchCode != ''">
                and batch_code = #{input.batchCode,jdbcType=VARCHAR}
            </if>
            <if test="input.policyNo != null and input.policyNo != ''">
                and policy_no = #{input.policyNo,jdbcType=VARCHAR}
            </if>
            <if test="input.reconcileType != null">
                and reconcile_type = #{input.reconcileType,jdbcType=INTEGER}
            </if>
        </where>
        order by update_time desc
    </select>
</mapper>