<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcileContractInfoDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.settlement.entity.SettlementReconcileContractInfoEntity"
               id="settlementReconcileContractInfoMap">
        <result property="id" column="id"/>
        <result property="reconcileCompanyCode" column="reconcile_company_code"/>
        <result property="externalSignatoryType" column="external_signatory_type"/>
        <result property="externalSignatoryCode" column="external_signatory_code"/>
        <result property="externalSignatoryName" column="external_signatory_name"/>
        <result property="innerSignatoryName" column="inner_signatory_name"/>
        <result property="innerSignatoryCode" column="inner_signatory_code"/>
        <result property="innerSignatoryType" column="inner_signatory_type"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findSettlementReconcileContractInfoList"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileContractInfoListOut">
        SELECT id,
               reconcile_company_code  AS reconcileCompanyCode,
               reconcile_company_name  AS reconcileCompanyName,
               external_signatory_type AS externalSignatoryType,
               external_signatory_code AS externalSignatoryCode,
               external_signatory_name AS externalSignatoryName,
               inner_signatory_name    AS innerSignatoryName,
               inner_signatory_code    AS innerSignatoryCode,
               inner_signatory_type    AS innerSignatoryType,
               settlement_company_name AS settlementCompanyName,
               create_time             AS createTime
        FROM settlement_reconcile_contract_info srci
        where srci.deleted = 0
        <if test="input.reconcileCompanyCode != null and input.reconcileCompanyCode != ''">
            and srci.reconcile_company_code = #{input.reconcileCompanyCode,jdbcType=VARCHAR}
        </if>
        <if test="input.reconcileCompanyName != null and input.reconcileCompanyName != ''">
            and srci.reconcile_company_name like concat('%',#{input.reconcileCompanyName,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.innerSignatoryCode != null and input.innerSignatoryCode != ''">
            and srci.inner_signatory_code = #{input.innerSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="input.externalSignatoryCode != null and input.externalSignatoryCode != ''">
            and srci.external_signatory_code = #{input.externalSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="input.innerSignatoryType != null and input.innerSignatoryType != ''">
            and srci.inner_signatory_type = #{input.innerSignatoryType,jdbcType=VARCHAR}
        </if>
        <if test="input.externalSignatoryType != null and input.externalSignatoryType != ''">
            and srci.external_signatory_type = #{input.externalSignatoryType,jdbcType=VARCHAR}
        </if>
        <if test="input.subjectReconcileCompanyCode != null and input.subjectReconcileCompanyCode != ''">
            and srci.reconcile_company_code =#{input.subjectReconcileCompanyCode,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>