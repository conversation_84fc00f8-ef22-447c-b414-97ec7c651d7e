<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementCostAutoInfoDao">

    <sql id="commonPageSettlementCostAutoInfoBySummary">
        select scai.settlement_month as settlementMonth,
        scai.region_code as regionCode,
        scai.region_name as regionName,
        scai.object_org_code as objectOrgCode,
        scai.object_org_name as objectOrgName,
        scai.send_object_code as sendObjectCode,
        scai.send_object_name as sendObjectName,
        scai.confirm_status as confirmStatus,
        scai.confirm_time as confirmTime,
        sum(scai.short_promotion) as shortPromotion,
        sum(scai.long_promotion) as longPromotion,
        sum(scai.long_reissue_promotion) as longReissuePromotion,
        sum(scai.add_comm) as addComm,
        sum(scai.supervisor_performance) as supervisorPerformance,
        sum(scai.supervisor_reissue_performance) as supervisorReissuePerformance,
        sum(scai.pco_allowance) as pcoAllowance,
        sum(scai.pco_performance) as pcoPerformance,
        sum(scai.pco_reissue_performance) as pcoReissuePerformance,
        sum(scai.director_incentive) as directorIncentive,
        sum(scai.agricultural_machinery_performance) as agriculturalMachineryPerformance,
        sum(scai.vehicle_performance) as vehiclePerformance,
        sum(scai.vehicle_vessel_tax) as vehicleVesselTax,
        sum(scai.long_not_renewal_rebate_comm) as longNotRenewalRebateComm,
        sum(scai.long_restatement_reissue_comm) as longRestatementReissueComm,
        sum(scai.rural_proxy) as ruralProxy,
        sum(scai.issuing_regional_marketing_fee) as issuingRegionalMarketingFee,
        sum(scai.custom_subject) as customSubject
        from settlement_cost_auto_info scai
        where scai.deleted = 0
        <if test="input.settlementMonth != null and input.settlementMonth != ''">
            and scai.settlement_month = #{input.settlementMonth}
        </if>
        <if test="input.settlementInstitution != null and input.settlementInstitution != ''">
            and scai.settlement_institution = #{input.settlementInstitution}
        </if>
        <if test="input.sendObjectCode != null and input.sendObjectCode != ''">
            and scai.send_object_code = #{input.sendObjectCode}
        </if>
        <if test="input.settlementStatus != null and input.settlementStatus != ''">
            and scai.confirm_status = #{input.settlementStatus}
        </if>
        <if test="input.objectOrgCode != null and input.objectOrgCode != ''">
            and scai.object_org_code = #{input.objectOrgCode}
        </if>
        <!--        <if test="input.confirmTime != null">-->
        <!--            and scai.confirm_time = #{input.confirmTime}-->
        <!--        </if>-->
        group by scai.settlement_month,scai.region_code,scai.object_org_code,scai.send_object_code
    </sql>

    <!--分页获取每个人每个机构下的汇总数据-->
    <select id="pageSettlementCostAutoInfoBySummary" resultType="com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity">
        <include refid="commonPageSettlementCostAutoInfoBySummary"></include>
    </select>
    <select id="queryAllSummaryDynamicSubject"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDynamicSubjectVO">

        SELECT distinct(send_subject_code) as subjectCode, send_subject_name as subjectName FROM `settlement_cost_auto_record`
        <where>
            <if test="settlementMonth != null and settlementMonth != ''">
                and `cost_settlement_cycle` = #{settlementMonth}
            </if>
            <if test="settlementInstitution != null and settlementInstitution != ''">
                and `settlement_institution` = #{settlementInstitution}
            </if>
            <if test="sendObjectCode != null and sendObjectCode != ''">
                and `send_object_code` = #{sendObjectCode}
            </if>
            and dynamic_flag = 1
        </where>
        group by send_subject_code


    </select>

    <select id="queryAllSummarySubject"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDynamicSubjectVO">

        SELECT distinct(send_subject_code) as subjectCode, send_subject_name as subjectName FROM `settlement_cost_auto_record`
        <where>
            <if test="settlementMonth != null and settlementMonth != ''">
                and `cost_settlement_cycle` = #{settlementMonth}
            </if>
            <if test="settlementInstitution != null and settlementInstitution != ''">
                and `settlement_institution` = #{settlementInstitution}
            </if>
            <if test="sendObjectCode != null and sendObjectCode != ''">
                and `send_object_code` = #{sendObjectCode}
            </if>
        </where>

    </select>
    <select id="queryInstitutionSub"
            resultType="com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoRecordEntity">

        SELECT * FROM `settlement_cost_auto_record` t
        where
        t.`cost_settlement_cycle` in
        <foreach collection="monthList" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
        and t.send_Object_Code in
        <foreach collection="objectCodeList" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="pageSettlementCostAutoInfoBySummaryExport"
            resultType="com.mpolicy.manage.modules.settlement.entity.SettlementCostAutoInfoEntity">
        <include refid="commonPageSettlementCostAutoInfoBySummary"></include>
        limit #{start}, #{batchSize}
    </select>
    <select id="pagePolicyDimension"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailPolicyDimensionVo">
        SELECT t3.`cost_settlement_cycle`
        , t3.`settlement_institution`
        , t3.`settlement_institution_name`
        , t3.`document_code`
        , t1.business_account_time as settlementDate
        , t1.`policy_no`
        , t1.`endorsement_no`
        , t1.`product_code`
        , t1.`product_name`
        , t1.`renewal_period`
        , t1.`payment_period`
        , t1.`payment_period_type`
        , t1.`premium`
        , t3.`amount_type`
        , t1.commission_type
        , t1.`grant_rate`
        , t1.`grant_amount`
        , t2.`applicant_name`
        , t2.`approved_time`
        , t7.`dic_value` as `productStatus`
        , t2.`long_short_flag`
        , t1.`product_group`
        , t1.`level2_code`
        , t1.`level3_code`
        , t3.`business_data_type`
        , t3.`source_document_code`
        , t4.`dic_value` as productGroupName
        , t5.`dic_value` as level2CodeName
        , t6.`dic_value` as level3CodeName
        , t1.payable_time as payableTime
        , t3.amount
        FROM `settlement_cost_info` t1
        INNER JOIN settlement_cost_policy_info t2 on t1.`cost_policy_id` = t2.id
        INNER JOIN settlement_cost_auto_record t3 on t3.`auto_cost_code` = t1.`auto_cost_code`
        left JOIN `mp_dictionary` t4 on t4.`dic_key` = t1.`product_group`
        left JOIN `mp_dictionary` t5 on t5.`dic_key` = t1.`level2_code`
        left JOIN `mp_dictionary` t6 on t6.`dic_key` = t1.`level3_code`
        left JOIN `mp_dictionary` t7 on t7.`dic_key` = t1.`product_status`
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t1.deleted = 0
        and t2.deleted = 0
        and t3.deleted = 0

    </select>
    <select id="shortSummary" resultType="java.math.BigDecimal">
        SELECT sum( ifnull(t3.amount, 0))
        FROM settlement_cost_auto_record t3
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t3.deleted = 0
        and t3.deleted = 0
    </select>
    <select id="longPromotionSummary"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryLongPromotion">
        SELECT sum( ifnull(t3.amount, 0)) as promotion,
        SUM(ifnull(t3.`grant_amount`, 0))  as costAmount,
        grant_rate as costRate,
        renewal_rate as longRenewalRate
        FROM settlement_cost_auto_record t3
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t3.deleted = 0
        and t3.deleted = 0
    </select>
    <select id="longReissuePromotionSummary"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryLongReissuePromotion">
        SELECT sum( ifnull(t3.amount, 0)) as longReissuePromotion,
        SUM(ifnull(t3.`grant_amount`, 0))  as costAmount,
        grant_rate as costRate,
        renewal_rate as longRenewalRate
        FROM settlement_cost_auto_record t3
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t3.deleted = 0
        and t3.deleted = 0
    </select>
    <select id="pageProductDimension"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailProductDimensionVO">
        SELECT t3.`cost_settlement_cycle`
        , t3.`settlement_institution`
        , t3.`settlement_institution_name`
        , t3.`document_code`
        , t1.settlement_date
        , t1.`product_code`
        , t1.`product_name`
        , t1.`premium`
        , t1.amount_type
        , t1.`grant_amount`
        , t1.long_short_flag
        , t1.`product_group`
        , t1.`level2_code`
        , t1.`level3_code`
        , t3.`business_data_type`
        , t3.`source_document_code`
        , t4.`dic_value` as productGroupName
        , t5.`dic_value` as level2CodeName
        , t6.`dic_value` as level3CodeName
        FROM `settlement_cost_auto_product` t1
        INNER JOIN  settlement_cost_auto_record t3 on t3.`auto_cost_code` = t1.`auto_cost_code`
        left JOIN `mp_dictionary` t4 on t4.`dic_key` = t1.`product_group`
        left JOIN `mp_dictionary` t5 on t5.`dic_key` = t1.`level2_code`
        left JOIN `mp_dictionary` t6 on t6.`dic_key` = t1.`level3_code`
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t1.deleted = 0
        and t3.deleted = 0
    </select>
    <select id="supervisorPerformanceSummary"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummarySupervisorPerformance">

        SELECT sum( ifnull(t3.amount, 0)) as promotion,
               t3.commission_rate,
               t3.renewal_rate,
               t3.`grant_rate`,
        SUM(ifnull(t3.`grant_amount`, 0))  as grantAmount
        FROM settlement_cost_auto_record t3
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t3.deleted = 0
        and t3.deleted = 0

    </select>
    <select id="pcoAllowanceSummary"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryPcoAllowance">

        SELECT
        t3.pco_level,
        SUM(ifnull(t3.`grant_amount`, 0)) as pcoAllowance
        FROM settlement_cost_auto_record t3
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t3.deleted = 0
        and t3.deleted = 0

    </select>
    <select id="pcoPerformanceSummary"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.detail.DetailSummaryPcoPerformance">
        SELECT sum( ifnull(t3.amount, 0)) as orgPromotion,
        t3.pco_level,
        t3.commission_rate as pcoPerformanceRate,
        t3.grant_rate as costRate,
        t3.renewal_rate as orgLongRenewalRate,
        SUM(ifnull(t3.`grant_amount`, 0))  as costAmount
        FROM settlement_cost_auto_record t3
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t3.deleted = 0
        and t3.deleted = 0
    </select>
    <select id="listConfirmInstitutionPage"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementConfirmInstitutionPageVO">
        SELECT SUM(ifnull(t1.grant_amount, 0)) as grantAmount, t2.dic_value as settlementInstitution, t1.cost_settlement_cycle as costSettlementCycle
        FROM  settlement_cost_auto_record t1
        left join mp_dictionary t2 on t1.settlement_institution = t2.dic_key
        WHERE t1.cost_settlement_cycle in
        <foreach collection="list" close=")" separator="," open="(" item="item">
            #{item}
        </foreach>
          and t1.deleted = 0
        GROUP BY t1.cost_settlement_cycle, t1.settlement_institution
    </select>
    <select id="listSettlementDetailAllSubjectVO"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailAllSubjectVO">
        SELECT distinct(send_subject_code) as subjectCode,subject_code as parent_subject_code,cost_data_type, send_subject_name as subjectName FROM `settlement_cost_auto_record`
        <where>
            <if test="settlementMonth != null and settlementMonth != ''">
                and `cost_settlement_cycle` = #{settlementMonth}
            </if>
            <if test="settlementInstitution != null and settlementInstitution != ''">
                and `settlement_institution` = #{settlementInstitution}
            </if>
            <if test="sendObjectCode != null and sendObjectCode != ''">
                and `send_object_code` = #{sendObjectCode}
            </if>
            and dynamic_flag = 1
        </where>
        group by subject_code,send_subject_code,cost_data_type

    </select>
    <select id="pagePolicyProductDimension"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementDetailProductDimensionVO">

        SELECT t3.`cost_settlement_cycle`
        , t3.`settlement_institution`
        , t3.`settlement_institution_name`
        , t3.`document_code`
        , t1.settlement_date
        , t1.`product_code`
        , t1.`product_name`
        , t1.`premium`
        , t1.amount_type
        , t1.`grant_amount`
        , t1.long_short_flag
        , t1.`product_group`
        , t1.`level2_code`
        , t1.`level3_code`
        , t4.`dic_value` as productGroupName
        , t5.`dic_value` as level2CodeName
        , t6.`dic_value` as level3CodeName
        FROM `settlement_cost_auto_policy` t1
        INNER JOIN  settlement_cost_auto_record t3 on t3.`auto_cost_code` = t1.`auto_cost_code`
        left JOIN `mp_dictionary` t4 on t4.`dic_key` = t1.`product_group`
        left JOIN `mp_dictionary` t5 on t5.`dic_key` = t1.`level2_code`
        left JOIN `mp_dictionary` t6 on t6.`dic_key` = t1.`level3_code`
        WHERE t3.`cost_settlement_cycle` = #{p.settlementMonth}
        <if test="p.settlementInstitution != null and p.settlementInstitution != ''">
            and t3.`settlement_institution` = #{p.settlementInstitution}
        </if>
        and t3.`object_org_code` = #{p.objectOrgCode}
        and t3.`send_object_code` = #{p.sendObjectCode}
        and t3.send_subject_code = #{p.costSubjectCode}
        and t1.deleted = 0
        and t3.deleted = 0

    </select>

</mapper>