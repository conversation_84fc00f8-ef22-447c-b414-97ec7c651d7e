<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanySubjectProductDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectProductEntity" id="settlementReconcileCompanySubjectProductMap">
        <result property="id" column="id"/>
        <result property="subjectRuleCode" column="subject_rule_code"/>
        <result property="insuranceProductCode" column="insurance_product_code"/>
        <result property="insuranceProductName" column="insurance_product_name"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>