<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyProductDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyProductEntity" id="settlementReconcileCompanyProductMap">
        <result property="id" column="id"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="companyProductName" column="company_product_name"/>
        <result property="insuranceProductCode" column="insurance_product_code"/>
        <result property="insuranceProductName" column="insurance_product_name"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <!--获取分页数据-->
    <select id="findSettlementReconcileCompanyProductList"
            resultType="com.mpolicy.manage.modules.settlement.vo.settlement.SettlementReconcileCompanyProductListOut">
        SELECT srcp.*
        FROM settlement_reconcile_company_product srcp
        <where>
            <if test="input.companyCode != null and input.companyCode != ''">
                and srcp.company_code =#{input.companyCode,jdbcType=VARCHAR}
            </if>
            <if test="input.insuranceProductCode != null and input.insuranceProductCode != ''">
                and srcp.insurance_product_code =#{input.insuranceProductCode,jdbcType=VARCHAR}
            </if>
            <if test="input.companyProductName != null and input.companyProductName != ''">
                and srcp.company_product_name like concat('%',#{input.companyProductName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.insuranceProductName != null and input.insuranceProductName != ''">
                and srcp.insurance_product_name like concat('%',#{input.insuranceProductName,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>