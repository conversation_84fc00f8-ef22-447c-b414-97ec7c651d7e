<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcileInvoiceDao">
    <!--获取数据列表-->
    <select id="findSettlementReconcileInvoiceList"
            resultType="com.mpolicy.manage.modules.settlement.vo.invoice.SettlementReconcileInvoiceListOut">
        SELECT sri.*
        FROM settlement_reconcile_invoice sri
        left join settlement_reconcile_invoice_map srim on sri.invoice_code = srim.invoice_code
        <where>
            <if test="input.reconcileType != null">
                and sri.reconcile_type = #{input.reconcileType,jdbcType=INTEGER}
            </if>
            <if test="input.invoicingOrgCode != null and input.invoicingOrgCode != ''">
                and sri.invoicing_org_code = #{input.invoicingOrgCode,jdbcType=INTEGER}
            </if>
            <if test="input.applyStatus != null and input.applyStatus != 4">
                and sri.apply_status = #{input.applyStatus,jdbcType=INTEGER}
            </if>
            <if test="input.applyStatus != null and input.applyStatus == 4">
                and sri.apply_status in (4,6)
            </if>
            <if test="input.invoiceMode != null">
                and sri.invoice_mode = #{input.invoiceMode,jdbcType=INTEGER}
            </if>
            <if test="input.pushCompanyStatus != null">
                and sri.push_company_status = #{input.pushCompanyStatus,jdbcType=INTEGER}
            </if>
            <if test="input.redFlushStatus != null">
                and sri.red_flush_status = #{input.redFlushStatus,jdbcType=INTEGER}
            </if>
            <if test="input.expressStatus != null">
                and sri.express_status = #{input.expressStatus,jdbcType=INTEGER}
            </if>
            <if test="input.sendMailboxStatus != null">
                and sri.send_mailbox_status = #{input.sendMailboxStatus,jdbcType=INTEGER}
            </if>
            <if test="input.invoiceTitle != null and input.invoiceTitle != ''">
                and sri.invoice_title like concat('%',#{input.invoiceTitle,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.reconcileCode != null and input.reconcileCode != ''">
                and srim.reconcile_code like concat('%',#{input.reconcileCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.invoiceCode != null and input.invoiceCode != ''">
                and sri.invoice_code  = #{input.invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="input.applyBeginTime != null and input.applyBeginTime != ''">
                and sri.apply_time  &gt;= #{input.applyBeginTime,jdbcType=VARCHAR}
            </if>
            <if test="input.applyEndTime != null and input.applyEndTime != ''">
                and sri.apply_time  &lt;= #{input.applyEndTime,jdbcType=VARCHAR}
            </if>
        </where>
        group by sri.id
        order by id desc
    </select>
</mapper>