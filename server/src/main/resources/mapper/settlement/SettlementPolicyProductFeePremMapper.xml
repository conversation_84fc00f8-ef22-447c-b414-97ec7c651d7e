<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementPolicyProductFeePremMapper">

    <resultMap id="BaseResultMap" type="generator.domain.SettlementPolicyProductFeePrem">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="premCode" column="prem_code" jdbcType="VARCHAR"/>
            <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
            <result property="batchCode" column="batch_code" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="period" column="period" jdbcType="INTEGER"/>
            <result property="reconcileType" column="reconcile_type" jdbcType="INTEGER"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="insuranceProductCode" column="insurance_product_code" jdbcType="VARCHAR"/>
            <result property="insuranceProductName" column="insurance_product_name" jdbcType="VARCHAR"/>
            <result property="premium" column="premium" jdbcType="DECIMAL"/>
            <result property="taxAfterPremium" column="tax_after_premium" jdbcType="DECIMAL"/>
            <result property="yearRate" column="year_rate" jdbcType="DECIMAL"/>
            <result property="settlementMethod" column="settlement_method" jdbcType="VARCHAR"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,prem_code,policy_no,
        batch_code,year,period,
        reconcile_type,product_code,product_name,
        insurance_product_code,insurance_product_name,premium,
        tax_after_premium,year_rate,settlement_method,
        tax_rate,create_user,create_time,
        update_user,update_time
    </sql>
    <select id="findSettlementPolicyProductPremList"
            resultType="com.mpolicy.manage.modules.settlement.vo.SettlementPolicyProductTaxPremListOut">
        SELECT id,prem_code,policy_no,
        batch_code,year,period,
        reconcile_type,product_code,product_name,
        insurance_product_code,insurance_product_name,premium,
        tax_after_premium,year_rate,settlement_method,
        tax_rate,create_user,create_time,
        update_user,update_time
        FROM settlement_policy_product_fee_prem
        <where>
            <if test="input.batchCode != null and input.batchCode != ''">
                and batch_code = #{input.batchCode,jdbcType=VARCHAR}
            </if>
            <if test="input.policyNo != null and input.policyNo != ''">
                and policy_no = #{input.policyNo,jdbcType=VARCHAR}
            </if>
            <if test="input.reconcileType != null">
                and reconcile_type = #{input.reconcileType,jdbcType=INTEGER}
            </if>
        </where>
        order by update_time desc

    </select>

</mapper>
