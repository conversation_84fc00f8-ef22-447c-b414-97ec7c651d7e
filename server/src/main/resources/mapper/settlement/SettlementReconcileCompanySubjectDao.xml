<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanySubjectDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanySubjectEntity" id="settlementReconcileCompanySubjectMap">
        <result property="id" column="id"/>
        <result property="subjectRuleCode" column="subject_rule_code"/>
        <result property="reconcileCompanyCode" column="reconcile_company_code"/>
        <result property="reconcileSubjectCode" column="reconcile_subject_code"/>
        <result property="statementDate" column="statement_date"/>
        <result property="statementDateMonthEnd" column="statement_date_month_end"/>
        <result property="subjectScope" column="subject_scope"/>
        <result property="gracePeriodStatus" column="grace_period_status"/>
        <result property="gracePeriodDay" column="grace_period_day"/>
        <result property="underwriteTimeEndType" column="underwrite_time_end_type"/>
        <result property="underwriteTimeEndDay" column="underwrite_time_end_day"/>
        <result property="paymentPeriodEndType" column="payment_period_end_type"/>
        <result property="paymentPeriodEndDay" column="payment_period_end_day"/>
        <result property="effectiveTimeEndType" column="effective_time_end_type"/>
        <result property="effectiveTimeEndDay" column="effective_time_end_day"/>
        <result property="receiptTimeEndType" column="receipt_time_end_type"/>
        <result property="receiptTimeEndDay" column="receipt_time_end_day"/>
        <result property="callbackTimeEndType" column="callback_time_end_type"/>
        <result property="callbackTimeEndDay" column="callback_time_end_day"/>
        <result property="renewalSameInitial" column="renewal_same_initial"/>
        <result property="beforeUnderwriteTimeEndType" column="before_underwrite_time_end_type"/>
        <result property="beforeUnderwriteTimeEndDay" column="before_underwrite_time_end_day"/>
        <result property="beforeEffectiveTimeEndType" column="before_effective_time_end_type"/>
        <result property="beforeEffectiveTimeEndDat" column="before_effective_time_end_dat"/>
        <result property="afterUnderwriteTimeEndType" column="after_underwrite_time_end_type"/>
        <result property="afterUnderwriteTimeEndDay" column="after_underwrite_time_end_day"/>
        <result property="afterEffectiveTimeEndType" column="after_effective_time_end_type"/>
        <result property="afterEffectiveTimeEndDay" column="after_effective_time_end_day"/>
        <result property="surrenderTimeEndType" column="surrender_time_end_type"/>
        <result property="surrenderTimeEndDay" column="surrender_time_end_day"/>
        <result property="correctTimeEndType" column="correct_time_end_type"/>
        <result property="correctTimeEndDay" column="correct_time_end_day"/>
        <result property="subjectRuleStatus" column="subject_rule_status"/>
        <result property="mergeCode" column="merge_code"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>