<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementProgrammeSubjectDao">
    <!--分页获取手续费列表-->
    <select id="listProgrammeSubject" resultType="com.mpolicy.manage.modules.settlement.vo.settlement.autocost.SettlementProgrammeSubjectVo">
        select scps.programme_code as programmeCode,scps.programme_name as programmeName,
               scsi.subject_code as subjectCode,scsi.subject_name as subjectName
        from settlement_cost_programme_subject scps ,settlement_cost_subject_info scsi
        where scps.subject_code = scsi.subject_code
              scps.programme_code = #{programmeCode}
              and scps.deleted = 0


    </select>
</mapper>