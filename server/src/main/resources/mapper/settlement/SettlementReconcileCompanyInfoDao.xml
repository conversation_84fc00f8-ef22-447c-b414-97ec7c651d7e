<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.settlement.dao.SettlementReconcileCompanyInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.settlement.entity.SettlementReconcileCompanyInfoEntity" id="settlementReconcileCompanyInfoMap">
        <result property="id" column="id"/>
        <result property="reconcileCompanyCode" column="reconcile_company_code"/>
        <result property="externalSignatoryType" column="external_signatory_type"/>
        <result property="externalSignatoryCode" column="external_signatory_code"/>
        <result property="externalSignatoryName" column="external_signatory_name"/>
        <result property="innerSignatoryName" column="inner_signatory_name"/>
        <result property="innerSignatoryCode" column="inner_signatory_code"/>
        <result property="companyCode" column="company_code"/>
        <result property="subjectRuleCount" column="subject_rule_count"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <!--分页获取数据-->
    <select id="queryPage" resultType="com.mpolicy.manage.modules.settlement.vo.ReconcileCompanyInfoListOut">
        SELECT srci.*
        FROM settlement_reconcile_company_info  srci
        where srci.deleted = 0
        <if test="params.settlementCompanyCode != null and params.settlementCompanyCode != ''">
            and srci.settlement_company_code =#{params.settlementCompanyCode,jdbcType=VARCHAR}
        </if>
        <if test="params.reconcileCompanyName != null and params.reconcileCompanyName != ''">
            and srci.reconcile_company_name like concat('%',#{params.reconcileCompanyName,jdbcType=VARCHAR},'%')
        </if>
        <if test="params.companyCode != null and params.companyCode != ''">
            and srci.company_code =#{params.companyCode,jdbcType=VARCHAR}
        </if>
        <if test="params.reconcileCompanyCode != null and params.reconcileCompanyCode != ''">
            and srci.reconcile_company_code =#{params.reconcileCompanyCode,jdbcType=VARCHAR}
        </if>
        <if test="params.innerSignatoryCode != null and params.innerSignatoryCode != ''">
            and srci.inner_signatory_code =#{params.innerSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="params.externalSignatoryCode != null and params.externalSignatoryCode != ''">
            and srci.external_signatory_code like concat('%',#{params.externalSignatoryCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="params.subjectReconcileCompanyCode != null and params.subjectReconcileCompanyCode != ''">
            and srci.reconcile_company_code =#{params.subjectReconcileCompanyCode,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>