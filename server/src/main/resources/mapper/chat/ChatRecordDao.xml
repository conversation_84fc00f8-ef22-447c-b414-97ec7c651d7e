<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.chat.dao.ChatRecordDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.chat.entity.ChatRecordEntity" id="chatRecordMap">
        <result property="messageId" column="message_id"/>
        <result property="timestamp" column="timestamp"/>
        <result property="orderCode" column="order_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="fromCode" column="from_code"/>
        <result property="toCode" column="to_code"/>
        <result property="groupCode" column="group_code"/>
        <result property="eventType" column="event_type"/>
        <result property="eventCode" column="event_code"/>
        <result property="sendContent" column="send_content"/>
        <result property="receiveContent" column="receive_content"/>
        <result property="agentOpenNum" column="agent_open_num"/>
        <result property="customerOpenNum" column="customer_open_num"/>
        <result property="eventStatus" column="event_status"/>
    </resultMap>
    <!--获取聊天记录表列表-->
    <select id="findChatRecordList" resultType="com.mpolicy.manage.modules.chat.entity.ChatRecordListOut">
        SELECT cr.event_type as eventType,cr.`timestamp`,cr.message_id as messageId,
        IF(cr.from_code =aui.agent_code,'顾问','客户') as sendName,cr.send_content as sendContent,
        IF(cr.from_code =aui.agent_code,'客户','顾问') as receiveName,cr.receive_content as receiveContent,
        cr.event_code as eventCode,cr.order_code as orderCode,cr.from_code  AS fromCode,
        cr.to_code AS toCode
        FROM chat_record cr
        LEFT JOIN agent_user_info aui ON cr.agent_code  = aui.agent_code
        LEFT JOIN customer_basic_info cbi ON cbi.customer_code = cr.customer_code
        <where>
            <if test="param2.sendBeginTime != null">
                and cr.timestamp &gt;=#{param2.sendBeginTime,jdbcType=BIGINT}
            </if>
            <if test="param2.sendEndTime != null">
                and cr.timestamp &lt;=#{param2.sendEndTime,jdbcType=BIGINT}
            </if>
            <if test="param2.fromCode != null and param2.fromCode!=''">
                and cr.from_code =#{param2.fromCode,jdbcType=VARCHAR}
            </if>
            <if test="param2.toCode != null and param2.toCode!=''">
                and cr.to_code =#{param2.toCode,jdbcType=VARCHAR}
            </if>
            <if test="param2.orderCode != null and param2.orderCode!=''">
                and cr.order_code =#{param2.orderCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY cr.timestamp DESC
    </select>


</mapper>