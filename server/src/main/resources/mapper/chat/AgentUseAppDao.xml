<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.chat.dao.AgentUseAppDao">

    <select id="findAgentCustomerList2" resultType="com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut">
        select ua.agent_code AS  'agentCode',
        count(DISTINCT CASE WHEN (ua.create_time >= #{param.totalBeginTime} and ua.create_time  &lt;  #{param.totalEndTime}) THEN ua.customer_code WHEN #{param.totalBeginTime}='' THEN ua.customer_code ELSE NUll END) AS  'customerBindTotal',
        SUM(IF(cbi.certification_status =1,1,0)) as realCustomerNum
        <choose>
            <when test="param.beginTime != null and param.beginTime != '' and param.endTime != null and param.endTime != '' ">
                ,SUM(IF(cbi.certification_status =1 ,IF((ua.create_time >= #{param.beginTime} and ua.create_time  &lt;  #{param.endTime}) , 1 ,0),0)) AS newRealCustomerNum,
                COUNT(DISTINCT IF (ua.create_time >= #{param.beginTime} and ua.create_time  &lt;  #{param.endTime}, ua.customer_code,NULL)) AS  'customerBindAdded'
            </when>
            <otherwise>
                ,SUM(IF(cbi.certification_status =1  ,1,0)) as newRealCustomerNum,
                COUNT(DISTINCT ua.customer_code ) AS  'customerBindAdded'
            </otherwise>
        </choose>
        FROM customer_agent_map ua
        LEFT JOIN customer_basic_info cbi ON ua.customer_code = cbi.customer_code
        WHERE ua.deleted = 0 GROUP BY ua.agent_code;
    </select>
    <!--新增客户数 新增实名客户数  客户数 实名客户数-->
    <select id="findAgentCustomerList" parameterType="com.mpolicy.manage.modules.chat.entity.AgentUserAppListOutVo" resultType="com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut">
        SELECT  ua.agent_code
        <choose>
            <when test="param.beginTime != null and param.beginTime != '' and param.endTime != null and param.endTime != '' ">
                ,SUM(IF(cbi.certification_status =1 ,IF((ua.create_time >= #{param.beginTime} and ua.create_time  &lt;  #{param.endTime}) , 1 ,0),0)) AS newRealCustomerNum,
                SUM( IF (ua.create_time >= #{param.beginTime} and ua.create_time  &lt;  #{param.endTime},1,0)) AS  'customerBindAdded'
            </when>
            <otherwise>
                ,COUNT(cbi.customer_code) customerBindAdded
                ,SUM(IF(cbi.certification_status =1,1,0)) as newRealCustomerNum
            </otherwise>
        </choose>
        <choose>
            <when test="param.totalBeginTime != null and param.totalBeginTime != '' and param.totalEndTime != null and param.totalEndTime != '' ">
                ,SUM(IF(cbi.certification_status =1 ,IF((ua.create_time >= #{param.beginTime} and ua.create_time  &lt;  #{param.endTime}) , 1 ,0),0)) AS realCustomerNum,
                SUM(IF (ua.create_time >= #{param.beginTime} and ua.create_time  &lt;  #{param.endTime}, 1,0)) AS  customerBindTotal
            </when>
            <otherwise>
                ,COUNT(cbi.customer_code) AS customerBindTotal
                ,SUM(IF(cbi.certification_status =1,1,0)) as realCustomerNum
            </otherwise>
        </choose>
        FROM customer_agent_map ua
        LEFT JOIN customer_basic_info cbi ON ua.customer_code = cbi.customer_code
        WHERE ua.deleted = 0 and ua.agent_code in
        <foreach collection="param.agentCodeList" open="(" close=")" item="agentCode" separator=",">
            #{agentCode,jdbcType=VARCHAR}
        </foreach>
         GROUP BY ua.agent_code
    </select>

    <select id="findDemandEvaluationList" resultType="com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut">
        select ua.agent_code
        <choose>
            <when test="param.beginTime != null and param.beginTime != '' and param.endTime != null and param.endTime != '' ">
                ,SUM(IF (iei.create_time >=#{param.beginTime} and iei.create_time &lt; #{param.endTime},1,0))  AS   'customerEvaluationAdded'
            </when>
            <otherwise>
                ,count(DISTINCT iei.evaluation_code) AS   'customerEvaluationAdded'
            </otherwise>
        </choose>
        <choose>
            <when test="param.totalBeginTime != null and param.totalBeginTime != '' and param.totalEndTime != null and param.totalEndTime != '' ">
                ,SUM(IF (iei.create_time >=#{param.totalBeginTime} and iei.create_time &lt; #{param.totalBeginTime},1,0))  AS   'customerEvaluationTotal'
            </when>
            <otherwise>
                ,count(DISTINCT iei.evaluation_code) AS   'customerEvaluationTotal'
            </otherwise>
        </choose>
        FROM customer_agent_map ua
        LEFT JOIN inte_evaluation_info iei ON iei.customer_code = ua.customer_code
        WHERE ua.deleted = 0  and ua.agent_code in
        <foreach collection="param.agentCodeList" open="(" close=")" item="agentCode" separator=",">
            #{agentCode,jdbcType=VARCHAR}
        </foreach>
        GROUP BY ua.agent_code
    </select>

    <select id="findPlanList" resultType="com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut">
        select e.agent_code
        <choose>
            <when test="param.beginTime != null and param.beginTime != '' and param.endTime != null and param.endTime != '' ">
                ,SUM(IF (e.create_time >=#{param.beginTime} and e.create_time &lt; #{param.endTime},1,0))  AS   'supportConceptAdded'
            </when>
            <otherwise>
               ,count(DISTINCT e.plan_group_code) AS 'supportConceptAdded'
            </otherwise>
        </choose>
        <choose>
            <when test="param.totalBeginTime != null and param.totalBeginTime != '' and param.totalEndTime != null and param.totalEndTime != '' ">
                ,SUM(IF (e.create_time >=#{param.beginTime} and e.create_time &lt; #{param.endTime},1,0))  AS   'supportConceptTotal'
            </when>
            <otherwise>
                ,count(DISTINCT e.plan_group_code) AS   'supportConceptTotal'
            </otherwise>
        </choose>
        FROM plan_group_info e
        WHERE e.deleted = 0 and e.agent_code in
        <foreach collection="param.agentCodeList" open="(" close=")" item="agentCode" separator=",">
            #{agentCode,jdbcType=VARCHAR}
        </foreach>
        GROUP BY e.agent_code
    </select>

    <select id="findPolicyCustodyList" resultType="com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut">
        select p.agent_code
        <choose>
            <when test="param.beginTime != null and param.beginTime != '' and param.endTime != null and param.endTime != '' ">
                ,SUM(IF (p.create_time >=#{param.beginTime} and p.create_time &lt; #{param.endTime},1,0))  AS   'policyCustodyAdded'
            </when>
            <otherwise>
                ,count(DISTINCT  p.record_sn ) AS 'policyCustodyAdded'
            </otherwise>
        </choose>
        <choose>
            <when test="param.totalBeginTime != null and param.totalBeginTime != '' and param.totalEndTime != null and param.totalEndTime != '' ">
                ,SUM(IF (p.create_time >=#{param.totalBeginTime} and p.create_time &lt; #{param.totalEndTime},1,0))  AS   'policyCustodyTotal'
            </when>
            <otherwise>
               , count(DISTINCT p.record_sn) AS 'policyCustodyTotal'
            </otherwise>
        </choose>
        FROM policy_user_record p
        WHERE  p.record_status = 'TRUST_STATUS:3' and p.agent_code in
        <foreach collection="param.agentCodeList" open="(" close=")" item="agentCode" separator=",">
            #{agentCode,jdbcType=VARCHAR}
        </foreach>
        GROUP BY p.agent_code
    </select>





</mapper>