<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.chat.dao.ChatWorkOrderDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.chat.entity.ChatWorkOrderEntity" id="chatWorkOrderMap">
        <result property="id" column="id"/>
        <result property="orderCode" column="order_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="orderStatus" column="order_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="commentStatus" column="comment_status"/>
    </resultMap>

    <select id="queryOrderPage" resultType="com.mpolicy.manage.modules.sys.vo.ChatWorkOrderVo">
        SELECT o.*,
               a.agent_name,
               c.nick_name,
               c.real_name
        FROM chat_work_order o
                 LEFT JOIN agent_user_info a
                           ON o.agent_code = a.agent_code
                 LEFT JOIN customer_basic_info c
                           ON o.customer_code = c.customer_code
            ${ew.customSqlSegment}
    </select>

    <!--获取服务工单列表-->
    <select id="findChatWorkOrderList" resultType="com.mpolicy.manage.modules.chat.entity.ChatWorkOrderListOut">
        SELECT cwo.order_code as orderCode,cwo.agent_code AS agentCode,
        aui.agent_name AS agentName,cwo.customer_code AS customerCode,
        cbi.nick_name AS nickName,cbi.real_name AS realName,
        cwo.create_time AS create_time,cwo.order_status as orderStatus,
        cwo.comment_status AS commentStatus
        FROM chat_work_order cwo
        LEFT JOIN agent_user_info aui ON cwo.agent_code =aui.agent_code
        LEFT JOIN customer_basic_info cbi ON cwo.customer_code = cbi.customer_code
        <where>
            <if test="param2.agentName != null and param2.agentName != ''">
                and aui.agent_name like CONCAT('%' ,#{param2.agentName,jdbcType=VARCHAR},'%')
            </if>
            <if test="param2.customerName != null and param2.customerName != ''">
             and (aui.nick_name like CONCAT('%' ,#{param2.customerName,jdbcType=VARCHAR},'%')
                or aui.real_name like CONCAT('%' ,#{param2.customerName,jdbcType=VARCHAR},'%'))
            </if>
            <if test="param2.orderStatus != null">
                and cwo.order_status =#{param2.orderStatus,jdbcType=INTEGER}
            </if>
            <if test="param2.commentStatus != null">
                and cwo.comment_status =#{param2.commentStatus,jdbcType=INTEGER}
            </if>
            <if test="param2.createBeginTime != null">
                and cwo.create_time &gt;=#{param2.createBeginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="param2.createEndTime != null">
                and cwo.create_time &lt;=#{param2.createBeginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="param2.orgList !=null and param2.orgList.size() >0 ">
                AND aui.org_code in
                <foreach collection="param2.orgList" item="orgCode" open="(" close=")" separator=",">
                    #{orgCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
         ORDER BY cwo.create_time DESC
    </select>
    <!--获取服务工单聊天记录-->
    <select id="findWorkOrderChatRecordList" resultType="com.mpolicy.manage.modules.chat.entity.WorkOrderChatRecordListOut">
        SELECT cr.event_type as eventType,cr.`timestamp`,
        IF(cr.from_code =aui.agent_code,aui.agent_name,IF(cbi.real_name IS NULL,cbi.nick_name,
        CONCAT(cbi.nick_name,'(',cbi.real_name,')'))) as sendName,cr.send_content as sendContent
        FROM chat_record cr
        LEFT JOIN agent_user_info aui ON cr.agent_code  = aui.agent_code
        LEFT JOIN customer_basic_info cbi ON cbi.customer_code = cr.customer_code
        <where>
           and cr.order_code=#{param2.orderCode,jdbcType=VARCHAR}
        </where>
        ORDER BY cr.timestamp DESC
    </select>
</mapper>