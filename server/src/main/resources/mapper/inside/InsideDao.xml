<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.inside.dao.InsideDao">
    <select id="getAgentOrRecommendCodeAndNameByReferrerCode" resultType="map">
        <choose>
            <when test="referrerType == 0">
                select referrer_wno, referrer_name, #{referrerCode} referrer_code from channel_application_referrer where referrer_code = #{referrerCode}
            </when>
            <when test="referrerType == 1">
                select agent_name, #{referrerCode} agent_code from agent_user_info where agent_code = #{referrerCode}
            </when>
            <otherwise>
                select CONCAT('ep_policy_contract_info.referrer_type=', #{referrerType}), #{referrerCode} referrer_code referrer_msg
            </otherwise>
        </choose>
    </select>
</mapper>