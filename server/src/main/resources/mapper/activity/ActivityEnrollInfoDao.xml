<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.activity.dao.ActivityEnrollInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.activity.entity.ActivityEnrollInfoEntity" id="activityEnrollInfoMap">
        <result property="id" column="id"/>
        <result property="activityCode" column="activity_code"/>
        <result property="enrollCode" column="enroll_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="nickName" column="nick_name"/>
        <result property="realName" column="real_name"/>
        <result property="referrerWno" column="referrer_wno"/>
        <result property="referrerRegion" column="referrer_region"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>