<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.activity.dao.RuralPromotionInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.activity.entity.RuralPromotionInfoEntity" id="ruralPromotionInfoMap">
        <result property="id" column="id"/>
        <result property="promotionCode" column="promotion_code"/>
        <result property="promotionName" column="promotion_name"/>
        <result property="customerCode" column="customer_code"/>
        <result property="referrerCode" column="referrer_code"/>
        <result property="referrerName" column="referrer_name"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="promotionDetails" column="promotion_details"/>
        <result property="promotionStatus" column="promotion_status"/>
        <result property="pageView" column="page_view"/>
        <result property="enrollNum" column="enroll_num"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="thumbnailUrl" column="thumbnail_url"/>
        <result property="bannerId" column="banner_id"/>
        <result property="appletShareCode" column="applet_share_code"/>
        <result property="appletShareTitle" column="applet_share_title"/>
        <result property="appletShareImage" column="applet_share_image"/>
        <result property="appletSharePath" column="applet_share_path"/>
        <result property="provinceCode" column="province_code"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="areaCode" column="area_code"/>
        <result property="areaName" column="area_name"/>
        <result property="address" column="address"/>
        <result property="createTime" column="create_time"/>
        <result property="crateUser" column="crate_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <select id="pageList" resultType="com.mpolicy.manage.modules.activity.vo.RuralPromotionPageListOut">
        SELECT
        rpi.*,
        IFNULL(rps.activity_count,0) as activityCount,
        IFNULL(rps.activity_premium,0) as activityPremium,
        IFNULL(rps.history_customer_num,0) as historyCustomerNum,
        IFNULL(rps.customer_num,0) as customerNum,
        IFNULL(rps.trust_people_num,0) as trustPeopleNum,
        IFNULL(rps.trust_policy_num,0) as trustPolicyNum
        FROM
        rural_promotion_info rpi
        LEFT JOIN rural_promotion_statistics rps
        ON rpi.promotion_code = rps.promotion_code
        <where>
            <if test="input.promotionName !=null and input.promotionName !='' ">
                AND rpi.rural_name like concat('%',#{input.promotionName,jdbcType=VARCHAR},'%')
            </if>
        </where>
        order by rpi.id desc
    </select>

    <select id="export" resultType="com.mpolicy.manage.modules.activity.vo.RuralPromotionPageListOut">
        select *,
                IFNULL(rps.activity_count,0) as activityCount,
                IFNULL(rps.activity_premium,0) as activityPremium,
                IFNULL(rps.promotion_count,0) as promotionCount,
                IFNULL(rps.promotion_premium,0) as promotionPremium,
                IFNULL(rps.history_customer_num,0) as historyCustomerNum,
                IFNULL(rps.customer_num,0) as customerNum,
                IFNULL(rps.trust_people_num,0) as trustPeopleNum,
                IFNULL(rps.trust_policy_num,0) as trustPolicyNum,
                b.referrer_region as referrerRegion,
                (SELECT mp.dic_value FROM mp_dictionary mp WHERE mp.dic_key = b.referrer_region) AS referrerRegionName,
                b.referrer_channel_name as referrerChannelName
        from
            rural_promotion_info rpi
        LEFT JOIN rural_promotion_statistics rps
        ON rpi.promotion_code = rps.promotion_code
        LEFT JOIN channel_application_referrer b
        ON rpi.referrer_code = b.referrer_code
        <where>
            <if test="input.promotionName !=null and input.promotionName !='' ">
                AND rpi.rural_name like concat('%',#{input.promotionName,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>