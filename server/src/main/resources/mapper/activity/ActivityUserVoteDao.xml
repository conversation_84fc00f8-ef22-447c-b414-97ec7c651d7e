<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.activity.dao.ActivityUserVoteDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.activity.entity.ActivityUserVoteEntity" id="activityUserVoteMap">
        <result property="id" column="id"/>
        <result property="acceptCustomerCode" column="accept_customer_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="voteDate" column="vote_date"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>