<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.activity.dao.ActivityGirlEnrollDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.activity.entity.ActivityGirlEnrollDataEntity" id="activityGirlEnrollDataMap">
        <result property="id" column="id"/>
        <result property="activityCode" column="activity_code"/>
        <result property="enrollCode" column="enroll_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="blessings" column="blessings"/>
        <result property="motto" column="motto"/>
        <result property="servingCustomerNum" column="serving_customer_num"/>
        <result property="totalNum" column="total_num"/>
        <result property="lifePhotoUrl" column="life_photo_url"/>
        <result property="status" column="status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="pageList" resultType="com.mpolicy.manage.modules.activity.vo.GirlEnrollPageListOut">
        SELECT
            aged.*,
            aei.nick_name,
            aei.real_name,
            aei.referrer_region,
            aei.referrer_wno
        FROM
            activity_girl_enroll_data aged
                LEFT JOIN activity_enroll_info aei
                          ON aged.enroll_code = aei.enroll_code
        <where>
            <if test="input.realName !=null and input.realName !='' ">
                AND aei.real_name like concat('%',#{input.realName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.referrerWno !=null and input.referrerWno !='' ">
                AND aei.referrer_wno like concat('%',#{input.referrerWno,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.referrerRegion !=null and input.referrerRegion !='' ">
                AND aei.referrer_region = #{input.referrerRegion,jdbcType=VARCHAR}
            </if>
        </where>
            order by aged.id desc
    </select>
</mapper>