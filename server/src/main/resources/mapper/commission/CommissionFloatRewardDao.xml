<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.commission.dao.CommissionFloatRewardDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.commission.entity.CommissionFloatRewardEntity"
               id="commissionFloatRewardMap">
        <result property="id" column="id"/>
        <result property="programmeCode" column="programme_code"/>
        <result property="programmeName" column="programme_name"/>
        <result property="programmeType" column="programme_type"/>
        <result property="programmeStatus" column="programme_status"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="programmeDesc" column="programme_desc"/>
        <result property="ruleMode" column="rule_mode"/>
        <result property="ruleModeCode" column="rule_mode_code"/>
        <result property="releaseTime" column="release_time"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findCommissionFloatRewardList"
            resultType="com.mpolicy.manage.modules.commission.vo.CommissionFloatRewardListOut">
        SELECT id,
               programme_code   AS programmeCode,
               programme_name   AS programmeName,
               programme_type   AS programmeType,
               programme_status AS programmeStatus,
               begin_time       AS beginTime,
               end_time         AS endTime,
               programme_desc   AS programmeDesc,
               rule_mode        AS ruleMode,
               rule_mode_code   AS ruleModeCode,
               release_time     AS releaseTime,
               remark,
               update_user      AS updateUser,
               update_time      AS updateTime
        FROM commission_float_reward
        where deleted = 0
        <if test="input.programmeCode != null and input.programmeCode != ''">
            and programme_code = #{input.programmeCode,jdbcType=VARCHAR}
        </if>
        <if test="input.programmeName!= null and input.programmeName != ''">
            and programme_name like concat('%',#{input.programmeName,jdbcType=VARCHAR},'%')
        </if>
    </select>
</mapper>