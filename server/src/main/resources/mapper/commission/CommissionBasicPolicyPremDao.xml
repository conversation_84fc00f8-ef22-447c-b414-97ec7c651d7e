<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.commission.dao.CommissionBasicPolicyPremDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.commission.entity.CommissionBasicPolicyPremEntity"
               id="commissionBasicPolicyPremMap">
        <result property="id" column="id"/>
        <result property="policyNo" column="policy_no"/>
        <result property="batchCode" column="batch_code"/>
        <result property="premium" column="premium"/>
        <result property="commissionRate" column="commission_rate"/>
        <result property="createUser" column="create_user"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findCommissionBasicPolicyPremList"
            resultType="com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListOut">
        SELECT id,
               policy_no       AS policyNo,
               batch_code      AS batchCode,
               premium,
        year,
            period,
               product_code,
               product_name,
               commission_rate AS commissionRate,
               create_user     AS createUser,
               create_time     AS createTime,
               update_user     AS updateUser,
               update_time     AS updateTime,
               settlement_company_name
        FROM commission_basic_policy_prem
        <where>
            <if test="input.batchCode != null and input.batchCode != ''">
                and batch_code = #{input.batchCode,jdbcType=VARCHAR}
            </if>
            <if test="input.policyNo != null and input.policyNo != ''">
                and policy_no = #{input.policyNo,jdbcType=VARCHAR}
            </if>
        </where>
        order by update_time
    </select>
</mapper>