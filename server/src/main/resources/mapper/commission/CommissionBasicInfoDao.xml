<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.commission.dao.CommissionBasicInfoDao">
    <!--获取数据列表-->
    <select id="findCommissionBasicInfoList"
            resultType="com.mpolicy.manage.modules.commission.vo.CommissionBasicInfoListOut">
        SELECT cbi.id,
               cbi.commission_code,
               cbi.company_code,
               cbi.company_name,
               cbi.settlement_company_code,
               cbi.settlement_company_name,
               cbi.begin_time,
               cbi.end_time,
               cbi.commission_status,
               cbi.prem_status,
               cbi.prem_file_path,
               cbi.update_user,
               cbi.update_time
        FROM commission_basic_info cbi
        LEFT JOIN commission_basic_prem cbp ON cbi.commission_code = cbp.commission_code
        where cbi.deleted = 0
        <if test="input.companyCode != null and input.companyCode != ''">
            and cbi.company_code = #{input.companyCode,jdbcType=VARCHAR}
        </if>
        <if test="input.settlementCompanyCode != null and input.settlementCompanyCode != ''">
            and cbi.settlement_company_code = #{input.settlementCompanyCode,jdbcType=VARCHAR}
        </if>
        <if test="input.productName != null and input.productName != ''">
            and cbp.product_name like concat('%', #{input.productName,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.commissionStatus != null">
            and cbi.commission_status = #{input.commissionStatus,jdbcType=INTEGER}
        </if>
        group by cbi.commission_code
        order by cbi.company_code,   cbi.update_time desc
    </select>
</mapper>