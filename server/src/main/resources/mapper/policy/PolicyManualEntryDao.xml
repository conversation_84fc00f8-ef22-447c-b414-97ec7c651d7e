<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.policy.dao.PolicyManualEntryDao">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.policy.entity.PolicyManualEntryEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="entryCode" column="entry_code" jdbcType="VARCHAR"/>
            <result property="contractCode" column="contract_code" jdbcType="VARCHAR"/>
            <result property="agentCode" column="agent_code" jdbcType="VARCHAR"/>
            <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="applicantName" column="applicant_name" jdbcType="VARCHAR"/>
            <result property="insuredsName" column="insureds_name" jdbcType="VARCHAR"/>
            <result property="portfolioName" column="portfolio_name" jdbcType="VARCHAR"/>
            <result property="insuredPeriodBrief" column="insured_period_brief" jdbcType="VARCHAR"/>
            <result property="insuredPeriod" column="insured_period" jdbcType="VARCHAR"/>
            <result property="paymentPeriodBrief" column="payment_period_brief" jdbcType="VARCHAR"/>
            <result property="paymentPeriod" column="payment_period" jdbcType="VARCHAR"/>
            <result property="paymentPhase" column="payment_phase" jdbcType="INTEGER"/>
            <result property="premium" column="premium" jdbcType="DECIMAL"/>
            <result property="policyStatus" column="policy_status" jdbcType="VARCHAR"/>
            <result property="isCommit" column="is_commit" jdbcType="TINYINT"/>
            <result property="data" column="data" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="revision" column="revision" jdbcType="BIGINT"/>
    </resultMap>

</mapper>
