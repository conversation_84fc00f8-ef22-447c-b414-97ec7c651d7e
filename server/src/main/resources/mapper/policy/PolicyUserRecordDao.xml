<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.PolicyUserRecordDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.PolicyUserRecordEntity" id="policyUserRecordMap">
        <result property="id" column="id"/>
        <result property="recordSn" column="record_sn"/>
        <result property="contractCode" column="contract_code"/>
        <result property="customerCode" column="customer_code"/>
        <result property="applicantType" column="applicant_type"/>
        <result property="insuredType" column="insured_type"/>
        <result property="customerTips" column="customer_tips"/>
        <result property="operatorTips" column="operator_tips"/>
        <result property="companyName" column="company_name"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="insuredsName" column="insureds_name"/>
        <result property="portfolioName" column="portfolio_name"/>
        <result property="customerName" column="customer_name"/>
        <result property="insuredPeriod" column="insured_period"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="paymentPhase" column="payment_phase"/>
        <result property="policyStatus" column="policy_status"/>
        <result property="recordStatus" column="record_status"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <select id="querySendMsgList" resultType="com.mpolicy.manage.modules.policy.vo.PolicyUserRecordMsgListVo">
        SELECT
            pur.id,
            pur.record_sn,
            pur.trust_policy_type,
            pur.customer_family_member_name,
            pur.customer_family_member_id_card,
            pur.contract_code,
            pur.customer_code,
            pur.agent_code,
            pur.order_code,
            pur.customer_applicant_relationship,
            pur.applicant_type,
            pur.insured_type,
            pur.policy_no,
            pur.customer_name,
            pur.premium,
            pur.policy_status,
            pur.record_status,
            pur.is_agent_upload,
            pur.create_user,
            pur.create_time,
            pur.revision,
            cbi.real_name,
            cbi.identification_num,
            cbi.referrer_code,
            cbi.inner_referrer_code
        FROM
            policy_user_record pur
                LEFT JOIN customer_basic_info cbi
                          ON pur.customer_code = cbi.customer_code
        WHERE pur.is_agent_upload = 0
          AND pur.record_status = 'TRUST_STATUS:0'
          AND pur.create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
          AND cbi.referrer_code IS NOT NULL ;
    </select>
</mapper>