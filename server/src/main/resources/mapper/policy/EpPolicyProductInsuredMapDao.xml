<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.EpPolicyProductInsuredMapDao">

    <select id="listInsuredProduct" resultType="com.mpolicy.policy.common.ep.policy.product.EpInsuredProductVo">
        select
            a.contract_code ,b.insured_code ,c.insured_name,c.insured_id_card,
            b.policy_product_code ,a.product_code ,a.product_name ,a.plan_code ,
            a.plan_name ,a.protocol_product_name ,
            ifnull(b.product_status ,a.product_status) as product_status  ,
            a.prod_type_code ,a.main_insurance ,a.additional_risks_type ,
            a.coverage ,a.coverage_unit ,
            ifnull(b.effective_date,a.effective_date) as effective_date ,
            ifnull(b.end_date ,a.end_date) as end_date ,
            ifnull(b.surrender_time,a.surrender_time )as surrender_time  ,
            ifnull(b.surrender_amount ,a.surrender_amount) as surrender_amount ,
            ifnull(b.insured_period_type,a.insured_period_type) as insured_period_type,
            ifnull(b.insured_period,a.insured_period) as insured_period,
            ifnull(b.period_type,a.period_type) as period_type,
            ifnull(b.payment_period_type,a.payment_period_type) as payment_period_type ,
            ifnull(b.payment_period ,a.payment_period) as payment_period_type ,
            a.ann_draw_age ,b.beneficiary_type ,
            ifnull(b.premium ,a.premium) as premium ,a.deductible ,
            ifnull(b.copies ,a.copies ) as copies
        from ep_policy_product_info a
                 left join ep_policy_product_insured_map b on a.contract_code = b.contract_code and a.product_code = b.product_code and b.deleted =0
                 left join ep_policy_insured_info c on b.contract_code =c.contract_code and b.insured_code =c.insured_code and c.deleted =0
        where   a.contract_code = #{contractCode}
          and     a.deleted =0

    </select>
</mapper>
