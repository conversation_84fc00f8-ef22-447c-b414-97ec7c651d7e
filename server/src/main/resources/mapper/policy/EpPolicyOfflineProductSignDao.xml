<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.EpPolicyOfflineProductSignDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.EpPolicyOfflineProductSignEntity" id="epPolicyOfflineProductSignMap">
        <result property="id" column="id"/>
        <result property="signCode" column="sign_code"/>
        <result property="signType" column="sign_type"/>
        <result property="businessCode" column="business_code"/>
        <result property="agentCode" column="agent_code"/>
        <result property="agentOrgCode" column="agent_org_code"/>
        <result property="agentOrgName" column="agent_org_name"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="commodityCode" column="commodity_code"/>
        <result property="commodityName" column="commodity_name"/>
        <result property="realName" column="real_name"/>
        <result property="idNo" column="id_no"/>
        <result property="mobile" column="mobile"/>
        <result property="signContractNo" column="sign_contract_no"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signMsg" column="sign_msg"/>
        <result property="policySignStatus" column="policy_sign_status"/>
        <result property="finishSignTime" column="finish_sign_time"/>
        <result property="signModel" column="sign_model"/>
        <result property="url" column="url"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <select id="pageList" resultType="com.mpolicy.manage.modules.policy.vo.EpPolicyOfflineProductSignVo">
        SELECT
            epops.*,
            aui.agent_name AS agentName
        FROM
            ep_policy_offline_product_sign epops
                LEFT JOIN agent_user_info aui
                          ON epops.agent_code = aui.agent_code
        <where>
            AND epops.sign_type = 'notification'
            <if test="params.businessCode !=null and params.businessCode !=''">
                AND epops.business_code = #{params.businessCode,jdbcType=VARCHAR}
            </if>
            <if test="params.finishSignTime !=null and params.finishSignTime !=''">
                AND DATE_FORMAT(epops.finish_sign_time,'%Y-%m-%d') = #{params.finishSignTime,jdbcType=VARCHAR}
            </if>
            <if test="params.commodityName !=null and params.commodityName !=''">
                AND epops.commodity_name like concat('%',#{params.commodityName,jdbcType=VARCHAR},'%')
            </if>
        </where>
        order by epops.id desc
    </select>
</mapper>
