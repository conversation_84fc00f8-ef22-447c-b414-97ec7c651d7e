<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.lost.EpPolicyLostRegisterDao">

    <sql id="lostList_query">
        <if test="recommendId !=null and recommendId !='' ">
            and a.recommend_id=#{recommendId}
        </if>
        <if test="registerUserId !=null and registerUserId !='' ">
            and a.register_user_id=#{registerUserId}
        </if>
        <if test="handleStatus !=null">
            and a.handle_status = #{handleStatus}
        </if>
        <if test="policyNo !=null and policyNo !='' ">
            and a.policy_no = #{policyNo}
        </if>
        <if test="startDate !=null and startDate !='' ">
            and a.register_time >= #{startDate}
        </if>
        <if test="endDate !=null and endDate !='' ">
            <![CDATA[
                and a.register_time <= #{endDate}
            ]]>
        </if>
        <if test="insuredName !=null and insuredName !='' ">
            and a.insured_name = #{insuredName}
        </if>
        <if test="insuredIdNumber !=null and insuredIdNumber !='' ">
            and a.insured_id_number = #{insuredIdNumber}
        </if>
        <if test="processUser !=null and processUser !='' ">
            <if test="processUser == 1 ">
                and (a.update_user is not null and a.update_user != 'NO_LOGIN_USER')
            </if>
            <if test="processUser == 2 ">
                and (a.update_user is null or a.update_user = 'NO_LOGIN_USER')
            </if>

        </if>
        <if test="handleResult !=null and handleResult !='' ">
            <if test="handleResult == 999 ">
                and a.manual_handle_result = #{handleResultDetail}
            </if>
            <if test="handleResult != 999 ">
                and a.handle_result= #{handleResult}
            </if>

        </if>
    </sql>

    <select id="countList"
            parameterType="com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterQuery" resultType="java.lang.Integer">
        select
            count(1)
        from ep_policy_lost_register a
        left join channel_application_referrer b  on a.register_user_id = b.referrer_wno
        left join ep_policy_contract_info c on a.policy_no = c.policy_no
        left join mp_dictionary d on b.referrer_region = d.dic_key
        where 1=1
        <include refid="lostList_query" />
    </select>

    <select id="queryList"
            parameterType="com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterQuery"
            resultType="com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterList">
        select
            a.id,
            a.register_time as registerTime,
            a.policy_no as policyNo,
            c.`main_product_name` productName,
            a.insured_name as insured_Name,
            a.insured_id_number as insuredIdNumber,
            a.recommend_Name as recommendName,
            a.recommend_id as recommendId,
            a.handle_status as handleStatus,
            a.handle_result as handleResult,
            a.manual_handle_result,
            a.update_time as updateTime,
            if(a.update_user is null,'系统',if(a.update_user = 'NO_LOGIN_USER','系统',a.update_user)) as updateUser,
            a.register_user_name as registerUserName,
            a.register_user_id as registerUserId,
            d.dic_value registerUserRegionName,
            b.referrer_ogr_name as referrerOgrName,
            a.handle_success_type as handleSuccessType,
            a.handle_success_result as handleSuccessResult,
            a.remark,
            a.fail_result as failResult
        from ep_policy_lost_register a
        left join channel_application_referrer b  on a.register_user_id = b.referrer_wno
        left join ep_policy_contract_info c on a.policy_no = c.policy_no
        left join mp_dictionary d on b.referrer_region = d.dic_key
        where 1=1
        <include refid="lostList_query" />
        order by register_time desc,a.id desc
        limit #{offset},#{size}
    </select>
    <select id="queryFailedData"
            resultType="com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterList">
        select
            a.id,
            a.register_time as registerTime,
            a.policy_no as policyNo,
            c.`main_product_name` productName,
            a.insured_name as insured_Name,
            a.insured_id_number as insuredIdNumber,
            a.recommend_Name as recommendName,
            a.recommend_id as recommendId,
            a.handle_status as handleStatus,
            a.handle_result as handleResult,
            a.manual_handle_result,
            a.update_time as updateTime,
            a.register_user_name as registerUserName,
            a.register_user_id as registerUserId,
            d.dic_value registerUserRegionName,
            b.referrer_ogr_name as referrerOgrName,
            a.remark
        from ep_policy_lost_register a
        left join channel_application_referrer b  on a.register_user_id = b.referrer_wno
        left join ep_policy_contract_info c on a.policy_no = c.policy_no
        left join mp_dictionary d on b.referrer_region = d.dic_key
        where a.handle_status = 2 and a.handle_result != 12
        order by register_time desc,a.id desc
    </select>

    <select id="queryResultList"
            resultType="com.mpolicy.manage.modules.policy.vo.lost.PolicyLostRegisterList">
        SELECT DISTINCT(manual_handle_result) FROM ep_policy_lost_register;
    </select>
</mapper>
