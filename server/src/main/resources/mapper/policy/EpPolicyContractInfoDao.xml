<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.EpPolicyContractInfoDao">

    <resultMap type="com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity" id="policyContractInfoMap">
        <result property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="policyNo" column="policy_no"/>
        <result property="applicantPolicyNo" column="applicant_policy_no"/>
        <result property="policyType" column="policy_type"/>
        <result property="policyProductType" column="policy_product_type"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="mainProductCode" column="main_product_code"/>
        <result property="orphanSingle" column="orphan_single"/>
        <result property="sourcePlatform" column="source_platform"/>
        <result property="mainProductName" column="main_product_name"/>
        <result property="portfolioName" column="portfolio_name"/>
        <result property="portfolioType" column="portfolio_type"/>
        <result property="orgCode" column="agent_org_code"/>
        <result property="policyReferrerName" column="policy_referrer_name"/>
        <result property="agentCode" column="agent_code"/>
        <result property="productsType" column="products_type"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="insuredName" column="insured_name"/>
        <result property="insuredsName" column="insured_names"/>
        <result property="policyStatus" column="policy_status"/>
        <result property="premiumTotal" column="premium_total"/>
        <result property="policySource" column="policy_source"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="salesType" column="sales_type"/>
        <result property="salesPlatform" column="sales_platform"/>
        <result property="insuredPeriodType" column="insured_period_type"/>
        <result property="insuredPeriod" column="insured_period"/>
        <result property="periodType" column="period_type"/>
        <result property="paymentPeriodType" column="payment_period_type"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="policyCoverage" column="policy_coverage"/>
        <result property="applicantTime" column="applicant_time"/>
        <result property="orderTime" column="order_time"/>
        <result property="approvedTime" column="approved_time"/>
        <result property="enforceTime" column="enforce_time"/>
        <result property="hesitatePeriod" column="hesitate_period"/>
        <result property="overHesitatePeriod" column="over_hesitate_period"/>
        <result property="policyReferrerCode" column="policy_referrer_code"/>
        <result property="referrerType" column="referrer_type"/>
        <result property="referrerCode" column="referrer_code"/>
        <result property="referrerName" column="referrer_name"/>
        <result property="channelBranchCode" column="channel_branch_code"/>
        <result property="channelBranchName" column="channel_branch_name"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="settlementStatus" column="settlement_status"/>
        <result property="selfPreservation" column="self_preservation"/>
        <result property="intact" column="intact"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryByApplicantIdCardList"
            resultType="com.mpolicy.manage.modules.policy.vo.policy.FastPolicyContractInfoVo">
        select b.policy_no as policyNo,a.applicant_id_card applicantIdCard,a.applicant_id_type as applicantIdType,b.order_time as orderTime,b.create_time as createTime,
        b.enforce_time as enforceTime
        from ep_policy_applicant_info a
        left join ep_policy_contract_info b on a.contract_code = b.contract_code
        where a.applicant_id_card in
        <foreach collection="data" open="(" close=")" item="code" separator=",">
            #{code}
        </foreach>
        and b.deleted=0
        and b.policy_type=1
        and b.intact!=2
    </select>
</mapper>
