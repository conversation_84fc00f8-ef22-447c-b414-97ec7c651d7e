<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.dao.PolicyGroupSubInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.EpPolicyGroupSubInfoEntity" id="policyGroupSubInfoMap">
        <result property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="insuredId" column="insured_id"/>
        <result property="mainInsuredId" column="main_insured_id"/>
        <result property="mainInsuredName" column="main_insure_name"/>
        <result property="insuredName" column="insured_name"/>
        <result property="firstInsuredRelation" column="first_insured_relation"/>
        <result property="insuredGender" column="insured_gender"/>
        <result property="insuredBirthday" column="insured_birthday"/>
        <result property="insuredIdType" column="insured_id_type"/>
        <result property="insuredIdCardValidityEnd" column="insured_id_card_validity_end"/>
        <result property="insuredNation" column="insured_nation"/>
        <result property="insuredAddress" column="insured_address"/>
        <result property="insuredCareer" column="insured_career"/>
        <result property="insuredOccupationalCategory" column="insured_occupational_category"/>
        <result property="insuredMobile" column="insured_mobile"/>
        <result property="insuredEmail" column="insured_email"/>
        <result property="insuredCompany" column="insured_company"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>