<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.baichuan.dao.BcChannelSellProductDao">

    <select id="getBcChannelProductDetailPage"
            resultType="com.mpolicy.manage.modules.baichuan.vo.BcChannelProductDetail">
        SELECT 
        bcsp.id,
        bcsp.config_type,
        bcsp.app_code,
        bcsp.commodity_code,
        bcsp.commodity_name,
        bcsp.sign_type,
        bcsp.effective_date,
        bcsp.invalid_date,
        bcsp.sell_desc,
        bcsp.sell_enable,
        bcsp.deleted,
        bcsp.create_user,
        bcsp.create_time,
        bcsp.update_user,
        bcsp.update_time,
        bcsp.revision,
        bcbi.app_name
        FROM `bc_channel_sell_product` bcsp
        LEFT JOIN `bc_channel_basic_info` bcbi ON bcsp.app_code = bcbi.app_code
        WHERE bcsp.`deleted` = 0
        <if test="bcChannelProductVo.appName != null and bcChannelProductVo.appName != ''">
            and bcbi.`app_name` like concat('%',#{bcChannelProductVo.appName,jdbcType=VARCHAR},'%')
        </if>
        <if test="bcChannelProductVo.appCode != null and bcChannelProductVo.appCode != ''">
            and bcsp.`app_code` = #{bcChannelProductVo.appCode,jdbcType=VARCHAR}
        </if>
        <if test="bcChannelProductVo.commodityName != null and bcChannelProductVo.commodityName != ''">
            and bcsp.`commodity_name` like concat('%',#{bcChannelProductVo.commodityName,jdbcType=VARCHAR},'%')
        </if>
    </select>


</mapper>