<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.ContentSearchConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.ContentSearchConfigEntity" id="contentSearchConfigMap">
        <result property="id" column="id"/>
        <result property="searchCode" column="search_code"/>
        <result property="contentCode" column="content_code"/>
        <result property="contentTag" column="content_tag"/>
        <result property="contentTitle" column="content_title"/>
        <result property="searchIcon" column="search_icon"/>
        <result property="searchType" column="search_type"/>
        <result property="sort" column="sort"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>