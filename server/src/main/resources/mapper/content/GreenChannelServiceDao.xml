<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.GreenChannelServiceDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.GreenChannelServiceEntity" id="greenChannelServiceMap">
        <result property="id" column="id"/>
        <result property="greenCode" column="green_code"/>
        <result property="productCode" column="product_code"/>
        <result property="serverContent" column="server_content"/>
        <result property="enabled" column="enabled"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>