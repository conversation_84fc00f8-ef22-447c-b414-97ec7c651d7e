<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.ContentBannerDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.ContentBannerEntity" id="contentBannerMap">
        <result property="id" column="id"/>
        <result property="bannerCode" column="banner_code"/>
        <result property="bannerTitle" column="banner_title"/>
        <result property="filePath" column="file_path"/>
        <result property="jumpParam" column="jump_param"/>
        <result property="bannerComment" column="banner_comment"/>
        <result property="bannerSort" column="banner_sort"/>
        <result property="bannerLocation" column="banner_location"/>
        <result property="jumpType" column="jump_type"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>