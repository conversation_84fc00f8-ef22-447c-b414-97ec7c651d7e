<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.OrgScheduleDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.OrgScheduleEntity" id="orgScheduleMap">
        <result property="id" column="id"/>
        <result property="scheduleCode" column="schedule_code"/>
        <result property="orgCode" column="org_code"/>
        <result property="scheduleTitle" column="schedule_title"/>
        <result property="scheduleContent" column="schedule_content"/>
        <result property="scheduleType" column="schedule_type"/>
        <result property="beginTime" column="begin_time"/>
        <result property="endTime" column="end_time"/>
        <result property="scheduleComment" column="schedule_comment"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>