<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.CustomerFeedbackInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.CustomerFeedbackInfoEntity" id="customerFeedbackInfoMap">
        <result property="id" column="id"/>
        <result property="feedbackType" column="feedback_type"/>
        <result property="feedbackContent" column="feedback_content"/>
        <result property="customerCode" column="customer_code"/>
        <result property="repContent" column="rep_content"/>
        <result property="repName" column="rep_name"/>
        <result property="repTime" column="rep_time"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>