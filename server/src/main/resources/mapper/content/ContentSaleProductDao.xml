<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.ContentSaleProductDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.ContentSaleProductEntity" id="contentSaleProductMap">
        <result property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="productName" column="product_name"/>
        <result property="insureType" column="insure_type"/>
    </resultMap>


</mapper>