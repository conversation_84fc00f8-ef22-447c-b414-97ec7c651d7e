<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.ClaimsConsultingDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.content.entity.ClaimsConsultingEntity" id="claimsConsultingMap">
        <result property="id" column="id"/>
        <result property="claimsConsultingCode" column="claims_consulting_code"/>
        <result property="claimsConsultingTitle" column="claims_consulting_title"/>
        <result property="claimsText" column="claims_text"/>
        <result property="contentCode" column="content_code"/>
        <result property="claimsSubjectCode" column="claims_subject_code"/>
        <result property="deleted" column="deleted"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>