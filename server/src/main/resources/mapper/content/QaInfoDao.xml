<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.content.dao.QaInfoDao">

    <!--	 可根据自己的需求，是否要使用
        <resultMap type="com.mpolicy.manage.modules.content.entity.QaInfoEntity" id="qaInfoMap">
            <result property="id" column="id"/>
            <result property="qaCode" column="qa_code"/>
            <result property="qaTitle" column="qa_title"/>
            <result property="qaText" column="qa_text"/>
            <result property="qaTag" column="qa_tag"/>
            <result property="qaSearchKeyword" column="qa_search_keyword"/>
            <result property="qaInitNum" column="qa_init_num"/>
            <result property="qaNum" column="qa_num"/>
            <result property="enabled" column="enabled"/>
            <result property="deleted" column="deleted"/>
            <result property="remark" column="remark"/>
            <result property="createUser" column="create_user"/>
            <result property="createTime" column="create_time"/>
            <result property="updateUser" column="update_user"/>
            <result property="updateTime" column="update_time"/>
        </resultMap>-->


</mapper>