<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.inte.dao.InteRecommendInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.inte.entity.InteRecommendInfoEntity" id="inteRecommendInfoMap">
        <result property="id" column="id"/>
        <result property="evaluationType" column="evaluation_type"/>
        <result property="type" column="type"/>
        <result property="productType" column="product_type"/>
        <result property="typeLabel" column="type_label"/>
        <result property="sizeLevel" column="size_level"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>