<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.inte.dao.InteRecommendMapDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.inte.entity.InteRecommendMapEntity" id="inteRecommendMapMap">
        <result property="id" column="id"/>
        <result property="iriId" column="iri_id"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productAbbreviation" column="product_abbreviation"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="mainProductCode" column="main_product_code"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>