<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.ClaimDocumentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.ClaimDocumentEntity" id="claimDocumentMap">
        <result property="id" column="id"/>
        <result property="claimNo" column="claim_no"/>
        <result property="documentType" column="document_type"/>
        <result property="documentName" column="document_name"/>
        <result property="fileCode" column="file_code"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="fileSize" column="file_size"/>
        <result property="filePath" column="file_path"/>
        <result property="domainPath" column="domain_path"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>