<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.ClaimApplyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.ClaimApplyEntity" id="claimApplyMap">
        <result property="id" column="id"/>
        <result property="claimNo" column="claim_no"/>
        <result property="customerCode" column="customer_code"/>
        <result property="customerName" column="customer_name"/>
        <result property="contractCode" column="contract_code"/>
        <result property="certNo" column="cert_no"/>
        <result property="policyCode" column="policy_code"/>
        <result property="policyName" column="policy_name"/>
        <result property="insuredName" column="insured_name"/>
        <result property="orgCode" column="org_code"/>
        <result property="channelBranchCode" column="channel_branch_code"/>
        <result property="holderName" column="holder_name"/>
        <result property="claimMobile" column="claim_mobile"/>
        <result property="claimCommitSummary" column="claim_commit_summary"/>
        <result property="claimNeedDocumentCodes" column="claim_need_document_codes"/>
        <result property="modifyDocumentSummary" column="modify_document_summary"/>
        <result property="refusedDesc" column="refused_desc"/>
        <result property="createSource" column="create_source"/>
        <result property="claimStatus" column="claim_status"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>