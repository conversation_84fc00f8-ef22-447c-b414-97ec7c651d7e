<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.invoice.dao.SettlementReconcileInvoiceInfoMapper">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.invoice.entity.SettlementReconcileInvoiceInfo">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="invoiceType" column="invoice_type" jdbcType="VARCHAR"/>
            <result property="titleType" column="title_type" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="taxpayerNum" column="taxpayer_num" jdbcType="VARCHAR"/>
            <result property="depositBank" column="deposit_bank" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="companyPhone" column="company_phone" jdbcType="VARCHAR"/>
            <result property="companyAddress" column="company_address" jdbcType="VARCHAR"/>
            <result property="mailAddress" column="mail_address" jdbcType="VARCHAR"/>
            <result property="needMail" column="need_mail" jdbcType="VARCHAR"/>
            <result property="receiverName" column="receiver_name" jdbcType="VARCHAR"/>
            <result property="receiverPhone" column="receiver_phone" jdbcType="VARCHAR"/>
            <result property="reveiverAddress" column="reveiver_address" jdbcType="VARCHAR"/>
            <result property="invoiceFileType" column="invoice_file_type" jdbcType="VARCHAR"/>
            <result property="invoiceTimeRequire" column="invoice_time_require" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="newestVersion" column="newest_version" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="revision" column="revision" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,invoice_type,title_type,
        title,taxpayer_num,deposit_bank,
        bank_account,company_phone,company_address,
        mail_address,need_mail,receiver_name,
        receiver_phone,reveiver_address,invoice_file_type,
        invoice_time_require,remark,version,
        newest_version,deleted,create_user,
        create_time,update_user,update_time,
        revision
    </sql>
</mapper>
