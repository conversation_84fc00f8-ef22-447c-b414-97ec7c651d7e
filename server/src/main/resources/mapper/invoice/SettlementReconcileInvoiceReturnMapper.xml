<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.invoice.dao.SettlementReconcileInvoiceReturnMapper">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.invoice.entity.SettlementReconcileInvoiceReturn">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="returnNum" column="return_num" jdbcType="VARCHAR"/>
            <result property="returnDate" column="return_date" jdbcType="TIMESTAMP"/>
            <result property="totalReturnMoney" column="total_return_money" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,return_num,return_date,
        total_return_money,remark,deleted,
        create_user,create_time,update_user,
        update_time
    </sql>

    <select id="querySettlementReconcileInfoList"
            resultType="com.mpolicy.manage.modules.settlement.entity.SettlementReconcileInfoEntity">
        select * from settlement_reconcile_info
        <where>
            (invoiced_amount > 0 or reconcile_status = 3)
            <if test="vo.reconcileMonth != null and vo.reconcileMonth != ''">
                and reconcile_month = #{vo.reconcileMonth}
            </if>
            <if test="vo.reconcileCode != null and vo.reconcileCode != ''">
                and reconcile_code = #{vo.reconcileCode}
            </if>
            <if test="vo.reconcileType != null and vo.reconcileType != ''">
                and reconcile_type = #{vo.reconcileMonth}
            </if>
            <if test="vo.companyCode != null and vo.companyCode != ''">
                and company_code = #{vo.companyCode}
            </if>
            <if test="vo.invoiceReturnStatus != null">
                <choose>
                    <when test="vo.invoiceReturnStatus == 0">
                        and return_money = 0
                    </when>
                    <when test="vo.invoiceReturnStatus == 1">
                        and return_money > 0 and (
                        ( invoiced_amount > return_money and invoiced_amount > 0)
                        or (company_amount  > 0 and company_amount > return_money and invoiced_amount = 0)
                        )
                    </when>
                    <when test="vo.invoiceReturnStatus == 2">
                        and (
                        (return_money = invoiced_amount and invoiced_amount > 0)
                        or (company_amount = invoiced_amount and invoiced_amount = 0 and return_money != 0)
                        )
                    </when>
                </choose>
            </if>
            and deleted = 0
        </where>
    </select>
</mapper>
