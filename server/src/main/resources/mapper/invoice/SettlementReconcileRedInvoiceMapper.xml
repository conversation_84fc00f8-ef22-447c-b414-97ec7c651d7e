<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.invoice.dao.SettlementReconcileRedInvoiceMapper">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.invoice.entity.SettlementReconcileRedInvoice">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="invoiceApplyNum" column="invoice_apply_num" jdbcType="VARCHAR"/>
            <result property="invoiceNum" column="invoice_num" jdbcType="VARCHAR"/>
            <result property="redInvoiceApplyNum" column="red_invoice_apply_num" jdbcType="VARCHAR"/>
            <result property="redInvoiceNumber" column="red_invoice_number" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="invoiceDate" column="invoice_date" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="revision" column="revision" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,invoice_apply_num,invoice_num,
        red_invoice_apply_num,red_invoice_number,status,
        invoice_date,deleted,create_user,
        create_time,update_user,update_time,
        revision
    </sql>
</mapper>
