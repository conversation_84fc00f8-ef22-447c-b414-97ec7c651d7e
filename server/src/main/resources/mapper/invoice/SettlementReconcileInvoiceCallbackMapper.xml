<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.invoice.dao.SettlementReconcileInvoiceCallbackMapper">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.invoice.entity.SettlementReconcileInvoiceCallback">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="businessNo" column="business_no" jdbcType="VARCHAR"/>
            <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/>
            <result property="originalText" column="original_text" jdbcType="VARCHAR"/>
            <result property="handleState" column="handle_state" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="revision" column="revision" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_no,serial_no,
        original_text,handle_state,remark,
        deleted,create_user,create_time,
        update_user,update_time,revision
    </sql>
</mapper>
