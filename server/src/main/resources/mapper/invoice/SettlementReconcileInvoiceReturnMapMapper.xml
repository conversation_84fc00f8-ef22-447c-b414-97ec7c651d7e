<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.invoice.dao.SettlementReconcileInvoiceReturnMapMapper">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.invoice.entity.SettlementReconcileInvoiceReturnMap">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="returnNum" column="return_num" jdbcType="VARCHAR"/>
            <result property="returnMoney" column="return_money" jdbcType="DECIMAL"/>
            <result property="reconcileCode" column="reconcile_code" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,return_num,return_money,
        reconcile_code,deleted,create_user,
        create_time,update_user,update_time
    </sql>
</mapper>
