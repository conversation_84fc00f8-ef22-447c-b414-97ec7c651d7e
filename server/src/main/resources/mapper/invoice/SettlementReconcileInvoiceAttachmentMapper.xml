<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mpolicy.manage.modules.invoice.dao.SettlementReconcileInvoiceAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.mpolicy.manage.modules.invoice.entity.SettlementReconcileInvoiceAttachment">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="invoiceNum" column="invoice_num" jdbcType="VARCHAR"/>
            <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
            <result property="invoiceType" column="invoice_type" jdbcType="VARCHAR"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="revision" column="revision" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,invoice_num,file_url,
        invoice_type,deleted,create_user,
        create_time,update_user,update_time,
        revision
    </sql>
</mapper>
