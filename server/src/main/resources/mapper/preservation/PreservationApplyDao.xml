<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.PreservationApplyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity" id="preservationApplyMap">
        <result property="id" column="id"/>
        <result property="preservationCode" column="preservation_code"/>
        <result property="contractCode" column="contract_code"/>
        <result property="endorsementNo" column="endorsement_no"/>
        <result property="policyCode" column="policy_code"/>
        <result property="policyName" column="policy_name"/>
        <result property="policyType" column="policy_type"/>
        <result property="preservationType" column="preservation_type"/>
        <result property="preservationProject" column="preservation_project"/>
        <result property="preservationWhy" column="preservation_why"/>
        <result property="terminationProductType" column="termination_product_type"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="holderName" column="holder_name"/>
        <result property="holderIdNo" column="holder_id_no"/>
        <result property="insuredNames" column="insured_names"/>
        <result property="insuredIdNos" column="insured_id_nos"/>
        <result property="surrenderCash" column="surrender_cash"/>
        <result property="manageOrgCode" column="manage_org_code"/>
        <result property="manageOrgName" column="manage_org_name"/>
        <result property="sellChannelCode" column="sell_channel_code"/>
        <result property="sellChannelName" column="sell_channel_name"/>
        <result property="policyAgentCode" column="policy_agent_code"/>
        <result property="policyAgentName" column="policy_agent_name"/>
        <result property="referrerCode" column="referrer_code"/>
        <result property="referrerName" column="referrer_name"/>
        <result property="channelReferrerCode" column="channel_referrer_code"/>
        <result property="channelReferrerName" column="channel_referrer_name"/>
        <result property="preservationEffectTime" column="preservation_effect_time"/>
        <result property="preservationStatus" column="preservation_status"/>
        <result property="processingStatus" column="processing_status"/>
        <result property="processingTime" column="processing_time"/>
        <result property="branchCode" column="branch_code"/>
        <result property="channelBranchCode" column="channel_branch_code"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <update id="batchUpdatePermission" parameterType="java.util.List">
        insert into ep_preservation_apply(
                `id`,`preservation_code`,`policy_code`,policy_name,preservation_type,
                preservation_project,preservation_why,preservation_effect_time,
                preservation_status,processing_status,sell_channel_code,sell_channel_name,manage_org_code,
                                            manage_org_name,policy_agent_code,policy_agent_name,referrer_code,referrer_name,
                                            channel_referrer_code,channel_referrer_name,channel_branch_code
        ) values
        <foreach collection="data" item = "item" index="index" separator=",">
            (#{item.id},#{item.preservationCode},#{item.policyCode},#{item.policyName}, #{item.preservationType},
             #{item.preservationProject},#{item.preservationWhy},#{item.preservationEffectTime},
             #{item.preservationStatus},#{item.processingStatus},
             #{item.sellChannelCode}, #{item.sellChannelName}, #{item.manageOrgCode}, #{item.manageOrgName},
            #{item.policyAgentCode}, #{item.policyAgentName}, #{item.referrerCode}, #{item.referrerName},
            #{item.channelReferrerCode}, #{item.channelReferrerName}, #{item.channelBranchCode}
             )
        </foreach>
        on duplicate key update
        `sell_channel_code`=VALUES(sell_channel_code),
        `sell_channel_name`=VALUES(sell_channel_name),
        `manage_org_code`=VALUES(manage_org_code),
        `manage_org_name`=VALUES(manage_org_name),
        `policy_agent_code`=VALUES(policy_agent_code),
        `policy_agent_name`=VALUES(policy_agent_name),
        `referrer_code`=VALUES(referrer_code),
        `referrer_name`=VALUES(referrer_name),
        `channel_referrer_code`=VALUES(channel_referrer_code),
        `channel_referrer_name`=VALUES(channel_referrer_name),
        `channel_branch_code`=VALUES(channel_branch_code),
        `update_time`=now()
    </update>

    <select id="querySurrender4EmptyFix" resultType="com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity">
        select a.*
        from `ep_preservation_apply` a
        where `preservation_project` in ('POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:SURRENDER' ,
                                         'POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:PROTOCOL_TERMINATION',
                                         'POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:HESITATION_SURRENDER')
          and not exists(
                select 1 from `ep_preservation_surrender` b where a.preservation_code=b.preservation_code and b.deleted=0
            )
          and deleted=0
          and not exists (
              select 1 from r_policy_fix_log c where a.policy_code=c.policy_no and c.type=1
            )
          <if test="policyNo!=null and policyNo!=''">
            and a.policy_code=#{policyNo}
          </if>
          order by id
        limit #{size}

    </select>

    <!-- 公用带权限查询保单sql -->
    <sql id="qryContractAuthority">
        AND b.deleted= 0
        AND b.show_model = 1
        <if test="map.orgCodes!= null and map.orgCodes.size() >0">
            AND b.org_code IN
            <foreach collection="map.orgCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.channelBranchCodes!= null and map.channelBranchCodes.size() >0">
            AND b.policy_channel_branch_code IN
            <foreach collection="map.channelBranchCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.channelDistributionCodes!= null and map.channelDistributionCodes.size() >0">
            AND b.channel_distribution_code IN
            <foreach collection="map.channelDistributionCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.condition!= null and map.condition">
            and b.sales_type = '1'
        </if>
        <if test="map.policyContractCode!= null and map.policyContractCode!= ''">
            and b.contract_code = #{map.policyContractCode}
        </if>
    </sql>

    <!-- 公用带权限查询保全sql -->
    <sql id="qryAuthority">
        SELECT
        a.*
        FROM
        ep_preservation_apply a
        LEFT JOIN ep_policy_contract_info b ON a.contract_code = b.contract_code
        WHERE
        a.deleted = 0
        and a.preservation_project not in ("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:COMMISSION_RATIO_UP")
        <if test="map.preservationCode!= null and map.preservationCode!=''">
            AND a.preservation_code = #{map.preservationCode}
        </if>
        <if test="map.policyCode!= null and map.policyCode!=''">
            AND a.policy_code = #{map.policyCode}
        </if>
        <if test="map.endorsementNo!= null and map.endorsementNo!=''">
            AND a.endorsement_no = #{map.endorsementNo}
        </if>
        <if test="map.preservationType!= null and map.preservationType!=''">
            AND a.preservation_type = #{map.preservationType}
        </if>
        <if test="map.preservationProject!= null and map.preservationProject!=''">
            AND a.preservation_project = #{map.preservationProject}
        </if>
        <if test="map.holderName!= null and map.holderName!=''">
            AND a.holder_name LIKE concat('%',#{map.holderName})
        </if>
        <if test="map.holderIdNo!= null and map.holderIdNo!=''">
            AND a.holder_id_no LIKE concat('%',#{map.holderIdNo})
        </if>
        <if test="map.insuredName!= null and map.insuredName!=''">
            AND a.insured_names LIKE concat('%',#{map.insuredName})
        </if>
        <if test="map.insuredIdNo!= null and map.insuredIdNo!=''">
            AND a.insured_id_nos LIKE concat('%',#{map.insuredIdNo})
        </if>
        <if test="map.preservationStatus!= null and map.preservationStatus!=''">
            AND a.preservation_status = #{map.preservationStatus}
        </if>
        <if test="map.createStartData!= null and map.createStartData!=''">
            AND date_format(a.create_time,'%Y-%m-%d') &gt;= #{map.createStartData}
        </if>
        <if test="map.createEndData!= null and map.createEndData!=''">
            AND date_format(a.create_time,'%Y-%m-%d') &lt;= #{map.createEndData}
        </if>
        <if test="map.preservationStartTime!= null and map.preservationStartTime!=''">
            AND date_format(a.preservation_effect_time,'%Y-%m-%d') &gt;= #{map.preservationStartTime}
        </if>
        <if test="map.preservationEndTime!= null and map.preservationEndTime!=''">
            AND date_format(a.preservation_effect_time,'%Y-%m-%d') &lt;= #{map.preservationEndTime}
        </if>
        <if test="map.companyCodes!= null and map.companyCodes.size() >0">
            AND a.company_code IN
            <foreach collection="map.companyCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.manageOrgCodes!= null and map.manageOrgCodes.size() >0">
            AND a.manage_org_code IN
            <foreach collection="map.manageOrgCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="map.sellChannelCodes!= null and map.sellChannelCodes.size() >0">
            AND a.sell_channel_code IN
            <foreach collection="map.sellChannelCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="qryContractAuthority"/>
    </sql>

    <!-- 带权限分页查询 -->
    <select id="pageByMap" resultType="com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity">
        <include refid="qryAuthority"/>
        ORDER BY a.id DESC
    </select>

    <!-- 带权限列表查询 -->
    <select id="listsByMap" resultType="com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity">
        <include refid="qryAuthority"/>
        ORDER BY a.id DESC
        LIMIT 100000
    </select>

    <!-- 带权限保单列表查询 -->
    <select id="listsContractByMap"
            resultType="com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity">
        SELECT
            b.*
        FROM
            ep_policy_contract_info b
        WHERE
        1=1
        <include refid="qryContractAuthority"/>
    </select>
</mapper>