<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.PreservationTeamPeopleDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.policy.entity.PreservationTeamPeopleEntity" id="preservationTeamPeopleMap">
        <result property="id" column="id"/>
        <result property="preservationCode" column="preservation_code"/>
        <result property="contractCode" column="contract_code"/>
        <result property="policyCode" column="policy_code"/>
        <result property="peopleNumber" column="people_number"/>
        <result property="peopleType" column="people_type"/>
        <result property="insuredName" column="insured_name"/>
        <result property="firstInsuredRelation" column="first_insured_relation"/>
        <result property="insuredGender" column="insured_gender"/>
        <result property="insuredBirthday" column="insured_birthday"/>
        <result property="insuredIdType" column="insured_id_type"/>
        <result property="insuredIdCard" column="insured_id_card"/>
        <result property="insuredIdCardValidityEnd" column="insured_id_card_validity_end"/>
        <result property="insuredNation" column="insured_nation"/>
        <result property="insuredAddress" column="insured_address"/>
        <result property="productCode" column="product_code"/>
        <result property="singlePremium" column="single_premium"/>
        <result property="insuredCareer" column="insured_career"/>
        <result property="insuredOccupationalCategory" column="insured_occupational_category"/>
        <result property="mainInsuredIdCard" column="main_insured_id_card"/>
        <result property="insuredMobile" column="insured_mobile"/>
        <result property="insuredEmail" column="insured_email"/>
        <result property="insuredCompany" column="insured_company"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <select id="qryGroupPolicyItemExportAllItemVo"
            resultType="com.mpolicy.manage.modules.policy.vo.group.export.GroupPolicyItemExportAllItemVo">
        SELECT
            a.contract_code,
            a.single_premium,
            a.first_insured_relation,
            a.insured_name,
            a.main_insured_name,
            a.insured_gender,
            a.insured_birthday,
            a.insured_id_type,
            a.insured_id_card,
            c.plan_code,
            c.plan_name,
            a.insured_career,
            a.insured_occupational_category,
            a.referrer_code,
            a.channel_referrer_code,
            a.channel_code,
            a.people_type,
            b.endorsement_no,
            b.preservation_effect_time
        FROM
            ep_preservation_team_people a
        LEFT JOIN ep_preservation_apply b ON a.preservation_code = b.preservation_code
        LEFT JOIN insurance_group_plan_info c ON a.plan_code=c.plan_code
        WHERE
            a.deleted = 0
        AND a.people_type IN ('减人','加人')
        AND a.contract_code IN
        <foreach collection="contractCodes" separator="," open="(" close=")" item="code">
            #{code}
        </foreach>
        AND b.deleted = 0
        ORDER BY b.preservation_effect_time,a.create_time ASC
    </select>

<!--    <update id="batchUpdateInsuredCodeByKey">-->
<!--        insert into ep_preservation_team_people(`id`,`preservation_code`,`contract_code`,`policy_code`,`insured_code`,`people_type`) values-->
<!--        <foreach collection="data" item = "item" index="index" separator=",">-->
<!--            (#{item.id},#{item.preservationCode},#{item.contractCode},#{item.policyCode},#{item.insuredCode},#{item.peopleType})-->
<!--        </foreach>-->
<!--        on duplicate key update-->
<!--        `insured_code`=VALUES(insured_code),-->
<!--        `update_time`=now()-->
<!--    </update>-->

    <update id="updatePreservationPeopleCode" parameterType="com.mpolicy.manage.modules.policy.entity.PreservationTeamPeopleEntity">
        update ep_preservation_team_people set insured_code = #{member.insuredCode},update_time=now() where id=#{member.id}
    </update>
</mapper>