<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.contract.dao.ContractEnclosureDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.contract.entity.ContractEnclosureEntity" id="contractEnclosureMap">
        <result property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="enclosureType" column="enclosure_type"/>
        <result property="fileCode" column="file_code"/>
        <result property="fileType" column="file_type"/>
        <result property="fileName" column="file_name"/>
        <result property="fileExt" column="file_ext"/>
        <result property="fileSize" column="file_size"/>
        <result property="filePath" column="file_path"/>
        <result property="domainPath" column="domain_path"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findContractEnclosureList" resultType="com.mpolicy.manage.modules.contract.vo.ContractEnclosureListOut">
        SELECT id,
               contract_code  ,
               enclosure_type ,
               file_code      ,
               file_type      ,
               file_name      ,
               file_ext       ,
               file_size      ,
               file_path      ,
               domain_path    ,
               deleted,
               create_user    ,
               create_time    ,
               update_user    ,
               update_time
        FROM ep_contract_enclosure
    </select>
</mapper>