<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.contract.dao.ContractInsuranceProductPremDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.contract.entity.ContractInsuranceProductPremEntity" id="contractInsuranceProductPremMap">
        <result property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="insuranceProductCode" column="insurance_product_code"/>
        <result property="insuranceProductName" column="insurance_product_name"/>
        <result property="mainInsuranceProductCode" column="main_insurance_product_code"/>
        <result property="mainInsuranceProductName" column="main_insurance_product_name"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="productPlan" column="product_plan"/>
        <result property="coveragePeriod" column="coverage_period"/>
        <result property="expireAge" column="expire_age"/>
        <result property="paymentType" column="payment_type"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="underwritingRate" column="underwriting_rate"/>
        <result property="applicantAge" column="applicant_age"/>
        <result property="applicantGender" column="applicant_gender"/>
        <result property="insuredAge" column="insured_age"/>
        <result property="insuredGender" column="insured_gender"/>
        <result property="insuranceType" column="insurance_type"/>
        <result property="costType" column="cost_type"/>
        <result property="settlementMethod" column="settlement_method"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="autoSettlementFlag" column="auto_settlement_flag"/>
        <result property="firstYearBrokage" column="first_year_brokage"/>
        <result property="renewalSettlementFlag" column="renewal_settlement_flag"/>
        <result property="renewalBrokage2year" column="renewal_brokage2year"/>
        <result property="renewalBrokage3year" column="renewal_brokage3year"/>
        <result property="renewalBrokage4year" column="renewal_brokage4year"/>
        <result property="renewalBrokage5year" column="renewal_brokage5year"/>
        <result property="renewalBrokage6year" column="renewal_brokage6year"/>
        <result property="effectiveStartDate" column="effective_start_date"/>
        <result property="effectiveEndDate" column="effective_end_date"/>
        <result property="applyBranches" column="apply_branches"/>
        <result property="settlementStandard" column="settlement_standard"/>
        <result property="selfPreservation" column="self_preservation"/>
        <result property="remarks" column="remarks"/>
        <result property="renewalAutoExpand" column="renewal_auto_expand"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

</mapper>