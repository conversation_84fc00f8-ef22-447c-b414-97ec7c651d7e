<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.contract.dao.ContractInnerSignatoryDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.contract.entity.ContractInnerSignatoryEntity" id="contractInnerSignatoryMap">
        <result property="id" column="id"/>
        <result property="innerSignatoryCode" column="inner_signatory_code"/>
        <result property="innerSignatoryName" column="inner_signatory_name"/>
        <result property="innerSignatoryType" column="inner_signatory_type"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>