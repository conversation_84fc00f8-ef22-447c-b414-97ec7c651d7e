<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.contract.dao.ContractBasicInfoDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.contract.entity.ContractBasicInfoEntity" id="contractBasicInfoMap">
        <result property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="contractName" column="contract_name"/>
        <result property="contractStatus" column="contract_status"/>
        <result property="externalSignatoryType" column="external_signatory_type"/>
        <result property="externalSignatoryCode" column="external_signatory_code"/>
        <result property="externalSignatoryName" column="external_signatory_name"/>
        <result property="innerSignatoryType" column="inner_signatory_type"/>
        <result property="innerSignatoryName" column="inner_signatory_name"/>
        <result property="innerSignatoryCode" column="inner_signatory_code"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="collectionAccountBank" column="collection_account_bank"/>
        <result property="collectionAccountNo" column="collection_account_no"/>
        <result property="productPremFlag" column="product_prem_flag"/>
        <result property="productPremDate" column="product_prem_date"/>
        <result property="productPremFileCode" column="product_prem_file_code"/>
        <result property="productPremFileName" column="product_prem_file_name"/>
        <result property="productPremFilePath" column="product_prem_file_path"/>
        <result property="remarks" column="remarks"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findContractBasicInfoList" resultType="com.mpolicy.manage.modules.contract.vo.ContractBasicInfoListOut">
        SELECT id,
               contract_code,
               contract_name,
               contract_status,
               external_signatory_type,
               external_signatory_code,
               external_signatory_name,
               inner_signatory_type,
               inner_signatory_name,
               inner_signatory_code,
               start_date,
               end_date,
               product_prem_flag,
               product_prem_file_path,
               update_user,
               update_time
        FROM ep_contract_basic_info
        where deleted = 0
        <if test="input.contractCode != null and input.contractCode != ''">
            and contract_code =#{input.contractCode,jdbcType=VARCHAR}
        </if>
        <if test="input.contractName != null and input.contractName != ''">
            and contract_name like concat('%',#{input.contractName,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.externalSignatoryCode != null and input.externalSignatoryCode != ''">
            and external_signatory_code =#{input.externalSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="input.externalSignatoryType != null and input.externalSignatoryType != ''">
            and external_signatory_type =#{input.externalSignatoryType,jdbcType=VARCHAR}
        </if>
        <if test="input.innerSignatoryCode != null and input.innerSignatoryCode != ''">
            and inner_signatory_code =#{input.innerSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="input.innerSignatoryType != null ">
            and inner_signatory_type =#{input.innerSignatoryType,jdbcType=INTEGER}
        </if>
        <if test="input.contractCodeList != null and input.contractCodeList.size() != 0">
            and contract_code in
            <foreach collection="input.contractCodeList" item="contractCode" open="(" close=")" separator=",">
                #{contractCode,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>