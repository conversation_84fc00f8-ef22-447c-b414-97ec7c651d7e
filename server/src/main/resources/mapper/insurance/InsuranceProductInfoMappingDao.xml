<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceProductInfoMappingDao">

    <select id="getProductInfoMappingOutList"
            resultType="com.mpolicy.manage.modules.insurance.vo.ProductInfoMappingOut">
        select a.id,
        a.product_code as productCode,
        a.product_name as productName,
        a.company_code as companyCode,
        a.company_name as companyName,
        a.product_mapping_info as productMappingInfo
        from insurance_product_info_mapping a
        <if test="input.insuranceTypeCode != null and input.insuranceTypeCode != ''">
            left join insurance_product_info_mapping_map b on a.product_code = b.product_code
        </if>
        <where>
            <if test="input.productCode != null and input.productCode != ''">
                and a.product_code = #{input.productCode,jdbcType=VARCHAR}
            </if>
            <if test="input.productName != null and input.productName != ''">
                and a.product_name like concat('%',#{input.productName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.companyName != null and input.companyName != ''">
                and a.company_name like concat('%',#{input.companyName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.insuranceTypeCode != null and input.insuranceTypeCode != ''">
                and b.insurance_type_code = #{input.insuranceTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="getProductInfoMappingOutExcelList"
            resultType="com.mpolicy.manage.modules.insurance.vo.ProductInfoMappingOut">
        select a.id,
        a.product_code as productCode,
        a.product_name as productName,
        a.company_code as companyCode,
        a.company_name as companyName,
        a.product_mapping_info as productMappingInfo
        from insurance_product_info_mapping a
        <if test="input.insuranceTypeCode != null and input.insuranceTypeCode != ''">
            left join insurance_product_info_mapping_map b on a.product_code = b.product_code
        </if>
        <where>
            <if test="input.productCode != null and input.productCode != ''">
                and a.product_code = #{input.productCode,jdbcType=VARCHAR}
            </if>
            <if test="input.productName != null and input.productName != ''">
                and a.product_name like concat('%',#{input.productCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.companyName != null and input.companyName != ''">
                and a.company_name like concat('%',#{input.companyName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.insuranceTypeCode != null and input.insuranceTypeCode != ''">
                and b.insurance_type_code = #{input.insuranceTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id desc
    </select>

</mapper>