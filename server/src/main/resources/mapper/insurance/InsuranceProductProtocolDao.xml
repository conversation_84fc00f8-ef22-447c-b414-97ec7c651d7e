<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.xjxh.dao.InsuranceProductProtocolDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceProductProtocolEntity" id="insuranceProductProtocolMap">
        <result property="id" column="id"/>
        <result property="productCode" column="product_code"/>
        <result property="productTermsFileCode" column="product_terms_file_code"/>
        <result property="productTermsName" column="product_terms_name"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>
