<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceRecommendProductDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceRecommendProductEntity" id="insuranceRecommendProductMap">
        <result property="id" column="id"/>
        <result property="recommendConditionId" column="recommend_condition_id"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="companyCode" column="company_code"/>
        <result property="planName" column="plan_name"/>
        <result property="goodsName" column="goods_name"/>
        <result property="mainInsuranceCode" column="main_insurance_code"/>
        <result property="coveragePeriod" column="coverage_period"/>
        <result property="sort" column="sort"/>
        <result property="deleted" column="deleted"/>
        <result property="customizeType" column="customize_type"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>
