<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceCompanyPersonDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyPersonEntity"
               id="insuranceCompanyPersonMap">
        <result property="id" column="id"/>
        <result property="personCode" column="person_code"/>
        <result property="personType" column="person_type"/>
        <result property="companyName" column="company_name"/>
        <result property="avatar" column="avatar"/>
        <result property="userName" column="user_name"/>
        <result property="idType" column="id_type"/>
        <result property="idCard" column="id_card"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="marital" column="marital"/>
        <result property="politics" column="politics"/>
        <result property="nation" column="nation"/>
        <result property="degree" column="degree"/>
        <result property="job" column="job"/>
        <result property="phone" column="phone"/>
        <result property="mobile" column="mobile"/>
        <result property="qq" column="qq"/>
        <result property="wechat" column="wechat"/>
        <result property="email" column="email"/>
        <result property="provinceCode" column="province_code"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="areaCode" column="area_code"/>
        <result property="areaName" column="area_name"/>
        <result property="address" column="address"/>
        <result property="emergencyContact" column="emergency_contact"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>
        <result property="emergencyContactProvinceCode" column="emergency_contact_province_code"/>
        <result property="emergencyContactProvinceName" column="emergency_contact_province_name"/>
        <result property="emergencyContactCityCode" column="emergency_contact_city_code"/>
        <result property="emergencyContactCityName" column="emergency_contact_city_name"/>
        <result property="emergencyContactAreaCode" column="emergency_contact_area_code"/>
        <result property="emergencyContactAreaName" column="emergency_contact_area_name"/>
        <result property="emergencyContactAddress" column="emergency_contact_address"/>
        <result property="introduce" column="introduce"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--获取数据列表-->
    <select id="findInsuranceCompanyPersonList"
            resultType="com.mpolicy.manage.modules.insurance.vo.InsuranceCompanyPersonListOut">
        SELECT id,
               person_code  AS personCode,
               person_type  AS personType,
               company_name AS companyName,
               avatar,
               user_name    AS userName,
               id_type      AS idType,
               id_card      AS idCard,
               gender,
               birthday,
               user_status,
               marital,
               politics,
               nation,
               degree,
               job,
               phone,
               mobile,
               qq,
               wechat,
               email,
               create_time  AS createTime
        FROM insurance_company_person icp
        where icp.deleted = 0
        <if test="input.userName != null and input.userName != ''">
            and user_name like concat('%',#{input.userName,jdbcType=VARCHAR},'%')
        </if>
        <if test="input.userStatus != null">
            and user_status = #{input.userStatus,jdbcType=INTEGER}
        </if>
    </select>
</mapper>