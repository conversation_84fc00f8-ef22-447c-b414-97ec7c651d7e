<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsurancePortfolioProductDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsurancePortfolioProductEntity" id="insurancePortfolioProductMap">
        <result property="id" column="id"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="productCode" column="product_code"/>
        <result property="bindFlag" column="bind_flag"/>
        <result property="bindSource" column="bind_source"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>