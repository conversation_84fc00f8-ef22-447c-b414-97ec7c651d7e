<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsurancePortfolioSellruleInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsurancePortfolioSellruleInfoEntity" id="insurancePortfolioSellruleInfoMap">
        <result property="id" column="id"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="portfolioName" column="portfolio_name"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productShortName" column="product_short_name"/>
        <result property="productCodeEx" column="product_code_ex"/>
        <result property="insuredBirthDay" column="insured_birth_day"/>
        <result property="insuredBirthDayFrom" column="insured_birth_day_from"/>
        <result property="insuredBirthDayTo" column="insured_birth_day_to"/>
        <result property="insuredSex" column="insured_sex"/>
        <result property="city" column="city"/>
        <result property="coverageYear" column="coverage_year"/>
        <result property="payPeriod" column="pay_period"/>
        <result property="basicAmount" column="basic_amount"/>
        <result property="receiveAge" column="receive_age"/>
        <result property="amount" column="amount"/>
        <result property="premium" column="premium"/>
        <result property="inputItems" column="input_items"/>
        <result property="productListItems" column="product_list_items"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>