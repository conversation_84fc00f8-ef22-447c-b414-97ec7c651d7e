<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceGroupPlanInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceGroupPlanInfoEntity" id="insuranceGroupPlanInfoMap">
        <result property="id" column="id"/>
        <result property="planName" column="plan_name"/>
        <result property="planCode" column="plan_code"/>
        <result property="companyCode" column="company_code"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


    <select id="queryGroupPlanList" resultType="com.mpolicy.manage.modules.insurance.vo.GroupPlanBean">
        SELECT * FROM (
        SELECT
            pi.id id,
            pi.plan_code planCode,
            pi.plan_name planName,
            pi.company_code companyCode,
            ( SELECT short_name FROM insurance_company ic WHERE ic.company_code = pi.company_code AND ic.deleted = 0 ) AS shortName,
            (
                SELECT
                    GROUP_CONCAT( product_name )
                FROM
                    insurance_product_info ipi
                WHERE
                    ipi.deleted = 0
                  AND ipi.product_code IN ( SELECT product_code FROM insurance_group_plan_product pp WHERE pp.plan_code = pi.plan_code AND pp.deleted = 0 )
            ) AS productName,
            pi.create_time createTime
        FROM
            insurance_group_plan_info pi
        WHERE
            pi.deleted = 0) AS t
            ${ew.customSqlSegment}
    </select>

    <select id="getCompanyList" resultType="com.alibaba.fastjson.JSONObject">

        SELECT
            ic.company_code AS companyCode,ic.short_name AS companyName
        FROM
            insurance_company ic LEFT JOIN insurance_product_info pi ON ic.company_code=pi.company_code
        WHERE
            ic.deleted=0 AND pi.company_code IS NOT NULL GROUP BY ic.company_code
    </select>
    <select id="getProductList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            product_code productCode,
            product_name productName,
            main_product_flag mainProductFlag
        FROM
            insurance_product_info
        where deleted = 0  AND product_group='PRODUCT:PRODUCTGROUP:4'
        <if test="companyCode != null and companyCode != ''">
            and company_code = #{companyCode,jdbcType=VARCHAR}
        </if>

    </select>

</mapper>
