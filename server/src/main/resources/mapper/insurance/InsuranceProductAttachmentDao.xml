<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceProductAttachmentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceProductAttachmentEntity" id="insuranceProductAttachmentMap">
        <result property="id" column="id"/>
        <result property="productCode" column="product_code"/>
        <result property="attachmentType" column="attachment_type"/>
        <result property="fileCode" column="file_code"/>
        <result property="productData" column="product_data"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>