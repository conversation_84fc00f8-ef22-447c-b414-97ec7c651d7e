<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsurancePortfolioInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsurancePortfolioInfoEntity"
               id="insurancePortfolioInfoMap">
        <result property="id" column="id"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="portfolioName" column="portfolio_name"/>
        <result property="portfolioShortName" column="portfolio_short_name"/>
        <result property="pinyin" column="pinyin"/>
        <result property="portfolioGroup" column="portfolio_group"/>
        <result property="portfolioType" column="portfolio_type"/>
        <result property="portfolioScore" column="portfolio_score"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="supportChannels" column="support_channels"/>
        <result property="allowMinPrem" column="allow_min_prem"/>
        <result property="allowChargeType" column="allow_charge_type"/>
        <result property="introduce" column="introduce"/>
        <result property="published" column="published"/>
        <result property="isOnlineSale" column="is_online_sale"/>
        <result property="renew" column="renew"/>
        <result property="surrPermit" column="surr_permit"/>
        <result property="isHot" column="is_hot"/>
        <result property="isNew" column="is_new"/>
        <result property="insuredFlag" column="insured_flag"/>
        <result property="joinComparison" column="join_comparison"/>
        <result property="joinPlan" column="join_plan"/>
        <result property="weights" column="weights"/>
        <result property="benefitShowFlag" column="benefit_show_flag"/>
        <result property="benefitShowFileCode" column="benefit_show_file_code"/>
        <result property="rulesFlag" column="rules_flag"/>
        <result property="rulesFileCode" column="rules_file_code"/>
        <result property="status" column="status"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <!--获取导出组合产品列表-->
    <select id="findExportList" resultType="com.mpolicy.manage.modules.sell.entity.ExportInsurancePortfolioListOut">
        SELECT ipi.portfolio_code,
        ipi.portfolio_name,
        ipi.company_name,
        ipi.portfolio_group,
        ipi.portfolio_type,
        ipi.join_plan,
        ipi.rules_flag,
        ipi.join_comparison,
        GROUP_CONCAT(ipti.product_code SEPARATOR ' ')AS product_code
        FROM insurance_portfolio_info ipi
        LEFT JOIN insurance_portfolio_product ipp on ipi.portfolio_code=ipp.portfolio_code
        LEFT JOIN insurance_product_info ipti on ipti.product_code=ipp.product_code
        where ipi.deleted = 0
        <if test="param.portfolioCode != null and param.portfolioCode != ''">
            and ipi.portfolio_code like concat(#{param.portfolioCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="param.portfolioName != null and param.portfolioName != ''">
            and ipi.portfolio_name like concat(#{param.portfolioName,jdbcType=VARCHAR},'%')
        </if>
        <if test="param.companyName != null and param.companyName != ''">
            and ipi.company_name like concat(#{param.companyName,jdbcType=VARCHAR},'%')
        </if>
        <if test="param.portfolioGroup != null and param.portfolioGroup != ''">
            and ipi.portfolio_group =#{param.portfolioGroup,jdbcType=VARCHAR}
        </if>
        <if test="param.portfolioType != null and param.portfolioType != ''">
            and ipi.portfolio_type =#{param.portfolioType,jdbcType=VARCHAR}
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            and ipi.rules_flag =#{param.orgType,jdbcType=VARCHAR}
        </if>
        GROUP BY ipi.portfolio_code,ipi.portfolio_type

    </select>

</mapper>