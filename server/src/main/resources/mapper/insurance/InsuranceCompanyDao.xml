<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceCompanyDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity" id="insuranceCompanyMap">
        <result property="id" column="id"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="companyType" column="company_type"/>
        <result property="formerName" column="former_name"/>
        <result property="shortName" column="short_name"/>
        <result property="pinyin" column="pinyin"/>
        <result property="creditCode" column="credit_code"/>
        <result property="taxpayerRegistrationNumber" column="taxpayer_registration_number"/>
        <result property="businessAddress" column="business_address"/>
        <result property="companyLogo" column="company_logo"/>
        <result property="companyAffiliatedLogo" column="company_affiliated_logo"/>
        <result property="provinceLocation" column="province_location"/>
        <result property="cityLocation" column="city_location"/>
        <result property="introduce" column="introduce"/>
        <result property="customerNotification" column="customer_notification"/>
        <result property="customerNotificationPath" column="customer_notification_path"/>
        <result property="customerNotificationCode" column="customer_notification_code"/>
        <result property="customerNotificationName" column="customer_notification_name"/>
        <result property="contactNumber" column="contact_number"/>
        <result property="lawType" column="law_type"/>
        <result property="companyLink" column="company_link"/>
        <result property="companyLocation" column="company_location"/>
        <result property="companyQrCode" column="company_qr_code"/>
        <result property="establishDate" column="establish_date"/>
        <result property="registeredAssets" column="registered_assets"/>
        <result property="workStatus" column="work_status"/>
        <result property="businessScope" column="business_scope"/>
        <result property="weights" column="weights"/>
        <result property="qrcodeUrl" column="qrcode_url"/>
        <result property="coolingOffPeriod" column="cooling_off_period"/>
        <result property="calPrecision" column="cal_precision"/>
        <result property="companyStatus" column="company_status"/>
        <result property="readNotice" column="read_notice"/>
        <result property="deleted" column="deleted"/>
        <result property="collaborate" column="collaborate"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>
