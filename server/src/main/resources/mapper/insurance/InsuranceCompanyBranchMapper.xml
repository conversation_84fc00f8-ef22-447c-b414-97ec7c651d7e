<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceCompanyBranchDao">
    <!--获取数据列表-->
    <select id="findInsuranceCompanyBranchList"
            resultType="com.mpolicy.manage.modules.insurance.vo.InsuranceCompanyBranchListOut">
        SELECT icb.id,
               icb.branch_code,
               icb.branch_name,
               ic.company_name,
               ic.short_name,
               icb.create_time,
               icb.branch_status,
               ic.company_type
        FROM insurance_company_branch icb
        LEFT JOIN insurance_company ic  on icb.company_code = ic.company_code
        <where>
            <if test="input.branchName != null and input.branchName != ''">
                and  icb.branch_name like concat('%',#{input.branchName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.beginTime != null">
                and icb.create_time &gt;=#{input.beginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="input.endTime != null">
                and icb.create_time &lt;=#{input.endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by icb.id desc
    </select>
</mapper>