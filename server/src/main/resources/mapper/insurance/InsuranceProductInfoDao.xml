<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceProductInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity" id="insuranceProductInfoMap">
        <result property="id" column="id"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="productShortName" column="product_short_name"/>
        <result property="pinyin" column="pinyin"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="mainProductFlag" column="main_product_flag"/>
        <result property="productGroup" column="product_group"/>
        <result property="productType" column="product_type"/>
        <result property="level2Code" column="level2_code"/>
        <result property="level3Code" column="level3_code"/>
        <result property="productFeature" column="product_feature"/>
        <result property="additionalRisksType" column="additional_risks_type"/>
        <result property="renewalMaxAge" column="renewal_max_age"/>
        <result property="longShortFlag" column="long_short_flag"/>
        <result property="surrPermit" column="surr_permit"/>
        <result property="renewalFlag" column="renewal_flag"/>
        <result property="paymentType" column="payment_type"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="insurancePeriod" column="insurance_period"/>
        <result property="bonusFlag" column="bonus_flag"/>
        <result property="premiumReturn" column="premium_return"/>
        <result property="keyWord" column="key_word"/>
        <result property="targetUser" column="target_user"/>
        <result property="termsFlag" column="terms_flag"/>
        <result property="productTermsName" column="product_terms_name"/>
        <result property="productTermsFileCode" column="product_terms_file_code"/>
        <result property="dutyFlag" column="duty_flag"/>
        <result property="dutyFileCode" column="duty_file_code"/>
        <result property="premTableFlag" column="prem_table_flag"/>
        <result property="premTableFileCode" column="prem_table_file_code"/>
        <result property="contractCode" column="contract_code"/>
        <result property="outProductCode" column="out_product_code"/>
        <result property="clauseVisibleFlag" column="clause_visible_flag"/>
        <result property="sellAmount" column="sell_amount"/>
        <result property="businessType" column="business_class"/>
        <result property="isReceipt" column="is_receipt"/>
        <result property="isCallback" column="is_callback"/>
        <result property="giveFlag" column="give_flag"/>
        <result property="hesitationPeriod" column="hesitation_period"/>
        <result property="protocolFlag" column="protocol_flag"/>
        <result property="insuredPeriodType" column="insured_period_type"/>
        <result property="policyCoverage" column="policy_coverage"/>
        <result property="insuredPeriod" column="insured_period"/>
        <result property="paymentPeriodTimes" column="payment_period_times"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
        <result property="ruralPlanCode" column="rural_plan_code"/>
    </resultMap>

    <!--获取导出险种产品列表-->
    <select id="findExportList" resultType="com.mpolicy.manage.modules.sell.entity.InsuranceProductListOut">
        SELECT product_code,product_name,company_code,company_name,main_product_flag,product_terms_file_code,prem_table_flag,duty_flag,
        if(is_receipt=1,'需要','不需要') as receipt,if(is_callback=1,'需要','不需要') as callback,
        protocol_flag,
        product_channel
        FROM insurance_product_info
        ${ew.customSqlSegment}
    </select>

</mapper>
