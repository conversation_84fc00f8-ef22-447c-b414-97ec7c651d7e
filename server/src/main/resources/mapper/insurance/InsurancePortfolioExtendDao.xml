<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance..dao.InsurancePortfolioExtendDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsurancePortfolioExtendEntity" id="insurancePortfolioExtendMap">
        <result property="id" column="id"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="insureAge" column="insure_age"/>
        <result property="coveragePeriod" column="coverage_period"/>
        <result property="amount" column="amount"/>
        <result property="payPeriod" column="pay_period"/>
        <result property="hesitationPeriod" column="hesitation_period"/>
        <result property="waitingPeriod" column="waiting_period"/>
        <result property="insurerJobType" column="insurer_job_type"/>
        <result property="renewalCondition" column="renewal_condition"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>