<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceGroupPlanProductDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceGroupPlanProductEntity" id="insuranceGroupPlanProductMap">
        <result property="id" column="id"/>
        <result property="planCode" column="plan_code"/>
        <result property="productCode" column="product_code"/>
        <result property="productCoverage" column="product_coverage"/>
        <result property="premium" column="premium"/>
        <result property="insuredPeriodType" column="insured_period_type"/>
        <result property="insuredPeriod" column="insured_period"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="paymentPeriodTimes" column="payment_period_times"/>
        <result property="paymentType" column="payment_type"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>
