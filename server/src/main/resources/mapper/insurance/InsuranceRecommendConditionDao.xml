<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceRecommendConditionDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insurance.entity.InsuranceRecommendConditionEntity" id="insuranceRecommendConditionMap">
        <result property="id" column="id"/>
        <result property="category" column="category"/>
        <result property="ageLeftValue" column="age_left_value"/>
        <result property="ageRightValue" column="age_right_value"/>
        <result property="openLeftValue" column="open_left_value"/>
        <result property="openRightValue" column="open_right_value"/>
        <result property="insureArea" column="insure_area"/>
        <result property="socialInsurance" column="social_insurance"/>
        <result property="channelCategory" column="channel_category"/>
        <result property="maturityAge" column="maturity_age"/>
        <result property="coefficientExpansion" column="coefficient_expansion"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>
