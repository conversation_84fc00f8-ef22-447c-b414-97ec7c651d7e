<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insurance.dao.InsuranceProductInfoDisclosuresDao">
    <!-- 获取数据列表 -->
    <select id="getProductInfoDisclosuresList"
            resultType="com.mpolicy.product.common.product.ProductInfoDisclosuresOut">
        select id,
               commodity_code as commodityCode,
               commodity_name as commodityName,
               company_code as companyCode,
               company_name as companyName,
               disclosures_Status as disclosuresStatus
        from insurance_product_info_disclosures a
        <where>
            <if test="input.commodityCode != null and input.commodityCode != ''">
                and commodity_code = #{input.commodityCode,jdbcType=VARCHAR}
            </if>
            <if test="input.commodityName != null and input.commodityName != ''">
                and commodity_name like concat('%',#{input.commodityName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.companyName != null and input.companyName != ''">
                and company_name like concat('%',#{input.companyName,jdbcType=VARCHAR},'%')
            </if>
                and deleted = 0
        </where>
        order by id desc
    </select>
</mapper>