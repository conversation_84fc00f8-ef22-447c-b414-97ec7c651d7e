<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.protocol.dao.EpProtocolInsuranceProductProductDao">
    <!--获取险种关联的产品信息-->
    <select id="findReconcileInsuranceProductList"
            resultType="com.mpolicy.manage.modules.protocol.vo.InsuranceProductProductMapOut">
        SELECT ipp.`product_code`,
               ipp.`product_name`,
               ipp.`product_plan`,
               ip.`insurance_product_code`,
               ip.`insurance_product_name`,
               ip.`is_check_renewal_period`,
               ip.`company_code`,
               ip.`company_name`,
               ip.company_short_name,
               ip.`reconcile_type`,
        ipp.id as insuranceProductId
        FROM `ep_protocol_insurance_product_product` ipp
        LEFT JOIN `ep_protocol_insurance_product` ip on ipp.`insurance_product_id` = ip.`id`
        WHERE ip.`reconcile_type` = #{reconcileType,jdbcType=INTEGER} and ipp.`product_code` IN
        <foreach collection="productCodeList" separator="," close=")" open="(" item="productCode">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="findReconcileInsuranceProduct"
            resultType="com.mpolicy.manage.modules.protocol.vo.InsuranceProductProductMapOut">
        SELECT ipp.`product_code`,
        ipp.`product_name`,
        ipp.`product_plan`,
        ip.`insurance_product_code`,
        ip.`insurance_product_name`,
        ip.`is_check_renewal_period`,
        ip.`company_code`,
        ip.`company_name`,
        ip.company_short_name,
        ip.`reconcile_type`,
        ipp.id as insuranceProductId
        FROM `ep_protocol_insurance_product_product` ipp
        LEFT JOIN `ep_protocol_insurance_product` ip on ipp.`insurance_product_id` = ip.`id`
        WHERE ip.`reconcile_type` = #{reconcileType,jdbcType=INTEGER} and ipp.`product_code` = #{productCode,jdbcType=VARCHAR}
    </select>
</mapper>