<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.protocol.dao.EpProtocolInsuranceProductDao">
    <!--获取数据列表-->
    <select id="findEpProtocolInsuranceProductList"
            resultType="com.mpolicy.manage.modules.protocol.vo.EpProtocolInsuranceProductListOut">
        SELECT epip.id,
               epip.company_code           AS companyCode,
               epip.company_name           AS companyName,
               epip.company_short_name     AS companyShortName,
               epip.insurance_product_code AS insuranceProductCode,
               epip.insurance_product_name AS insuranceProductName,
               epip.is_check_renewal_period,
        COUNT(epipp.`id`)  as productNum,GROUP_CONCAT(epipp.product_name) as productName
        FROM ep_protocol_insurance_product epip
        LEFT JOIN ep_protocol_insurance_product_product epipp on epip.id = epipp.insurance_product_id
        where  epip.reconcile_type = #{input.reconcileType,jdbcType=INTEGER}
            <if test="input.companyCode != null and input.companyCode != ''">
                and epip.company_code = #{input.companyCode,jdbcType=VARCHAR}
            </if>
            <if test="input.insuranceProductCode != null and input.insuranceProductCode != ''">
                and epip.insurance_product_code like concat('%',#{input.insuranceProductCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.insuranceProductName != null and input.insuranceProductName != ''">
                and epip.insurance_product_name like concat('%',#{input.insuranceProductName,jdbcType=VARCHAR},'%')
            </if>
            <if test="input.productCode != null and input.productCode != ''">
                and epipp.product_code like concat('%', #{input.productCode,jdbcType=VARCHAR},'%')
            </if>
        group by epip.id
        HAVING 1=1
        <if test="input.isRelevanceProduct != null and input.isRelevanceProduct ==0">
            and productNum = 0
        </if>
        <if test="input.isRelevanceProduct != null and input.isRelevanceProduct ==1">
            and productNum > 0
        </if>
        order by epip.id desc
    </select>
</mapper>