<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureOrderInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureOrderInfoEntity" id="insureOrderInfoMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="sourceInsureOrderCode" column="source_insure_order_code"/>
        <result property="portfolioGroup" column="portfolio_group"/>
        <result property="insureOrderType" column="insure_order_type"/>
        <result property="insureMode" column="insure_mode"/>
        <result property="userNo" column="user_no"/>
        <result property="userName" column="user_name"/>
        <result property="openId" column="open_id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="insAdvancePolicyCode" column="ins_advance_policy_code"/>
        <result property="underwriteTime" column="underwrite_time"/>
        <result property="insUnderwriteMsg" column="ins_underwrite_msg"/>
        <result property="familyPolicyCode" column="family_policy_code"/>
        <result property="applicantPolicyNo" column="applicant_policy_no"/>
        <result property="policyCode" column="policy_code"/>
        <result property="companyPolicyUrl" column="company_policy_url"/>
        <result property="policyUrl" column="policy_url"/>
        <result property="insurePayType" column="insure_pay_type"/>
        <result property="insurePayNo" column="insure_pay_no"/>
        <result property="insurePayTime" column="insure_pay_time"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="companyLogo" column="company_logo"/>
        <result property="policyType" column="policy_type"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="portfolioName" column="portfolio_name"/>
        <result property="commoditySellMode" column="commodity_sell_mode"/>
        <result property="commodityCode" column="commodity_code"/>
        <result property="commodityName" column="commodity_name"/>
        <result property="planCode" column="plan_code"/>
        <result property="coverageYear" column="coverage_year"/>
        <result property="payPeriod" column="pay_period"/>
        <result property="amount" column="amount"/>
        <result property="payMode" column="pay_mode"/>
        <result property="premium" column="premium"/>
        <result property="payPremium" column="pay_premium"/>
        <result property="numberOfIns" column="number_of_ins"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="invalidDate" column="invalid_date"/>
        <result property="autoPayment" column="auto_payment"/>
        <result property="autoRenewFlag" column="auto_renew_flag"/>
        <result property="holderName" column="holder_name"/>
        <result property="holderIdNo" column="holder_id_no"/>
        <result property="insuredNames" column="insured_names"/>
        <result property="insuredIdNos" column="insured_id_nos"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelCode" column="channel_code"/>
        <result property="userChannelCode" column="user_channel_name"/>
        <result property="userChannelName" column="user_channel_name"/>
        <result property="insureAgentType" column="insure_agent_type"/>
        <result property="agentCode" column="agent_code"/>
        <result property="agentName" column="agent_name"/>
        <result property="referrerCode" column="referrer_code"/>
        <result property="referrerName" column="referrer_name"/>
        <result property="branchCode" column="branch_code"/>
        <result property="channelBranchCode" column="channel_branch_code"/>
        <result property="terminalType" column="terminal_type"/>
        <result property="customParam" column="custom_param"/>
        <result property="insureIp" column="insure_ip"/>
        <result property="insureTime" column="insure_time"/>
        <result property="insureActivateStatus" column="insure_activate_status"/>
        <result property="insureOrderStatus" column="insure_order_status"/>
        <result property="isSts" column="is_sts"/>
        <result property="stsFilePath" column="sts_file_path"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <sql id="insureOrderInfoFileds">
        t_order.id,
        t_order.insure_order_code,
        t_order.source_insure_order_code,
        t_order.insure_order_type,
        t_order.portfolio_group,
        t_order.insure_mode,
        t_order.user_no,
        t_order.user_name,
        t_order.open_id,
        t_order.contract_code,
        t_order.ins_advance_policy_code,
        t_order.underwrite_time,
        t_order.ins_underwrite_msg,
        t_order.family_policy_code,
        t_order.applicant_policy_no,
        t_order.policy_code,
        t_order.company_policy_url,
        t_order.policy_url,
        t_order.insure_pay_type,
        t_order.insure_pay_no,
        t_order.insure_pay_time,
        t_order.company_code,
        t_order.company_name,
        t_order.company_logo,
        t_order.policy_type,
        t_order.portfolio_code,
        t_order.portfolio_name,
        t_order.commodity_sell_mode,
        t_order.commodity_code,
        t_order.commodity_name,
        t_order.plan_code,
        t_order.coverage_year,
        t_order.pay_period,
        t_order.amount,
        t_order.pay_mode,
        t_order.premium,
        t_order.pay_premium,
        t_order.number_of_ins,
        t_order.specify_effective_date,
        t_order.estimate_invalid_date,
        t_order.effective_date,
        t_order.invalid_date,
        t_order.auto_payment,
        t_order.auto_renew_flag,
        t_order.holder_name,
        t_order.holder_id_no,
        t_order.insured_names,
        t_order.insured_id_nos,
        t_order.org_code,
        t_order.org_name,
        t_order.channel_code,
        t_order.channel_name,
        t_order.user_channel_code,
        t_order.user_channel_name,
        t_order.insure_agent_type,
        t_order.agent_code,
        t_order.agent_name,
        t_order.referrer_code,
        t_order.referrer_name,
        t_order.referrer_id_no,
        t_order.referrer_source,
        t_order.manage_wno,
        t_order.child_channel_code,
        t_order.customer_source,
        t_order.branch_code,
        t_order.channel_branch_code,
        t_order.terminal_type,
        t_order.custom_param,
        t_order.propagandist_code,
        t_order.propagandist_referrer_code,
        t_order.insure_ip,
        t_order.insure_time,
        t_order.insure_time_second,
        t_order.insure_activate_status,
        t_order.insure_order_status,
        t_order.is_sts,
        t_order.sts_file_path,
        t_order.is_give,
        t_order.prepare_order_code,
        t_order.buy_channel,
        t_order.activity_code,
        t_order.product_activity_code,
        t_order.source_policy_plan_code,
        t_order.refund_amount,
        t_order.refund_user,
        t_order.refund_time,
        t_order.refund_end_time,
        t_order.refund_reason,
        t_order.refund_fail_reason,
        t_order.rural_proxy_name,
        t_order.rural_proxy_id_no,
        t_order.rural_proxy_order_type,
        t_order.one_id,
        t_order.is_baichuan,
        t_order.bc_channel_code,
        t_order.deleted,
        t_order.create_user,
        t_order.create_time,
        t_order.update_user,
        t_order.update_time,
        t_order.revision
    </sql>

    <sql id="queryInsureOrderInfoByPageConditions">
        <if test="input.insureOrderCode != null and input.insureOrderCode != ''">
            and t_order.insure_order_code = #{input.insureOrderCode}
        </if>
        <if test="input.companyCode != null and input.companyCode != ''">
            and t_order.company_code = #{input.companyCode}
        </if>
        <if test="input.customerCode != null and input.customerCode != ''">
            and t_order.customer_code = #{input.customerCode}
        </if>
        <if test="input.holderName != null and input.holderName != ''">
            and t_order.holder_name = #{input.holderName}
        </if>
        <if test="input.agentCode != null and input.agentCode != ''">
            and t_order.agent_code = #{input.agentCode}
        </if>
        <if test="input.referrerCode != null and input.referrerCode != ''">
            and t_order.referrer_code = #{input.referrerCode}
        </if>
        <if test="input.portfolioGroup != null and input.portfolioGroup != ''">
            and t_order.portfolio_group = #{input.portfolioGroup}
        </if>
        <if test="input.ruralProxyOrderType != null">
            and t_order.rural_proxy_order_type = #{input.ruralProxyOrderType}
        </if>
        <trim prefix="and t_order.insure_order_code in (" suffix=")" prefixOverrides="UNION">
            <if test="input.holderMobile != null and input.holderMobile != ''">
                SELECT insure_order_code FROM insure_order_holder WHERE deleted = 0 and holder_mobile = #{input.holderMobile}
            </if>
            <if test="input.holderIdNo != null and input.holderIdNo != ''">
                UNION SELECT insure_order_code FROM insure_order_holder WHERE deleted = 0 and holder_certi_no = #{holderIdNo}
            </if>
            <if test="input.insuredName != null and input.insuredName != ''">
                UNION SELECT insure_order_code FROM insure_order_insured WHERE deleted = 0 and insured_name = #{input.insuredName}
            </if>
            <if test="input.insuredIdNo != null and input.insuredIdNo != ''">
                UNION SELECT insure_order_code FROM insure_order_insured WHERE deleted = 0 and insured_certi_no = #{input.insuredIdNo}
            </if>
        </trim>
        <if test="input.channelCode != null and input.channelCode != ''">
            and t_order.channel_code = #{input.channelCode}
        </if>
        <if test="input.commodityCode != null and input.commodityCode != ''">
            and t_order.commodity_code = #{input.commodityCode}
        </if>
        <if test="input.insureOrderStatus != null">
            and t_order.insure_order_status = #{input.insureOrderStatus}
        </if>
        <if test="input.orgList != null and input.orgList.size > 0">
            and t_order.org_code in
            <foreach collection="input.orgList" item="item" open="(" close=")" separator=" , ">
                #{item}
            </foreach>
        </if>
        <if test="input.channelBranchCodeList != null and input.channelBranchCodeList.size > 0">
            and t_order.channel_branch_code in
            <foreach collection="input.channelBranchCodeList" item="item" open="(" close=")" separator=" , ">
                #{item}
            </foreach>
        </if>
        <if test="input.startDate != null and input.startDate != ''">
            and date_format(t_order.create_time,'%Y-%m-%d') >= #{input.startDate}
        </if>
        <if test="input.endDate != null and input.endDate != ''">
            and date_format(t_order.create_time,'%Y-%m-%d') &lt;= #{input.endDate}
        </if>
    </sql>

    <select id="queryInsureOrderInfoByPage" parameterType="java.util.Map" resultType="com.mpolicy.manage.modules.insure.entity.InsureOrderInfoEntity">
        select <include refid="insureOrderInfoFileds"></include> from (select <include refid="insureOrderInfoFileds"></include>
        from insure_order_info t_order
        where t_order.deleted = 0 and t_order.insure_order_status &lt;> 11
        <include refid="queryInsureOrderInfoByPageConditions">
        </include>
        <if test="input.policyCode != null and input.policyCode != ''">
            and t_order.policy_code = #{input.policyCode}
        </if>
        union
        select <include refid="insureOrderInfoFileds"></include>
        from insure_order_info t_order
        join insure_order_insured t_insured
        on t_order.insure_order_code = t_insured.insure_order_code
        where t_order.deleted = 0 and t_insured.deleted = 0 and t_order.insure_order_status &lt;> 11
        <include refid="queryInsureOrderInfoByPageConditions">
        </include>
        <if test="input.policyCode != null and input.policyCode != ''">
            and t_insured.insured_policy_code = #{input.policyCode}
        </if>
        order by id desc) t_order
    </select>

    <select id="queryInsureOrderList" parameterType="java.util.Map" resultType="com.mpolicy.manage.modules.insure.vo.insureOrderInfoOut">
        SELECT
        ii.insure_order_code as insureOrderCode,
        ii.insure_order_status as insureOrderStatus,
        ii.premium as  premium,
        ii.insure_pay_time as  insurePayTime,
        ii.commodity_name as commodityName,
        ih.holder_name as holderName,
        ih.holder_certi_code as holderCertiCode,
        ih.holder_certi_no as holderCertiNo,
        io.insured_owner_rela as insuredOwnerRela,
        io.insured_name as insuredName,
        io.insured_certi_code as insuredCertiCode,
        io.insured_certi_no as insuredCertiNo,
        io.insured_mobile as insuredMobile,
        ii.user_no as userNo,
        ii.user_name as userName,
        au.business_code as businessCode,
        au.agent_name as agentName,
        ca.referrer_wno as referrerWno,
        ca.referrer_name as referrerName,
        ii.channel_code as channelCode
        FROM
        insure_order_info ii
        LEFT JOIN insure_order_holder ih ON ii.insure_order_code = ih.insure_order_code
        AND ih.deleted = 0
        LEFT JOIN insure_order_insured io ON ii.insure_order_code = io.insure_order_code
        AND io.deleted = 0
        LEFT JOIN agent_user_info au ON ii.agent_code = au.agent_code
        LEFT JOIN channel_application_referrer ca ON ii.referrer_code = ca.referrer_code
        AND ca.deleted = 0
        WHERE
        ii.deleted=0 and ii.insure_order_status != 11
        <if test="input.insureOrderCode != null and input.insureOrderCode != ''">
            AND ii.insure_order_code = #{input.insureOrderCode}
        </if>
        <if test="input.companyCode != null and input.companyCode != ''">
            AND ii.company_code = #{input.companyCode}
        </if>
        <if test="input.holderName != null and input.holderName != ''">
            AND ii.holder_name = #{input.holderName}
        </if>
        <if test="input.insuredName != null and input.insuredName != ''">
            AND ii.insured_names like concat('%',#{input.insuredName},'%'))
        </if>
        <if test="input.insuredName != null and input.insuredName != ''">
            AND ii.insured_id_nos like concat('%',#{input.insuredIdNo},'%'))
        </if>
        <if test="input.policyCode != null and input.policyCode != ''">
            AND ii.policy_code like concat('%',#{input.policyCode},'%'))
        </if>
        <if test="input.portfolioGroup != null and input.portfolioGroup != ''">
            AND ii.portfolio_group like concat('%',#{input.portfolioGroup},'%'))
        </if>
        <if test="input.commodityCode != null and input.commodityCode != ''">
            AND ii.commodity_code = #{input.commodityCode}
        </if>
        <if test="input.channelCode != null and input.channelCode != ''">
            AND ii.channel_code like concat('%',#{input.channelCode},'%'))
        </if>
        <if test="input.insureOrderStatus != null and input.insureOrderStatus != ''">
            AND ii.insure_order_status = #{input.insureOrderStatus}
        </if>
        <if test="input.agentCode != null and input.agentCode != ''">
            AND ii.agent_code = #{input.agentCode}
        </if>
        <if test="input.referrerCode != null and input.referrerCode != ''">
            AND ii.referrer_code = #{input.referrerCode}
        </if>
        <if test="input.startDate != null and input.startDate != ''">
            AND date_format(ii.create_time,'%Y-%m-%d') >= #{input.startDate}
        </if>
        <if test="input.endDate != null and input.endDate != ''">
            AND date_format(ii.create_time,'%Y-%m-%d') &lt;= #{input.endDate}
        </if>
        <if test="input.orgList!=null and input.orgList.size>0">
            and ii.org_code in
            <foreach collection="input.orgList" separator="," item="orgCode" open="(" close=")">
                #{orgCode}
            </foreach>
        </if>
        <if test="input.channelBranchCodeList!=null and input.channelBranchCodeList.size>0">
            and ii.channel_branch_code in
            <foreach collection="input.channelBranchCodeList" separator="," item="code" open="(" close=")">
                #{code}
            </foreach>
        </if>
        order by ii.id desc
    </select>
    <select id="znQueryInsureOrderList" parameterType="java.util.Map" resultType="com.mpolicy.manage.modules.insure.vo.insureOrderInfoProOut">
        SELECT
        ii.insure_order_code as insureOrderCode,
        ii.insure_order_status as insureOrderStatus,
        ii.premium as  premium,
        ii.insure_pay_time as  insurePayTime,
        ii.commodity_name as commodityName,
        ii.create_time as createTime,
        ih.holder_name as holderName,
        ih.holder_certi_code as holderCertiCode,
        ih.holder_certi_no as holderCertiNo,
        ih.holder_mobile as holderMobile,
        io.insured_owner_rela as insuredOwnerRela,
        io.insured_name as insuredName,
        io.insured_certi_code as insuredCertiCode,
        io.insured_certi_no as insuredCertiNo,
        io.insured_mobile as insuredMobile
        FROM
        insure_order_info ii
        LEFT JOIN insure_order_holder ih ON ii.insure_order_code = ih.insure_order_code
        AND ih.deleted = 0
        LEFT JOIN insure_order_insured io ON ii.insure_order_code = io.insure_order_code
        AND io.deleted = 0
        WHERE ii.deleted=0 and ii.insure_order_status != 11
        <if test="input.commodityCode != null and input.commodityCode != ''">
            AND ii.commodity_code = #{input.commodityCode}
        </if>
        <if test="input.insureOrderStatus != null and input.insureOrderStatus != ''">
            AND ii.insure_order_status = #{input.insureOrderStatus}
        </if>
        <if test="input.startDate != null and input.startDate != ''">
            AND date_format(ii.create_time,'%Y-%m-%d') >= #{input.startDate}
        </if>
        <if test="input.endDate != null and input.endDate != ''">
            AND date_format(ii.create_time,'%Y-%m-%d') &lt;= #{input.endDate}
        </if>
        order by ii.id desc
    </select>
</mapper>
