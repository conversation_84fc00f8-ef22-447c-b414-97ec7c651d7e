<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureOrderHolderDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureOrderHolderEntity" id="insureOrderHolderMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="holderName" column="holder_name"/>
        <result property="holderEname" column="holder_ename"/>
        <result property="holderNationality" column="holder_nationality"/>
        <result property="holderNation" column="holder_nation"/>
        <result property="holderBirthDay" column="holder_birth_day"/>
        <result property="holderCertiCode" column="holder_certi_code"/>
        <result property="holderCertiNo" column="holder_certi_no"/>
        <result property="holderCertiLongTerm" column="holder_certi_long_term"/>
        <result property="holderCertiEffectiveDate" column="holder_certi_effective_date"/>
        <result property="holderCertiInvalidDate" column="holder_certi_invalid_date"/>
        <result property="holderAge" column="holder_age"/>
        <result property="holderEducation" column="holder_education"/>
        <result property="holderGender" column="holder_gender"/>
        <result property="holderEmail" column="holder_email"/>
        <result property="provincesCode" column="provinces_code"/>
        <result property="provincesName" column="provinces_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="areaCode" column="area_code"/>
        <result property="areaName" column="area_name"/>
        <result property="holderAddress" column="holder_address"/>
        <result property="holderZip" column="holder_zip"/>
        <result property="holderJobType" column="holder_job_type"/>
        <result property="holderJobCodeLevel1" column="holder_job_code_level1"/>
        <result property="holderJobCodeName1" column="holder_job_code_name1"/>
        <result property="holderJobCodeLevel2" column="holder_job_code_level2"/>
        <result property="holderJobCodeName2" column="holder_job_code_name2"/>
        <result property="holderJobCodeLevel3" column="holder_job_code_level3"/>
        <result property="holderJobCodeName3" column="holder_job_code_name3"/>
        <result property="holderWorkCompanyType" column="holder_work_company_type"/>
        <result property="holderWorkCompany" column="holder_work_company"/>
        <result property="holderWorkCompanyName" column="holder_work_company_name"/>
        <result property="holderWorkCompanyAddr" column="holder_work_company_addr"/>
        <result property="holderTel" column="holder_tel"/>
        <result property="holderMobile" column="holder_mobile"/>
        <result property="holderIncome" column="holder_income"/>
        <result property="holderHeight" column="holder_height"/>
        <result property="holderWeight" column="holder_weight"/>
        <result property="holderMaritalStat" column="holder_marital_stat"/>
        <result property="holderHealthStatus" column="holder_health_status"/>
        <result property="holderIsSmoking" column="holder_is_smoking"/>
        <result property="holderHomeTel" column="holder_home_tel"/>
        <result property="holderJobTel" column="holder_job_tel"/>
        <result property="healthData" column="health_data"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>