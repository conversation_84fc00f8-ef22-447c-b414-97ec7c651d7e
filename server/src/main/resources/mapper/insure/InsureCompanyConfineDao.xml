<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureCompanyConfineDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureCompanyConfineEntity" id="insureCompanyConfineMap">
        <result property="id" column="id"/>
        <result property="confineCode" column="confine_code"/>
        <result property="title" column="title"/>
        <result property="companyCodes" column="company_codes"/>
        <result property="productCodes" column="product_codes"/>
        <result property="clientType" column="client_type"/>
        <result property="isStatus" column="is_status"/>
        <result property="confineStartTime" column="confine_start_time"/>
        <result property="confineEndTime" column="confine_end_time"/>
        <result property="confineDesc" column="confine_desc"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="revision" column="revision"/>
    </resultMap>

    <select id="getCompanyList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            ic.company_code AS companyCode,ic.short_name AS companyName
        FROM
            insurance_company ic LEFT JOIN sell_product_info pi ON ic.company_code=pi.supplier_code
        WHERE
            ic.deleted=0 AND pi.supplier_code IS NOT NULL GROUP BY ic.company_code
    </select>

    <select id="getProductList" parameterType="com.alibaba.fastjson.JSONObject" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            product_code AS productCode,
            product_name AS productName
        FROM
            sell_product_info
        <where>
            <if test="productStatus != null and productStatus != ''">
                and product_status = #{productStatus,jdbcType=INTEGER}
            </if>
            <if test="companyCodes != null and companyCodes.size() > 0">
                and supplier_code in
                <foreach collection="companyCodes" open="(" close=")"  item="item" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCodes != null and productCodes.size() > 0">
                and product_code in
                <foreach collection="productCodes" open="(" close=")"  item="item" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
