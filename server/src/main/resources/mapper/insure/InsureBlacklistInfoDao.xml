<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureBlacklistInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureBlacklistInfoEntity" id="insureBlacklistInfoMap">
        <result property="id" column="id"/>
        <result property="blacklistType" column="blacklist_type"/>
        <result property="identificationType" column="identification_type"/>
        <result property="identificationName" column="identification_name"/>
        <result property="identificationNum" column="identification_num"/>
        <result property="blacklistDesc" column="blacklist_desc"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>