<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureOrderInsuredBeneDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureOrderInsuredBeneEntity" id="insureOrderInsuredBeneMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="insureOrderInsuredCode" column="insure_order_insured_code"/>
        <result property="beneType" column="bene_type"/>
        <result property="beneOwnerRela" column="bene_owner_rela"/>
        <result property="beneInsuredRela" column="bene_insured_rela"/>
        <result property="beneName" column="bene_name"/>
        <result property="beneCertiCode" column="bene_certi_code"/>
        <result property="beneCertiNo" column="bene_certi_no"/>
        <result property="beneCertiLongTerm" column="bene_certi_long_term"/>
        <result property="beneCertiEffectiveDate" column="bene_certi_effective_date"/>
        <result property="beneCertiInvalidDate" column="bene_certi_invalid_date"/>
        <result property="beneGender" column="bene_gender"/>
        <result property="beneBirthDay" column="bene_birth_day"/>
        <result property="beneNationality" column="bene_nationality"/>
        <result property="provincesCode" column="provinces_code"/>
        <result property="provincesName" column="provinces_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="areaCode" column="area_code"/>
        <result property="areaName" column="area_name"/>
        <result property="beneContactAddress" column="bene_contact_address"/>
        <result property="beneOrder" column="bene_order"/>
        <result property="beneRate" column="bene_rate"/>
        <result property="beneMobile" column="bene_mobile"/>
        <result property="beneEmail" column="bene_email"/>
        <result property="beneTel" column="bene_tel"/>
        <result property="beneTelPhone" column="bene_tel_phone"/>
        <result property="beneJobType" column="bene_job_type"/>
        <result property="beneJobCodeLevel1" column="bene_job_code_level1"/>
        <result property="beneJobCodeName1" column="bene_job_code_name1"/>
        <result property="beneJobCodeLevel2" column="bene_job_code_level2"/>
        <result property="beneJobCodeName2" column="bene_job_code_name2"/>
        <result property="beneJobCodeLevel3" column="bene_job_code_level3"/>
        <result property="beneJobCodeName3" column="bene_job_code_name3"/>
        <result property="beneIncome" column="bene_income"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>