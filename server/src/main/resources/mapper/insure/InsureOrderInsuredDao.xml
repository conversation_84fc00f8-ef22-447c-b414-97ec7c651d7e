<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureOrderInsuredDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureOrderInsuredEntity" id="insureOrderInsuredMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="insureOrderInsuredCode" column="insure_order_insured_code"/>
        <result property="preOrderNo" column="pre_order_no"/>
        <result property="insuredPolicyCode" column="insured_policy_code"/>
        <result property="insuredOwnerRela" column="insured_owner_rela"/>
        <result property="insuredName" column="insured_name"/>
        <result property="totalPremium" column="total_premium"/>
        <result property="insuredNationality" column="insured_nationality"/>
        <result property="insuredNation" column="insured_nation"/>
        <result property="insuredCertiCode" column="insured_certi_code"/>
        <result property="insuredCertiNo" column="insured_certi_no"/>
        <result property="insuredCertiLongTerm" column="insured_certi_long_term"/>
        <result property="insuredCertiEffectiveDate" column="insured_certi_effective_date"/>
        <result property="insuredCertiInvalidDate" column="insured_certi_invalid_date"/>
        <result property="insuredBirthDay" column="insured_birth_day"/>
        <result property="insuredAge" column="insured_age"/>
        <result property="insuredGender" column="insured_gender"/>
        <result property="insuredSocialTag" column="insured_social_tag"/>
        <result property="provincesCode" column="provinces_code"/>
        <result property="provincesName" column="provinces_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="areaCode" column="area_code"/>
        <result property="areaName" column="area_name"/>
        <result property="insuredAddress" column="insured_address"/>
        <result property="insuredZip" column="insured_zip"/>
        <result property="insuredJobType" column="insured_job_type"/>
        <result property="insuredJobCodeLevel1" column="insured_job_code_level1"/>
        <result property="insuredJobCodeName1" column="insured_job_code_name1"/>
        <result property="insuredJobCodeLevel2" column="insured_job_code_level2"/>
        <result property="insuredJobCodeName2" column="insured_job_code_name2"/>
        <result property="insuredJobCodeLevel3" column="insured_job_code_level3"/>
        <result property="insuredJobCodeName3" column="insured_job_code_name3"/>
        <result property="insuredWorkCompanyType" column="insured_work_company_type"/>
        <result property="insuredWorkCompanyName" column="insured_work_company_name"/>
        <result property="insuredMobile" column="insured_mobile"/>
        <result property="insuredEmail" column="insured_email"/>
        <result property="insuredIncome" column="insured_income"/>
        <result property="insuredHeight" column="insured_height"/>
        <result property="insuredWeight" column="insured_weight"/>
        <result property="insuredHaveLegalBeneficiary" column="insured_have_legal_beneficiary"/>
        <result property="insuredMaritalStat" column="insured_marital_stat"/>
        <result property="insuredHealthStatus" column="insured_health_status"/>
        <result property="insuredIsSmoking" column="insured_is_smoking"/>
        <result property="insuredJobTel" column="insured_job_tel"/>
        <result property="insuredHomeTel" column="insured_home_tel"/>
        <result property="beneType" column="bene_type"/>
        <result property="healthData" column="health_data"/>
        <result property="intHealthFlag" column="int_health_flag"/>
        <result property="intHealthData" column="int_health_data"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>