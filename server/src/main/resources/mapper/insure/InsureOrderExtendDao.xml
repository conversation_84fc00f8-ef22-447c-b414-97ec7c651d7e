<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureOrderExtendDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureOrderExtendEntity" id="insureOrderExtendMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="policyForm" column="policy_form"/>
        <result property="autoPayment" column="auto_payment"/>
        <result property="firstPayBankCode" column="first_pay_bank_code"/>
        <result property="firstPayBankAddr" column="first_pay_bank_addr"/>
        <result property="firstPayAcctNo" column="first_pay_acct_no"/>
        <result property="firstPayAcctName" column="first_pay_acct_name"/>
        <result property="firstPayType" column="first_pay_type"/>
        <result property="partPayBankCode" column="part_pay_bank_code"/>
        <result property="partPayBankAddr" column="part_pay_bank_addr"/>
        <result property="partPayAcctNo" column="part_pay_acct_no"/>
        <result property="partPayAcctName" column="part_pay_acct_name"/>
        <result property="partPayType" column="part_pay_type"/>
        <result property="autoRenewFlag" column="auto_renew_flag"/>
        <result property="payCardHolderName" column="pay_card_holder_name"/>
        <result property="payCardHolderId" column="pay_card_holder_id"/>
        <result property="payBankCode" column="pay_bank_code"/>
        <result property="payAcctNo" column="pay_acct_no"/>
        <result property="payCardHolderMobile" column="pay_card_holder_mobile"/>
        <result property="protocolNo" column="protocol_no"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>