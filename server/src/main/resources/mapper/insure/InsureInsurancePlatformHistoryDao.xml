<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureInsurancePlatformHistoryDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureInsurancePlatformHistoryEntity" id="insureInsurancePlatformHistoryMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="prePolicyNo" column="pre_policy_no"/>
        <result property="userNo" column="user_no"/>
        <result property="operateType" column="operate_type"/>
        <result property="operateName" column="operate_name"/>
        <result property="operateFlag" column="operate_flag"/>
        <result property="requestData" column="request_data"/>
        <result property="responseData" column="response_data"/>
        <result property="operateRemark" column="operate_remark"/>
        <result property="consumTime" column="consum_time"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>


</mapper>