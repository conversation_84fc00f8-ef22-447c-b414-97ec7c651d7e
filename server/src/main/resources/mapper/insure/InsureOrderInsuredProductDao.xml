<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.insure.dao.InsureOrderInsuredProductDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.mpolicy.manage.modules.insure.entity.InsureOrderInsuredProductEntity" id="insureOrderInsuredProductMap">
        <result property="id" column="id"/>
        <result property="insureOrderCode" column="insure_order_code"/>
        <result property="insureOrderInsuredCode" column="insure_order_insured_code"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="portfolioCode" column="portfolio_code"/>
        <result property="portfolioName" column="portfolio_name"/>
        <result property="protocolProductCode" column="protocol_product_code"/>
        <result property="mainProductFlag" column="main_product_flag"/>
        <result property="additionalRisksType" column="additional_risks_type"/>
        <result property="productPlan" column="product_plan"/>
        <result property="coverageYear" column="coverage_year"/>
        <result property="payPeriod" column="pay_period"/>
        <result property="amount" column="amount"/>
        <result property="payMode" column="pay_mode"/>
        <result property="numberOfIns" column="number_of_ins"/>
        <result property="premium" column="premium"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="invalidDate" column="invalid_date"/>
        <result property="deleted" column="deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="revision" column="revision"/>
    </resultMap>
</mapper>