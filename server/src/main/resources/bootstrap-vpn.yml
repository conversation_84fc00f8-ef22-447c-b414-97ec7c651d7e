spring:
  application:
    name: admin-center
  cloud:
    config:
      profile: ${spring.profiles.active}
      # 方式一：发现模式
      #discovery:
      #        enabled: true
      #        service-id: CONFIG
      # 方式二：http指定模式 推荐本地使用
      uri:
        - https://api-test.xiaowhale.com/config/
    # 修复github webhook 只能刷新config server 无法刷新config client的问题
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 总上传的数据大小
      max-request-size: 200MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  rabbitmq:
    host: amqp-test.xiaowhale.net
    port: 5672
    username: MjphbXFwLWNuLWk3bTI2b2h3eTAwNDpMVEFJNXQ5SkRkZDRIMUpXaXRhWmN2dkE=
    password: ODI5QTM5ODMzNjIwNDc1RjFBNzNCRDBEMjUwRTUyNEZGNzNFRTBDMDoxNjIzODM0MDc4MDkx
    virtual-host: ims-test
    # 消息确认模式
    publisher-confirms: true
    publisher-returns: true
    template:
      mandatory: true
    #消费端
    listener:
      simple:
        # 手动签收
        acknowledge-mode: manual
        #初始连接数量
        concurrency: 5
        #最大连接数量
        max-concurrency: 10
        #限流
        prefetch: 1
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      #      url: ************************************************************************************************************************************************************
      url: *********************************************************************************************************************************************************************
      username: mp_business
      password: ahLeicaesie4pi
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
##多数据源的配置
dynamic:
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      #      url: *******************************************************************************************************************************************************
      url: ****************************************************************************************************************************************************************
      username: mp_business
      password: ahLeicaesie4pi
    slave:
      driver-class-name: com.mysql.jdbc.Driver
#      url: ****************************************************************************************************************************************************************
#      username: mp_business
#      password: bohsai3Tax7ooz
      #      url: *******************************************************************************************************************************************************
      url: ****************************************************************************************************************************************************************
      username: mp_business_read
      password: ut3hicee9Ush9u

#mybatis plus
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.mpolicy.**.entity
  #不运用common的自动填充
  automatic: false
  global-config:
    #数据库相关配置
    db-config:
      #主键类型 UTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      db-column-underline: true
      #刷新mapper 调试神器
      refresh-mapper: true
      #数据库大写下划线转换
      #capital-mode: true
      # Sequence序列接口实现类配置
      #key-generator: com.baomidou.mybatisplus.incrementer.OracleKeyGenerator
      #逻辑删除配置
      logic-delete-value: -1
      logic-not-delete-value: 0
      #自定义填充策略接口实现
      #meta-object-handler: com.baomidou.springboot.xxx
      #自定义SQL注入器
      sql-injector: com.baomidou.mybatisplus.core.injector.DefaultSqlInjector
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
#redis
redis:
  model: standalone
  open: true # 缓存服务开关，关闭或者异常都会抛响应异常
  host: redis-test.internal.xiaowhale.net
  #  host: redis-test.xiaowhale.net
  password: sunhaijun:kaaYie4rie3juo # 密码（默认为空）
  port: 6379     #  模式的端口
  timeout: 6000  # 连接超时时长（毫秒）
  database: 0 # db
  poolMaxTotal: 64
  poolMaxIdle: 32
  poolMaxWait: 3000

# redisson 配置
redisson:
  redisModel: standalone
  password: sunhaijun:kaaYie4rie3juo
  database: 8
  # 单机配置
  #  address: redis://redis-test.xiaowhale.net:6379
  address: redis://redis-test.internal.xiaowhale.net:6379

# 端口
server:
  port: 8092
# 注册中心
eureka:
  client:
    # 表示是否将自己注册在EurekaServer上，默认为true。由于当前应用就是EurekaServer，所以置为false
    register-with-eureka: false
    service-url:
      defaultZone: https://api-test.xiaowhale.com/eureka/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
    com.mpolicy.manage.modules.sys.dao: debug
    io.swagger.models.parameters.AbstractSerializableParameter: error
    com.mpolicy.manage.modules.policy.dao: debug
logfile:
  dir: logs
  additivity: true
# 消息开关
xjxh:
  rabbitmq:
    switch: true
# 定时任务 xxl:
xxl:
  job:
    switch: false # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    addresses: http://xxljob-test.internal.xiaowhale.net:8089/xxl-job-admin # 测试环境
    appname: xxl-job-executor-admin # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8192 # 定时任务调度器器和执⾏器交互端⼝，⾮应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 80000
        readTimeout: 80000
        #loggerLevel: basic
hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 70000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 40000
wx:
  miniapp:
    appid: wx4a1b4ea624d15b11
mp:
  download:
    folder: logs/
  domain:
    h5: https://app-test.xiaowhale.com/
    oss: https://oss-xjxhserver.xiaowhale.com/
    access:
      oss: https://oss-xjxhserver.xiaowhale.com/
fttm:
  biz:
    url: http://fttm-service.tsg.cfpamf.com/
  mail:
    enabled: true
    from: 北京小鲸向海保险代理有限公司<<EMAIL>>
    host: smtp.xiaowhale.com
    password: xj@12345678
    port: 465
    user: <EMAIL>
ims:
  feign:
    customer-server: https://api-test.xiaowhale.com/customer-server/
    #    customer-server: http://localhost:8081
    im-server: https://api-test.xiaowhale.com/im-server/
    product-center: https://api-test.xiaowhale.com/product-center/
    #product-center: http://localhost:8083/
    policy-center-server: https://api-test.xiaowhale.com/policy-center/
    #policy-center-server: http://localhost:8082
    insure-order-center: https://api-test.xiaowhale.com/insure-order-center
    settlement-center-core: https://api-test.xiaowhale.com/settlement-center-core/
    #settlement-center-core: http://localhost:9402
    #public-api: http://localhost:8086
    public-api: https://api-test.xiaowhale.com/public-api/
insurance:
  feign:
    bff-project: http://insurance-bff-project.tsg.cfpamf.com

  mail:
    enabled: true
    from: 北京小鲸向海保险代理有限公司<<EMAIL>>
    host: smtp.xiaowhale.com
    password: xj@12345678
    port: 465
    user: <EMAIL>
##################
policy:
  preservation:
    support-status: POLICY_STATUS:1,POLICY_STATUS:2,POLICY_STATUS:3,POLICY_STATUS:4,POLICY_STATUS:5,POLICY_STATUS:6,POLICY_STATUS:7,POLICY_STATUS:8

admin:
  temp-file-path: logs/

alilog:
  accessId: LTAI5tGWtfnJ2rTYpDrM1Zr8
  accessKey: ******************************
  host: cn-beijing.log.aliyuncs.com

zhnx:
  insurance-operation: http://insurance-operation.tsg.cfpamf.com