spring:
  application:
    name: admin-center
  cloud:
    config:
      discovery:
        enabled: true
        service-id: CONFIG
      profile: ${spring.profiles.active}
    # 修复github webhook 只能刷新config server 无法刷新config client的问题
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 总上传的数据大小
      max-request-size: 100MB
# 端口
server:
  port: 8092
  servlet:
    context-path: /api
# log文件写入路径配置
logfile:
  dir: logs
  additivity: true
# 注册中心
eureka:
  client:
    service-url:
      defaultZone: http://register-uat.internal.xiaowhale.net:9761/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 消息开关
xjxh:
  rabbitmq:
    switch: true
# 定时任务 xxl:
xxl:
  job:
    switch: false # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    addresses: http://xxljob-uat.internal.xiaowhale.net:8089/xxl-job-admin
    appname: xxl-job-executor-admin # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8192 # 定时任务调度器器和执⾏器交互端⼝，⾮应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# feign日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
    com.mpolicy.manage.modules.sys.dao: debug
    io.swagger.models.parameters.AbstractSerializableParameter: error
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 80000
        readTimeout: 80000
        #loggerLevel: basic
hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 70000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 40000
wx:
  miniapp:
    appid: wxf4f3efd0ea7c9cd4
mp:
  download:
    folder: logs/
  domain:
    h5: https://app.xiaowhale.com/
    oss: https://oss-xjxhserver.xiaowhale.com/
    access:
      oss: https://xjxhserver.oss-cn-beijing-internal.aliyuncs.com
# 文件临时存储
admin:
  temp-file-path: logs/

alilog:
  accessId: LTAI5tGWtfnJ2rTYpDrM1Zr8
  accessKey: ******************************
  host: cn-beijing.log.aliyuncs.com
zhnx:
  insurance-operation: http://insurance-operation.osg.cfpamf.com
dingTalkRobotMessage:
  domain: https://oapi.dingtalk.com
  customerGiftNoticeToken: 2814f203ba568955f90d2f5bf63ad44307571420d1449d68e688c37de7745ddb
bc:
  insureHostName: https://app-test.xiaowhale.com