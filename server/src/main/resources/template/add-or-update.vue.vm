<template>
  <el-dialog
    :title="!dataForm.${pk.attrname} ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
#foreach($column in $columns)
#if($column.columnName != $pk.columnName)
    <el-form-item label="${column.comments}" prop="${column.attrname}">
      <el-input v-model="dataForm.${column.attrname}" placeholder="${column.comments}"></el-input>
    </el-form-item>
#end
#end
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        buttonCommit:false,
        visible: false,
        dataForm: {
#foreach($column in $columns)
#if($column.columnName == $pk.columnName)
          ${column.attrname}: '',
#else
          ${column.attrname}: ''#if($velocityCount != $columns.size()),#end

#end
#end
        },
        dataRule: {
#foreach($column in $columns)
#if($column.columnName != $pk.columnName)
          ${column.attrname}: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ]#if($velocityCount != $columns.size()),#end

#end
#end
        }
      }
    },
    methods: {
      init (${pk.attrname}) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.dataForm.${pk.attrname} = ${pk.attrname} || ''
          if (this.dataForm.${pk.attrname}) {
              #[[this.$newHttp.get(`/]]#${moduleName}/${pathName}/info/${id}`)
                  .then(({code,data,msg}) => {
                      if (code === 20000) {
                          Object.assign(this.dataForm, data)
                      } else {
                          #[[this.$message.error(msg)]]#
                      }
                  })
          }
         })
      },
      // 表单提交
      dataFormSubmit () {
          if (this.buttonCommit){
              return
          }
          this.buttonCommit= true
        #[[this.$refs['dataForm'].validate((valid) => {]]#
          if (valid) {
              if (!this.dataForm.${pk.attrname}) {
                #[[this.$newHttp.post(]]#'/${moduleName}/${pathName}/save',this.dataForm)
                    .then(({code,msg}) => {
                    if (code === 20000) {
                        #[[this.$message.success('新增成功')]]#
                        this.visible = false
                        #[[this.$emit('refreshDataList')]]#
                    } else {
                        #[[this.$message.error(msg)]]#
                    }
                }).finally(()=>{
                   this.buttonCommit = false
                })
              }else{
                #[[this.$newHttp.post(]]#'/${moduleName}/${pathName}/update',this.dataForm)
                    .then(({code,msg}) => {
                    if (code === 20000) {
                        #[[this.$message.success('更新成功')]]#
                        this.visible = false
                        #[[this.$emit('refreshDataList')]]#
                    } else {
                        #[[this.$message.error(msg)]]#
                    }
                }).finally(()=>{
                   this.buttonCommit = false
                })
              }
          }else {
            this.$message.error('页面校验不通过')
            this.buttonCommit = false
        }
        })
      }
    }
  }
</script>
