package ${package}.${moduleName}.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import java.io.Serializable;
import java.util.Date;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@TableName("${tableName}")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ${className}Entity implements Serializable {
	private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
	/**
	 * $column.comments
	 */
	#if($column.columnName == $pk.columnName)
	@TableId
	#elseif($column.columnName == "create_user")
	@TableField(fill = FieldFill.INSERT)
	#elseif($column.columnName == "create_time")
	@TableField(fill = FieldFill.INSERT)
	#elseif($column.columnName == "update_user")
	@TableField(fill = FieldFill.INSERT_UPDATE)
	#elseif($column.columnName == "update_time")
	@TableField(fill = FieldFill.INSERT_UPDATE)
	#elseif($column.columnName == "deleted")
	@TableLogic
	#elseif($column.columnName == "revision")
	@Version
	@TableField(fill = FieldFill.INSERT)
	#end
	private $column.attrType $column.attrname;
#end
}
