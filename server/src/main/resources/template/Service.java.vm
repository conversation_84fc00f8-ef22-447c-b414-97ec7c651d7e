package ${package}.${moduleName}.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import ${package}.${moduleName}.entity.${className}Entity;
import ${package}.${moduleName}.vo.${className}ListInput;
import ${package}.${moduleName}.vo.${className}ListOut;
import ${package}.${moduleName}.vo.${className}InfoOut;
import ${package}.${moduleName}.vo.${className}SaveInput;
import ${package}.${moduleName}.vo.${className}UpdateInput;
import java.util.Map;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
public interface ${className}Service extends IService<${className}Entity> {

    /**
     * 获取${comments}列表
     * @param input
     * @return
     */
    PageUtils<${className}ListOut> find${className}List(${className}ListInput input);

    /**
     * 获取${comments}详情
     * @param ${pk.attrname}
     * @return
     */
        ${className}InfoOut find${className}ById(${pk.attrType} ${pk.attrname});

    /**
     * 新增${comments}数据
     * @param input
     * @return
     */
    void save${className}(${className}SaveInput input);

    /**
     * 修改${comments}数据
     * @param input
     * @return
     */
    void update${className}ById(${className}UpdateInput input);
}

