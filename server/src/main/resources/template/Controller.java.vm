package ${package}.${moduleName}.controller;

import java.util.Arrays;
import java.util.Map;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import ${package}.${moduleName}.vo.${className}ListInput;
import ${package}.${moduleName}.vo.${className}ListOut;
import ${package}.${moduleName}.vo.${className}InfoOut;
import ${package}.${moduleName}.vo.${className}SaveInput;
import ${package}.${moduleName}.vo.${className}UpdateInput;
import ${package}.${moduleName}.service.${className}Service;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@RestController
@RequestMapping("${moduleName}/${pathName}")
@Api(tags = "${comments}")
public class ${className}Controller {

    @Autowired
    private ${className}Service ${classname}Service;


    @GetMapping("list")
    @RequiresPermissions("${moduleName}:${pathName}:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<${className}ListOut>> list(${className}ListInput input){
        PageUtils<${className}ListOut> page = ${classname}Service.find${className}List(input);
        return Result.success(page);
    }


    @GetMapping("info/{${pk.attrname}}")
    @RequiresPermissions("${moduleName}:${pathName}:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<${className}InfoOut> info(@PathVariable(value = "${pk.attrname}" ,required = false )
                                            @NotNull(message = "操作的数据id不能为空")
                                            @ApiParam(value = "详情ID") ${pk.attrType} ${pk.attrname}){
            ${className}InfoOut ${classname} = ${classname}Service.find${className}ById(${pk.attrname});
        return Result.success( ${classname});
    }

    @SysLog("保存${comments}数据")
    @PostMapping("save")
    @RequiresPermissions("${moduleName}:${pathName}:save")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid ${className}SaveInput input){
            ${classname}Service.save${className}(input);
        return Result.success();
    }

    @SysLog("修改${comments}数据")
    @PostMapping("update")
    @RequiresPermissions("${moduleName}:${pathName}:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid ${className}UpdateInput input){
            ${classname}Service.update${className}ById(input);
        return Result.success();
    }

    @SysLog("删除${comments}信息")
    @PostMapping("delete")
    @RequiresPermissions("${moduleName}:${pathName}:delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false)
                         @NotEmpty(message = "删除的数据ids不能为空" )
                         @ApiParam(value = "批量删除的ID") ${pk.attrType}[] ${pk.attrname}s){
            ${classname}Service.removeByIds(Arrays.asList(${pk.attrname}s));
        return Result.success();
    }
}
