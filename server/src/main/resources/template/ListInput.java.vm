package ${package}.${moduleName}.vo;
import com.mpolicy.manage.common.BasePage;
#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */

@Data
public class ${className}ListInput extends BasePage implements Serializable {
	private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
	#if($column.columnName != "create_time"
	&& $column.columnName != "create_user"
	&& $column.columnName != "create_user_id"
	&& $column.columnName != "update_time"
	&& $column.columnName != "update_user"
	&& $column.columnName != "update_user_id"
	&& $column.columnName != "deleted"
	&& $column.columnName != "revision" )
		/**
		 * $column.comments
		 */
		private $column.attrType $column.attrname;
	#end
#end

}
