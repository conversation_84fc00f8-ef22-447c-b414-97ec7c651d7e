<template>
  <div class="mod-config">
    <el-form :inline="true" :model="searchForm" @keyup.enter.native="getDataList(true)">
      <el-form-item>
        <el-input v-model="searchForm.key" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
        <el-button v-auth="'${moduleName}:${pathName}:save'" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-auth="'${moduleName}:${pathName}:delete'" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
#foreach($column in $columns)
      <el-table-column
        prop="${column.attrname}"
        header-align="center"
        align="center"
        label="${column.comments}">
      </el-table-column>
#end
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button v-auth="'${moduleName}:${pathName}:update'" type="text" size="small" @click="addOrUpdateHandle(scope.row.${pk.attrname})">修改</el-button>
          <el-button v-auth="'${moduleName}:${pathName}:delete'" type="text" size="small" @click="deleteHandle(scope.row.${pk.attrname})">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="searchForm.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchForm.limit"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './${pathName}-add-or-update'
  export default {
    data () {
      return {
        searchForm: {
          key: '',
          page:1,
          limit:10
        },
        dataList: [],
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList(true)
    },
    methods: {
      // 获取数据列表
      getDataList (isReset) {
        if (isReset) {
          this.searchForm.page = 1
        }
        this.dataListLoading = true
        #[[this.$newHttp.get(]]#'/${moduleName}/${pathName}/list',this.searchForm)
         .then(({ code, data, msg }) => {
          if (code === 20000) {
            this.dataList = data.list
            this.totalPage = data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            #[[this.$message.error(msg)]]#
          }
        }).finally(() => {
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.searchForm.limit = val
        this.searchForm.page = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.searchForm.page = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        #[[this.$nextTick(() => {]]#
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.${pk.attrname}
        })
        #[[this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {]]#
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          #[[this.$newHttp.post(]]#'/${moduleName}/${pathName}/delete',ids)
            .then(({ code, msg }) => {
            if (code === 20000) {
              #[[this.$message.success('删除成功')]]#
                 this.getDataList()
            } else {
              #[[this.$message.error(msg)]]#
            }
          })
        })
      }
    }
  }
</script>
