<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="${package}.${moduleName}.dao.${className}Dao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="${package}.${moduleName}.entity.${className}Entity" id="${classname}Map">
#foreach($column in $columns)
        <result property="${column.attrname}" column="${column.columnName}"/>
#end
    </resultMap>

    <!--获取数据列表-->
    <select id="find${className}List" resultType="${package}.${moduleName}.vo.${className}ListOut">
        SELECT #foreach($column in $columns)
            #if($column.columnName != $column.attrname )
                ${column.columnName} AS ${column.attrname},
            #else
                ${column.columnName} ,
            #end
        #end
        FROM ${tableName}
    </select>

</mapper>