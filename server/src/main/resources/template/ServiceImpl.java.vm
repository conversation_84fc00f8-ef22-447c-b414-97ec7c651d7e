package ${package}.${moduleName}.service.impl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import cn.hutool.core.bean.BeanUtil;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import ${package}.${moduleName}.dao.${className}Dao;
import ${package}.${moduleName}.entity.${className}Entity;
import ${package}.${moduleName}.vo.${className}ListInput;
import ${package}.${moduleName}.vo.${className}ListOut;
import ${package}.${moduleName}.vo.${className}InfoOut;
import ${package}.${moduleName}.vo.${className}SaveInput;
import ${package}.${moduleName}.vo.${className}UpdateInput;
import ${package}.${moduleName}.service.${className}Service;
import org.springframework.transaction.annotation.Transactional;

@Service("${classname}Service")
public class ${className}ServiceImpl extends ServiceImpl<${className}Dao, ${className}Entity> implements ${className}Service {


    /**
     * 获取${comments}列表
     * @param input
     * @return
     */
    @Override
    public PageUtils<${className}ListOut> find${className}List(${className}ListInput input){
        IPage<${className}ListOut> page = baseMapper.find${className}List(new Page<${className}ListOut>(input.getPage(), input.getLimit()), input);
        return new PageUtils(page);
    }

    /**
     * 获取${comments}详情
     * @param ${pk.attrname}
     * @return
     */
    @Override
    public ${className}InfoOut find${className}ById(${pk.attrType} ${pk.attrname}){
            ${className}Entity  ${classname} = baseMapper.selectById(${pk.attrname});
        if (${classname} == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在"));
        }
            ${className}InfoOut out = new ${className}InfoOut();
        BeanUtil.copyProperties(${classname}, out);
        return out;
    }

    /**
     * 新增${comments}数据
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save${className}(${className}SaveInput input){
            ${className}Entity save = new ${className}Entity();
        BeanUtil.copyProperties(input, save);
        baseMapper.insert(save);
    }

    /**
     * 修改${comments}数据
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update${className}ById(${className}UpdateInput input){
            ${className}Entity ${classname} = baseMapper.selectById(input.get${pk.attrName}());
        if (${classname} == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("修改的信息不存在"));
        }
            ${className}Entity update = new ${className}Entity();
        BeanUtil.copyProperties(input, update);
        baseMapper.updateById(update);
    }
}
