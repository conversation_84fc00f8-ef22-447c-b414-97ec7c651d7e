package ${package}.${moduleName}.dao;

import ${package}.${moduleName}.entity.${className}Entity;
import ${package}.${moduleName}.vo.${className}ListInput;
import ${package}.${moduleName}.vo.${className}ListOut;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
public interface ${className}Da<PERSON> extends BaseMapper<${className}Entity> {


    /**
     * 获取${comments}列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<${className}ListOut> find${className}List(@Param("page") Page<${className}ListOut> page, @Param("input") ${className}ListInput input);

}
