package ${package}.${moduleName}.vo;

import com.baomidou.mybatisplus.annotation.*;

#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */


@Data
public class ${className}UpdateInput extends ${className}SaveInput implements Serializable {
	private static final long serialVersionUID = 1L;

	private  ${pk.attrType} ${pk.attrname};

}
