spring:
  application:
    name: admin-center
  cloud:
    config:
      profile: ${spring.profiles.active}
      # 方式一：发现模式
      #discovery:
      #        enabled: true
      #        service-id: CONFIG
      # 方式二：http指定模式 推荐本地使用
      uri:
        - https://api-test.xiaowhale.com/config/
    # 修复github webhook 只能刷新config server 无法刷新config client的问题
    bus:
      #Workaround for defect in https://github.com/spring-cloud/spring-cloud-bus/issues/124
      id: ${vcap.application.name:${spring.application.name:application}}:${vcap.application.instance_index:${spring.profiles.active:${local.server.port:${server.port:0}}}}:${vcap.application.instance_id:${random.value}}
  # 文件上传大小设置
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 总上传的数据大小
      max-request-size: 200MB
# 端口
server:
  port: 8092
# 注册中心
eureka:
  client:
    # 表示是否将自己注册在EurekaServer上，默认为true。由于当前应用就是EurekaServer，所以置为false
    register-with-eureka: false
    service-url:
      defaultZone: https://api-test.xiaowhale.com/eureka/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
# 日志
logging:
  level:
    org.springframework.cloud.netflix.feign: debug
    com.mpolicy.manage.modules.sys.dao: debug
    io.swagger.models.parameters.AbstractSerializableParameter: error
    com.mpolicy.manage.modules.policy.dao: debug
logfile:
  dir: logs
  additivity: true
# 消息开关
xjxh:
  rabbitmq:
    switch: true
# 定时任务 xxl:
xxl:
  job:
    switch: false # 启动xxl-job⾃自动装配
    accessToken: xjxh_job # accessToken ⽬目前固定为xxlJobAccessToken admin:
    addresses: http://xxljob-test.internal.xiaowhale.net:8089/xxl-job-admin # 测试环境
    appname: xxl-job-executor-admin # 执⾏行行器器名称
    ip: '' # 容器器部署暂时不不⽤用
    port: 8192 # 定时任务调度器器和执⾏器交互端⼝，⾮应⽤用
    logpath: logs/jobs # ⽇日志路路径
    logretentiondays: 10 # ⽇日志⽂文件保存天数
# feign配置
feign:
  hystrix:
    # 开启feign的hystrix支持,默认是false
    enabled: true
  client:
    config:
      default:
        connectTimeout: 80000
        readTimeout: 80000
        #loggerLevel: basic
hystrix:
  command:
    longServiceMethod:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 70000
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 40000
wx:
  miniapp:
    appid: wx4a1b4ea624d15b11
mp:
  download:
    folder: logs/
  domain:
    h5: https://app-test.xiaowhale.com/
    oss: https://oss-xjxhserver.xiaowhale.com/
    access:
      oss: https://oss-xjxhserver.xiaowhale.com/
ims:
  feign:
    customer-server: https://api-test.xiaowhale.com/customer-server/
#    customer-server: http://localhost:8081
    im-server: https://api-test.xiaowhale.com/im-server/
    product-center: https://api-test.xiaowhale.com/product-center/
    #product-center: http://localhost:8083/
    #policy-center-server: https://api-test.xiaowhale.com/policy-center/
    #policy-center-server: http://ims-policy-center.tsg.xiaowhale.com/
    policy-center-server: http://localhost:8082
    insure-order-center: https://api-test.xiaowhale.com/insure-order-center
    settlement-center-core: https://api-test.xiaowhale.com/settlement-center-core/
    #settlement-center-core: http://localhost:9402
    #public-api: http://localhost:8086
    public-api: https://api-test.xiaowhale.com/public-api/
fttm:
  biz:
    url: http://fttm-service.tsg.cfpamf.com/
  mail:
    enabled: true
    from: 北京小鲸向海保险代理有限公司<<EMAIL>>
    host: smtp.xiaowhale.com
    password: xj@12345678
    port: 465
    user: <EMAIL>
##################
policy:
  preservation:
    support-status: POLICY_STATUS:1,POLICY_STATUS:2,POLICY_STATUS:3,POLICY_STATUS:4,POLICY_STATUS:5,POLICY_STATUS:6,POLICY_STATUS:7,POLICY_STATUS:8
    management:
      insurance-company-filter: XMXH000000,FXLH000000
admin:
  temp-file-path: logs/

alilog:
  accessId: LTAI5tGWtfnJ2rTYpDrM1Zr8
  accessKey: ******************************
  host: cn-beijing.log.aliyuncs.com
zhnx:
  insurance-operation: http://insurance-operation.tsg.cfpamf.com
insurance:
  feign:
    operation: http://insurance-operation.tsg.cfpamf.com
    bff-project: http://insurance-bff-project.tsg.cfpamf.com
dingTalkRobotMessage:
  domain: https://oapi.dingtalk.com
  customerGiftNoticeToken: 2814f203ba568955f90d2f5bf63ad44307571420d1449d68e688c37de7745ddb
  sellRenewalNoticeToken: 2814f203ba568955f90d2f5bf63ad44307571420d1449d68e688c37de7745ddb

consult:
  message:
    templateId: 6v6i-T9P6cqKmqBlFNE4xiF-Q1tFdfZl5YzbFSvwJfU
    jumpUrl: pages/public/publicWeb?url=%2FinsureHolder%2FgroupConsult&from=whaleMini&id=

dingTalkRobot:
  protocolExpireNotify:
    domain: https://oapi.dingtalk.com
    token: 6c34376525f1e473c21515cc9cf66d50835cda70b0849f15902115e0b4b45a93
    secret: SEC2bf0373f2b65f2a6c5918975f51fc91c97f1b1469277f92dbbd097a3eb868e2a
    at: 15700719141
    redirectUrl: https://ims-admin-test.xiaowhale.com/doc.html#/product-protocolManage
bc:
  insureHostName: https://app-test.xiaowhale.com
