<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mpolicy-admin-server</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>mpolicy-admin-server</name>
    <description>mpolicy-admin-server</description>

    <parent>
        <groupId>com.mpolicy</groupId>
        <artifactId>mpolicy-admin</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <mpolicy.common.web.version>2.0.11</mpolicy.common.web.version>
        <mpolicy.common.service.version>2.0.8</mpolicy.common.service.version>
        <mpolicy.admin.common.vsersion>1.0.0-SNAPSHOT</mpolicy.admin.common.vsersion>
        <mpolicy.authorize.client.vsersion>2.0.1</mpolicy.authorize.client.vsersion>
        <mpolicy.product.client.vsersion>2.0.28</mpolicy.product.client.vsersion>
        <mpolicy.tool.client.vsersion>2.0.12</mpolicy.tool.client.vsersion>
        <mpolicy.customer.client.vsersion>2.0.1</mpolicy.customer.client.vsersion>
        <mpolicy.agent.client.vsersion>2.0.13</mpolicy.agent.client.vsersion>
        <mpolicy.policy.client.vsersion>2.5.49-SNAPSHOT</mpolicy.policy.client.vsersion>
        <mpolicy.public.api.client.vsersion>2.0.87-SNAPSHOT</mpolicy.public.api.client.vsersion>
        <mpolicy.settlement.client.vsersion>1.0.43</mpolicy.settlement.client.vsersion>
        <mpolicy.im.client.vsersion>2.0.1</mpolicy.im.client.vsersion>
        <mpolicy.order.client.version>2.0.32</mpolicy.order.client.version>
        <commons.configuration.version>1.10</commons.configuration.version>
        <velocity.version>1.7</velocity.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-common-web</artifactId>
            <version>${mpolicy.common.web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-common-service</artifactId>
            <version>${mpolicy.common.service.version}</version>
        </dependency>
        <!-- order-feign -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-order-client</artifactId>
            <version>${mpolicy.order.client.version}</version>
        </dependency>
        <!-- 保单feign-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-policy-client</artifactId>
            <version>${mpolicy.policy.client.vsersion}</version>
        </dependency>
        <!-- authorize feign -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-authorize-client</artifactId>
            <version>${mpolicy.authorize.client.vsersion}</version>
        </dependency>
        <!-- im feign-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-im-client</artifactId>
            <version>${mpolicy.im.client.vsersion}</version>
        </dependency>
        <!-- 工具feign-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-tool-client</artifactId>
            <version>${mpolicy.tool.client.vsersion}</version>
        </dependency>
        <!-- 客户feign-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-customer-client</artifactId>
            <version>${mpolicy.customer.client.vsersion}</version>
        </dependency>
        <!-- 代理人feign-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-agent-client</artifactId>
            <version>${mpolicy.agent.client.vsersion}</version>
        </dependency>
        <!-- 结算中心-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-settlement-center-core-client</artifactId>
            <version>${mpolicy.settlement.client.vsersion}</version>
        </dependency>
        <!-- 公共服务-->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-public-client</artifactId>
            <version>${mpolicy.public.api.client.vsersion}</version>
        </dependency>
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-admin-common</artifactId>
            <version>${mpolicy.admin.common.vsersion}</version>
        </dependency>
        <!-- product-client -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-product-client</artifactId>
            <version>${mpolicy.product.client.vsersion}</version>
        </dependency>
        <!-- spring actuator-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-config-client</artifactId>
        </dependency>
        <!--接入bus config的需要引入
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>
        -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <!--包含sleuth和zipkin-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
        </dependency>
        <!--swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-aggregation-spring-boot-starter</artifactId>
        </dependency>
        <!--  swagger导出PDF/HTML所需依赖 -->
        <dependency>
            <groupId>io.github.swagger2markup</groupId>
            <artifactId>swagger2markup</artifactId>
        </dependency>
        <!--  shiro 权限 -->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <!--代码生成器,模板-->
        <dependency>
            <artifactId>velocity</artifactId>
            <groupId>org.apache.velocity</groupId>
            <version>${velocity.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>${commons.configuration.version}</version>
        </dependency>
        <!--  kaptcha 权限 -->
        <dependency>
            <groupId>com.github.axet</groupId>
            <artifactId>kaptcha</artifactId>
            <version>${kaptcha.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>${qcloud.cos.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
            <version>0.6.75</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <!-- deploy 时忽略此model -->
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
