package com.mpolicy.manage.modules.regulators.vo;

import com.mpolicy.manage.modules.regulators.enums.RegulatorsReportTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 监管报备报告处理data
 *
 * <AUTHOR>
 * @date 2022-01-20 15:23
 */
@Data
@ApiModel(value = "监管报备报告上传对象")
public class RegulatorsReportData implements RegulatorsReportBasic {

    /**
     * 机构报备唯一编码
     */
    private String regulatorsNo;

    /**
     * 机构编码报备唯一编号
     */
    private String orgRegulatorsNo;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称 报备机构分支数量
     */
    private String orgName;

    /**
     * 报备年度
     */
    private Integer regulatorsYear;

    /**
     * 报备月度
     */
    private Integer regulatorsMonth;

    /**
     * 报备类型编码-字典
     */
    private RegulatorsReportTypeEnum reportType;

    /**
     * 报备类型名称
     */
    private String reportName;

    /**
     * 报告文件编码
     */
    private String reportFileCode;

    /**
     * 操作者
     */
    private String userName;


    @Override
    public String getReportOrgCode() {
        return this.orgCode;
    }

    @Override
    public String reportRegulatorsNo() {
        return this.regulatorsNo;
    }

    @Override
    public Integer reportRegulatorsYear() {
        return this.regulatorsYear;
    }

    @Override
    public Integer reportRegulatorsMonth() {
        return this.regulatorsMonth;
    }

    @Override
    public String getReportUploadUserName() {
        return this.userName;
    }


}
