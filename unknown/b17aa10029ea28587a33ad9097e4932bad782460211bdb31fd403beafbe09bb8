package com.mpolicy.manage.modules.commission.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */
@TableName("commission_basic_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionBasicInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 佣金编码
     */
    private String commissionCode;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    /**
     * 结算保司编码
     */
    private String settlementCompanyCode;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
    /**
     * 开始时间
     */
    private Date beginTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 佣金配置状态0:未生效 1:已生效 2:已到期
     */
    private Integer commissionStatus;
    /**
     * 费率上传状态0:未上传 1:已上传
     */
    private Integer premStatus;
    /**
     * 文件下载地址
     */
    private String premFilePath;
    /**
     * 文件编码
     */
    private String premFileCode;
    /**
     * 描述信息
     */
    private String remark;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
