package com.mpolicy.manage.modules.commission.dao;

import com.mpolicy.manage.modules.commission.entity.CommissionFloatRewardEntity;
import com.mpolicy.manage.modules.commission.vo.CommissionFloatRewardListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionFloatRewardListOut;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 浮动奖励佣金配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-22 14:10:56
 */
public interface CommissionFloatRewardDao extends BaseMapper<CommissionFloatRewardEntity> {


    /**
     * 获取浮动奖励佣金配置列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<CommissionFloatRewardListOut> findCommissionFloatRewardList(@Param("page") Page<CommissionFloatRewardListOut> page, @Param("input") CommissionFloatRewardListInput input);

}
