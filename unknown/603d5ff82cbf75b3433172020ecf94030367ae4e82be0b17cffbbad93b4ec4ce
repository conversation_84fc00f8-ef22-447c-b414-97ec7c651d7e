package com.mpolicy.manage.modules.commission.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionBasicInfoInfoOut implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 佣金编码
     */
    private String commissionCode;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    /**
     * 结算保司编码
     */
    private String settlementCompanyCode;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
    /**
     * 开始时间
     */
    private Date beginTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 佣金配置状态0:未生效 1:已生效 2:已到期
     */
    private Integer commissionStatus;
    /**
     * 费率上传状态0:未上传 1:已上传
     */
    private Integer premStatus;
    /**
     * 描述信息
     */
    private String remark;

}
