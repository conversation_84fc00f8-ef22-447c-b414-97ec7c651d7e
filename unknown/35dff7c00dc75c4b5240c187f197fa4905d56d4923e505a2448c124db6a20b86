package com.mpolicy.manage.modules.commission.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class CommissionBasicPremTemplate implements Serializable {
    private static final long serialVersionUID = 2011018376141308360L;

    /**
     * 产品编码
     */
    @Alias("险种编码")
    private String productCode;
    /**
     * 费率编码
     */
    private String premCode;
    private String rateCode;
    /**
     * 结算机构编码
     */
    private String settlementCompanyCode;

    @Alias("险种名称")
    private String productName;

    @Alias("主险险种编码")
    private String mainProductCode;

    @Alias("继续率红线")
    private String persistencyRate;

    @Alias("投保计划")
    private String productPlan;

    @Alias("保障期间")
    private String coveragePeriod;

    @Alias("满期年龄")
    private String expireAge;

    @Alias("投保人年龄")
    private String applicantAge;

    @Alias("投保人性别")
    private String applicantGender;

    @Alias("被保人年龄")
    private String insuredAge;

    @Alias("被保人性别")
    private String insuredGender;

    @Alias("新单/续投")
    private String insuranceType;

    @Alias("缴费方式")
    private String paymentType;

    @Alias("缴费期")
    private String  paymentPeriod;

    @Alias("标保系数")
    private String underwritingRate;

    @Alias("费用类型")
    private String costType;

    @Alias("结算方式")
    private String settlementMethodDesc;

    @Alias("首期自动结算")
    private String autoSettlementFlag;

    @Alias("首年度费率")
    private String firstYearBrokage;

    @Alias("续期自动结算")
    private String renewalSettlementFlag;

    @Alias("续期佣金第二年")
    private String renewalBrokage2year;

    @Alias("续期佣金第三年")
    private String renewalBrokage3year;

    @Alias("续期佣金第四年")
    private String renewalBrokage4year;

    @Alias("续期佣金第五年")
    private String renewalBrokage5year;

    @Alias("续期佣金第六年")
    private String renewalBrokage6year;

    @Alias("扩展年份续期佣金")
    private String renewalAutoExpand;

    @Alias("有效期起期")
    private String effectiveStartDate;

    @Alias("有效期止期")
    private String effectiveEndDate;

    @Alias("适用分公司")
    private String orgName;

    @Alias("适用分公司组织ID")
    private String orgCode;

    @Alias("结算标准")
    private String settlementStandard;

    @Alias("备注")
    private String remarks;

    @Alias("自保件")
    private String selfPreservation;

    @Alias("税率")
    private String taxRate;
    /**
     * 渠道编码
     */
    @Alias("销售渠道编码")
    private String channelCode;
    /**
     * 渠道名称
     */
    @Alias("销售渠道")
    private String channelName;
}
