package com.mpolicy.manage.modules.commission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicPolicyPremEntity;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListOut;
import com.mpolicy.manage.modules.settlement.vo.UploadPolicyProductPremInput;

/**
 * 基础佣金费率-一单一议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */
public interface CommissionBasicPolicyPremService extends IService<CommissionBasicPolicyPremEntity> {

    /**
     * 获取基础佣金费率-一单一议列表
     * @param input
     * @return
     */
    PageUtils<CommissionBasicPolicyPremListOut> findCommissionBasicPolicyPremList(CommissionBasicPolicyPremListInput input);

    /**
     * 上传一单一议保单费率
     * @param input
     */
    void uploadPremiumFile(UploadPolicyProductPremInput input);

    /**
     * 删除费率
     * @param id
     */
    void deletePolicyProductPrem(Integer id);
}

