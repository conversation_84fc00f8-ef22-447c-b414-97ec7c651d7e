package com.mpolicy.manage.modules.commission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.commission.entity.CommissionFloatRewardEntity;
import com.mpolicy.manage.modules.commission.vo.*;

/**
 * 浮动奖励佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-22 14:10:56
 */
public interface CommissionFloatRewardService extends IService<CommissionFloatRewardEntity> {

    /**
     * 获取浮动奖励佣金配置列表
     * @param input
     * @return
     */
    PageUtils<CommissionFloatRewardListOut> findCommissionFloatRewardList(CommissionFloatRewardListInput input);

    /**
     * 获取浮动奖励佣金配置详情
     * @param id
     * @return
     */
        CommissionFloatRewardInfoOut findCommissionFloatRewardById(Integer id);

    /**
     * 新增浮动奖励佣金配置数据
     * @param input
     * @return
     */
    void saveCommissionFloatReward(CommissionFloatRewardSaveInput input);

    /**
     * 修改浮动奖励佣金配置数据
     * @param input
     * @return
     */
    void updateCommissionFloatRewardById(CommissionFloatRewardUpdateInput input);

    /**
     * 修改浮动奖励佣金配置状态
     * @param input
     */
    void updateStatus(CommissionFloatRewardStatusInput input);
}

