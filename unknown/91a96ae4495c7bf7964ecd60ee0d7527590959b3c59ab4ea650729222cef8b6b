package com.mpolicy.manage.modules.commission.vo;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */

@Data
public class CommissionBasicInfoListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 佣金编码
     */
    private String commissionCode;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 结算保司编码
     */
    private String settlementCompanyCode;
    /**
     * 佣金配置状态0:未生效 1:已生效 2:已到期
     */
    private Integer commissionStatus;
    /**
     * 描述信息
     */
    private String productName;
}
