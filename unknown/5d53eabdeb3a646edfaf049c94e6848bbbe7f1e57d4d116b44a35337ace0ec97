package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinApplyExtendDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinApplyExtendEntity;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineJoinApplyExtendService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("blAgentOnlineJoinApplyExtendService")
public class BlAgentOnlineJoinApplyExtendServiceImpl extends ServiceImpl<BlAgentOnlineJoinApplyExtendDao, BlAgentOnlineJoinApplyExtendEntity> implements BlAgentOnlineJoinApplyExtendService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<BlAgentOnlineJoinApplyExtendEntity> page = this.page(
                new Query<BlAgentOnlineJoinApplyExtendEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
