package com.mpolicy.manage.modules.regulators.service.report.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.modules.regulators.service.report.data.CompanyAssetsLiabilitiesData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产负债表报告数据解析
 *
 * <AUTHOR>
 * @date 2022-01-21 18:48
 */
@Slf4j
public class CompanyAssetsLiabilitiesListener extends ReportObjectEventListener<Object, CompanyAssetsLiabilitiesData> {

    List<CompanyAssetsLiabilitiesData> datas = new ArrayList<>();

    @Override
    public List<CompanyAssetsLiabilitiesData> getReadData() {
        return datas;
    }

    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.debug("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);
        JSONArray lineData = JSON.parseArray(xlsLineData);
        if(lineData.size() < 4){
            return;
        }
        // 解析赋值对象
        CompanyAssetsLiabilitiesData bean = new CompanyAssetsLiabilitiesData();
        bean.setAssetsName(lineData.getString(0));
        bean.setLineNumber(lineData.getString(1));
        bean.setYearBeginNumber(lineData.getString(2));
        bean.setYearEndNumber(lineData.getString(3));
        datas.add(bean);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
