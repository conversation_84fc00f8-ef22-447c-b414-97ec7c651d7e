package com.mpolicy.manage.modules.commission.dao;

import com.mpolicy.manage.modules.commission.entity.CommissionBasicInfoEntity;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicInfoListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicInfoListOut;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 基础佣金配置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */
public interface CommissionBasicInfoDao extends BaseMapper<CommissionBasicInfoEntity> {


    /**
     * 获取基础佣金配置列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<CommissionBasicInfoListOut> findCommissionBasicInfoList(@Param("page") Page<CommissionBasicInfoListOut> page, @Param("input") CommissionBasicInfoListInput input);

}
