package com.mpolicy.manage.modules.commission.vo;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础佣金费率-一单一议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */

@Data
public class CommissionBasicPolicyPremListInput extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 批单号
     */
    private String batchCode;

}
