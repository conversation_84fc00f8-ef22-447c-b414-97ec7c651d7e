package com.mpolicy.manage.modules.regulators.service.report.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 资产负债表报告数据
 *
 * <AUTHOR>
 * @date 2022-01-21 13:26
 */
@Data
@ApiModel(value = "资产负债表报告数据")
public class CompanyAssetsLiabilitiesData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资产名称",example = "货币资产")
    private String assetsName;

    @ApiModelProperty(value = "资产行次",example = "1")
    private String lineNumber;

    @ApiModelProperty(value = "年初数",example = "100")
    private String yearBeginNumber;

    @ApiModelProperty(value = "年末数",example = "100")
    private String yearEndNumber;
}
