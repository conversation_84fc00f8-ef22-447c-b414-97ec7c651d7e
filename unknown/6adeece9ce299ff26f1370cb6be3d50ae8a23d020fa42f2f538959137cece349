package com.mpolicy.manage.modules.agentApply.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.authorize.client.AuthorizeCenterClient;
import com.mpolicy.authorize.common.sms.SendMsgInput;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.customer.client.CustomerClient;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.enums.SmsCodeEnum;
import com.mpolicy.manage.modules.agent.entity.*;
import com.mpolicy.manage.modules.agent.service.*;
import com.mpolicy.manage.modules.agent.util.AgentConstantUtil;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentOnlineFileInfoVo;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinApplyDao;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineJoinApplyExtendDao;
import com.mpolicy.manage.modules.agentApply.entity.*;
import com.mpolicy.manage.modules.agentApply.enums.AgentApplyStatusEnum;
import com.mpolicy.manage.modules.agentApply.enums.AgentOnlineTypeEnum;
import com.mpolicy.manage.modules.agentApply.enums.AgentSignModelEnum;
import com.mpolicy.manage.modules.agentApply.service.*;
import com.mpolicy.manage.modules.agentApply.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @return 代理人线上入职实现
 * @Date 2022/11/25 18:21
 * <AUTHOR>
 **/
@Service("blAgentOnlineJoinApplyService")
public class BlAgentOnlineJoinApplyServiceImpl extends ServiceImpl<BlAgentOnlineJoinApplyDao, BlAgentOnlineJoinApplyEntity> implements BlAgentOnlineJoinApplyService {

    @Autowired
    private BlAgentOnlineJoinApplyExtendService blAgentOnlineJoinApplyExtendService;
    @Autowired
    private BlAgentOnlineJoinAttachmentService blAgentOnlineJoinAttachmentService;
    @Autowired
    private BlAgentOnlineJoinTrainService blAgentOnlineJoinTrainService;
    @Autowired
    private BlAgentOnlineJoinHistoryService blAgentOnlineJoinHistoryService;
    @Autowired
    private BlAgentOnlineJoinApplyExtendDao blAgentOnlineJoinApplyExtendDao;
    //代理人相关
    @Autowired
    private AgentUserInfoService agentUserInfoService;
    @Autowired
    private AgentExtendService agentExtendService;
    @Autowired
    private AgentAccessoryService agentAccessoryService;
    @Autowired
    private AgentUserAccountService agentUserAccountService;
    @Autowired
    private AuthorizeCenterClient authorizeCenterClient;

    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private FddCustomerContractService fddCustomerContractService;

    @Value("${spring.profiles.active}")
    private String active;
    @Value("${wx.miniapp.appid}")
    private String appid;

    @Autowired
    private AgentSignFileDetailService agentSignFileDetailService;

    @Autowired
    private BlAgentOnlineStudioIpService blAgentOnlineStudioIpService;

    @Autowired
    private AgentStudioIpService agentStudioIpService;

    @Override
    public PageUtils<AgentApplyPageListOut> pageList(AgentApplyPageListVO input) {
        List<String> orgList = PolicyPermissionHelper.getOrgCodeList();
        if(CollectionUtils.isNotEmpty(orgList)){
            input.setOrgCodeList(orgList);
        }
        IPage<AgentApplyPageListOut> pageList = this.baseMapper.pageList(new Page(input.getPage(), input.getLimit()),input);
        //签署合同
        List<String> agentCodeList = pageList.getRecords().stream().map(AgentApplyPageListOut::getAgentCode).collect(Collectors.toList());
        List<FddCustomerContractEntity> contractList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(agentCodeList)){
            contractList = fddCustomerContractService.lambdaQuery().in(FddCustomerContractEntity::getBuzNo, agentCodeList).orderByDesc(FddCustomerContractEntity::getId).list();
        }
        //构建返回
        List<FddCustomerContractEntity> finalContractList = contractList;
        pageList.getRecords().stream().forEach(a->{
            AgentOnlineTypeEnum agentOnlineTypeEnum = AgentOnlineTypeEnum.getNameByCode(a.getAgentType());
            if(Objects.nonNull(agentOnlineTypeEnum)){
                a.setAgentTypeName(agentOnlineTypeEnum.getName());
            }
            a.setStatusName(AgentApplyStatusEnum.getNameByCode(a.getStatus()).getName());
            FddCustomerContractEntity fddCustomerContractEntity = finalContractList.stream().filter(x -> (x.getBuzNo().equals(a.getAgentCode())&&x.getModel().equals(AgentSignModelEnum.MODEL1.getCode()))).findFirst().orElse(null);
            FddCustomerContractEntity marketingContractEntity = finalContractList.stream().filter(x -> (x.getBuzNo().equals(a.getAgentCode())&&x.getModel().equals(AgentSignModelEnum.MODEL3.getCode()))).findFirst().orElse(null);
            if(Objects.nonNull(fddCustomerContractEntity)){
                a.setAgreementUrl(fddCustomerContractEntity.getUrl());
            }
            if(Objects.nonNull(marketingContractEntity)){
                a.setMarketingUrl(marketingContractEntity.getUrl());
            }
            //
            if(a.getStatus().equals(AgentApplyStatusEnum.STATUS7.getCode())){
                List<BlAgentOnlineJoinHistoryEntity> historyEntityList = blAgentOnlineJoinHistoryService.lambdaQuery()
                        .eq(BlAgentOnlineJoinHistoryEntity::getAgentCode,a.getAgentCode())
                        .eq(BlAgentOnlineJoinHistoryEntity::getStatus,-1)
                        .orderByDesc(BlAgentOnlineJoinHistoryEntity::getId).list();
                if(CollectionUtils.isNotEmpty(historyEntityList)){
                    a.setReason(historyEntityList.get(0).getReason());
                }
            }
        });
        return new PageUtils<>(pageList);
    }

    @Override
    public List<AgentApplyExportVo> export(AgentApplyPageListVO input) {
        List<String> orgList = PolicyPermissionHelper.getOrgCodeList();
        if(CollectionUtils.isNotEmpty(orgList)){
            input.setOrgCodeList(orgList);
        }
        Map<String, String> agentPositionMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POSITION);
        List<AgentApplyExportVo> list = this.baseMapper.export(input);
        list.stream().forEach(a->{
            a.setPosition(agentPositionMap.get(a.getPosition()));
            a.setStatusDesc(AgentApplyStatusEnum.getNameByCode(a.getStatus()).getName());
        });
        return list;
    }

    @Override
    public AgentApplyInfoOut queryDetail(String agentCode) {
        AgentApplyInfoOut out = new AgentApplyInfoOut();

        out.setAgentCode(agentCode);
        //基本信息
        AgentApplyBasicInfoOut basicInfoOut = new AgentApplyBasicInfoOut();
        BlAgentOnlineJoinApplyEntity basicInfo = checkApplyExist(agentCode);
        BeanUtils.copyProperties(basicInfo,basicInfoOut);
        if(Objects.nonNull(basicInfo.getIdLongTerm())){
            basicInfoOut.setIdLongTerm(basicInfo.getIdLongTerm().equals(1));
        }
        //区域信息
        AgentApplyBasicInfoOut areaInfo = this.baseMapper.findArea(agentCode);
        if(Objects.nonNull(areaInfo)){
            basicInfoOut.setLiveProvinceCode(areaInfo.getLiveProvinceCode());
            basicInfoOut.setLiveCityCode(areaInfo.getLiveCityCode());
            basicInfoOut.setCityCode(areaInfo.getCityCode());
            basicInfoOut.setServerProvince(areaInfo.getServerProvince());
        }
        //日期的坑
        basicInfoOut.setIdStartDate(DateUtils.format(basicInfo.getIdStartDate()));
        basicInfoOut.setIdEndDate(DateUtils.format(basicInfo.getIdEndDate()));
        basicInfoOut.setUpdateTime(DateUtils.format(basicInfo.getUpdateTime()));
        basicInfoOut.setInterview(DateUtils.format(basicInfo.getInterview()));
        basicInfoOut.setEntryDate(DateUtils.format(basicInfo.getEntryDate()));
        out.setAgentUserInfo(basicInfoOut);
        //附件列表
        List<AgentApplyAttachmentOut> fileListOut = null;
        List<BlAgentOnlineJoinAttachmentEntity> fileList = blAgentOnlineJoinAttachmentService.lambdaQuery().eq(BlAgentOnlineJoinAttachmentEntity::getAgentCode,agentCode).list();
        if(CollectionUtils.isNotEmpty(fileList)){
            fileListOut = fileList.stream().map(a->{
                AgentApplyAttachmentOut bean = new AgentApplyAttachmentOut();
                BeanUtils.copyProperties(a,bean);
                return bean;
            }).collect(Collectors.toList());
            out.setAccessoryList(fileListOut);
        }
        //扩展列表
        AgentApplyExtendInfoOut extend = blAgentOnlineJoinApplyExtendDao.findArea(agentCode);
        if(Objects.nonNull(extend)){
            if(Objects.nonNull(extend.getHonor())){
                extend.setHonorList(StrUtil.split(extend.getHonor(), ','));
            }
            out.setAgentExtend(extend);
        }
        //线上入职信息
        AgentOnlineFileInfoVo agentOnlineFileInfoVo = this.queryFileInfo(agentCode);
        out.setAgentOnlineFileInfo(agentOnlineFileInfoVo);
        //工作室信息
        AgentOnlineStudioIpVo agentOnlineStudioIpVo = this.queryStudioIpVo(agentCode);
        out.setAgentOnlineStudioIpVo(agentOnlineStudioIpVo);
        return out;
    }

    private AgentOnlineStudioIpVo queryStudioIpVo(String agentCode){
        BlAgentOnlineStudioIpEntity one = this.blAgentOnlineStudioIpService.lambdaQuery().eq(BlAgentOnlineStudioIpEntity::getAgentCode, agentCode).one();
        if(Objects.isNull(one)){
            return new AgentOnlineStudioIpVo();
        }
        AgentOnlineStudioIpVo out = new AgentOnlineStudioIpVo();
        BeanUtils.copyProperties(one, out);
        return out;
    }

    private BlAgentOnlineJoinApplyEntity checkApplyExist(String agentCode){
        BlAgentOnlineJoinApplyEntity basicInfo = this.lambdaQuery().eq(BlAgentOnlineJoinApplyEntity::getAgentCode,agentCode).one();
        if(Objects.isNull(basicInfo)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        //补齐上传文件路径
        basicInfo.setBankCardPhone(DomainUtil.addOssDomainIfNotExist(basicInfo.getBankCardPhone()));
        basicInfo.setCertificatesPhone(DomainUtil.addOssDomainIfNotExist(basicInfo.getCertificatesPhone()));
        basicInfo.setDegreePhone(DomainUtil.addOssDomainIfNotExist(basicInfo.getDegreePhone()));
        basicInfo.setIdCardBack(DomainUtil.addOssDomainIfNotExist(basicInfo.getIdCardBack()));
        basicInfo.setIdCardFront(DomainUtil.addOssDomainIfNotExist(basicInfo.getIdCardFront()));
        basicInfo.setInterviewUrl(DomainUtil.addOssDomainIfNotExist(basicInfo.getInterviewUrl()));
        return basicInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AgentApplyUpdateInfoVO update) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(update.getAgentCode());
        if(!AgentApplyStatusEnum.STATUS7.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        //更新基本信息
        BlAgentOnlineJoinApplyEntity updateApplyBean = new BlAgentOnlineJoinApplyEntity();
        BeanUtils.copyProperties(update.getAgentUserInfo(),updateApplyBean);
        updateApplyBean.setId(entity.getId());
        if(Objects.isNull(update.getAgentUserInfo().getAvatar())){
            updateApplyBean.setAvatar(update.getAgentUserInfo().getCertificatesPhone());
        }
        updateApplyBean.setUpdateTime(null);
        updateApplyBean.setEntryDate(DateUtils.stringToDate(update.getAgentUserInfo().getEntryDate(),DateUtils.DATE_PATTERN));
        updateApplyBean.setStatus(AgentApplyStatusEnum.STATUS6.getCode());
        updateApplyBean.setIdLongTerm(update.getAgentUserInfo().isIdLongTerm()?1:0);
        this.baseMapper.updateById(updateApplyBean);
        //更新扩展信息
        BlAgentOnlineJoinApplyExtendEntity extend = blAgentOnlineJoinApplyExtendService.lambdaQuery().eq(BlAgentOnlineJoinApplyExtendEntity::getAgentCode,update.getAgentCode()).one();
        BlAgentOnlineJoinApplyExtendEntity extendUpdate = new BlAgentOnlineJoinApplyExtendEntity();
        BeanUtils.copyProperties(update.getAgentExtend(),extendUpdate);
        extendUpdate.setId(extend.getId());
        if(CollectionUtils.isNotEmpty(update.getAgentExtend().getHonorList())){
            extendUpdate.setHonor(CollUtil.join(update.getAgentExtend().getHonorList(), ","));
        }
        extendUpdate.setLongTerm(update.getAgentExtend().isLongTerm()?1:0);
        extendUpdate.setRevision(extend.getRevision());
        extendUpdate.setUpdateTime(null);
        blAgentOnlineJoinApplyExtendService.updateById(extendUpdate);
        //更新附件信息
        if(CollectionUtils.isNotEmpty(update.getAccessoryList())){
            blAgentOnlineJoinAttachmentService.remove(Wrappers.<BlAgentOnlineJoinAttachmentEntity>lambdaQuery().eq(BlAgentOnlineJoinAttachmentEntity::getAgentCode,update.getAgentCode()));
            List<BlAgentOnlineJoinAttachmentEntity> attachmentList = update.getAccessoryList().stream().map(a->{
                BlAgentOnlineJoinAttachmentEntity bean = new BlAgentOnlineJoinAttachmentEntity();
                BeanUtils.copyProperties(a,bean);
                bean.setAgentCode(update.getAgentCode());
                //todo 暂定为代理人其他类型信息
                bean.setIdentificationType("AGENT_ACCESSORY_TYPE:AGENT_OTHER");
                return bean;
            }).collect(Collectors.toList());
            blAgentOnlineJoinAttachmentService.saveBatch(attachmentList);
        }
        //更新工作室信息
        if(Objects.nonNull(update.getAgentOnlineStudioIpVo())){
            BlAgentOnlineStudioIpEntity one = blAgentOnlineStudioIpService.lambdaQuery().eq(BlAgentOnlineStudioIpEntity::getAgentCode, update.getAgentCode()).one();
            BlAgentOnlineStudioIpEntity studioIp = new BlAgentOnlineStudioIpEntity();
            BeanUtils.copyProperties(update.getAgentOnlineStudioIpVo(), studioIp);
            if(Objects.nonNull(one)){
                studioIp.setId(one.getId());
                blAgentOnlineStudioIpService.updateById(studioIp);
            }else{
                studioIp.setAgentCode(update.getAgentCode());
                blAgentOnlineStudioIpService.save(studioIp);
            }

        }
    }

    @Override
    public void finishInterview(AgentApplyFinishInterviewVO finish) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(finish.getAgentCode());
        if(!AgentApplyStatusEnum.STATUS1.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        BlAgentOnlineJoinApplyEntity updateBean = new BlAgentOnlineJoinApplyEntity();
        updateBean.setId(entity.getId());
        updateBean.setInterviewUrlFileCode(finish.getInterviewUrlFileCode());
        updateBean.setInterviewUrl(finish.getInterviewUrl());
        updateBean.setRevision(entity.getRevision());
        updateBean.setUpdateTime(new Date());
        updateBean.setStatus(AgentApplyStatusEnum.STATUS2.getCode());
        int count = this.baseMapper.updateById(updateBean);
        if(count == 0){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("更新失败"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishTrain(AgentApplyFinishTrainVO train) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(train.getAgentCode());
        if(!AgentApplyStatusEnum.STATUS2.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        BlAgentOnlineJoinApplyEntity updateBean = new BlAgentOnlineJoinApplyEntity();
        updateBean.setId(entity.getId());
        updateBean.setStatus(AgentApplyStatusEnum.STATUS3.getCode());
        updateBean.setRevision(entity.getRevision());
        updateBean.setUpdateTime(new Date());
        this.baseMapper.updateById(updateBean);
        //培训信息
        List<BlAgentOnlineJoinTrainEntity> listTrain = train.getDateList().stream().map(a->{
            BlAgentOnlineJoinTrainEntity bean = new BlAgentOnlineJoinTrainEntity();
            bean.setAgentCode(train.getAgentCode());
            bean.setTrainDate(a);
            bean.setTrainCode(CommonUtils.createCode("ATO"));
            return bean;
        }).collect(Collectors.toList());
        blAgentOnlineJoinTrainService.saveBatch(listTrain);
        //发送短信
        SendMsgInput sendMsgInput = new SendMsgInput();
        sendMsgInput.setMobile(entity.getMobile());
        sendMsgInput.setSmsCode(SmsCodeEnum.AGENT_ONLINE_APPLY_EXAM.getCodeType());
        if (!active.contains("dev")) {
            authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, sendMsgInput);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void replenishInfo(AgentApplyReplenishVO vo) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(vo.getAgentCode());
        if(!AgentApplyStatusEnum.STATUS5.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        //补充信息
        BlAgentOnlineJoinApplyExtendEntity extend = blAgentOnlineJoinApplyExtendService.lambdaQuery().eq(BlAgentOnlineJoinApplyExtendEntity::getAgentCode,vo.getAgentCode()).one();
        if(Objects.isNull(extend)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不正确"));
        }
        BlAgentOnlineJoinApplyExtendEntity updateExtend = new BlAgentOnlineJoinApplyExtendEntity();
        BeanUtils.copyProperties(vo,updateExtend);
        updateExtend.setId(extend.getId());
        updateExtend.setRevision(extend.getRevision());
        blAgentOnlineJoinApplyExtendService.updateById(updateExtend);
        if(vo.getIsStaging() == 1){
            //暂存时,先清空以前的附件信息
            blAgentOnlineJoinAttachmentService.lambdaUpdate().eq(BlAgentOnlineJoinAttachmentEntity::getAgentCode,vo.getAgentCode())
                    .eq(BlAgentOnlineJoinAttachmentEntity::getIdentificationType,"AGENT_ACCESSORY_TYPE:AGENT_OTHER")
                    .set(BlAgentOnlineJoinAttachmentEntity::getDeleted,1).update();
        }
        //附件信息
        if(CollectionUtils.isNotEmpty(vo.getFileList())){
            List<BlAgentOnlineJoinAttachmentEntity> attachmentList = vo.getFileList().stream().map(a->{
                BlAgentOnlineJoinAttachmentEntity bean = new BlAgentOnlineJoinAttachmentEntity();
                BeanUtils.copyProperties(a,bean);
                bean.setAgentCode(vo.getAgentCode());
                //todo 暂定为代理人其他类型信息
                bean.setIdentificationType("AGENT_ACCESSORY_TYPE:AGENT_OTHER");
                return bean;
            }).collect(Collectors.toList());
            blAgentOnlineJoinAttachmentService.saveBatch(attachmentList);
        }
        //更新状态
        BlAgentOnlineJoinApplyEntity updateBean = new BlAgentOnlineJoinApplyEntity();
        updateBean.setId(entity.getId());
        if(vo.getIsStaging() == 0){
            updateBean.setStatus(AgentApplyStatusEnum.STATUS6.getCode());
        }
        updateBean.setRevision(entity.getRevision());
        updateBean.setPosition(vo.getPosition());
        updateBean.setPositionDegree(vo.getPositionDegree());
        updateBean.setBusinessCode(vo.getBusinessCode());
        updateBean.setUpdateTime(new Date());
        this.baseMapper.updateById(updateBean);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pass(String agentCode) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(agentCode);
        if(!AgentApplyStatusEnum.STATUS6.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        BlAgentOnlineJoinApplyEntity updateBean = new BlAgentOnlineJoinApplyEntity();
        updateBean.setId(entity.getId());
        updateBean.setStatus(AgentApplyStatusEnum.STATUS8.getCode());
        updateBean.setRevision(entity.getRevision());
        this.baseMapper.updateById(updateBean);
        //更新审核信息表
        BlAgentOnlineJoinHistoryEntity history = createHistory(agentCode,0,null);
        blAgentOnlineJoinHistoryService.save(history);
        createAgentUserInfo(entity,agentCode);
    }

    /**
     * 生成正式代理人信息并生成账号
     * @param agentCode
     */
    private void createAgentUserInfo(BlAgentOnlineJoinApplyEntity entity,String agentCode){
        handleUserInfo(entity,agentCode);
        handleExtend(agentCode);
//        handleFile(agentCode);
        handleAccount(entity,agentCode);
        handleStudioIp(entity, agentCode);
    }

    private void handleStudioIp(BlAgentOnlineJoinApplyEntity entity,String agentCode){
        BlAgentOnlineStudioIpEntity one = blAgentOnlineStudioIpService.lambdaQuery().eq(BlAgentOnlineStudioIpEntity::getAgentCode, agentCode).one();
        if(Objects.nonNull(one)){
            AgentStudioIpEntity agentStudioIp = new AgentStudioIpEntity();
            BeanUtils.copyProperties(one, agentStudioIp);
            agentStudioIp.setAgentCode(agentCode);
            agentStudioIpService.save(agentStudioIp);
        }
    }

    /**
     * 生成代理人信息
     * @param entity
     * @param agentCode
     */
    private void handleUserInfo(BlAgentOnlineJoinApplyEntity entity,String agentCode){
        AgentUserInfoEntity agentUserInfoEntity = new AgentUserInfoEntity();
        BeanUtils.copyProperties(entity,agentUserInfoEntity);
        agentUserInfoEntity.setQuitStatus(0);
        if(Objects.isNull(entity.getEntryDate())){
            entity.setEntryDate(new Date());
        }
        agentUserInfoEntity.setEntryDate(DateUtils.format(entity.getEntryDate()));
        //人员性质
        if(Objects.isNull(agentUserInfoEntity.getAgentNature())){
            //默认代理人
            agentUserInfoEntity.setAgentNature("AGENT:AGENT_NATURE:0");
        }
        //人员类别
        if(Objects.isNull(agentUserInfoEntity.getAgentCategory())){
            agentUserInfoEntity.setAgentCategory("AGENT:AGENT_CATEGORY:0");
        }
        //国籍
        if(Objects.isNull(agentUserInfoEntity.getCountry())){
            agentUserInfoEntity.setCountry("COUNTRY_LIST:CN");
        }
        if(Objects.isNull(agentUserInfoEntity.getMarital())){
            agentUserInfoEntity.setMarital("MARITAL_STATUS:-1");
        }
        if(Objects.nonNull(entity.getIdLongTerm())){
            agentUserInfoEntity.setIdLongTerm(1 == entity.getIdLongTerm()?true:false);
        }
        agentUserInfoEntity.setServiceAttribute("AGENT_SERVER_TYPE:1");
        agentUserInfoEntity.setCityCode(agentUserInfoEntity.getAreaCode());
        AgentUserInfoEntity recruit = agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode,entity.getRecruitCode()).one();
        if(Objects.nonNull(recruit)){
            agentUserInfoEntity.setAcquisitionArea(recruit.getAcquisitionArea());
            agentUserInfoEntity.setCityCode(recruit.getCityCode());
        }
        //线上入职代理人默认可以被选做专属顾问
        agentUserInfoEntity.setIsOptional(1);
        agentUserInfoEntity.setMarketingSignStatus(1);
        agentUserInfoEntity.setAppletsCode(createQRCode(agentUserInfoEntity.getAgentCode()));
        agentUserInfoService.save(agentUserInfoEntity);
        //创建完代理人信息后，生成签署文件信息
        agentSignFileDetailService.generateFile(agentCode);
    }

    /**
     * 生成微信二维码
     *
     * @param channelApplicationCode 渠道code
     */
    private String createQRCode(String channelApplicationCode) {
        Result<String> wxaCode = customerClient.createWxaCode(appid, AdminPublicConstant.WX_QR_AGENT, channelApplicationCode, null);
        if (wxaCode.isSuccess()) {
            return DomainUtil.removeDomain(wxaCode.getData());
        }
        return null;
    }

    /**
     * 生成代理人扩展信息
     * @param agentCode
     */
    private void handleExtend(String agentCode){
        BlAgentOnlineJoinApplyExtendEntity extendEntity = blAgentOnlineJoinApplyExtendService.lambdaQuery().eq(BlAgentOnlineJoinApplyExtendEntity::getAgentCode,agentCode).one();
        if(Objects.nonNull(extendEntity)){
            AgentExtendEntity extend = new AgentExtendEntity();
            BeanUtils.copyProperties(extendEntity,extend);
            extend.setStartDate(DateUtils.format(extendEntity.getStartDate()));
            extend.setEndDate(DateUtils.format(extendEntity.getEndDate()));
            agentExtendService.save(extend);
        }
    }

    /**
     * 生成代理人附件信息
     * @param agentCode
     */
    private void handleFile(String agentCode){
        List<BlAgentOnlineJoinAttachmentEntity> fileList = blAgentOnlineJoinAttachmentService.lambdaQuery().eq(BlAgentOnlineJoinAttachmentEntity::getAgentCode,agentCode).list();
        if(CollectionUtils.isNotEmpty(fileList)){
            List<AgentAccessoryEntity> list = fileList.stream().map(a->{
                AgentAccessoryEntity bean = new AgentAccessoryEntity();
                BeanUtils.copyProperties(a,bean);
                return bean;
            }).collect(Collectors.toList());
            agentAccessoryService.saveBatch(list);
        }
    }

    /**
     * 生成代理人账号信息
     * @param agentCode
     */
    private void handleAccount(BlAgentOnlineJoinApplyEntity entity,String agentCode){

        //判断账号信息是否存在
        AgentUserAccountEntity accountInfo = agentUserAccountService.lambdaQuery()
                .eq(AgentUserAccountEntity::getAccountType, 0)
                .eq(AgentUserAccountEntity::getAgentCode, agentCode).one();
        if (accountInfo == null) {
            String salt = RandomUtil.randomString(4);
            AgentUserAccountEntity agentUserAccount = new AgentUserAccountEntity();
            agentUserAccount.setAccount(entity.getMobile());
            agentUserAccount.setAccountType(0);
            agentUserAccount.setAgentCode(agentCode);
            agentUserAccount.setPassword(SecureUtil.md5("xj" + entity.getIdCard().substring(entity.getIdCard().length() - 6) + salt));
            agentUserAccount.setSalt(salt);
            agentUserAccountService.save(agentUserAccount);
            // 进行短信发送 增员人
            SendMsgInput recruit = new SendMsgInput();
            AgentUserInfoEntity userInfoEntity = agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode,entity.getRecruitCode()).one();
            recruit.setMobile(userInfoEntity.getMobile());
            recruit.setSmsCode(SmsCodeEnum.NEW_AGENT_APPLY_RECRUIT.getCodeType());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("op", "代理人");
            if(Objects.nonNull(userInfoEntity)&&Objects.nonNull(userInfoEntity.getAgentName())) {
                jsonObject.put("op", userInfoEntity.getAgentName());
            }
            jsonObject.put("name", entity.getAgentName());
            recruit.setData(jsonObject);
            //进行短信发送  被增员人
            SendMsgInput agent = new SendMsgInput();
            agent.setMobile(entity.getMobile());
            agent.setSmsCode(SmsCodeEnum.NEW_AGENT_APPLY.getCodeType());
            JSONObject json = new JSONObject();
            json.put("name", entity.getAgentName());
            agent.setData(jsonObject);
            if (!active.contains("dev")) {
                authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, recruit);
                authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, agent);
            }
        } else if (!accountInfo.getAccount().equals(entity.getMobile())) {
            agentUserAccountService.lambdaUpdate()
                    .set(AgentUserAccountEntity::getAccount, entity.getMobile())
                    .eq(AgentUserAccountEntity::getAccount, accountInfo.getAccount())
                    .update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reject(AgentApplyRejectVO vo) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(vo.getAgentCode());
        if(!AgentApplyStatusEnum.STATUS6.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        BlAgentOnlineJoinApplyEntity updateBean = new BlAgentOnlineJoinApplyEntity();
        updateBean.setId(entity.getId());
        updateBean.setStatus(AgentApplyStatusEnum.STATUS7.getCode());
        updateBean.setRevision(entity.getRevision());
        this.baseMapper.updateById(updateBean);
        //更新审核信息表
        BlAgentOnlineJoinHistoryEntity history = createHistory(vo.getAgentCode(),-1,vo.getReason());
        blAgentOnlineJoinHistoryService.save(history);
    }

    private BlAgentOnlineJoinHistoryEntity createHistory(String agentCode,Integer status,String reason){
        BlAgentOnlineJoinHistoryEntity history = new BlAgentOnlineJoinHistoryEntity();
        history.setAgentCode(agentCode);
        history.setReason(reason);
        history.setStatus(status);
        return history;
    }

    @Override
    public AgentOnlineFileInfoVo queryFileInfo(String agentCode) {
        BlAgentOnlineJoinApplyEntity bean = this.lambdaQuery().eq(BlAgentOnlineJoinApplyEntity::getAgentCode, agentCode).one();
        if(Objects.isNull(bean)){
            return null;
        }
        AgentOnlineFileInfoVo result = new AgentOnlineFileInfoVo();
        BeanUtils.copyProperties(bean,result);
        //合同信息
        List<FddCustomerContractEntity> list = fddCustomerContractService.lambdaQuery().eq(FddCustomerContractEntity::getBuzNo, agentCode).orderByDesc(FddCustomerContractEntity::getId).list();
        if(CollectionUtils.isNotEmpty(list)){
            result.setAgreementUrl(list.get(0).getUrl());
        }
        //其他附件
        List<BlAgentOnlineJoinAttachmentEntity> list1 = blAgentOnlineJoinAttachmentService.lambdaQuery().eq(BlAgentOnlineJoinAttachmentEntity::getAgentCode, agentCode).eq(BlAgentOnlineJoinAttachmentEntity::getDeleted, 0).list();
        if(CollectionUtils.isNotEmpty(list1)){
            List<AgentApplyAttachmentOut> collect = list1.stream().map(a ->{
                AgentApplyAttachmentOut file = new AgentApplyAttachmentOut();
                BeanUtils.copyProperties(a,file);
                return file;
            }).collect(Collectors.toList());
            result.setOtherFileList(collect);
        }
        return result;
    }

    @Override
    public void close(String agentCode) {
        BlAgentOnlineJoinApplyEntity entity = checkApplyExist(agentCode);
        if(!AgentApplyStatusEnum.STATUS6.getCode().equals(entity.getStatus())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人申请状态不正确"));
        }
        this.lambdaUpdate().eq(BlAgentOnlineJoinApplyEntity::getAgentCode,agentCode)
                .set(BlAgentOnlineJoinApplyEntity::getStatus,AgentApplyStatusEnum.STATUS9.getCode())
                .update();
        //关闭后发送短信
        //发给增员人
        SendMsgInput recruit = new SendMsgInput();
        AgentUserInfoEntity userInfoEntity = agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode,entity.getRecruitCode()).one();
        recruit.setMobile(userInfoEntity.getMobile());
        recruit.setSmsCode(SmsCodeEnum.CLOSE_AGENT_APPLY_RECRUIT.getCodeType());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", entity.getAgentName());
        recruit.setData(jsonObject);
        //给代理人人发送短信
        SendMsgInput agent = new SendMsgInput();
        agent.setMobile(entity.getMobile());
        agent.setSmsCode(SmsCodeEnum.CLOSE_AGENT_APPLY.getCodeType());
        if (!active.contains("dev")) {
            authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, recruit);
            authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, agent);
        }
    }
}
