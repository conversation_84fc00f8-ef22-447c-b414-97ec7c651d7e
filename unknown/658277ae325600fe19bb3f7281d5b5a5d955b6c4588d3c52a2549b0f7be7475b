package com.mpolicy.manage.modules.agentApply.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinAttachmentEntity;

import java.util.Map;

/**
 * 代理人线上入职附件信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
public interface BlAgentOnlineJoinAttachmentService extends IService<BlAgentOnlineJoinAttachmentEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

