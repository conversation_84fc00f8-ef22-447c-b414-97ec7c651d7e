package com.mpolicy.manage.modules.commission.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicPolicyPremEntity;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListOut;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 基础佣金费率-一单一议
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */
public interface CommissionBasicPolicyPremDao extends ImsBaseMapper<CommissionBasicPolicyPremEntity> {


    /**
     * 获取基础佣金费率-一单一议列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<CommissionBasicPolicyPremListOut> findCommissionBasicPolicyPremList(@Param("page") Page<CommissionBasicPolicyPremListOut> page, @Param("input") CommissionBasicPolicyPremListInput input);

}
