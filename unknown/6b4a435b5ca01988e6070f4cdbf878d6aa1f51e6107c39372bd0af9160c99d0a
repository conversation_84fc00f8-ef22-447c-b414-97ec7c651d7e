package com.mpolicy.manage.modules.commission.controller;

import cn.hutool.core.date.DateUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.modules.commission.service.CommissionBasicInfoService;
import com.mpolicy.manage.modules.commission.vo.*;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 基础佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 20:26:56
 */
@RestController
@RequestMapping("commission/basic")
@Api(tags = "基础佣金配置")
public class CommissionBasicInfoController {

    @Autowired
    private CommissionBasicInfoService commissionBasicInfoService;


    @GetMapping("list")
    @RequiresPermissions("commission:basic-info:list")
    @ApiOperation(value = "获取分页列表数据", notes = "获取分页列表数据", httpMethod = "GET")
    public Result<PageUtils<CommissionBasicInfoListOut>> list(CommissionBasicInfoListInput input) {
        PageUtils<CommissionBasicInfoListOut> page = commissionBasicInfoService.findCommissionBasicInfoList(input);
        page.getList().forEach(action->{
            action.setPremFilePath(DomainUtil.addOssDomainIfNotExist(action.getPremFilePath()));
        });
        return Result.success(page);
    }


    @GetMapping("info/{id}")
    @RequiresPermissions("commission:basic-info:info")
    @ApiOperation(value = "获取数据信息详情", notes = "获取数据信息详情", httpMethod = "GET")
    public Result<CommissionBasicInfoInfoOut> info(@PathVariable(value = "id", required = false)
                                                   @NotNull(message = "操作的数据id不能为空")
                                                   @ApiParam(value = "详情ID") Integer id) {
        CommissionBasicInfoInfoOut commissionBasicInfo = commissionBasicInfoService.findCommissionBasicInfoById(id);
        return Result.success(commissionBasicInfo);
    }

    @SysLog("保存基础佣金配置数据")
    @PostMapping("save")
    @RequiresPermissions("commission:basic-info:save")
    @ApiOperation(value = "保存数据信息", notes = "保存数据信息", httpMethod = "POST")
    public Result save(@RequestBody(required = false) @Valid CommissionBasicInfoSaveInput input) {
        //如果结束时间不为空的时候判断一下是否晚于开始时间
        if (input.getEndTime() != null) {
            if (DateUtil.compare(input.getBeginTime(),input.getEndTime()) >1){
                return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("开始时间不能大于结束时间"));
            }
        }
        commissionBasicInfoService.saveCommissionBasicInfo(input);
        return Result.success();
    }

    @SysLog("修改基础佣金配置数据")
    @PostMapping("update")
    @RequiresPermissions("commission:basic-info:update")
    @ApiOperation(value = "修改数据信息", notes = "修改数据信息", httpMethod = "POST")
    public Result update(@RequestBody(required = false) @Valid CommissionBasicInfoUpdateInput input) {
        //如果结束时间不为空的时候判断一下是否晚于开始时间
        if (input.getEndTime() != null) {
            if (DateUtil.compare(input.getBeginTime(),input.getEndTime()) >1){
                return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("开始时间不能大于结束时间"));
            }
        }
        commissionBasicInfoService.updateCommissionBasicInfoById(input);
        return Result.success();
    }

    @SysLog("删除基础佣金配置信息")
    @PostMapping("delete")
    @RequiresPermissions("commission:basic-info:delete")
    @ApiOperation(value = "删除数据信息", notes = "删除数据信息", httpMethod = "POST")
    public Result delete(@RequestBody(required = false)
                         @NotEmpty(message = "删除的数据ids不能为空")
                         @ApiParam(value = "批量删除的ID") Integer[] ids) {
        commissionBasicInfoService.deleteByIds(Arrays.asList(ids));
        return Result.success();
    }
}
