package com.mpolicy.manage.modules.agentApply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.dao.BlAgentOnlineQuestionDao;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineAnswerEntity;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineQuestionEntity;
import com.mpolicy.manage.modules.agentApply.enums.AgentApplyExamPartEnum;
import com.mpolicy.manage.modules.agentApply.enums.AgentApplyExamTypeEnum;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineAnswerService;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineQuestionService;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyAnswerOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamPageListOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamPageListVO;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyQuestionOut;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service("blAgentOnlineQuestionService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BlAgentOnlineQuestionServiceImpl extends ServiceImpl<BlAgentOnlineQuestionDao, BlAgentOnlineQuestionEntity> implements BlAgentOnlineQuestionService {

    private final BlAgentOnlineAnswerService blAgentOnlineAnswerService;

    @Override
    public PageUtils<AgentApplyExamPageListOut> pageList(AgentApplyExamPageListVO input) {
        IPage<BlAgentOnlineQuestionEntity> pageList = this.page(new Page<>(input.getPage(),input.getLimit()),
                new QueryWrapper<BlAgentOnlineQuestionEntity>().lambda().eq(StringUtils.isNotBlank(input.getCode()),BlAgentOnlineQuestionEntity::getCode,input.getCode()));
        //获取考试答案
        List<BlAgentOnlineAnswerEntity> answerList = blAgentOnlineAnswerService.lambdaQuery().list();
        if(CollectionUtils.isNotEmpty(pageList.getRecords())){
            List<AgentApplyExamPageListOut> list = pageList.getRecords().stream().map(a->{
                AgentApplyExamPageListOut bean = new AgentApplyExamPageListOut();
                BeanUtils.copyProperties(a,bean);
                bean.setPartName(AgentApplyExamPartEnum.getNameByCode(a.getPart()));
                bean.setTypeName(AgentApplyExamTypeEnum.getNameByCode(a.getType()));
                List<BlAgentOnlineAnswerEntity> answers = answerList.stream().filter(b->a.getCode().equals(b.getQCode())).collect(Collectors.toList());
                List<AgentApplyAnswerOut> answerOuts = answers.stream().map(c->{
                    AgentApplyAnswerOut answerBean = new AgentApplyAnswerOut();
                    BeanUtils.copyProperties(c,answerBean);
                    return answerBean;
                }).collect(Collectors.toList());
                bean.setAnswer(answerOuts);
                return bean;
            }).collect(Collectors.toList());
            return new PageUtils<>(list,(int) pageList.getTotal(),(int) pageList.getPages(), (int) pageList.getCurrent());
        }
        return new PageUtils<>(null);
    }

    @Override
    public AgentApplyQuestionOut info(String code) {
        AgentApplyQuestionOut out = new AgentApplyQuestionOut();
        BlAgentOnlineQuestionEntity question = getBasicQuestion(code);
        BeanUtils.copyProperties(question,out);
        out.setPartName(AgentApplyExamPartEnum.getNameByCode(out.getPart()));
        out.setTypeName(AgentApplyExamTypeEnum.getNameByCode(out.getType()));
        //答案
        List<BlAgentOnlineAnswerEntity> list = blAgentOnlineAnswerService.lambdaQuery()
                .eq(BlAgentOnlineAnswerEntity::getQCode,code)
                .orderByAsc(BlAgentOnlineAnswerEntity::getId).list();
        if(CollectionUtils.isNotEmpty(list)){
            List<AgentApplyAnswerOut> answerList = list.stream().map(a->{
                AgentApplyAnswerOut bean = new AgentApplyAnswerOut();
                BeanUtils.copyProperties(a,bean);
                return bean;
            }).collect(Collectors.toList());
            out.setAnswer(answerList);
        }
        return out;
    }

    private BlAgentOnlineQuestionEntity getBasicQuestion(String code){
        BlAgentOnlineQuestionEntity question = this.lambdaQuery().eq(BlAgentOnlineQuestionEntity::getCode,code).one();
        if(Objects.isNull(question)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未获取到题目信息"));
        }
        return question;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String code) {
        BlAgentOnlineQuestionEntity question = getBasicQuestion(code);
        this.removeById(question.getId());
        blAgentOnlineAnswerService.remove(new LambdaQueryWrapper<BlAgentOnlineAnswerEntity>().eq(BlAgentOnlineAnswerEntity::getQCode,question.getCode()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AgentApplyQuestionOut input) {
        BlAgentOnlineQuestionEntity question = new BlAgentOnlineQuestionEntity();
        BeanUtils.copyProperties(input,question);
        question.setCode(CommonUtils.createCode("BO"));
        this.save(question);
        //答案部分
        List<BlAgentOnlineAnswerEntity> list = buildAnswer(input.getAnswer(),question.getCode());
        blAgentOnlineAnswerService.saveBatch(list);
    }

    private List<BlAgentOnlineAnswerEntity> buildAnswer(List<AgentApplyAnswerOut> list,String qCode){
        List<BlAgentOnlineAnswerEntity> outList = list.stream().map(a->{
            BlAgentOnlineAnswerEntity answer = new BlAgentOnlineAnswerEntity();
            BeanUtils.copyProperties(a,answer);
            answer.setQCode(qCode);
            return answer;
        }).collect(Collectors.toList());
        return outList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AgentApplyQuestionOut input) {
        BlAgentOnlineQuestionEntity question = getBasicQuestion(input.getCode());
        BlAgentOnlineQuestionEntity update = new BlAgentOnlineQuestionEntity();
        BeanUtils.copyProperties(input,update);
        update.setId(question.getId());
        this.updateById(update);
        //更新答案信息
        blAgentOnlineAnswerService.remove(new LambdaQueryWrapper<BlAgentOnlineAnswerEntity>().eq(BlAgentOnlineAnswerEntity::getQCode,question.getCode()));
        List<BlAgentOnlineAnswerEntity> list = buildAnswer(input.getAnswer(),question.getCode());
        blAgentOnlineAnswerService.saveBatch(list);
    }
}
