package com.mpolicy.manage.modules.commission.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UploadCommissionBasicPremInput implements Serializable {
    private static final long serialVersionUID = -7106895821646156544L;


    @ApiModelProperty(name = "fileSystem", required = true, value = "文件所属模块", example = "customer")
    @NotBlank(message = "文件所属模块不能为空")
    private String fileSystem;

    @ApiModelProperty(name = "modelCode", value = "文件用途", example = "portrait")
    @NotBlank(message = "文件用途不能为空")
    private String modelCode;

    @ApiModelProperty(name = "protocolCode", required = true, value = "协议编码")
    @NotBlank(message = "协议编码不能为空")
    private String commissionCode;

    @ApiModelProperty(name = "protocolCode", required = true, value = "上传的文件")
    @NotNull(message = "上传的文件不能为空")
    private MultipartFile file;
}
