package com.mpolicy.manage.modules.commission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.common.utils.ListUtil;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.enums.PremChangeTypeEnum;
import com.mpolicy.manage.enums.SettlementMethodEnum;
import com.mpolicy.manage.modules.commission.dao.CommissionBasicPremDao;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicInfoEntity;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.manage.modules.commission.service.CommissionBasicInfoService;
import com.mpolicy.manage.modules.commission.service.CommissionBasicPremService;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicInfoListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPremTemplate;
import com.mpolicy.manage.modules.commission.vo.ExportCommissionBasicPrem;
import com.mpolicy.manage.modules.commission.vo.UploadCommissionBasicPremInput;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.protocol.enums.SettlementProtocolEventEnum;
import com.mpolicy.manage.modules.protocol.helper.SettlementProtocolHelper;
import com.mpolicy.manage.modules.settlement.entity.SettlementSubjectEntity;
import com.mpolicy.manage.modules.settlement.service.SettlementPremChangeLogService;
import com.mpolicy.manage.modules.settlement.service.SettlementSubjectService;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.manage.utils.ProductPremUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("commissionBasicPremService")
public class CommissionBasicPremServiceImpl extends ServiceImpl<CommissionBasicPremDao, CommissionBasicPremEntity> implements CommissionBasicPremService {

    @Autowired
    private SettlementSubjectService settlementSubjectService;
    @Autowired
    private CommissionBasicInfoService commissionBasicInfoService;
    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;
    @Autowired
    private CommissionBasicPremDao commissionBasicPremDao;

    @Autowired
    private StorageService storageService;

    @Autowired
    private SysDocumentService sysDocumentService;
    @Autowired
    private SettlementPremChangeLogService settlementPremChangeLogService;

    /**
     * 删除基础佣金费率表
     *
     * @param commissionCode 费率编码
     */
    @Override
    public void deletePremFile(String commissionCode) {
        SysUserEntity userEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        Optional.ofNullable(commissionBasicInfoService.lambdaQuery().eq(CommissionBasicInfoEntity::getCommissionCode,
                commissionCode).one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("基础佣金配置不存在")));
        //更新状态
        commissionBasicInfoService.lambdaUpdate()
                .eq(CommissionBasicInfoEntity::getCommissionCode, commissionCode)
                .set(CommissionBasicInfoEntity::getPremStatus, StatusEnum.INVALID.getCode())
                .set(CommissionBasicInfoEntity::getPremFileCode, null)
                .set(CommissionBasicInfoEntity::getPremFilePath, null)
                .update();
        List<String> premCodeList = this.lambdaQuery().eq(CommissionBasicPremEntity::getCommissionCode, commissionCode)
                .list().stream().map(CommissionBasicPremEntity::getPremCode)
                .distinct()
                .collect(Collectors.toList());

        // 删除之前上传的的费率信息
        this.lambdaUpdate().eq(CommissionBasicPremEntity::getCommissionCode, commissionCode).remove();
        //MQ通知
        String pushEventCode = CommonUtils.createCodeLastNumber("PE");
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", userEntity.getUsername());
        //事件编码
        msgData.put("pushEventCode", pushEventCode);
        //移除的费率key
        msgData.put("removePremCodeList", JSONObject.toJSONString(premCodeList));
        //插入的费率key
        msgData.put("insertPremCodeList", JSONObject.toJSONString(Collections.emptyList()));
        msgData.put("updatePremCodeList", JSONObject.toJSONString(Collections.emptyList()));
        //保存记录
        settlementPremChangeLogService.saveSettlementPremChangeLog(pushEventCode, PremChangeTypeEnum.COMMISSION_PRODUCT.getCode(), commissionCode, msgData.toJSONString());
        // 推送事件
        SettlementProtocolHelper.pushSettlementProtocolEvent(commissionCode, SettlementProtocolEventEnum.COMMISSION_PRODUCT_PREM_CHANGE, msgData);
    }

    /**
     * 更新上传基础佣金费率表
     *
     * @param input 请求参数
     */
    @Override
    public void uploadPremFile(UploadCommissionBasicPremInput input) {
        SysUserEntity userEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (userEntity == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("非法操作哦~"));
        }
        // 1.获取协议信息是否存在
        CommissionBasicInfoEntity commissionBasicInfo = Optional.ofNullable(commissionBasicInfoService.lambdaQuery()
                        .eq(CommissionBasicInfoEntity::getCommissionCode, input.getCommissionCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("基础佣金配置不存在")));
        // 3.读取文件 校验当前文件是否正常
        List<CommissionBasicPremTemplate> readFile = readCommissionBasicPremFile(input.getFile(), commissionBasicInfo);
        if (readFile.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("上传的费率文件不能为空"));
        }
        log.info("读取文件中的费率信息:{}", JSONUtil.toJsonStr(readFile));
        // 4.校验并修正文件内容,返回产品和协议的关联关系
        List<CommissionBasicPremEntity> premList = this.handlePremFile(readFile, commissionBasicInfo);
        //
        Map<String, CommissionBasicPremEntity> commissionBasicPremMap = lambdaQuery().eq(CommissionBasicPremEntity::getCommissionCode, input.getCommissionCode())
                .list().stream().collect(Collectors.toMap(CommissionBasicPremEntity::getPremCode, v -> v,
                        (v1, v2) -> {
                            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("数据库存在PremCode相同数据"));
                        }));
        Map<String, CommissionBasicPremEntity> importCommissionBasicPremMap = premList.stream().collect(Collectors.toMap(CommissionBasicPremEntity::getPremCode, v -> v,
                (v1, v2) -> {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("上传文件存在相同PremCode数据"));
                }));
        ListUtil<String> listUtil = new ListUtil<>();
        Map<String, List<String>> toMap = listUtil.splitListToMap(
                new ArrayList<>(commissionBasicPremMap.keySet()),
                new ArrayList<>(importCommissionBasicPremMap.keySet()));
        List<String> removePremCodeList = toMap.get(ListUtil.REMOVE_LIST_KEY);
        List<String> insertPremCodeList = toMap.get(ListUtil.SAVE_LIST_KEY);
        List<String> updatePremCodeList = toMap.get(ListUtil.UPDATE_LIST_KEY);
        // 判断费率是否发生了变更,如果没有变更的话那么就不需要处理 不是查询因子就不用管
        updatePremCodeList = updatePremCodeList.stream().filter(premCode -> {
            CommissionBasicPremEntity commissionBasicPrem = commissionBasicPremMap.get(premCode);
            CommissionBasicPremEntity importCommissionBasicPrem = importCommissionBasicPremMap.get(premCode);
            return !commissionBasicPrem.getRateCode().equals(importCommissionBasicPrem.getRateCode());
        }).collect(Collectors.toList());

        // 插入数据
        if (!insertPremCodeList.isEmpty()) {
            //校验一下险种所属公司是否正确
            List<String> productCodeList = premList.stream().map(CommissionBasicPremEntity::getProductCode).distinct().collect(Collectors.toList());
            //险种可以正常匹配到保司
            Map<String, String> productMap = insuranceProductInfoService.lambdaQuery()
                    .in(InsuranceProductInfoEntity::getProductCode, productCodeList)
                    .eq(InsuranceProductInfoEntity::getCompanyCode, commissionBasicInfo.getCompanyCode())
                    .list().stream()
                    .collect(Collectors.toMap(InsuranceProductInfoEntity::getProductCode, InsuranceProductInfoEntity::getProductName));
            productCodeList.removeAll(new ArrayList<>(productMap.keySet()));
            if (!productCodeList.isEmpty()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("险种编码{}不属于保司[{}]", JSONUtil.toJsonStr(productCodeList), commissionBasicInfo.getCompanyName())));
            }
            List<CommissionBasicPremEntity> insertBatch = premList.stream().filter(f -> insertPremCodeList.contains(f.getPremCode()))
                    .peek(m -> m.setProductName(productMap.get(m.getProductCode())))
                    .collect(Collectors.toList());
            commissionBasicPremDao.insertBatchSomeColumn(insertBatch);
        }
        // 删除数据
        if (!removePremCodeList.isEmpty()) {
            this.lambdaUpdate().in(CommissionBasicPremEntity::getPremCode, removePremCodeList).remove();
        }
        // 更新数据
        if (!updatePremCodeList.isEmpty()) {
            this.updateBatchById(updatePremCodeList.stream().map(premCode -> {
                CommissionBasicPremEntity importCommissionBasicPrem = importCommissionBasicPremMap.get(premCode);
                CommissionBasicPremEntity commissionBasicPrem = commissionBasicPremMap.get(premCode);
                importCommissionBasicPrem.setId(commissionBasicPrem.getId());
                importCommissionBasicPrem.setRevision(commissionBasicPrem.getRevision());
                return importCommissionBasicPrem;
            }).collect(Collectors.toList()));
        }
        // 7.更新费率信息
        SysDocumentEntity sysDocument = saveFileDocument(input);
        commissionBasicInfoService.lambdaUpdate()
                .eq(CommissionBasicInfoEntity::getCommissionCode, input.getCommissionCode())
                .set(CommissionBasicInfoEntity::getPremStatus, StatusEnum.NORMAL.getCode())
                .set(CommissionBasicInfoEntity::getPremFileCode, sysDocument.getFileCode())
                .set(CommissionBasicInfoEntity::getPremFilePath, sysDocument.getFilePath())
                .update();
        String pushEventCode = CommonUtils.createCodeLastNumber("PE");
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", userEntity.getUsername());
        //事件编码
        msgData.put("pushEventCode", pushEventCode);
        //移除的费率key
        msgData.put("removePremCodeList", JSONObject.toJSONString(removePremCodeList));
        //更新的费率信息
        msgData.put("updatePremCodeList", JSONObject.toJSONString(updatePremCodeList));
        //插入的费率key
        msgData.put("insertPremCodeList", JSONObject.toJSONString(insertPremCodeList));

        settlementPremChangeLogService.saveSettlementPremChangeLog(pushEventCode, PremChangeTypeEnum.COMMISSION_PRODUCT.getCode(), input.getCommissionCode(), msgData.toJSONString());

        SettlementProtocolHelper.pushSettlementProtocolEvent(input.getCommissionCode(), SettlementProtocolEventEnum.COMMISSION_PRODUCT_PREM_CHANGE, msgData);
    }

    private SysDocumentEntity saveFileDocument(UploadCommissionBasicPremInput input) {
        try {
            // 7.将文件上传到OSS,更新协议上传费率表状态和费率表文件
            MultipartFile file = input.getFile();
            String fileSystem = input.getFileSystem();
            String modelCode = input.getModelCode();
            String commissionCode = input.getCommissionCode();
            FileModelEnum fileModelEnum = FileModelEnum.decode(input.getFileSystem());
            String fileName = fileModelEnum.ossObjectFileName(input.getModelCode(), file.getOriginalFilename());
            OssBaseOut ossResult = storageService.uploadBytesFile(fileName, input.getFile().getBytes());
            SysDocumentEntity bean = new SysDocumentEntity();
            // 文件大小
            bean.setFileSize(file.getSize());
            bean.setFileName(file.getOriginalFilename());
            bean.setFileType(file.getContentType());
            bean.setFileExt(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1));
            // 文件编码
            bean.setFileCode(CommonUtils.createCode("oss"));
            // oss访问
            bean.setFilePath(ossResult.getFilePath());
            bean.setDomainPath(ossResult.getAccessDomainPath());
            bean.setFileSystem(fileSystem);
            bean.setFileModule(modelCode);
            bean.setRelationCode(commissionCode);
            sysDocumentService.save(bean);
            return bean;
        } catch (Exception e) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("上传文件到OSS失败了"));
        }
    }

    /**
     * 修正数据
     *
     * @param readFile
     * @param commissionBasicInfo
     * @return
     */
    private List<CommissionBasicPremEntity> handlePremFile(List<CommissionBasicPremTemplate> readFile, CommissionBasicInfoEntity commissionBasicInfo) {
        //todo 不校验时间存在交集的情况
        return readFile.stream().map(m -> {
            CommissionBasicPremEntity commissionBasicPrem = new CommissionBasicPremEntity();
            commissionBasicPrem.setPremCode(m.getPremCode());
            commissionBasicPrem.setCommissionCode(commissionBasicInfo.getCommissionCode());
            commissionBasicPrem.setCompanyName(commissionBasicInfo.getCompanyName());
            commissionBasicPrem.setCompanyCode(commissionBasicInfo.getCompanyCode());
            commissionBasicPrem.setSettlementCompanyCode(commissionBasicInfo.getSettlementCompanyCode());
            commissionBasicPrem.setSettlementCompanyName(commissionBasicInfo.getSettlementCompanyName());
            commissionBasicPrem.setProductName(m.getProductName());
            commissionBasicPrem.setProductCode(m.getProductCode());
            commissionBasicPrem.setMainProductCode(m.getMainProductCode());
            commissionBasicPrem.setOrgName(m.getOrgName());
            commissionBasicPrem.setOrgCode(m.getOrgCode());
            commissionBasicPrem.setApplicantAge(m.getApplicantAge());
            commissionBasicPrem.setApplicantGender(m.getApplicantGender());
            commissionBasicPrem.setAutoSettlementFlag(StrUtil.isBlank(m.getAutoSettlementFlag()) ? null : "是".equals(m.getAutoSettlementFlag()) ? 1 : 0);
            commissionBasicPrem.setRenewalSettlementFlag(StrUtil.isBlank(m.getRenewalSettlementFlag()) ? null : "是".equals(m.getRenewalSettlementFlag()) ? 1 : 0);
            commissionBasicPrem.setCostType(m.getCostType());
            commissionBasicPrem.setCoveragePeriod(m.getCoveragePeriod());
            commissionBasicPrem.setSettlementMethod(SettlementMethodEnum.matchSearchDesc(m.getSettlementMethodDesc()).getCode());
            commissionBasicPrem.setEffectiveEndDate(m.getEffectiveEndDate());
            commissionBasicPrem.setEffectiveStartDate(m.getEffectiveStartDate());
            commissionBasicPrem.setExpireAge(m.getExpireAge());
            commissionBasicPrem.setFirstYearBrokage(m.getFirstYearBrokage());
            commissionBasicPrem.setInsuranceType(m.getInsuranceType());
            commissionBasicPrem.setInsuredAge(m.getInsuredAge());
            commissionBasicPrem.setInsuredGender(m.getInsuredGender());
            commissionBasicPrem.setPaymentPeriod(m.getPaymentPeriod());
            commissionBasicPrem.setPaymentType(m.getPaymentType());
            commissionBasicPrem.setProductPlan(m.getProductPlan());
            commissionBasicPrem.setRemarks(m.getRemarks());
            commissionBasicPrem.setRenewalAutoExpand(m.getRenewalAutoExpand());
            commissionBasicPrem.setRenewalBrokage2year(m.getRenewalBrokage2year());
            commissionBasicPrem.setRenewalBrokage3year(m.getRenewalBrokage3year());
            commissionBasicPrem.setRenewalBrokage4year(m.getRenewalBrokage4year());
            commissionBasicPrem.setRenewalBrokage5year(m.getRenewalBrokage5year());
            commissionBasicPrem.setRenewalBrokage6year(m.getRenewalBrokage6year());
            commissionBasicPrem.setSettlementStandard(m.getSettlementStandard());
            commissionBasicPrem.setUnderwritingRate(m.getUnderwritingRate());
            commissionBasicPrem.setPersistencyRate(m.getPersistencyRate());
            commissionBasicPrem.setSelfPreservation(m.getSelfPreservation());
            commissionBasicPrem.setChannelCode(m.getChannelCode());
            commissionBasicPrem.setChannelName(m.getChannelName());
            commissionBasicPrem.setRateCode(m.getRateCode());
            return commissionBasicPrem;
        }).collect(Collectors.toList());
    }

    /**
     * 读取费率文件
     *
     * @param file
     * @param commissionBasicInfo
     * @return
     */
    private List<CommissionBasicPremTemplate> readCommissionBasicPremFile(MultipartFile file,
                                                                          CommissionBasicInfoEntity commissionBasicInfo) {
        List<CommissionBasicPremTemplate> readAll;
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            readAll = reader.readAll(CommissionBasicPremTemplate.class);
        } catch (Exception e) {
            log.warn("读取费率表异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件出现异常"));
        }
        List<String> subjectNameList = settlementSubjectService.list().stream().map(SettlementSubjectEntity::getSubjectName).distinct().collect(Collectors.toList());
        List<CommissionBasicPremTemplate> resultList = new ArrayList<>();
        Map<String, Integer> setKey = new HashMap<>(1);
        for (int i = 0; i < readAll.size(); i++) {
            CommissionBasicPremTemplate productPrem = readAll.get(i);
            productPrem.setSettlementCompanyCode(commissionBasicInfo.getSettlementCompanyCode());
            // 校验必填项没有填写
            if (!ProductPremUtil.checkCommissionPremRequired(productPrem)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件第" + (i + 2) + "行数据必填项未填写,请下载模版进行核对后上传"));
            }
            SettlementMethodEnum settlementMethodEnum =
                SettlementMethodEnum.matchSearchDesc(productPrem.getSettlementMethodDesc());
            // 校验结算方式是否合法
            if (settlementMethodEnum ==null){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" +  (i + 2) + "结算方式不合法"));
            }
            if (!subjectNameList.contains(productPrem.getCostType())){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" +  (i + 2) + "科目没有配置,请先配置科目"));
            }
            DateTime effectiveStartDate = DateUtil.parseDate(productPrem.getEffectiveStartDate());
            DateTime effectiveEndDate = DateUtil.parseDate(productPrem.getEffectiveEndDate());
            productPrem.setEffectiveStartDate(effectiveStartDate.toString());
            productPrem.setEffectiveEndDate(effectiveEndDate.toString());
            if (commissionBasicInfo.getEndTime() == null) {
                //判断有效期起止时间是否在协议的开始时间和结束时间内
                if (DateUtil.compare(effectiveStartDate, commissionBasicInfo.getBeginTime()) == -1) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件第" + (i + 2) + "行数据有效期起期不在协议期内"));
                }
                if (DateUtil.compare(effectiveEndDate, commissionBasicInfo.getBeginTime()) == -1) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件第" + (i + 2) + "行数据有效期止期不在协议期内"));
                }
            } else {
                //判断有效期起止时间是否在协议的开始时间和结束时间内
                if (!DateUtil.isIn(effectiveStartDate, commissionBasicInfo.getBeginTime(), commissionBasicInfo.getEndTime())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件第" + (i + 2) + "行数据有效期起期不在协议期内"));
                }
                if (!DateUtil.isIn(effectiveEndDate, commissionBasicInfo.getBeginTime(), commissionBasicInfo.getEndTime())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件第" + (i + 2) + "行数据有效期止期不在协议期内"));
                }
            }
            String[] productCodeList = productPrem.getProductCode().split(",");
            for (int i1 = 0; i1 < productCodeList.length; i1++) {
                CommissionBasicPremTemplate template = BeanUtil.copyProperties(productPrem, CommissionBasicPremTemplate.class);
                template.setProductCode(productCodeList[i1]);
                //判断是否存在了出费率以外其他属性相同的数据
                String key = ProductPremUtil.getCommissionPremCode(template, true);
                String rateCode = ProductPremUtil.getCommissionRateCode(template);
                if (setKey.containsKey(key)) {
                    Integer row = setKey.get(key);
                    log.warn("第{}行和第{}行生成的KEY={}", i + 2, row, key);
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("读取文件第{}行和第{}行除费率外其他属性数据相同,请检查", i + 2, setKey.get(key))));
                }
                template.setPremCode(key);
                template.setRateCode(rateCode);
                setKey.put(key, i + 2);
                resultList.add(template);
            }

        }
        return resultList;
    }

    /**
     * 导出基础佣金费率表
     *
     * @param input
     * @return
     */
    @Override
    public List<ExportCommissionBasicPrem> exportPremiumFile(CommissionBasicInfoListInput input) {
        return null;
    }
}
