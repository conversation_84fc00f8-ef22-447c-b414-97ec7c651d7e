package com.mpolicy.manage.modules.commission.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础佣金费率-一单一议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionBasicPolicyPremListOut implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 批单号
     */
    private String batchCode;
    private Integer year;
    private Integer period;
    private String productCode;
    private String productName;
    /**
     * 保费
     */
    private BigDecimal premium;
    /**
     * 基础佣金费率
     */
    private BigDecimal commissionRate;
    /**
     * 结算机构
     */
    private String settlementCompanyName;
    /**
     * 操作人
     */
    private String updateUser;
    /**
     * 操作时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
