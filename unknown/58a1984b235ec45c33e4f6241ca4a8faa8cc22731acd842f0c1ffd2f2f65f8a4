package com.mpolicy.manage.modules.commission.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 浮动奖励佣金配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-22 14:10:56
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionFloatRewardSaveInput implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 方案名称
     */
    private String programmeName;
    /**
     * 方案类型0:加佣奖励
     */
    private Integer programmeType;
    /**
     * 方案状态:0:未发布 1:未开始 2:活动中 3:活动结束 4:活动暂停 5:活动作废
     */
    private Integer programmeStatus;
    /**
     * 开始时间
     */
    private Date beginTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 方案介绍
     */
    private String programmeDesc;
    /**
     * 规则方式0:脚本配置
     */
    private Integer ruleMode;
    /**
     * 规则方式对外编码
     */
    private String ruleModeCode;
    /**
     * 发布时间
     */
    private Date releaseTime;
    /**
     * 描述信息
     */
    private String remark;

}
