package com.mpolicy.manage.modules.commission.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.enums.PremChangeTypeEnum;
import com.mpolicy.manage.modules.commission.dao.CommissionBasicPolicyPremDao;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicPolicyPremEntity;
import com.mpolicy.manage.modules.commission.service.CommissionBasicPolicyPremService;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremExcel;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListInput;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicPolicyPremListOut;
import com.mpolicy.manage.modules.protocol.enums.SettlementProtocolEventEnum;
import com.mpolicy.manage.modules.protocol.helper.SettlementProtocolHelper;
import com.mpolicy.manage.modules.settlement.service.SettlementPremChangeLogService;
import com.mpolicy.manage.modules.settlement.vo.UploadPolicyProductPremInput;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.utils.ProductPremUtil;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("commissionBasicPolicyPremService")
public class CommissionBasicPolicyPremServiceImpl
    extends ServiceImpl<CommissionBasicPolicyPremDao, CommissionBasicPolicyPremEntity>
    implements CommissionBasicPolicyPremService {

    @Autowired
    private CommissionBasicPolicyPremDao commissionBasicPolicyPremDao;
    @Autowired
    private SettlementPremChangeLogService settlementPremChangeLogService;

    /**
     * 获取基础佣金费率-一单一议列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<CommissionBasicPolicyPremListOut> findCommissionBasicPolicyPremList(
        CommissionBasicPolicyPremListInput input) {
        IPage<CommissionBasicPolicyPremListOut> page = baseMapper.findCommissionBasicPolicyPremList(
            new Page<CommissionBasicPolicyPremListOut>(input.getPage(), input.getLimit()), input);
        return new PageUtils(page);
    }

    /**
     * 上传一单一议保单费率
     *
     * @param input
     */
    @Override
    public void uploadPremiumFile(UploadPolicyProductPremInput input) {
        SysUserEntity userEntity = (SysUserEntity)SecurityUtils.getSubject().getPrincipal();
        if (userEntity == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("非法操作哦~"));
        }
        List<CommissionBasicPolicyPremExcel> readAll;
        try {
            ExcelReader reader = ExcelUtil.getReader(input.getFile().getInputStream());
            readAll = reader.readAll(CommissionBasicPolicyPremExcel.class);
        } catch (Exception e) {
            log.warn("读取费率表异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件出现异常"));
        }
        if (readAll.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("读取文件内容为空"));
        }
        log.info("开始解析补全费率表");
        Map<String, Integer> map = new HashMap<>(1);
        Map<String, String> settlementCompanyMap = DicCacheHelper.getSons(Constant.SETTLEMENT_INSTITUTION_DIC).stream()
            .collect(Collectors.toMap(DicCacheHelper.DicEntity::getValue, DicCacheHelper.DicEntity::getKey));
        List<CommissionBasicPolicyPremEntity> excelPremList = new ArrayList<>();
        for (int i = 0; i < readAll.size(); i++) {
            CommissionBasicPolicyPremExcel action = readAll.get(i);
            if (StrUtil.isBlank(action.getPolicyNo())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + (i + 1) + "行保单号为空啦"));
            }
            if (action.getCommissionRate() == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + (i + 1) + "行费率为空啦"));
            }
            if (StrUtil.isBlank(action.getSettlementCompanyName())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + (i + 1) + "行结算机构为空啦"));
            }
            String settlementCompanyCode = settlementCompanyMap.get(action.getSettlementCompanyName());
            if (StrUtil.isBlank(settlementCompanyCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + (i + 1) + "行结算机构名称错误"));
            }
            if(action.getCommissionRate().compareTo(BigDecimal.ZERO) <0 || action.getCommissionRate().compareTo(new BigDecimal("1"))>0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件第" + (i + 1) + "行费率不在[0,1]区间范围内"));
            }


            CommissionBasicPolicyPremEntity commissionBasicPolicyPrem = new CommissionBasicPolicyPremEntity();
            commissionBasicPolicyPrem.setPolicyNo(action.getPolicyNo());
            commissionBasicPolicyPrem.setBatchCode(
                StrUtil.isBlank(action.getBatchCode()) ? null : action.getBatchCode());
            commissionBasicPolicyPrem.setCommissionRate(action.getCommissionRate());
            commissionBasicPolicyPrem.setPremium(action.getPremium());
            commissionBasicPolicyPrem.setSettlementCompanyCode(settlementCompanyCode);
            commissionBasicPolicyPrem.setSettlementCompanyName(action.getSettlementCompanyName());
            commissionBasicPolicyPrem.setProductCode(action.getProductCode());
            commissionBasicPolicyPrem.setProductName(action.getProductName());
            commissionBasicPolicyPrem.setVehicleVesselTax(action.getVehicleVesselTax());
            commissionBasicPolicyPrem.setVehicleVesselTaxRate(action.getVehicleVesselTaxRate());
            commissionBasicPolicyPrem.setPeriod(action.getPeriod());
            commissionBasicPolicyPrem.setYear(action.getYear());
            // 匹配因子唯一值,当前只左存储,后面还会更新这个值.
            String premCode = ProductPremUtil.getCommissionPolicyFactor(commissionBasicPolicyPrem);
            if (map.containsKey(premCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("文件第{}行和文件第{}行保单号重复,请检查", map.get(premCode), i + 1)));
            } else {
                map.put(premCode, i + 1);
            }
            commissionBasicPolicyPrem.setPremCode(premCode);
            excelPremList.add(commissionBasicPolicyPrem);
        }
        List<String> policyNos = excelPremList.stream().map(CommissionBasicPolicyPremEntity::getPolicyNo).distinct()
            .collect(Collectors.toList());

        log.info("补充文件文件内容:{}", JSONUtil.toJsonStr(readAll));
        Map<String, CommissionBasicPolicyPremEntity> policyPremMap =
            lambdaQuery().in(CommissionBasicPolicyPremEntity::getPolicyNo, policyNos).list().stream()
                .collect(Collectors.toMap(ProductPremUtil::getCommissionPolicyFactor, v -> v, (v1, v2) -> {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("数据库中存在相同premCode他们的ID是[{}-{}]的数据,请联系开发人员检查", v1.getId(),
                            v2.getId())));
                }));

        // 新增费率信息
        List<CommissionBasicPolicyPremEntity> insertPolicyPremList = new ArrayList<>();
        List<CommissionBasicPolicyPremEntity> updatePolicyPremList = new ArrayList<>();
        List<String> removePolicyPremList = new ArrayList<>();
        excelPremList.forEach(action -> {
            // 费率的匹配因子一样
            if (policyPremMap.containsKey(action.getPremCode())) {
                CommissionBasicPolicyPremEntity commissionBasicPolicyPrem = policyPremMap.get(action.getPremCode());
                //判断他们的费率信息是否一样,如果不一样才处理
                if (!ProductPremUtil.getCommissionPolicyRateCode(action)
                    .equals(ProductPremUtil.getCommissionPolicyRateCode(commissionBasicPolicyPrem))) {
                    action.setId(commissionBasicPolicyPrem.getId());
                    String premCode = ProductPremUtil.getCommissionPolicyPremCode(action);
                    action.setPremCode(premCode);
                    updatePolicyPremList.add(action);
                    removePolicyPremList.add(commissionBasicPolicyPrem.getPremCode());
                }
            } else {
                // 匹配因子不一样直接进行追加
                String premCode = ProductPremUtil.getCommissionPolicyPremCode(action);
                action.setPremCode(premCode);
                insertPolicyPremList.add(action);
            }
        });

        //批量插入
        if (!insertPolicyPremList.isEmpty()) {
            CollUtil.split(insertPolicyPremList, 3000).forEach(updateList -> {
                try {
                    commissionBasicPolicyPremDao.insertBatchSomeColumn(updateList);
                } catch (Exception e) {
                    log.error("插入数据异常", e);
                    throw e;
                }
            });

        }
        if (!updatePolicyPremList.isEmpty()) {
            // 批量更新数据
            updateBatchById(updatePolicyPremList);
        }
        if (removePolicyPremList.isEmpty() && updatePolicyPremList.isEmpty() && insertPolicyPremList.isEmpty()) {
            log.info("数据没有变更不处理.....");
            return;
        }

        String pushEventCode = CommonUtils.createCodeLastNumber("PE");
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", userEntity.getUsername());
        //事件编码
        msgData.put("pushEventCode", pushEventCode);
        // 移除的保单数据
        msgData.put("removePolicyPremList", removePolicyPremList);
        // 修改的保单数据
        msgData.put("updatePolicyPremList", JSONObject.toJSONString(
            updatePolicyPremList.stream().map(CommissionBasicPolicyPremEntity::getPremCode)
                .collect(Collectors.toList())));
        // 新增的保单号数据
        msgData.put("insertPolicyPremList", JSONObject.toJSONString(
            insertPolicyPremList.stream().map(CommissionBasicPolicyPremEntity::getPremCode)
                .collect(Collectors.toList())));
        // 保存变更记录
        settlementPremChangeLogService.saveSettlementPremChangeLog(pushEventCode,
            PremChangeTypeEnum.COMMISSION_POLICY.getCode(), pushEventCode, msgData.toJSONString());
        // 推送事件
        SettlementProtocolHelper.pushSettlementProtocolEvent(pushEventCode,
            SettlementProtocolEventEnum.COMMISSION_POLICY_PREM_CHANGE, msgData);
    }

    /**
     * 删除费率
     *
     * @param id
     */
    @Override
    public void deletePolicyProductPrem(Integer id) {
        SysUserEntity userEntity = (SysUserEntity)SecurityUtils.getSubject().getPrincipal();
        if (userEntity == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("非法操作哦~"));
        }
        //获取数据
        CommissionBasicPolicyPremEntity policyProductPrem = getById(id);
        // 删除数据
        removeById(id);
        String businessCode = CommonUtils.createCodeLastNumber("PE");
        //MQ通知
        JSONObject msgData = new JSONObject();
        //操作时间"
        msgData.put("opeTime", DateUtil.date().toString());
        //操作人
        msgData.put("opeName", userEntity.getUsername());
        //事件编码
        msgData.put("pushEventCode", businessCode);
        // 移除的保单数据
        msgData.put("removePolicyPremList",
            JSONObject.toJSONString(CollUtil.newArrayList(policyProductPrem.getPremCode())));
        // 修改的保单数据
        msgData.put("updatePolicyPremList", "[]");
        // 新增的保单号数据
        msgData.put("insertPolicyPremList", "[]");
        // 保存记录
        settlementPremChangeLogService.saveSettlementPremChangeLog(businessCode,
            PremChangeTypeEnum.COMMISSION_POLICY.getCode(), businessCode, msgData.toJSONString());
        // 推送事件
        SettlementProtocolHelper.pushSettlementProtocolEvent(businessCode,
            SettlementProtocolEventEnum.COMMISSION_POLICY_PREM_CHANGE, msgData);
    }

}
