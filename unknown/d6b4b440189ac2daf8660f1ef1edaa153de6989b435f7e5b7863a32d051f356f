package com.mpolicy.manage.modules.agentApply.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineQuestionEntity;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamPageListOut;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyExamPageListVO;
import com.mpolicy.manage.modules.agentApply.vo.AgentApplyQuestionOut;

/**
 * 代理人入职申请题目表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-07 13:40:42
 */
public interface BlAgentOnlineQuestionService extends IService<BlAgentOnlineQuestionEntity> {

    /**
     * 分页获取代理人考试题目列表
     * @param input
     * @return
     */
    PageUtils<AgentApplyExamPageListOut> pageList(AgentApplyExamPageListVO input);

    /**
     * 根据题目编码获取题目及答案
     * @param code
     * @return
     */
    AgentApplyQuestionOut info(String code);

    /**
     * 根据题目编码删除题目及答案
     * @param code
     */
    void delete(String code);

    /**
     * 新增题目信息
     * @param input
     */
    void save(AgentApplyQuestionOut input);

    /**
     * 修改题目信息
     * @param input
     */
    void update(AgentApplyQuestionOut input);
}

