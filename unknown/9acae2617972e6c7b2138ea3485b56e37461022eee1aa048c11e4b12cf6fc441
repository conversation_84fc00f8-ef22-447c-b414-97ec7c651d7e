package com.mpolicy.manage.modules.commission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicInfoListInput;
import com.mpolicy.manage.modules.commission.vo.ExportCommissionBasicPrem;
import com.mpolicy.manage.modules.commission.vo.UploadCommissionBasicPremInput;

import java.util.List;

/**
 * 基础佣金费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-31 18:39:39
 */
public interface CommissionBasicPremService extends IService<CommissionBasicPremEntity> {

    /**
     * 删除费率文件
     * @param commissionCode 费率编码
     */
    void deletePremFile(String commissionCode);

    /**
     * 上传更新费率表
     * @param input 请求参数
     */
    void uploadPremFile(UploadCommissionBasicPremInput input);

    /**
     * 基础佣金费率表
     * @param input
     * @return
     */
    List<ExportCommissionBasicPrem> exportPremiumFile(CommissionBasicInfoListInput input);
}

