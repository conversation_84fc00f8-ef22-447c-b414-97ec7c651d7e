package com.mpolicy.manage.modules.commission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.commission.dao.CommissionBasicInfoDao;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicInfoEntity;
import com.mpolicy.manage.modules.commission.entity.CommissionBasicPremEntity;
import com.mpolicy.manage.modules.commission.service.CommissionBasicInfoService;
import com.mpolicy.manage.modules.commission.service.CommissionBasicPremService;
import com.mpolicy.manage.modules.commission.vo.*;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.service.common.service.DicCacheHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("commissionBasicInfoService")
public class CommissionBasicInfoServiceImpl extends ServiceImpl<CommissionBasicInfoDao, CommissionBasicInfoEntity> implements CommissionBasicInfoService {

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private CommissionBasicPremService commissionBasicPremService;


    /**
     * 获取基础佣金配置列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<CommissionBasicInfoListOut> findCommissionBasicInfoList(CommissionBasicInfoListInput input) {
        IPage<CommissionBasicInfoListOut> page = baseMapper.findCommissionBasicInfoList(new Page<CommissionBasicInfoListOut>(input.getPage(), input.getLimit()), input);
        return new PageUtils(page);
    }

    /**
     * 获取基础佣金配置详情
     *
     * @param id
     * @return
     */
    @Override
    public CommissionBasicInfoInfoOut findCommissionBasicInfoById(Integer id) {
        CommissionBasicInfoEntity commissionBasicInfo = baseMapper.selectById(id);
        if (commissionBasicInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在"));
        }
        CommissionBasicInfoInfoOut out = new CommissionBasicInfoInfoOut();
        BeanUtil.copyProperties(commissionBasicInfo, out);
        return out;
    }

    /**
     * 新增基础佣金配置数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCommissionBasicInfo(CommissionBasicInfoSaveInput input) {
        //判断一下添加的数据是否存在
        Integer count = lambdaQuery()
                .eq(CommissionBasicInfoEntity::getCompanyCode, input.getCompanyCode())
                .eq(CommissionBasicInfoEntity::getSettlementCompanyCode, input.getSettlementCompanyCode())
                .count();
        InsuranceCompanyEntity insuranceCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                        .eq(InsuranceCompanyEntity::getCompanyCode, input.getCompanyCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("机构信息不存在")));
        String settlementCompanyName = DicCacheHelper.getValue(input.getSettlementCompanyCode());
        if (StrUtil.isBlank(settlementCompanyName)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算机构不存在"));
        }
        if (count != null && count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保司[],结算机构[]已经存在", insuranceCompany.getCompanyName(), settlementCompanyName)));
        }
        CommissionBasicInfoEntity save = new CommissionBasicInfoEntity();
        BeanUtil.copyProperties(input, save);
        save.setCommissionCode(CommonUtils.createCodeLastNumber("CB"));
        save.setCompanyName(insuranceCompany.getCompanyName());
        save.setSettlementCompanyName(settlementCompanyName);
        baseMapper.insert(save);
    }

    /**
     * 修改基础佣金配置数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCommissionBasicInfoById(CommissionBasicInfoUpdateInput input) {
        SysUserEntity userEntity = ShiroUtils.getUserEntity();
        CommissionBasicInfoEntity commissionBasicInfo = baseMapper.selectById(input.getId());
        if (commissionBasicInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("修改的信息不存在"));
        }
        InsuranceCompanyEntity insuranceCompany = Optional.ofNullable(insuranceCompanyService.lambdaQuery()
                        .eq(InsuranceCompanyEntity::getCompanyCode, input.getCompanyCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("机构信息不存在")));

        Integer count = lambdaQuery().eq(CommissionBasicInfoEntity::getCompanyCode, input.getCompanyCode())
                .eq(CommissionBasicInfoEntity::getSettlementCompanyCode, input.getSettlementCompanyCode())
                .ne(CommissionBasicInfoEntity::getCommissionCode, input.getCommissionCode())
                .count();
        String settlementCompanyName = DicCacheHelper.getValue(input.getSettlementCompanyCode());
        if (StrUtil.isBlank(settlementCompanyName)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("结算机构不存在"));
        }
        if (count != null && count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保司[],结算机构[]已经存在", insuranceCompany.getCompanyName(), settlementCompanyName)));
        }

        //已经上传了费率那么就不能再修改保司信息了
        if (StatusEnum.NORMAL.getCode().equals(commissionBasicInfo.getPremStatus())) {
            if (!commissionBasicInfo.getCompanyCode().equals(input.getCompanyCode())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("费率表已经上传,如果修改保司请删除数据后在上传"));
            }
        }
        //修改信息
        lambdaUpdate().eq(CommissionBasicInfoEntity::getCommissionCode, commissionBasicInfo.getCommissionCode())
                .set(CommissionBasicInfoEntity::getCompanyCode, input.getCompanyCode())
                .set(CommissionBasicInfoEntity::getCompanyName, insuranceCompany.getCompanyName())
                .set(CommissionBasicInfoEntity::getSettlementCompanyCode, input.getSettlementCompanyCode())
                .set(CommissionBasicInfoEntity::getSettlementCompanyName, settlementCompanyName)
                .set(CommissionBasicInfoEntity::getBeginTime, input.getBeginTime())
                .set(CommissionBasicInfoEntity::getEndTime, input.getEndTime())
                .set(CommissionBasicInfoEntity::getRemark, input.getRemark())
                .set(CommissionBasicInfoEntity::getUpdateTime, new Date())
                .set(CommissionBasicInfoEntity::getUpdateUser, userEntity.getUsername())
                .update();
    }

    @Override
    public void deleteByIds(List<Integer> ids) {
        List<CommissionBasicInfoEntity> commissionBasicList = lambdaQuery()
                .in(CommissionBasicInfoEntity::getId, ids)
                .ne(CommissionBasicInfoEntity::getCommissionStatus, 0)
                .list();
        if (commissionBasicList.size() > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("状态不是待生效的配置不允许删除"));
        }
        // 删除数据
        removeByIds(ids);
        // 删除费率表 .....
        commissionBasicPremService.lambdaUpdate()
                .in(CommissionBasicPremEntity::getCommissionCode, commissionBasicList.stream().map(CommissionBasicInfoEntity::getCommissionCode).collect(Collectors.toList()))
                .remove();
    }
}
