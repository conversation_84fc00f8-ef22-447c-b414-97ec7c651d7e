package com.mpolicy.manage.modules.agentApply.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineAnswerJoinEntity;

import java.util.Map;

/**
 * 代理人入职答题表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-12-07 13:40:42
 */
public interface BlAgentOnlineAnswerJoinService extends IService<BlAgentOnlineAnswerJoinEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

