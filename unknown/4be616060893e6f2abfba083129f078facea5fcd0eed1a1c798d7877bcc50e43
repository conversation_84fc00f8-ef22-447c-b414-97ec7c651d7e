package com.mpolicy.manage.modules.commission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.ProgrammeStatusEnum;
import com.mpolicy.manage.modules.commission.dao.CommissionFloatRewardDao;
import com.mpolicy.manage.modules.commission.entity.CommissionFloatRewardEntity;
import com.mpolicy.manage.modules.commission.service.CommissionFloatRewardService;
import com.mpolicy.manage.modules.commission.vo.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service("commissionFloatRewardService")
public class CommissionFloatRewardServiceImpl extends ServiceImpl<CommissionFloatRewardDao, CommissionFloatRewardEntity> implements CommissionFloatRewardService {


    /**
     * 获取浮动奖励佣金配置列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<CommissionFloatRewardListOut> findCommissionFloatRewardList(CommissionFloatRewardListInput input) {
        IPage<CommissionFloatRewardListOut> page = baseMapper.findCommissionFloatRewardList(new Page<CommissionFloatRewardListOut>(input.getPage(), input.getLimit()), input);
        page.getRecords().forEach(action -> {
            if (action.getProgrammeType() == 0) {
                action.setProgrammeTypeDesc("加佣奖励");
            }
            //时间
            action.setProgrammeTime(DateUtil.date(action.getBeginTime()) + "至" + DateUtil.date(action.getEndTime()));
        });
        return new PageUtils(page);
    }

    /**
     * 获取浮动奖励佣金配置详情
     *
     * @param id
     * @return
     */
    @Override
    public CommissionFloatRewardInfoOut findCommissionFloatRewardById(Integer id) {
        CommissionFloatRewardEntity commissionFloatReward = baseMapper.selectById(id);
        if (commissionFloatReward == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("详情信息不存在"));
        }
        CommissionFloatRewardInfoOut out = new CommissionFloatRewardInfoOut();
        BeanUtil.copyProperties(commissionFloatReward, out);
        return out;
    }

    /**
     * 新增浮动奖励佣金配置数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCommissionFloatReward(CommissionFloatRewardSaveInput input) {
        CommissionFloatRewardEntity save = new CommissionFloatRewardEntity();
        BeanUtil.copyProperties(input, save);
        //添加
        baseMapper.insert(save);
        // 创建编码
        lambdaUpdate().eq(CommissionFloatRewardEntity::getId, save.getId())
                .set(CommissionFloatRewardEntity::getProgrammeCode, "FD" + StrUtil.padPre(save.getId().toString(), 6, "0"))
                .update();
    }

    /**
     * 修改浮动奖励佣金配置数据
     *
     * @param input
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCommissionFloatRewardById(CommissionFloatRewardUpdateInput input) {
        CommissionFloatRewardEntity commissionFloatReward = baseMapper.selectById(input.getId());
        if (commissionFloatReward == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("修改的信息不存在"));
        }
        CommissionFloatRewardEntity update = new CommissionFloatRewardEntity();
        BeanUtil.copyProperties(input, update);
        baseMapper.updateById(update);
    }

    /**
     * 修改浮动奖励佣金配置状态
     *
     * @param input
     */
    @Override
    public void updateStatus(CommissionFloatRewardStatusInput input) {
        CommissionFloatRewardEntity commissionFloatReward = baseMapper.selectById(input.getId());
        if (commissionFloatReward == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("修改的信息不存在"));
        }
        Date currentTime = new Date();
        switch (ProgrammeStatusEnum.matchSearchCode(input.getProgrammeStatus())) {
            case NOT_STARTED:
                //设置活动开始|继续
                Integer programmeStatus = ProgrammeStatusEnum.NOT_STARTED.getCode();
                if (DateUtil.compare(currentTime, commissionFloatReward.getBeginTime()) > -1) {
                    programmeStatus = ProgrammeStatusEnum.IN_PROGRESS.getCode();
                }
                if (DateUtil.compare(currentTime, commissionFloatReward.getEndTime()) > -1) {
                    programmeStatus = ProgrammeStatusEnum.ENDED.getCode();
                }
                lambdaUpdate()
                        .set(CommissionFloatRewardEntity::getProgrammeStatus, programmeStatus)
                        .set(ProgrammeStatusEnum.NETWORK_SALES.getCode().equals(commissionFloatReward.getProgrammeStatus()),
                                CommissionFloatRewardEntity::getReleaseTime, new Date())
                        .eq(CommissionFloatRewardEntity::getId, input.getId())
                        .update();
                break;
            case PAUSED:
            case VOIDED:
                //设置活动作废 / 暂停
                lambdaUpdate()
                        .set(CommissionFloatRewardEntity::getProgrammeStatus, input.getProgrammeStatus())
                        .eq(CommissionFloatRewardEntity::getId, input.getId())
                        .update();
                break;
            default: {
                //啥也不做....
            }
        }


    }
}
