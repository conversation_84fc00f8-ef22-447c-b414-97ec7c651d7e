package com.mpolicy.manage.modules.commission.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.commission.service.CommissionBasicPremService;
import com.mpolicy.manage.modules.commission.vo.CommissionBasicInfoListInput;
import com.mpolicy.manage.modules.commission.vo.ExportCommissionBasicPrem;
import com.mpolicy.manage.modules.commission.vo.UploadCommissionBasicPremInput;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * 基础佣金费率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-31 18:39:39
 */
@Slf4j
@Api(tags = "基础佣金费率")
@RestController
@RequestMapping("commission/basic/prem")
public class CommissionBasicPremController {

    @Autowired
    private CommissionBasicPremService commissionBasicPremService;

    /**
     * 上传更新费率表
     *
     * @return
     */
    @ApiOperation(value = "上传更新费率表", notes = "上传更新费率表")
    @PostMapping("uploadPremFile")
    public Result uploadPremFile(UploadCommissionBasicPremInput input) {
        FileModelEnum fileModelEnum = FileModelEnum.decode(input.getFileSystem());
        if (fileModelEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件操作分类参数错误"));
        }
        MultipartFile file = input.getFile();
        // 验证文件格式
        if (!fileModelEnum.checkFileType(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("上传文件格式不支持"));
        }
        // 上传文件
        commissionBasicPremService.uploadPremFile(input);
        return Result.success();
    }

    /**
     * 删除费率文件
     * @param commissionCode
     * @return
     */
    @ApiOperation(value = "删除费率文件", notes = "删除费率文件")
    @PostMapping("deletePremFile/{commissionCode}")
    public Result deletePremiumFile(@PathVariable(value = "commissionCode", required = false)
                                            String commissionCode) {
        commissionBasicPremService.deletePremFile(commissionCode);
        return Result.success();
    }

    /**
     * 导出所有协议 费率表
     */
    @PostMapping("exportPremiumFile")
    @ApiOperation(value = "导出费率表", notes = "导出费率表")
    @NoRepeatSubmit(keyName = "token")
    public void exportAllProtocol(HttpServletResponse response,
                                  @RequestBody(required = false)
                                  @Valid CommissionBasicInfoListInput input) {
        Long start = System.currentTimeMillis();
        log.info("基础佣金费率表");
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            String fileName = StrUtil.format("基础佣金费率表-{}.xlsx", DateUtil.today());
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode(fileName));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, ExportCommissionBasicPrem.class);
            sheet.setSheetName("协议费率表");
            int page = 1;
            int limitSize = 5000;
            while (true) {
                input.setPage(page);
                input.setLimit(limitSize);
                List<ExportCommissionBasicPrem> result = commissionBasicPremService.exportPremiumFile(input);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(result)) {
                    log.info("基础佣金费率表，page={},limitSize={},limitSize = {}", page, limitSize, result.size());
                    writer.write(result, sheet);
                    page++;
                } else {
                    log.info("基础佣金费率表构建完成，执行导出");
                    break;
                }
            }
            writer.finish();
            out.flush();
        } catch (IOException e) {
            log.info("基础佣金费率表异常", e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("导出基础佣金费率表异常"));
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        Long end = System.currentTimeMillis();
        log.info("导出协议费率表，用时为：{}毫秒", end - start);
    }
}
