package com.mpolicy.manage.modules.regulators.service.report.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mpolicy.manage.modules.regulators.service.report.data.CompanyIncomeData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 利润表报告数据解析
 *
 * <AUTHOR>
 * @date 2022-01-21 18:48
 */
@Slf4j
public class CompanyIncomeListener extends ReportObjectEventListener<Object, CompanyIncomeData> {

    List<CompanyIncomeData> datas = new ArrayList<>();

    @Override
    public List<CompanyIncomeData> getReadData() {
        return datas;
    }

    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.debug("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);

        // 暂时用判断 超过14行，不解析
        JSONArray lineData = JSON.parseArray(xlsLineData);
        if(lineData.size() < 5){
            return;
        }
        // 解析赋值对象
        CompanyIncomeData bean = new CompanyIncomeData();
        bean.setIncomeProject(lineData.getString(0));
        bean.setLineNumber(lineData.getString(1));
        bean.setMonthNumber(lineData.getString(2));
        bean.setYearNumber(lineData.getString(3));
        // 父节点逻辑写死
        bean.setParentLineNumber(builderParentLineNumber(bean.getLineNumber()));
        datas.add(bean);
    }


    /**
     * <p>
     * 结合导入的模板文件，进行父节点写死配置
     * </p>
     *
     * @param lineNumber lineNumber
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/1/21
     */
    private String builderParentLineNumber(String lineNumber) {
        if (StringUtils.isBlank(lineNumber)) {
            return StrUtil.EMPTY;
        }

        int number = Integer.parseInt(lineNumber);
        if (number == 1 || number == 8 || number == 13 || number == 17 || number == 19) {
            return "0";
        }
        if (number > 1 && number < 8) {
            return "1";
        }
        if (number > 8 && number < 13) {
            return "8";
        }
        if (number > 13 && number < 17) {
            return "13";
        }
        if (number == 18) {
            return "17";
        }
        return "19";
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
