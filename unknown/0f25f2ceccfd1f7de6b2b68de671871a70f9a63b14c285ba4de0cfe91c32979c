package com.mpolicy.manage.modules.commission.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础佣金费率-一单一议
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-14 16:08:41
 */
@TableName("commission_basic_policy_prem")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionBasicPolicyPremEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 费率编码
     */
    private String premCode;
    /**
     * 批单号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String batchCode;

    /**
     * 保单年期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer year;
    /**
     * 缴费期次
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer period;

    /**
     * 险种编码
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productCode;

    /**
     * 险种名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productName;
    /**
     * 保费 没啥用就是个显示
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal premium;
    /**
     * 基础佣金费率
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal commissionRate;
    /**
     * 车船税（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal vehicleVesselTax;
    /**
     * 车船税费率
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal vehicleVesselTaxRate;
    /**
     * 结算保司编码
     */
    private String settlementCompanyCode;
    /**
     * 结算保司名称
     */
    private String settlementCompanyName;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
