package com.mpolicy.manage.modules.agentApply.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentOnlineFileInfoVo;
import com.mpolicy.manage.modules.agentApply.entity.BlAgentOnlineJoinApplyEntity;
import com.mpolicy.manage.modules.agentApply.vo.*;

import java.util.List;

/**
 * 代理人线上入职用户申请信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2022-11-24 16:28:21
 */
public interface BlAgentOnlineJoinApplyService extends IService<BlAgentOnlineJoinApplyEntity> {

    /**
     * 分页查询代理人申请列表
     * @return
     */
    PageUtils<AgentApplyPageListOut> pageList(AgentApplyPageListVO input);

    /**
     * 导出数据查询
     * @param input
     * @return
     */
    List<AgentApplyExportVo> export(AgentApplyPageListVO input);

    /**
     * 通过代理人编码查询代理人详细信息
     * @param agentCode
     * @return
     */
    AgentApplyInfoOut queryDetail(String agentCode);

    /**
     * 代理人入职申请信息更改
     * @param update
     */
    void update(AgentApplyUpdateInfoVO update);

    /**
     * 代理人完成面试信息
     * @param finish
     */
    void finishInterview(AgentApplyFinishInterviewVO finish);

    /**
     * 代理人完成培训
     * @param train
     */
    void finishTrain(AgentApplyFinishTrainVO train);

    /**
     * 补充代理人申请信息
     * @param vo
     */
    void replenishInfo(AgentApplyReplenishVO vo);

    /**
     * 代理人申请信息复审通过
     * @param agentCode
     */
    void pass(String agentCode);

    /**
     * 代理人审核驳回
     * @param vo
     */
    void reject(AgentApplyRejectVO vo);

    /**
     * 通过代理人编码查询代理人线上入职相关附件信息
     * @param agentCode
     * @return
     */
    AgentOnlineFileInfoVo queryFileInfo(String agentCode);

    /**
     * 关闭代理人申请信息
     * @param agentCode 代理人编码
     */
    void close(String agentCode);
}

